(function(){var t,e=[].indexOf||function(t){for(var e=0,n=this.length;n>e;e++)if(e in this&&this[e]===t)return e;return-1},n=[].slice,r=function(t,e){return function(){return t.apply(e,arguments)}},a={}.hasOwnProperty;(t=function(t){return"object"==typeof exports&&"object"==typeof module?t(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)})(function(t){var o,i,l,u,s,c,d,h,p,f,m,v,g,b,C,y,w,A,x,N,S;return i=function(t,e,n){var r,a,o,i;for(t+="",a=t.split("."),o=a[0],i=a.length>1?n+a[1]:"",r=/(\d+)(\d{3})/;r.test(o);)o=o.replace(r,"$1"+e+"$2");return o+i},m=function(e){var n;return n={digitsAfterDecimal:2,scaler:1,thousandsSep:",",decimalSep:".",prefix:"",suffix:"",showZero:!1},e=t.extend({},n,e),function(t){var n;return isNaN(t)||!isFinite(t)?"":0!==t||e.showZero?(n=i((e.scaler*t).toFixed(e.digitsAfterDecimal),e.thousandsSep,e.decimalSep),""+e.prefix+n+e.suffix):""}},A=m(),x=m({digitsAfterDecimal:0}),N=m({digitsAfterDecimal:1,scaler:100,suffix:"%"}),l={count:function(t){return null==t&&(t=x),function(){return function(e,n,r){return{count:0,push:function(){return this.count++},value:function(){return this.count},format:t}}}},countUnique:function(t){return null==t&&(t=x),function(n){var r;return r=n[0],function(n,a,o){return{uniq:[],push:function(t){var n;return n=t[r],e.call(this.uniq,n)<0?this.uniq.push(t[r]):void 0},value:function(){return this.uniq.length},format:t,numInputs:null!=r?0:1}}}},listUnique:function(t){return function(n){var r;return r=n[0],function(n,a,o){return{uniq:[],push:function(t){var n;return n=t[r],e.call(this.uniq,n)<0?this.uniq.push(t[r]):void 0},value:function(){return this.uniq.join(t)},format:function(t){return t},numInputs:null!=r?0:1}}}},sum:function(t){return null==t&&(t=A),function(e){var n;return n=e[0],function(e,r,a){return{sum:0,push:function(t){return isNaN(parseFloat(t[n]))?void 0:this.sum+=parseFloat(t[n])},value:function(){return this.sum},format:t,numInputs:null!=n?0:1}}}},min:function(t){return null==t&&(t=A),function(e){var n;return n=e[0],function(e,r,a){return{val:null,push:function(t){var e,r;return r=parseFloat(t[n]),isNaN(r)?void 0:this.val=Math.min(r,null!=(e=this.val)?e:r)},value:function(){return this.val},format:t,numInputs:null!=n?0:1}}}},max:function(t){return null==t&&(t=A),function(e){var n;return n=e[0],function(e,r,a){return{val:null,push:function(t){var e,r;return r=parseFloat(t[n]),isNaN(r)?void 0:this.val=Math.max(r,null!=(e=this.val)?e:r)},value:function(){return this.val},format:t,numInputs:null!=n?0:1}}}},first:function(t){return null==t&&(t=A),function(e){var n;return n=e[0],function(e,r,a){return{val:null,sorter:d(null!=e?e.sorters:void 0,n),push:function(t){var e,r;return r=t[n],this.sorter(r,null!=(e=this.val)?e:r)<=0?this.val=r:void 0},value:function(){return this.val},format:function(e){return isNaN(e)?e:t(e)},numInputs:null!=n?0:1}}}},last:function(t){return null==t&&(t=A),function(e){var n;return n=e[0],function(e,r,a){return{val:null,sorter:d(null!=e?e.sorters:void 0,n),push:function(t){var e,r;return r=t[n],this.sorter(r,null!=(e=this.val)?e:r)>=0?this.val=r:void 0},value:function(){return this.val},format:function(e){return isNaN(e)?e:t(e)},numInputs:null!=n?0:1}}}},average:function(t){return null==t&&(t=A),function(e){var n;return n=e[0],function(e,r,a){return{sum:0,len:0,push:function(t){return isNaN(parseFloat(t[n]))?void 0:(this.sum+=parseFloat(t[n]),this.len++)},value:function(){return this.sum/this.len},format:t,numInputs:null!=n?0:1}}}},sumOverSum:function(t){return null==t&&(t=A),function(e){var n,r;return r=e[0],n=e[1],function(e,a,o){return{sumNum:0,sumDenom:0,push:function(t){return isNaN(parseFloat(t[r]))||(this.sumNum+=parseFloat(t[r])),isNaN(parseFloat(t[n]))?void 0:this.sumDenom+=parseFloat(t[n])},value:function(){return this.sumNum/this.sumDenom},format:t,numInputs:null!=r&&null!=n?0:2}}}},sumOverSumBound80:function(t,e){return null==t&&(t=!0),null==e&&(e=A),function(n){var r,a;return a=n[0],r=n[1],function(n,o,i){return{sumNum:0,sumDenom:0,push:function(t){return isNaN(parseFloat(t[a]))||(this.sumNum+=parseFloat(t[a])),isNaN(parseFloat(t[r]))?void 0:this.sumDenom+=parseFloat(t[r])},value:function(){var e;return e=t?1:-1,(.821187207574908/this.sumDenom+this.sumNum/this.sumDenom+1.2815515655446004*e*Math.sqrt(.410593603787454/(this.sumDenom*this.sumDenom)+this.sumNum*(1-this.sumNum/this.sumDenom)/(this.sumDenom*this.sumDenom)))/(1+1.642374415149816/this.sumDenom)},format:e,numInputs:null!=a&&null!=r?0:2}}}},fractionOf:function(t,e,r){return null==e&&(e="total"),null==r&&(r=N),function(){var a;return a=1<=arguments.length?n.call(arguments,0):[],function(n,o,i){return{selector:{total:[[],[]],row:[o,[]],col:[[],i]}[e],inner:t.apply(null,a)(n,o,i),push:function(t){return this.inner.push(t)},format:r,value:function(){return this.inner.value()/n.getAggregator.apply(n,this.selector).inner.value()},numInputs:t.apply(null,a)().numInputs}}}}},u=function(t){return{Count:t.count(x),"Count Unique Values":t.countUnique(x),"List Unique Values":t.listUnique(", "),Sum:t.sum(A),"Integer Sum":t.sum(x),Average:t.average(A),Minimum:t.min(A),Maximum:t.max(A),First:t.first(A),Last:t.last(A),"Sum over Sum":t.sumOverSum(A),"80% Upper Bound":t.sumOverSumBound80(!0,A),"80% Lower Bound":t.sumOverSumBound80(!1,A),"Sum as Fraction of Total":t.fractionOf(t.sum(),"total",N),"Sum as Fraction of Rows":t.fractionOf(t.sum(),"row",N),"Sum as Fraction of Columns":t.fractionOf(t.sum(),"col",N),"Count as Fraction of Total":t.fractionOf(t.count(),"total",N),"Count as Fraction of Rows":t.fractionOf(t.count(),"row",N),"Count as Fraction of Columns":t.fractionOf(t.count(),"col",N)}}(l),b={Table:function(t,e){return v(t,e)},"Table Barchart":function(e,n){return t(v(e,n)).barchart()},Heatmap:function(e,n){return t(v(e,n)).heatmap("heatmap",n)},"Row Heatmap":function(e,n){return t(v(e,n)).heatmap("rowheatmap",n)},"Col Heatmap":function(e,n){return t(v(e,n)).heatmap("colheatmap",n)}},h={en:{aggregators:u,renderers:b,localeStrings:{renderError:"An error occurred rendering the PivotTable results.",computeError:"An error occurred computing the PivotTable results.",uiRenderError:"An error occurred rendering the PivotTable UI.",selectAll:"Select All",selectNone:"Select None",tooMany:"(too many to list)",filterResults:"Filter values",apply:"Apply",cancel:"Cancel",totals:"Totals",vs:"vs",by:"by"}}},p=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],s=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],S=function(t){return("0"+t).substr(-2,2)},c={bin:function(t,e){return function(n){return n[t]-n[t]%e}},dateFormat:function(t,e,n,r,a){var o;return null==n&&(n=!1),null==r&&(r=p),null==a&&(a=s),o=n?"UTC":"",function(n){var i;return i=new Date(Date.parse(n[t])),isNaN(i)?"":e.replace(/%(.)/g,function(t,e){switch(e){case"y":return i["get"+o+"FullYear"]();case"m":return S(i["get"+o+"Month"]()+1);case"n":return r[i["get"+o+"Month"]()];case"d":return S(i["get"+o+"Date"]());case"w":return a[i["get"+o+"Day"]()];case"x":return i["get"+o+"Day"]();case"H":return S(i["get"+o+"Hours"]());case"M":return S(i["get"+o+"Minutes"]());case"S":return S(i["get"+o+"Seconds"]());default:return"%"+e}})}}},C=/(\d+)|(\D+)/g,g=/\d/,y=/^0/,f=function(t){return function(t,e){var n,r,a,o,i,l;if(null!=e&&null==t)return-1;if(null!=t&&null==e)return 1;if("number"==typeof t&&isNaN(t))return-1;if("number"==typeof e&&isNaN(e))return 1;if(i=+t,l=+e,l>i)return-1;if(i>l)return 1;if("number"==typeof t&&"number"!=typeof e)return-1;if("number"==typeof e&&"number"!=typeof t)return 1;if("number"==typeof t&&"number"==typeof e)return 0;if(isNaN(l)&&!isNaN(i))return-1;if(isNaN(i)&&!isNaN(l))return 1;if(n=String(t),a=String(e),n===a)return 0;if(!g.test(n)||!g.test(a))return n>a?1:-1;for(n=n.match(C),a=a.match(C);n.length&&a.length;)if(r=n.shift(),o=a.shift(),r!==o)return g.test(r)&&g.test(o)?r.replace(y,".0")-o.replace(y,".0"):r>o?1:-1;return n.length-a.length}}(this),w=function(t){var e,n,r,a;r={},n={};for(e in t)a=t[e],r[a]=e,"string"==typeof a&&(n[a.toLowerCase()]=e);return function(t,e){return null!=r[t]&&null!=r[e]?r[t]-r[e]:null!=r[t]?-1:null!=r[e]?1:null!=n[t]&&null!=n[e]?n[t]-n[e]:null!=n[t]?-1:null!=n[e]?1:f(t,e)}},d=function(e,n){var r;if(null!=e)if(t.isFunction(e)){if(r=e(n),t.isFunction(r))return r}else if(null!=e[n])return e[n];return f},o=function(){function e(t,n){var a,o,i,u,s,c,d,h,p,f;null==n&&(n={}),this.getAggregator=r(this.getAggregator,this),this.getRowKeys=r(this.getRowKeys,this),this.getColKeys=r(this.getColKeys,this),this.sortKeys=r(this.sortKeys,this),this.arrSort=r(this.arrSort,this),this.input=t,this.aggregator=null!=(a=n.aggregator)?a:l.count()(),this.aggregatorName=null!=(o=n.aggregatorName)?o:"Count",this.colAttrs=null!=(i=n.cols)?i:[],this.rowAttrs=null!=(u=n.rows)?u:[],this.valAttrs=null!=(s=n.vals)?s:[],this.sorters=null!=(c=n.sorters)?c:{},this.rowOrder=null!=(d=n.rowOrder)?d:"key_a_to_z",this.colOrder=null!=(h=n.colOrder)?h:"key_a_to_z",this.derivedAttributes=null!=(p=n.derivedAttributes)?p:{},this.filter=null!=(f=n.filter)?f:function(){return!0},this.tree={},this.rowKeys=[],this.colKeys=[],this.rowTotals={},this.colTotals={},this.allTotal=this.aggregator(this,[],[]),this.sorted=!1,e.forEachRecord(this.input,this.derivedAttributes,function(t){return function(e){return t.filter(e)?t.processRecord(e):void 0}}(this))}return e.forEachRecord=function(e,n,r){var o,i,l,u,s,c,d,h,p,f,m,v;if(o=t.isEmptyObject(n)?r:function(t){var e,a,o;for(e in n)o=n[e],t[e]=null!=(a=o(t))?a:t[e];return r(t)},t.isFunction(e))return e(o);if(t.isArray(e)){if(t.isArray(e[0])){f=[];for(l in e)if(a.call(e,l)&&(i=e[l],l>0)){h={},p=e[0];for(u in p)a.call(p,u)&&(s=p[u],h[s]=i[u]);f.push(o(h))}return f}for(m=[],c=0,d=e.length;d>c;c++)h=e[c],m.push(o(h));return m}if(e instanceof jQuery)return v=[],t("thead > tr > th",e).each(function(e){return v.push(t(this).text())}),t("tbody > tr",e).each(function(e){return h={},t("td",this).each(function(e){return h[v[e]]=t(this).text()}),o(h)});throw new Error("unknown input format")},e.prototype.forEachMatchingRecord=function(t,n){return e.forEachRecord(this.input,this.derivedAttributes,function(e){return function(r){var a,o,i;if(e.filter(r)){for(a in t)if(i=t[a],i!==(null!=(o=r[a])?o:"null"))return;return n(r)}}}(this))},e.prototype.arrSort=function(t){var e,n;return n=function(){var n,r,a;for(a=[],n=0,r=t.length;r>n;n++)e=t[n],a.push(d(this.sorters,e));return a}.call(this),function(t,e){var r,o,i;for(o in n)if(a.call(n,o)&&(i=n[o],r=i(t[o],e[o]),0!==r))return r;return 0}},e.prototype.sortKeys=function(){var t;if(!this.sorted){switch(this.sorted=!0,t=function(t){return function(e,n){return t.getAggregator(e,n).value()}}(this),this.rowOrder){case"value_a_to_z":this.rowKeys.sort(function(e){return function(e,n){return f(t(e,[]),t(n,[]))}}(this));break;case"value_z_to_a":this.rowKeys.sort(function(e){return function(e,n){return-f(t(e,[]),t(n,[]))}}(this));break;default:this.rowKeys.sort(this.arrSort(this.rowAttrs))}switch(this.colOrder){case"value_a_to_z":return this.colKeys.sort(function(e){return function(e,n){return f(t([],e),t([],n))}}(this));case"value_z_to_a":return this.colKeys.sort(function(e){return function(e,n){return-f(t([],e),t([],n))}}(this));default:return this.colKeys.sort(this.arrSort(this.colAttrs))}}},e.prototype.getColKeys=function(){return this.sortKeys(),this.colKeys},e.prototype.getRowKeys=function(){return this.sortKeys(),this.rowKeys},e.prototype.processRecord=function(t){var e,n,r,a,o,i,l,u,s,c,d,h,p;for(e=[],h=[],u=this.colAttrs,a=0,o=u.length;o>a;a++)p=u[a],e.push(null!=(s=t[p])?s:"null");for(c=this.rowAttrs,l=0,i=c.length;i>l;l++)p=c[l],h.push(null!=(d=t[p])?d:"null");return r=h.join(String.fromCharCode(0)),n=e.join(String.fromCharCode(0)),this.allTotal.push(t),0!==h.length&&(this.rowTotals[r]||(this.rowKeys.push(h),this.rowTotals[r]=this.aggregator(this,h,[])),this.rowTotals[r].push(t)),0!==e.length&&(this.colTotals[n]||(this.colKeys.push(e),this.colTotals[n]=this.aggregator(this,[],e)),this.colTotals[n].push(t)),0!==e.length&&0!==h.length?(this.tree[r]||(this.tree[r]={}),this.tree[r][n]||(this.tree[r][n]=this.aggregator(this,h,e)),this.tree[r][n].push(t)):void 0},e.prototype.getAggregator=function(t,e){var n,r,a;return a=t.join(String.fromCharCode(0)),r=e.join(String.fromCharCode(0)),n=0===t.length&&0===e.length?this.allTotal:0===t.length?this.colTotals[r]:0===e.length?this.rowTotals[a]:this.tree[a][r],null!=n?n:{value:function(){return null},format:function(){return""}}},e}(),t.pivotUtilities={aggregatorTemplates:l,aggregators:u,renderers:b,derivers:c,locales:h,naturalSort:f,numberFormat:m,sortAs:w,PivotData:o},v=function(e,n){var r,o,i,l,u,s,c,d,h,p,f,m,v,g,b,C,y,w,A,x,N,S,T,k;s={table:{clickCallback:null},localeStrings:{totals:"Totals"}},n=t.extend(!0,{},s,n),i=e.colAttrs,m=e.rowAttrs,g=e.getRowKeys(),u=e.getColKeys(),n.table.clickCallback&&(c=function(t,r,o){var l,u,s;u={};for(s in i)a.call(i,s)&&(l=i[s],null!=o[s]&&(u[l]=o[s]));for(s in m)a.call(m,s)&&(l=m[s],null!=r[s]&&(u[l]=r[s]));return function(r){return n.table.clickCallback(r,t,u,e)}}),f=document.createElement("table"),f.className="pvtTable",b=function(t,e,n){var r,a,o,i,l,u,s,c;if(0!==e){for(i=!0,c=r=0,l=n;l>=0?l>=r:r>=l;c=l>=0?++r:--r)t[e-1][c]!==t[e][c]&&(i=!1);if(i)return-1}for(a=0;e+a<t.length;){for(s=!1,c=o=0,u=n;u>=0?u>=o:o>=u;c=u>=0?++o:--o)t[e][c]!==t[e+a][c]&&(s=!0);if(s)break;a++}return a},A=document.createElement("thead");for(h in i)if(a.call(i,h)){o=i[h],N=document.createElement("tr"),0===parseInt(h)&&0!==m.length&&(w=document.createElement("th"),w.setAttribute("colspan",m.length),w.setAttribute("rowspan",i.length),N.appendChild(w)),w=document.createElement("th"),w.className="pvtAxisLabel",w.textContent=o,N.appendChild(w);for(d in u)a.call(u,d)&&(l=u[d],k=b(u,parseInt(d),parseInt(h)),-1!==k&&(w=document.createElement("th"),w.className="pvtColLabel",w.textContent=l[h],w.setAttribute("colspan",k),parseInt(h)===i.length-1&&0!==m.length&&w.setAttribute("rowspan",2),N.appendChild(w)));0===parseInt(h)&&(w=document.createElement("th"),w.className="pvtTotalLabel",w.innerHTML=n.localeStrings.totals,w.setAttribute("rowspan",i.length+(0===m.length?0:1)),N.appendChild(w)),A.appendChild(N)}if(0!==m.length){N=document.createElement("tr");for(d in m)a.call(m,d)&&(p=m[d],w=document.createElement("th"),w.className="pvtAxisLabel",w.textContent=p,N.appendChild(w));w=document.createElement("th"),0===i.length&&(w.className="pvtTotalLabel",w.innerHTML=n.localeStrings.totals),N.appendChild(w),A.appendChild(N)}f.appendChild(A),C=document.createElement("tbody");for(d in g)if(a.call(g,d)){v=g[d],N=document.createElement("tr");for(h in v)a.call(v,h)&&(S=v[h],k=b(g,parseInt(d),parseInt(h)),-1!==k&&(w=document.createElement("th"),w.className="pvtRowLabel",w.textContent=S,w.setAttribute("rowspan",k),parseInt(h)===m.length-1&&0!==i.length&&w.setAttribute("colspan",2),N.appendChild(w)));for(h in u)a.call(u,h)&&(l=u[h],r=e.getAggregator(v,l),T=r.value(),y=document.createElement("td"),y.className="pvtVal row"+d+" col"+h,y.textContent=r.format(T),y.setAttribute("data-value",T),null!=c&&(y.onclick=c(T,v,l)),N.appendChild(y));x=e.getAggregator(v,[]),T=x.value(),y=document.createElement("td"),y.className="pvtTotal rowTotal",y.textContent=x.format(T),y.setAttribute("data-value",T),null!=c&&(y.onclick=c(T,v,[])),y.setAttribute("data-for","row"+d),N.appendChild(y),C.appendChild(N)}N=document.createElement("tr"),w=document.createElement("th"),w.className="pvtTotalLabel",w.innerHTML=n.localeStrings.totals,w.setAttribute("colspan",m.length+(0===i.length?0:1)),N.appendChild(w);for(h in u)a.call(u,h)&&(l=u[h],x=e.getAggregator([],l),T=x.value(),y=document.createElement("td"),y.className="pvtTotal colTotal",y.textContent=x.format(T),y.setAttribute("data-value",T),null!=c&&(y.onclick=c(T,[],l)),y.setAttribute("data-for","col"+h),N.appendChild(y));return x=e.getAggregator([],[]),T=x.value(),y=document.createElement("td"),y.className="pvtGrandTotal",y.textContent=x.format(T),y.setAttribute("data-value",T),null!=c&&(y.onclick=c(T,[],[])),N.appendChild(y),C.appendChild(N),f.appendChild(C),f.setAttribute("data-numrows",g.length),f.setAttribute("data-numcols",u.length),f},t.fn.pivot=function(e,n,r){var a,i,u,s,c,d,p,f;null==r&&(r="en"),null==h[r]&&(r="en"),a={cols:[],rows:[],vals:[],rowOrder:"key_a_to_z",colOrder:"key_a_to_z",dataClass:o,filter:function(){return!0},aggregator:l.count()(),aggregatorName:"Count",sorters:{},derivedAttributes:{},renderer:v},s=t.extend(!0,{},h.en.localeStrings,h[r].localeStrings),u={rendererOptions:{localeStrings:s},localeStrings:s},c=t.extend(!0,{},u,t.extend({},a,n)),p=null;try{d=new c.dataClass(e,c);try{p=c.renderer(d,c.rendererOptions)}catch(m){i=m,"undefined"!=typeof console&&null!==console&&console.error(i.stack),p=t("<span>").html(c.localeStrings.renderError)}}catch(m){i=m,"undefined"!=typeof console&&null!==console&&console.error(i.stack),p=t("<span>").html(c.localeStrings.computeError)}for(f=this[0];f.hasChildNodes();)f.removeChild(f.lastChild);return this.append(p)},t.fn.pivotUI=function(n,r,i,l){var u,s,c,p,m,v,g,b,C,y,w,A,x,N,S,T,k,_,O,F,E,R,D,I,M,K,L,q,z,U,V,j,H,B,W,P,J,G,Q,Z,$,Y;null==i&&(i=!1),null==l&&(l="en"),null==h[l]&&(l="en"),g={derivedAttributes:{},aggregators:h[l].aggregators,renderers:h[l].renderers,hiddenAttributes:[],menuLimit:500,cols:[],rows:[],vals:[],rowOrder:"key_a_to_z",colOrder:"key_a_to_z",dataClass:o,exclusions:{},inclusions:{},unusedAttrsVertical:85,autoSortUnusedAttrs:!1,onRefresh:null,filter:function(){return!0},sorters:{}},_=t.extend(!0,{},h.en.localeStrings,h[l].localeStrings),k={rendererOptions:{localeStrings:_},localeStrings:_},C=this.data("pivotUIOptions"),R=null==C||i?t.extend(!0,{},k,t.extend({},g,r)):C;try{m={},O=[],M=0,o.forEachRecord(n,R.derivedAttributes,function(t){var e,n,r,o;if(R.filter(t)){O.push(t);for(e in t)a.call(t,e)&&null==m[e]&&(m[e]={},M>0&&(m[e]["null"]=M));for(e in m)o=null!=(r=t[e])?r:"null",null==(n=m[e])[o]&&(n[o]=0),m[e][o]++;return M++}}),G=t("<table>",{"class":"pvtUi"}).attr("cellpadding",5),H=t("<td>"),j=t("<select>").addClass("pvtRenderer").appendTo(H).bind("change",function(){return U()}),K=R.renderers;for(Y in K)a.call(K,Y)&&t("<option>").val(Y).html(Y).appendTo(j);if(Q=t("<td>").addClass("pvtAxisContainer pvtUnused"),W=function(){var t;t=[];for(u in m)e.call(R.hiddenAttributes,u)<0&&t.push(u);return t}(),$=!1,Z="auto"===R.unusedAttrsVertical?120:parseInt(R.unusedAttrsVertical),!isNaN(Z)){for(p=0,x=0,N=W.length;N>x;x++)u=W[x],p+=u.length;$=p>Z}Q.addClass(R.unusedAttrsVertical===!0||$?"pvtVertList":"pvtHorizList"),y=function(n){var r,a,o,i,l,u,s,c,h,p,f,v,g,b,C,y,A,x,N;if(N=function(){var t;t=[];for(C in m[n])t.push(C);return t}(),c=!1,x=t("<div>").addClass("pvtFilterBox").hide(),x.append(t("<h4>").append(t("<span>").text(n),t("<span>").addClass("count").text("("+N.length+")"))),N.length>R.menuLimit)x.append(t("<p>").html(R.localeStrings.tooMany));else for(N.length>5&&(i=t("<p>").appendTo(x),g=d(R.sorters,n),f=R.localeStrings.filterResults,t("<input>",{type:"text"}).appendTo(i).attr({placeholder:f,"class":"pvtSearch"}).bind("keyup",function(){var n,r,a;return a=t(this).val().toLowerCase().trim(),r=function(t,n){return function(r){var o,i;return o=a.substring(t.length).trim(),0===o.length?!0:(i=Math.sign(g(r.toLowerCase(),o)),e.call(n,i)>=0)}},n=a.startsWith(">=")?r(">=",[1,0]):a.startsWith("<=")?r("<=",[-1,0]):a.startsWith(">")?r(">",[1]):a.startsWith("<")?r("<",[-1]):a.startsWith("~")?function(t){return 0===a.substring(1).trim().length?!0:t.toLowerCase().match(a.substring(1))}:function(t){return-1!==t.toLowerCase().indexOf(a)},x.find(".pvtCheckContainer p label span.value").each(function(){return n(t(this).text())?t(this).parent().parent().show():t(this).parent().parent().hide()})}),i.append(t("<br>")),t("<button>",{type:"button"}).appendTo(i).html(R.localeStrings.selectAll).bind("click",function(){return x.find("input:visible:not(:checked)").prop("checked",!0).toggleClass("changed"),!1}),t("<button>",{type:"button"}).appendTo(i).html(R.localeStrings.selectNone).bind("click",function(){return x.find("input:visible:checked").prop("checked",!1).toggleClass("changed"),!1})),a=t("<div>").addClass("pvtCheckContainer").appendTo(x),v=N.sort(d(R.sorters,n)),p=0,h=v.length;h>p;p++)y=v[p],A=m[n][y],l=t("<label>"),u=!1,R.inclusions[n]?u=e.call(R.inclusions[n],y)<0:R.exclusions[n]&&(u=e.call(R.exclusions[n],y)>=0),c||(c=u),t("<input>").attr("type","checkbox").addClass("pvtFilter").attr("checked",!u).data("filter",[n,y]).appendTo(l).bind("change",function(){return t(this).toggleClass("changed")}),l.append(t("<span>").addClass("value").text(y)),l.append(t("<span>").addClass("count").text("("+A+")")),a.append(t("<p>").append(l));return o=function(){return x.find("[type='checkbox']").length>x.find("[type='checkbox']:checked").length?r.addClass("pvtFilteredAttribute"):r.removeClass("pvtFilteredAttribute"),x.find(".pvtSearch").val(""),x.find(".pvtCheckContainer p").show(),x.hide()},s=t("<p>").appendTo(x),N.length<=R.menuLimit&&t("<button>",{type:"button"}).text(R.localeStrings.apply).appendTo(s).bind("click",function(){return x.find(".changed").removeClass("changed").length&&U(),o()}),t("<button>",{type:"button"}).text(R.localeStrings.cancel).appendTo(s).bind("click",function(){return x.find(".changed:checked").removeClass("changed").prop("checked",!1),x.find(".changed:not(:checked)").removeClass("changed").prop("checked",!0),o()}),b=t("<span>").addClass("pvtTriangle").html(" &#x25BE;").bind("click",function(e){var n,r,a;return r=t(e.currentTarget).position(),n=r.left,a=r.top,x.css({left:n+10,top:a+10}).show()}),r=t("<li>").addClass("axis_"+w).append(t("<span>").addClass("pvtAttr").text(n).data("attrName",n).append(b)),c&&r.addClass("pvtFilteredAttribute"),Q.append(r).append(x)};for(w in W)a.call(W,w)&&(c=W[w],y(c));P=t("<tr>").appendTo(G),s=t("<select>").addClass("pvtAggregator").bind("change",function(){return U()}),L=R.aggregators;for(Y in L)a.call(L,Y)&&s.append(t("<option>").val(Y).html(Y));for(D={key_a_to_z:{rowSymbol:"&varr;",colSymbol:"&harr;",next:"value_a_to_z"},value_a_to_z:{rowSymbol:"&darr;",colSymbol:"&rarr;",next:"value_z_to_a"},value_z_to_a:{rowSymbol:"&uarr;",colSymbol:"&larr;",next:"key_a_to_z"}},B=t("<a>",{role:"button"}).addClass("pvtRowOrder").data("order",R.rowOrder).html(D[R.rowOrder].rowSymbol).bind("click",function(){return t(this).data("order",D[t(this).data("order")].next),t(this).html(D[t(this).data("order")].rowSymbol),U()}),v=t("<a>",{role:"button"}).addClass("pvtColOrder").data("order",R.colOrder).html(D[R.colOrder].colSymbol).bind("click",function(){return t(this).data("order",D[t(this).data("order")].next),t(this).html(D[t(this).data("order")].colSymbol),U()}),t("<td>").addClass("pvtVals").appendTo(P).append(s).append(B).append(v).append(t("<br>")),t("<td>").addClass("pvtAxisContainer pvtHorizList pvtCols").appendTo(P),J=t("<tr>").appendTo(G),J.append(t("<td>").addClass("pvtAxisContainer pvtRows").attr("valign","top")),I=t("<td>").attr("valign","top").addClass("pvtRendererArea").appendTo(J),R.unusedAttrsVertical===!0||$?(G.find("tr:nth-child(1)").prepend(H),G.find("tr:nth-child(2)").prepend(Q)):G.prepend(t("<tr>").append(H).append(Q)),this.html(G),q=R.cols,F=0,S=q.length;S>F;F++)Y=q[F],this.find(".pvtCols").append(this.find(".axis_"+t.inArray(Y,W)));for(z=R.rows,E=0,T=z.length;T>E;E++)Y=z[E],this.find(".pvtRows").append(this.find(".axis_"+t.inArray(Y,W)));null!=R.aggregatorName&&this.find(".pvtAggregator").val(R.aggregatorName),null!=R.rendererName&&this.find(".pvtRenderer").val(R.rendererName),A=!0,V=function(n){return function(){var r,a,o,i,l,u,d,h,p,m,g,b,C,y;if(b={derivedAttributes:R.derivedAttributes,localeStrings:R.localeStrings,rendererOptions:R.rendererOptions,sorters:R.sorters,cols:[],rows:[],dataClass:R.dataClass},l=null!=(p=R.aggregators[s.val()]([])().numInputs)?p:0,y=[],n.find(".pvtRows li span.pvtAttr").each(function(){return b.rows.push(t(this).data("attrName"))}),n.find(".pvtCols li span.pvtAttr").each(function(){return b.cols.push(t(this).data("attrName"))}),n.find(".pvtVals select.pvtAttrDropdown").each(function(){return 0===l?t(this).remove():(l--,""!==t(this).val()?y.push(t(this).val()):void 0)}),0!==l)for(d=n.find(".pvtVals"),Y=h=0,m=l;m>=0?m>h:h>m;Y=m>=0?++h:--h){for(i=t("<select>").addClass("pvtAttrDropdown").append(t("<option>")).bind("change",function(){return U()}),g=0,o=W.length;o>g;g++)c=W[g],i.append(t("<option>").val(c).text(c));d.append(i)}return A&&(y=R.vals,w=0,n.find(".pvtVals select.pvtAttrDropdown").each(function(){return t(this).val(y[w]),w++}),A=!1),b.aggregatorName=s.val(),b.vals=y,b.aggregator=R.aggregators[s.val()](y),b.renderer=R.renderers[j.val()],b.rowOrder=B.data("order"),b.colOrder=v.data("order"),r={},n.find("input.pvtFilter").not(":checked").each(function(){var e;return e=t(this).data("filter"),null!=r[e[0]]?r[e[0]].push(e[1]):r[e[0]]=[e[1]]}),a={},n.find("input.pvtFilter:checked").each(function(){var e;return e=t(this).data("filter"),null!=r[e[0]]?null!=a[e[0]]?a[e[0]].push(e[1]):a[e[0]]=[e[1]]:void 0}),b.filter=function(t){var n,a,o,i;if(!R.filter(t))return!1;for(a in r)if(n=r[a],o=""+(null!=(i=t[a])?i:"null"),e.call(n,o)>=0)return!1;return!0},I.pivot(O,b),u=t.extend({},R,{cols:b.cols,rows:b.rows,colOrder:b.colOrder,rowOrder:b.rowOrder,vals:y,exclusions:r,inclusions:a,inclusionsInfo:a,aggregatorName:s.val(),rendererName:j.val()}),n.data("pivotUIOptions",u),R.autoSortUnusedAttrs&&(C=n.find("td.pvtUnused.pvtAxisContainer"),t(C).children("li").sort(function(e,n){return f(t(e).text(),t(n).text())}).appendTo(C)),I.css("opacity",1),null!=R.onRefresh?R.onRefresh(u):void 0}}(this),U=function(t){return function(){return I.css("opacity",.5),setTimeout(V,10)}}(this),U(),this.find(".pvtAxisContainer").sortable({update:function(t,e){return null==e.sender?U():void 0},connectWith:this.find(".pvtAxisContainer"),items:"li",placeholder:"pvtPlaceholder"})}catch(X){b=X,"undefined"!=typeof console&&null!==console&&console.error(b.stack),this.html(R.localeStrings.uiRenderError)}return this},t.fn.heatmap=function(e,n){var r,a,o,i,l,u,s,c,d,h,p;switch(null==e&&(e="heatmap"),c=this.data("numrows"),s=this.data("numcols"),r=null!=n&&null!=(d=n.heatmap)?d.colorScaleGenerator:void 0,null==r&&(r=function(t){var e,n;return n=Math.min.apply(Math,t),e=Math.max.apply(Math,t),function(t){var r;return r=255-Math.round(255*(t-n)/(e-n)),"rgb(255,"+r+","+r+")"}}),a=function(e){return function(n){var a,o,i;return o=function(r){return e.find(n).each(function(){var e;return e=t(this).data("value"),null!=e&&isFinite(e)?r(e,t(this)):void 0})},i=[],o(function(t){return i.push(t)}),a=r(i),o(function(t,e){return e.css("background-color",a(t))})}}(this),e){case"heatmap":a(".pvtVal");break;case"rowheatmap":for(o=l=0,h=c;h>=0?h>l:l>h;o=h>=0?++l:--l)a(".pvtVal.row"+o);break;case"colheatmap":for(i=u=0,p=s;p>=0?p>u:u>p;i=p>=0?++u:--u)a(".pvtVal.col"+i)}return a(".pvtTotal.rowTotal"),a(".pvtTotal.colTotal"),this},t.fn.barchart=function(){var e,n,r,a,o,i;for(o=this.data("numrows"),a=this.data("numcols"),e=function(e){return function(n){var r,a,o,i;return r=function(r){return e.find(n).each(function(){var e;return e=t(this).data("value"),null!=e&&isFinite(e)?r(e,t(this)):void 0})},i=[],r(function(t){return i.push(t)}),a=Math.max.apply(Math,i),o=function(t){return 100*t/(1.4*a)},r(function(e,n){var r,a;return r=n.text(),a=t("<div>").css({position:"relative",height:"55px"}),a.append(t("<div>").css({position:"absolute",bottom:0,left:0,right:0,height:o(e)+"%","background-color":"gray"})),a.append(t("<div>").text(r).css({position:"relative","padding-left":"5px","padding-right":"5px"})),n.css({padding:0,"padding-top":"5px","text-align":"center"}).html(a)})}}(this),n=r=0,i=o;i>=0?i>r:r>i;n=i>=0?++r:--r)e(".pvtVal.row"+n);return e(".pvtTotal.colTotal"),this}})}).call(this);
//# sourceMappingURL=pivot.min.js.map