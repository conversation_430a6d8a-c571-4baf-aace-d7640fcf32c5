/* Common */
$fontSize:14px;
$fontFamily:"Roboto","Helvetica Neue",sans-serif;
$lineHeight:1.5em;
$textColor:#424242;
$textSecondaryColor:#757575;
$borderRadius:3px;
$dividerColor:#eaeaea;
$dividerLightColor:#f8f8f8;
$iconWidth:20px;
$iconHeight:20px;
$iconFontSize:20px;
$grayBgColor:#757575;
$hoverBgColor:#e8e8e8;
$hoverTextColor:#000000;
$greenColor: #1fac90;
$lightGreenColor: #75C538;
$mintColor: #75C53826;
$blueColor: #262b74;
$flowerBlueColor: #5F5AF7;
$orangeColor: #FF6810;
$redColor: #E22D2D;
$boxShadow: rgba(0, 0, 0, 0.15) 0px 5px 15px;

/* Layout */
$bodyBgColor:#fcfbfb;
$menuItemHoverBgColor:#e8e8e8;
$menuitemColor:#757575;
$footerBgColor:#ffffff;

/* Topbar */
$topbarSubmenuBgColor:#f7f7f7;
$topbarSubmenuItemHoverBgColor:#e3e3e3;

/******************************/
/*           THEME            */
/******************************/
$headerPadding:.625em 1em;
$headerFontSize:1em;

$contentPadding:.625em 1em;
$contentBorderColor:#d8d8d8;
$contentBgColor:#ffffff;

$inputBorderColor:#bdbdbd;
$inputFontSize:1em;
$inputHeaderFontSize:1em;
$inputOptionFontSize:1em;
$inputHeaderPadding:.625em 1em;
$invalidInputLabelColor:#e62a10;
$invalidInputBorderColor:#e62a10;

$buttonFontSize:1em;

$listItemPadding:.625em 1em;

$radioButtonBorderColor:#757575;
$checkboxBorderColor:#757575;

$dataTableBorderColor:#cacaca;
$dataTableRowBgColorEven:#f4f4f4;
$DDTableBorderColor: #D0D2FF;
$DDTableBgColor: #F7F5FF;
$DDTableHeaderTextColor: #10052D;
$DDTableTextColor: #292929;
$DDTableTextLightColor: #585858;
