body {
    .ui-picklist {
        .ui-picklist-caption {
            font-size: $inputHeaderFontSize;
            padding: $inputHeaderPadding;
        }
        
        .ui-picklist-list {
            padding: 0;
        }
        
        li.ui-picklist-item {
            font-size: $inputOptionFontSize;
            padding: $listItemPadding;
            margin: 0;
            @include border-radius(0);
            color: $textColor;
            
            &:not(.ui-state-highlight):hover {
                @include hover-element();
            }
            
            &.ui-state-highlight {
                background-color: $accentColor;
                color: $accentTextColor;
            }
        }
        
        .ui-picklist-buttons {
            width: 3em;
            
            .ui-button {
                &.ui-button-icon-only {
                    width: 2.5em;
                    margin-right: 0;
                    display: inline-block;
                    margin-bottom: .5em;
                }
            }        
        }
        
        .ui-picklist-buttons-cell {
            text-align: center;
        }
                
        &.ui-picklist-responsive {
            .ui-picklist-buttons {
                .ui-button {
                    &.ui-button-icon-only {
                        margin: 0 auto;
                        display: block;
                        margin-bottom: .5em;
                    }
                }
            }
            
            .ui-picklist-list {
                .ui-picklist-item {
                    .ui-chkbox {
                        margin-right: .5em;
                        vertical-align: top;
                    }
                    
                    .ui-chkbox,.ui-chkbox * {
                        box-sizing: content-box;
                    }
                }
            }
        }
    }
    
    .ui-orderlist {
        .ui-orderlist-caption {
            font-size: $inputHeaderFontSize;
            padding: $inputHeaderPadding;
            box-sizing: border-box;
        }
        
        .ui-orderlist-list {
            padding: 0;
            box-sizing: border-box;
            
            li.ui-orderlist-item {
                font-size: $inputOptionFontSize;
                padding: $listItemPadding;
                margin: 0;
                @include border-radius(0);
                color: $textColor;

                &:not(.ui-state-highlight):hover {
                    @include hover-element();
                }
                
                &.ui-state-highlight {
                    background-color: $accentColor;
                    color: $accentTextColor;
                }
            }
        }
        
        .ui-orderlist-controls {
            width: 3.75em;
            text-align: center;
            
            .ui-button {
                &.ui-button-icon-only {
                    width: 2.5em;
                    margin-bottom: .5em;
                    margin-right: 0;
                    display: inline-block;
                }
            }
        }
    }
    
    .ui-paginator {
        background-color: $primaryDarkColor;
        padding: $headerPadding;
        
        > a {
            box-sizing: border-box;
            color: #ffffff;
            
            .fa {
                display: none;
            }
            
            &:not(.ui-state-disabled):not(.ui-state-active):hover {
                background-color: $primaryLightColor;
                @include border-radius(50%);
                @include transition(background-color .3s);
            }
        }
        
        .ui-paginator-next {
            padding: 0;
            vertical-align: middle;
            @include material-icon("navigate_next");
        }

        .ui-paginator-last {
            padding: 0;
            vertical-align: middle;
            @include material-icon("last_page");
        }

        .ui-paginator-prev {
            padding: 0;
            vertical-align: middle;
            @include material-icon("navigate_before");
        }

        .ui-paginator-first {
            padding: 0;
            vertical-align: middle;
            @include material-icon("first_page");
        }
        
        .ui-paginator-pages {
            vertical-align: middle;
            margin: 0 .375em 0 .375em;
            
            a {
                padding: 0;
                color: #ffffff;
                min-width: 1.714em;
                min-height: 1.714em;
                line-height: 1.714em;
                @include border-radius(50%);
                @include transition(background-color .3s);
                
                &:not(.ui-state-active):hover {
                    background-color: $primaryLightColor;
                }        
                
                &.ui-state-active {
                    background-color: $accentColor;
                    color: $accentTextColor;
                }
            }
        }
    }
    
    .ui-datagrid {
        .ui-datagrid-header {
            padding: $headerPadding;
        }
        
        .ui-panel {
            .ui-panel-titlebar {
                background-color: #ffffff;
                color: $textColor;
                border-color: $dividerColor;
            }
        }
    }
    
    .ui-datalist {
        .ui-datalist-header {
            padding: $headerPadding;
        }
    }
    
    .ui-datatable {
        .ui-datatable-header,
        .ui-datatable-footer {
            padding: $headerPadding;
            
            .ui-inputtext {
                color: #ffffff;
                
                &:focus {
                    border-color: #ffffff;
                }
            }  
        }
        
        .ui-paginator {
            padding: $headerPadding;
        }
        
        .ui-datatable-thead {
            > tr {
                 border-color: $dataTableBorderColor;

                 > th {
                    padding: .625em .875em;
                    background-color: #ffffff;
                                    
                    &.ui-sortable-column:not(.ui-state-active):hover {
                        @include hover-element();
                    }
                    
                    .ui-column-title {
                        display: inline-block;
                        vertical-align: middle;
                    }
                    
                    .ui-sortable-column-icon {
                        vertical-align: middle;
                        color: $textSecondaryColor;
                    }
                                        
                    &.ui-state-active,&.ui-state-highlight {
                        background-color: $accentColor;
                        color: $accentTextColor;
                        border-top-color: $accentColor;
                        
                        .fa {
                            color: $accentTextColor;
                        }
                        
                        .ui-inputtext {
                            color: $accentTextColor;
                            
                            &.ui-state-focus {
                                border-color: $accentTextColor;
                            }
                        }
                    }
                    
                    &.ui-selection-column {
                        .ui-chkbox-box {
                            box-sizing: content-box;
                        }
                    }
                }
            }
        }
        
        .ui-datatable-tfoot {
            > tr {
                > td {
                    padding: .625em .875em;
                    border: 1px solid #bdbdbd;
                    background-color: #ffffff;
                }
            }
        }
        
        .ui-datatable-data { 
            tr.ui-datatable-even {
                background-color: $dataTableRowBgColorEven;
                                
                &.ui-state-highlight {
                    background-color: $accentColor;
                    color: $accentTextColor;
                }
            }
                               
            tr {
                border: 1px solid $dataTableBorderColor;

                td {
                    padding: .625em .875em;
                    
                    .ui-row-toggler  {
                        display: inherit;
                    }
                    
                    &.ui-state-highlight {
                        .ui-inputtext {
                            color: #ffffff;
                            border-color: #ffffff;
                            
                            &:focus {
                                border-color: #ffffff;
                            }
                        }
                    }
                    
                    &.ui-state-error {
                        background-color: #e62a10;
                        border-color: #e62a10;
                        color: #ffffff;
                    }
                    
                    input.ui-cell-editor {
                        padding: 2px 2px 1px 2px;
                        font-size: $inputFontSize;
                    }
                    
                    &.ui-selection-column {
                        .ui-chkbox-box, .ui-radiobutton-box {
                            box-sizing: content-box;
                        }
                    }
                }
                
                &.ui-state-highlight{
                    background-color: $accentColor;
                    border-color: $accentColor;
                    color: $accentTextColor;
                }
                
                .ui-cell-editor-input {
                    input {
                        color: $accentTextColor;
                    }
                }
            }
            
            tr.ui-state-error {
                background-color: #e62a10;
                border-color: #e62a10;
                color: #ffffff;
                
                .ui-inputtext,
                .ui-inputtext.ui-state-error {
                    border-color: #ffffff;
                }
            }
            
            tr.ui-state-highlight {
                td.ui-selection-column {
                    .ui-radiobutton-box {
                        border-color: #ffffff;
                        
                        .ui-radiobutton-icon {
                            background-color: #ffffff;
                        }
                    }
                    
                    .ui-chkbox-box {
                        border-color: #ffffff;
                        background-color: #ffffff;
                        
                        .ui-chkbox-icon {
                            color: $textSecondaryColor;
                        }
                    }
                }
                
                .ui-inputtext {
                    color: #ffffff;
                    border-color: #ffffff;
                    
                    &:focus {
                        border-color: #ffffff;
                    }
                }
            }
            
            tr.ui-rowgroup-header {
                td {
                    a {
                        height: 1.5em;
                    }
                }
            }

            &.ui-datatable-hoverable-rows {
                > tr.ui-widget-content:not(.ui-state-highlight):hover {
                    cursor: pointer;
                    @include hover-element();
                }
            }
        }        
        
        &.ui-datatable-scrollable {
            .ui-datatable-scrollable-header,  .ui-datatable-scrollable-footer {
                border: 0 none;
                background-color: transparent;
                
                .ui-datatable-data {
                    td {
                        color: $textColor;
                    }
                }
            }
            
            thead {
                tr {
                    th {
                        color: $textColor;
                        font-size: $fontSize;
                    }
                }
            }
            
            tfoot {
                tr {
                    td {
                        color: $textColor;
                        font-size: $fontSize;
                    }
                }
            }
        }
    }
    
    .ui-carousel {
        padding: 0;
        
        .ui-carousel-header{
            padding: $headerPadding;
            font-size: $headerFontSize;
            overflow: visible;
            
            .ui-carousel-header-title {
                overflow: visible;
            }
            
            .fa {
                color: #ffffff;
            }
            
            .ui-carousel-button {
                margin: -.143em 0 0 0;
            }
            
            .ui-carousel-page-links {
                margin: -.143em 0 0 0;
            }
        }
    }
    
    .ui-tree {
        padding: $contentPadding / 2;
        
        .ui-treenode-children {
            padding-left: 1.75em;
        }
        
        .ui-treenode-content {
            
            .ui-chkbox {
                margin-right: .5em; 
                
                .fa {
                    color: #757575;
                }   
            }
            
            .ui-tree-toggler {
                vertical-align: middle;
                margin: 0 0 0 .25em;
            }
            
            .ui-treenode-icon  {
                vertical-align: middle;
                margin: 0 .25em;
            }
            
            .ui-chkbox {
                margin: 0 .25em;
            }
            
            .ui-treenode-label {
                margin: 0;
                vertical-align: middle;                
            }

            &.ui-treenode-selectable {
                .ui-treenode-label:not(.ui-state-highlight):hover {
                    @include hover-element();
                }
            }
            
            &.ui-treenode-dragover {
                background: $primaryLightColor;
                
                > span {
                    color: $primaryTextColor;
                }
            }
        }
                
        &.ui-tree-horizontal {
            padding-left: 0;
            padding-right: 0;
            
            .ui-treenode-content {
                background-color: #ffffff;
                border: 1px solid $dividerColor;
                
                .ui-tree-toggler {
                    vertical-align: top;
                }
                
                .ui-treenode-icon  {
                    vertical-align: top;
                    margin-right: .25em;
                }

                &.ui-treenode-selectable:hover {
                    @include hover-element();
                }
                
                &.ui-state-highlight {
                    background-color: $accentColor;
                    color: $accentTextColor;
                }
            }
        }
    }
    
    .ui-tree-draghelper {
        border: 1px solid $primaryColor;
    }
    
    .fc {
        .fc-button-group {            
            .ui-icon-circle-triangle-e {
                @include material-icon("play_circle_outline");
                text-indent: 0;
                position: relative;
                top: 2px;
            }
            
            .ui-icon-circle-triangle-w {
                @include material-icon("play_circle_outline"); 
                @include rotate(180deg);
                text-indent: 0;
                position: relative;
                top: 2px;
            }
            
            .ui-state-active {
                background-color: $accentColor;
            }
        }
        
        .fc-event {
            background-color: $primaryLightColor;
            color: $primaryTextColor;
        }
    
        table {
            box-sizing: border-box;
            
            th {
                padding: $headerPadding;
            }
        }
    }
    
    .ui-treetable {
        .ui-treetable-header {
            padding: $headerPadding;
            font-size: $headerFontSize;
        }
        
        thead {
            tr {
                border-bottom: 1px solid $dividerColor;
            
                th {
                    background-color: #ffffff;
                    padding: .625em .875em;
                    border: 0 none;
                    
                    .fa {
                        color: $textSecondaryColor;
                    }
                    
                    &:first-child {
                        border-left: 1px solid $dividerColor;
                    }
                    
                    &:last-child {
                        border-right: 1px solid $dividerColor;
                    }
                    
                    .ui-sortable-column-icon {
                        vertical-align: middle;
                        margin: -.25em 0 0 0;
                    }
                    
                    &.ui-state-active {
                        background-color: $accentColor;
                        color: $accentTextColor;
                        
                        .fa {
                            color: $accentTextColor;
                        }
                    }
                    
                    .ui-column-resizer {
                        @include material-icon("code");
                        font-size: 1em;
                        color: $textSecondaryColor;
                    }
                }    
            }
        }
        
        tfoot {
            td {
                border: 0 none;
                padding: .625em .875em;
            }
        }
        
        tbody {                        
            .ui-treetable-row {
                background-color: #ffffff;
                
                td {
                    border: 0 none;
                    padding: .625em .875em;
                    
                    .ui-treetable-toggler {
                        display: inline-block;
                        vertical-align: middle;
                        margin: 0 .167em;
                        float: none;
                    }
                    
                    .ui-chkbox {
                        margin-right: .5em;
                    }
                    
                    &.ui-treetable-child-table-container {
                        padding: 0;
                    }
                }

                &.ui-treetable-row-selectable:not(.ui-state-highlight):hover {
                    @include hover-element();
                }
                
                &.ui-state-highlight {
                    background-color: $accentColor;
                    color: $accentTextColor;
                    
                    .ui-chkbox {
                        .ui-chkbox-box {
                            border-color: $accentTextColor;
                        }
                    }
                }
            }
        }
        
        &.ui-treetable-scrollable {
            .ui-treetable-scrollable-header, .ui-treetable-scrollable-footer {
                background-color: transparent;
                border: 0 none;   
            }
            
            thead {
                th {
                    background-color: #ffffff;
                    color: $textColor;
                }
            }
        }
    }
}

@media (max-width: 640px) {
    body {                
        .ui-picklist {
            &.ui-picklist-responsive {
                .ui-picklist-list-wrapper {
                    margin-bottom: .5em;
                }
                
                .ui-picklist-buttons {
                    padding: .5em 0;
                    
                    .ui-button {
                        &.ui-button-icon-only {
                            display: inline-block;
                            margin-right: .25em;
                            margin-bottom: 0;
                        }
                    }
                    
                    .fa-angle-right {
                        @include icon-override("play_arrow"); @include rotate(90deg);
                    }
                    .fa-angle-double-right {
                        @include icon-override("skip_previous"); @include rotate(-90deg);
                        
                    }
                    .fa-angle-left {
                        @include icon-override("play_arrow"); @include rotate(-90deg);
                    }
                    .fa-angle-double-left {
                        @include icon-override("skip_next"); @include rotate(-90deg);
                    }
                }
            }
        }
        
        .ui-orderlist {
            &.ui-grid-responsive {
                .ui-orderlist-controls {
                    text-align: center;
                    width: auto;
                    
                    .ui-button {
                        margin-right: .25em;  
                    }
                }
            }
        }
    }
}