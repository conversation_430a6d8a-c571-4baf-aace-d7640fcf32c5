.exception-body {
    background-color: #f5f5f5;
    padding-top: 200px;
    height: auto;
    background-size: contain;
        
    * {
        @include border-box-sizing();
    }
    
    .exception-panel {
        background-color: #ffffff;
        width: 400px;
        height: 323px;
        margin: 0 auto;
        padding-bottom: 20px;
        -webkit-box-shadow: 0 12px 15px 0 rgba(0, 0, 0, 0.24), 0 17px 50px 0 rgba(0, 0, 0, 0.19);
        -moz-box-shadow: 0 12px 15px 0 rgba(0, 0, 0, 0.24), 0 17px 50px 0 rgba(0, 0, 0, 0.19);
    	box-shadow: 0 12px 15px 0 rgba(0, 0, 0, 0.24), 0 17px 50px 0 rgba(0, 0, 0, 0.19);
                
        .ui-button {
            background-color: #ffffff;
            display: block;
            @include no-shadow();
                        
            &.ui-state-hover {
                @include hover-element();
            }
            
            margin: 100px 0 0 30px;
        }
    }
    
    .exception-band {
        height: 80px;
        margin-top: -160px;
        z-index: 1000;
        
        .exception-content {
            margin: 0 auto;
            width: 400px;
            position: relative;
            padding: 15px 30px;
            
            h1 {
                padding: 0;
                margin: 0;
            }
            
            p {
                padding: 0;
                margin: 0;
            }
            
            img {
                position: absolute;
                width: 48px;
                right: 30px;
                top: 16px;
            }
        }
    }
    
    &.error-body {
        background: url("../images/exception/error-image.png") no-repeat 0 -100px;
        .exception-panel {
            img {
                width: 100%;
            }
            
            .ui-button {
                color: #f44336;
                
                &.ui-state-hover {
                    @include hover-element();
                    color: #f44336;
                }
            }
        }
        
        .exception-band {
            background-color: #f44336;
            
            .exception-content {
                color: #ffebee;
            }
        }
    }
    
    &.pagenotfound-body {
        background: url("../images/exception/404-image.png") no-repeat 0 -100px;

        .exception-panel {
            img {
                width: 200px;
                margin: 0 auto;
                display: block;
                position: relative;
                top: 4px;
            }
            
            .ui-button {
                color: #00796b;
                margin-top: 105px;
                
                &.ui-state-hover {
                    @include hover-element();
                    color: #00796b;
                }
            }
        }

        .exception-band {
            background-color: #00796b;
            
            .exception-content {
                color: #b2dfdb;
            }
        }
    }
    
    &.accessdenied-body {
        background: url("../images/exception/access-image.png") no-repeat 0 -100px;

        .exception-panel {
            img {
                width: 300px;
                display: block;
                margin: 0 auto;
                position: relative;
                top: 6px;
            }
            
            .ui-button {
                color: #f57c00;
                margin-top: 110px;
                
                &.ui-state-hover {
                    @include hover-element();
                    color: #f57c00;
                }
            }
        }

        .exception-band {
            background-color: #f57c00;
            
            .exception-content {
                color: #ffe0b2;
            }
        }
    }
}

@media (min-width: 1441px) {
    .exception-body {
        background-size: contain !important;
    }
}

@media (max-width: 640px) {
    .exception-body {                        
        .exception-panel {
            width: 350px;
        }
        
        .exception-band {
            margin-top: -180px;
            
            .exception-content {
                img {
                    right: 48px;
                }
            }
        }
        
        &.error-body {
            .exception-panel {
                .ui-button {
                    margin-top: 111px;
                }
            }
        }
        
        &.pagenotfound-body {
            .exception-panel {
                img {
                    width: 175px;
                }
                
                .ui-button {
                    margin-top: 117px;
                }
            }
            
        }
        
        &.accessdenied-body {
            .exception-panel {
                img {
                    width: 262px;
                }
                
                .ui-button {
                    margin-top: 120px;
                }
            }
            
        }
    }
}

@media (max-width: 480px) {
    .exception-body {                                
        .exception-band {          
            .exception-content {
                img {
                    right: 24px;
                }
                
                width: 350px;
                padding: 15px 10px;
            }
        }
    }
}