.layout-wrapper {
    
    &.layout-rtl {
        .layout-sidebar {
            right: 0;
            
            .layout-tabmenu {
                float: right;
                
                .layout-tabmenu-contents {
                    direction: rtl;
                    right: 60px;
                    left: auto;
                    
                    .layout-tabmenu-content {
                        .layout-submenu-content {
                            padding: 6px 6px 6px 0px;
                            
                            .nano {
                                .nano-content {
                                    margin-right: 0px !important;
                                    margin-left: -17px;
                                    padding-right: 0px;
                                    padding-left: 6px;
                                    
                                    .navigation-menu {
                                        li {
                                            > a {
                                                i {
                                                    &:first-child {
                                                        margin-right: 0px;
                                                        margin-left: 4px;
                                                    }
                                                    
                                                    &:last-child {
                                                        float: left;
                                                    }
                                                }
                                            }
                                            
                                            > ul {
                                                padding: 0px 18px 0px 0px;
                                            }
                                        }
                                    }
                                }
                                
                                > .nano-pane {
                                    right: auto;
                                    left: 0;
                                }
                            }
                        }
                        
                        .layout-submenu-title { 
                            .menu-button {
                                float: left;
                            }
                            
                            .menu-pin-button {
                                float: left;
                            }
                        } 
                    }
                }
                
                .layout-tabmenu-nav {
                    li {
                        &.active-item {
                            border-left: 0px;
                            border-right: 4px solid $activeMenuItemTextColor; 
                        
                            a {
                                i {
                                    margin-left: 0;
                                    margin-right: -4px;
                                }
                            }
                            
                            .layout-tabmenu-tooltip {
                                right: 56px;
                            }
                        }
                        
                        .layout-tabmenu-tooltip {
                            left: auto;
                            right: 60px;
                            direction: rtl;

                            .layout-tabmenu-tooltip-arrow {
                                left: auto;
                                right: 0px;
                                border-width: 5px 0px 5px 5px;
                                border-color: transparent transparent transparent #424242;
                            }
                        }
                    }
                }
            }
            
            &.layout-sidebar-dark {
                .layout-tabmenu {
                    .layout-tabmenu-nav {                    
                        li {      
                            .layout-tabmenu-tooltip {                            
                                .layout-tabmenu-tooltip-arrow {
                                    border-color: transparent transparent transparent $darkActiveMenuItemTextColor;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        .layout-main {
            margin-left: 0px;
            margin-right: 65px;
            @include transition(margin-right .3s);
        }
        
        .topbar {
            direction: rtl;
            
            .topbar-menu {
                float: left;
                margin: 15px 0px 0px 10px;
                
                > li {
                    float: left;
                    margin-left: 0px;
                    margin-right: 15px;
                    
                    > ul {
                        right: auto;
                        left: 5px;
                        
                        a {
                            .topbar-submenuitem-badge {
                                float: left;
                            }
                            
                            .fa, img {
                                margin-right: 0px;
                                margin-left: 8px;
                            }
                        }
                    }
                    
                    > a {
                        .topbar-badge {
                            right: auto;
                            left: -4px;
                        }
                    }
                    
                    &.search-item {
                        input {
                            padding-left: 0px;
                            padding-right: 20px;
                            direction: rtl;
                        }
                        
                        i {
                            left: auto;
                            right: 6px;
                        }
                    }
                }
            }
            
            .app-name {
                margin-left: 0px;
                margin-right: 15px;
            }
        }
    }
}