<!DOCTYPE html>
<html>

  <head>
    <script>document.write('<base href="' + document.location + '" />');</script>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <title>PrimeNG - BARCELONA</title>

    <!-- Layout CSS -->
    <link rel="stylesheet" type="text/css" href="../theme/theme-indigo.css">
    <link rel="stylesheet" type="text/css" href="../layout/css/layout-indigo.css">
    <link rel="stylesheet" type="text/css" href="primeng.min.css">
    
    <!-- Ripple -->
    <script src="jquery.js"></script>
    <script src="../layout/js/ripple.js"></script>
    <script type="text/javascript">
        $(function() {
            $('input').on('blur', function(e) {
                var el = $(this);
                if(el.val() != '')
                    el.addClass('ui-state-filled');
                else
                    el.removeClass('ui-state-filled');
            });
        });
    </script>
  </head>
 
  <!-- 3. Display the application -->
  <body class="login-body">
        
        <div class="login-panel ui-fluid">
            <div class="ui-g">
                <div class="ui-g-12 login-header">
                    <h1>LOGIN</h1>
                    <h2>Welcome to Barcelona</h2>
                    <img src="../layout/images/logo.png">
                </div>
                <div class="ui-g-12">
                    <span class="md-inputfield">
                        <input type="text" autocomplete="off" class="ui-inputtext ui-corner-all ui-state-default ui-widget">
                        <label>Username</label>
                    </span>
                </div>
                <div class="ui-g-12">
                    <span class="md-inputfield">
                        <input type="password" autocomplete="off" class="ui-inputtext ui-corner-all ui-state-default ui-widget">
                        <label>Password</label>
                    </span>
                </div>
                <div class="ui-g-12 button-container">
                    <button type="button" class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-icon-left">
                        <span class="ui-button-icon-left ui-c fa fa-fw ui-icon-person"></span>
                        <span class="ui-button-text ui-c">Sign In</span>
                    </button>
                </div>
            </div>
        </div>
    </body>

</html>
