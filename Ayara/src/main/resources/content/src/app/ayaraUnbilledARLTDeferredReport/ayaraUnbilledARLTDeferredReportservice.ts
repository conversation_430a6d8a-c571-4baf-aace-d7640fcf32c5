import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require: any;
const appSettings = require('../appsettings');


@Injectable()
export class AyaraUnbilledARLTDeferredReportService {

    constructor(private http: HttpClient) { }
    
    getAyaraUnbilledARLTDeferredReport(customerNumber: any, rcNumber: any, orderNumber: any, orderLineNumber: any) {

        if (customerNumber == undefined) {
            customerNumber = '';
        }
        if (rcNumber == undefined) {
            rcNumber = '';
        }
        if (orderNumber == undefined) {
            orderNumber = '';
        }
        if (orderLineNumber == undefined) {
            orderLineNumber = '';
        }

        
        let serviceUrl = appSettings.apiUrl + '/unbilledARLTDefererdReport?customerNumber=' + customerNumber + '&rcNumber=' + rcNumber+'&orderNumber=' + orderNumber+'&orderLineNumber=' + orderLineNumber;
        return this.http.get(serviceUrl).toPromise().then((data: any) => {
            return data;
        });
    }

    getAyaraUnbilledARLTDeferredReportFile(customerNumber: any, rcNumber: any, orderNumber: any, orderLineNumber: any, exportCols: any) {

         if (customerNumber == undefined) {
            customerNumber = '';
        }
        if (rcNumber == undefined) {
            rcNumber = '';
        }
        if (orderNumber == undefined) {
            orderNumber = '';
        }
        
        if (orderLineNumber == undefined) {
            orderLineNumber = '';
        }


        let serviceUrl = appSettings.apiUrl + '/exportUnbilledARLTDefererdReport?customerNumber=' + customerNumber + '&rcNumber=' + rcNumber+'&orderNumber=' + orderNumber+'&orderLineNumber=' + orderLineNumber;

        if (exportCols != undefined && exportCols != "") {
            serviceUrl = serviceUrl + '&exportCols=' + encodeURIComponent(exportCols);
        }
        return serviceUrl;
    }
    
    fetchRCNumbers(rc: any) {
        let unpostedRCUrl = appSettings.apiUrl + '/fetchAllRC?rc='+rc;

        return this.http.get(unpostedRCUrl).toPromise().then((data: any) => {
               return data;
         });
    }
    
    getParameterValues(paramName: string, paramValue: string) {
        return this.http.get<any>(appSettings.apiUrl + `/fetchFilterValues?parameterName=${paramName}&parameterValue=${paramValue}`);
    }

}
