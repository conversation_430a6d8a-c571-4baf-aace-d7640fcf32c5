<div class="content-section implementation">
</div>

<div class="card-wrapper waterfall-detail-report">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card-block">

					<p-panel header="LT Deferred & Unbilled AR Reclass" [toggleable]="false" (onBeforeToggle)=onBeforeToggle($event)>
						<p-header>
							<div class="pull-right icons-list">
								<a (click)="goToOperationReports()" class="add-column">
									<em class="fa fa-reply"></em>Back</a>
								<a (click)="onConfiguringColumns($event)" class="add-column">
									<em class="fa fa-cog"></em>Columns</a>
								<a (click)="showDialogToSearch()" title="Search">
									<em class="fa fa-search"></em>
								</a>
								<a (click)="reset(dt)" title="Reset">
									<em class="fa fa-refresh"></em>
								</a>
								<a (click)="dt.exportCSV()" title="Export">
									<em class="fa fa-external-link"></em>
								</a>
							<!--	<a (click)="exportCSVfile()" title="Export">
									<em class="fa fa-external-link"></em>
								</a> -->

								<div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
									<div class="user-popup">
										<div class="content overflow">
											<input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()" />
											<label for="selectall">Select All</label>
											<a class="close" title="Close" (click)="closeConfigureColumns($event)">&times;</a>
											<p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
												<ng-template let-col let-index="index" pTemplate="item">
													<div *ngIf="col.drag">
														<div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
															<div class="drag">
																<input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" />
																<label>{{col.header}}</label>
															</div>
														</div>
													</div>
													<div *ngIf="!col.drag">
														<div class="ui-helper-clearfix">
															<div>
																<input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"
																/>
																<label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
															</div>
														</div>
													</div>
												</ng-template>
											</p-listbox>
										</div>
										<div class="pull-right">
											<a class="configColBtn" (click)="saveColumns()">Save</a>
											<a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
										</div>
									</div>
								</div>
							</div>
						</p-header>

						<div class="x-scroll mb-2">
							
							<p-table class="ui-datatable arrangementMgrTbl" [loading]="loading" #dt id="unbilleARLT-dt" [value]="unbilledARLTRepList"
							exportFilename="Unbilled_AR_Report_{{currentDate|date:'yyyyMMddhhmmss'}}" [paginator]="true" [rows]="10" 
							  [columns]="columns" [resizableColumns]="true" columnResizeMode="expand"
							  scrollable="true"
							 >

								<ng-template pTemplate="colgroup" let-columns>
									<colgroup>
										<col *ngFor="let col of columns">
									</colgroup>
								</ng-template>

								<ng-template pTemplate="header" class="arrangementMgrTblHead">
									<tr>
										<th style="width:100px" *ngFor="let col of columns" pResizableColumn [ngStyle]="{'display': col.display, 'text-align':col.text}">
											{{col.header}}
										</th>

									</tr>
								</ng-template>
								<ng-template pTemplate="body" let-rowData let-unbilledARLTRep>
									<tr [pSelectableRow]="rowData">
										<td style="width:100px" *ngFor="let col of columns" [ngStyle]="{'display': col.display, 'text-align': col.text}">
											<span title="{{unbilledARLTRep[col.field]}}">{{(fieldType(unbilledARLTRep[col.field])==
												'number' ? (unbilledARLTRep[col.field]|number:'1.2-2') : unbilledARLTRep[col.field])}}</span>
										</td>


									</tr>
								</ng-template>
								<ng-template pTemplate="emptymessage">
									<tr>
										<td class="no-data">{{noData}}</td>
									</tr>
								</ng-template>





							</p-table>
						</div>
						
					</p-panel>
				</div>
			</div>
		</div>
	</div>
</div>


<p-dialog header="Search" width="800" [visible]="displayDialog" [closable]="false" [draggable]="true" showEffect="fade"
 [modal]="true">
	<form>
		<div class="ui-grid ui-grid-responsive ui-fluid">
			<div class="ui-g-12">
			<div class="ui-g-6">
				<span class="selectSpan">Customer Name</span>
				<p-autoComplete  inputStyleClass="textbox" [suggestions]="customers" 
				(completeMethod)="searchCustomerNames($event)" styleClass="wid100"
								   appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
									placeholder="Customer Name Type atleast 3 Characters" id="from" [(ngModel)]="selectedCustomer" field="customerName"></p-autoComplete>
			</div>
			<div class="ui-g-6 pull-right">
				<span class="selectSpan">Revenue Contract Number</span>
				<p-autoComplete  inputStyleClass="textbox" [suggestions]="dealArrangementNumbers" 
					(completeMethod)="searchRCList($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								 placeholder="Revenue Contract Number Type atleast 3 Characters" id="from" [(ngModel)]="rcNumber"></p-autoComplete>
            </div>
		  </div>
		  <div class="ui-g-12">
			<div class="ui-g-6">
				<span class="selectSpan"> Customer Number</span>
					<input pInputText name="customerNumber" id="customerNumber" class="textbox" placeholder="Customer Number" [ngModelOptions]="{standalone: true}" [(ngModel)]="selectedCustomer.customerNumber" [disabled]="true"
					/>
					
			</div>
			<div class="ui-g-6 pull-right">
                <span class="md-inputfield">
                   <span class="selectSpan">Order Number</span>
                      <p-autoComplete [(ngModel)]="orderNumber"
                        appendTo="body" placeholder="Type Order Number Value" [suggestions]="valueResults" (completeMethod)="searchValues('ORDER_NUMBER',$event)" 
                          [ngModelOptions]="{standalone: true}" minLength="3">
                      </p-autoComplete>
                </span>
            </div>
			
		  </div>
		  <div class="ui-g-12">
			<div class="ui-g-6">
				<span class="md-inputfield">
                    <span class="selectSpan">Order Line Number</span>
                      <p-autoComplete [(ngModel)]="orderLineNumber"
                      appendTo="body" placeholder="Type Order Line Number Value" [suggestions]="valueResults" (completeMethod)="searchValues('ORDER_LINE_NUMBER',$event)" 
                           [ngModelOptions]="{standalone: true}" minLength="3">
                      </p-autoComplete>     
                </span>
			</div>
		  </div>
		  
		</div>
	</form>
	<p-footer>
		<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
			<button type="submit" pButton class="primary-btn" label="Search" (click)="search()"></button>
			<button type="reset" pButton class="secondary-btn" (click)="reset(dt)" label="Cancel"></button>
		</div>
	</p-footer>
</p-dialog>