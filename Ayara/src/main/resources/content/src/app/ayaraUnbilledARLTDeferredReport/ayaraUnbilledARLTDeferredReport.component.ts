import { Location } from '@angular/common';
import { Component, Injectable } from '@angular/core';
import { Table } from 'primeng/table';
import { CommonSharedService } from '../shared/common.service';
import { NotificationService } from '../shared/notifications.service';
import { AyaraUnbilledARLTDeferredReportService } from './ayaraUnbilledARLTDeferredReportservice';
import { RmanCustomersService } from '../rmanCustomers/rmanCustomersservice';
import { RmanLookupCodesService } from '../rmanLookupCodes/rmanLookupCodesservice';



declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Injectable()
@Component({
  templateUrl: './ayaraUnbilledARLTDeferredReport.component.html',
  selector: 'ayaraUnbilledARLTDeferredReport-data',
  providers: [AyaraUnbilledARLTDeferredReportService,RmanCustomersService]
})


export class AyaraUnbilledARLTDeferredReportComponent {

  paginationOptions: any;
  ayaraUnbilledARLTDeferredReportList: any[];
  data: any[];
  columns: any[] = [];
  pageSize: number;
  totalElements: number;
  customerName: any;
  customerNumber: any;
  rcNumber: any;
  orderNumber: any;
  orderLineNumber: any;
  rowCount: any;
  unbilledARLTRepList: any[];
  unbilledARLTList: any[];
  displayDialog: boolean;
  fiscalPeriodsList: any[] = [];
  fromPeriodArray: any[] = [];
  totalRecords: any;
  customers:any[] = [];
  selectedCustomer: any = {};

  noData = appSettings.noData;
  loading: boolean;
  collapsed: boolean = false;
  _dataTable: any;

  showAddColumns = true;
  isSelectAllChecked = true;
  globalCols: any[];
  clonedCols: any[];
  userId: number;
  showPaginator: boolean = true;
  startIndex: number;

  valueResults: string[];

  exportCols: string[] = [];
  disableExport: boolean = true;
  
  customerNames: String[]  = [];
  customerNumbers: String[]= [];
  dealArrangementNumbers: String[]  = [];
  
  currentDate: Date = new Date();

  constructor(private ayaraUnbilledARLTDeferredReportService: AyaraUnbilledARLTDeferredReportService, private location: Location, private commonSharedService: CommonSharedService, private notificationService: NotificationService,
  private rmanCustomersService: RmanCustomersService) {
    this.globalCols = [];
    this.getAllAyaraUnbilledARLTDeferredReport();
    this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };
  }

  ngOnInit() {
    this.rowCount = 20;
  }

  getTableColumns(pageName: string, tableName: string) {
    this.isSelectAllChecked = true;
    const substrings = ["Jan-", "Feb-", "Mar-","Apr-","May-","Jun-","Jul-","Aug-","Sep-","Oct-","Nov-","Dec-","Future"];
    this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
      if (response && response != null && response.userId) {
        this.columns = [];
        let colsList = response.tableColumns.split(",");
        if (colsList.length > 0) {
          colsList.forEach((item, index) => {
            if (item) {
              this.startIndex = this.globalCols.findIndex(col => col.field == item);
              this.onDrop(index);
            }
          });
        }
        this.globalCols.forEach(col => {
          if (response.tableColumns.indexOf(col.field) !== -1) {
            this.columns.push(col);
          } else {
            if(this.checkIfStringStartsWith(col.field,substrings)==true){ 
					this.columns.push(col);
				}else{
					col.showField = false;	
				}	
          }
        });
        if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
        this.showPaginator = this.columns.length !== 0;
        this.userId = response.userId;
      } else {
        this.columns = this.globalCols;
      }
    }).catch(() => {
      this.notificationService.showError('Error occured while getting table columns data');
      this.loading = false;
    });
  }
  
  checkIfStringStartsWith(str, substrs) {
        return substrs.some(substr => str.startsWith(substr));
  }
  	
  saveColumns() {
    let selectedCols = "";
    this.showAddColumns = !this.showAddColumns;
    const colLength = this.globalCols.length - 1;
    this.globalCols.forEach((col, index) => {
		const substrings = ["Jan-", "Feb-", "Mar-","Apr-","May-","Jun-","Jul-","Aug-","Sep-","Oct-","Nov-","Dec-","Future"];
      if (col.showField) {
		if(this.checkIfStringStartsWith(col.field,substrings)==false){  
	        selectedCols += col.field;
	        if (index < colLength) {
	          selectedCols += ",";
	        }
	    }   
      }
    });
    this.loading = true;
    this.commonSharedService.saveOrUpdateTableColumns("ayaraUnbilledARLTDeferredReport", "WaterFall Details Report", selectedCols, this.userId).then((response) => {
      this.columns = this.globalCols.filter(item => item.showField);
      this.userId = response["userId"];
      this.showPaginator = this.columns.length !== 0;
      this.loading = false;
    }).catch(() => {
      this.notificationService.showError('Error occured while saving');
      this.loading = false;
    });
  }

  onDragStart(index: number) {
    this.startIndex = index;
  }

  onDrop(dropIndex: number) {
    const general = this.globalCols[this.startIndex]; // get element
    this.globalCols.splice(this.startIndex, 1);       // delete from old position
    this.globalCols.splice(dropIndex, 0, general);    // add to new position
  }

  selectColumns(col: any) {
    let cols = this.globalCols.filter(item => !item.showField);
    if (cols.length > 0) {
      this.isSelectAllChecked = false;
    } else {
      this.isSelectAllChecked = true;
    }
  }

  onSelectAll() {
    this.isSelectAllChecked = !this.isSelectAllChecked;
    this.globalCols.forEach(col => {
      if (this.isSelectAllChecked) {
        col.showField = true;
      } else {
        if (col.drag) {
          col.showField = false;
        }
      }
    });
  }

  onConfiguringColumns(event: any) {
    this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
    this.showAddColumns = false;
  }

  closeConfigureColumns(event: any) {
    this.showAddColumns = true;
    this.globalCols = this.clonedCols;
    let configCol = this.globalCols.filter(item => !item.showField);
    this.isSelectAllChecked = !(configCol.length > 0);
  }

  goToOperationReports() {
    this.location.back();
  }

  onBeforeToggle(evt: any) {
    this.collapsed = evt.collapsed;
  }
  paginate(data) {
    this.loading = true;
    this.unbilledARLTRepList = this.unbilledARLTList.slice(data.first, data.first + 10);
    this.loading = false;
  }

  onRowSelect(data) {
  }

  getAllAyaraUnbilledARLTDeferredReport() {
    this.globalCols = [];
    this.loading = true;
    this.customerNumber = this.selectedCustomer.customerNumber;
    this.ayaraUnbilledARLTDeferredReportService.getAyaraUnbilledARLTDeferredReport(this.customerNumber, this.rcNumber, this.orderNumber, this.orderLineNumber).then((data: any) => {
      this.unbilledARLTRepList = data.Content;
	  this.unbilledARLTList = data.Content;
      let dTemp: any = this.unbilledARLTRepList[0];
      for (let prop in dTemp) {
        this.globalCols.push({
          field: prop,
          header: prop,
          style: { 'width': '100px', 'text-align': 'right' },
          display: 'table-cell',
          showField: true,
          text: "right",
          drag: true
        });

      }

      let leftAlignItems = ["Customer", "Revenue Contract", "Deal Line Number", "SD Flag", "Proof Posted", "Period"];

      for (let index = 0; index < this.globalCols.length; index++) {
        if (leftAlignItems.indexOf(this.globalCols[index].header) == -1) {
          this.globalCols[index].text = "right";
        } else {
          this.globalCols[index].text = "left";
        }
      }

     // this.unbilledARLTRepList = data.Content.slice(0, 10);
      this.totalRecords = data.Content.length;
      this.loading = false;

      this.columns = [];
      this.getTableColumns("ayaraUnbilledARLTDeferredReport", "Unbilled AR LT Deferred Report");
      this.disableExport = false;

    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });
  }




  showDialogToSearch() {
    this.customerNumber = '';
  	this.rcNumber = '';
  	this.orderNumber = '';
  	this.orderLineNumber = '';
    this.displayDialog = true;
    this.selectedCustomer = {};
  }

  
  exportCSVfile() {
    this.exportCols = [];

    for (let index = 0; index < this.columns.length; index++) {
      if (this.columns[index].showField) {
        var header = this.columns[index].header;
        this.exportCols.push(header.replace(/[#$/()]/g, ''));
      }
    }

    let exportServiceUrl = this.ayaraUnbilledARLTDeferredReportService.getAyaraUnbilledARLTDeferredReportFile(this.customerNumber,this.rcNumber,this.orderNumber,this.orderLineNumber,this.exportCols);
    window.location.href = exportServiceUrl;
  }

  search() {
    this.getAllAyaraUnbilledARLTDeferredReport();
    this.displayDialog = false;
  }
  
  searchCustomerNames(event:any){
	  this.rmanCustomersService.getAllRmanCustomers(this.paginationOptions, { 'customerName': event.query },true).then((response: any) => {
	  //this.customerNames= response.content.map(item => item.customerName);
	  	this.customers = response.content;
	  }).catch(err => {
      this.customers = [];
    });  
  }
  
  searchCustomerNumbers(event:any){
	  this.rmanCustomersService.getAllRmanCustomers(this.paginationOptions, { 'customerNumber': event.query },false).then((response: any) => {
	  	this.customerNumbers= response.content.map(item => item.customerNumber);
	  }).catch(err => {
      this.customerNumbers = [];
    });  
  }
  
  
  reset(dt: Table) {
    this.customerNumber = '';
    this.rcNumber = '';
    this.selectedCustomer = {};
    this.getAllAyaraUnbilledARLTDeferredReport();
    this.displayDialog = false;
  }

  fieldType(fieldValue: any) {
    return typeof fieldValue;
  }

  isDateField(fieldName: any) {

    if (fieldName.search('Date') == -1) {
      return false;
    } else {
      return true;
    }
  }
  
  searchRCList(event:any){
	  this.ayaraUnbilledARLTDeferredReportService.fetchRCNumbers(event.query).then((response: any) => {
	  	this.dealArrangementNumbers= response.map(item => item);
	  }).catch(err => {
      this.dealArrangementNumbers = [];
    });  
   }		

  searchValues(parameterName:any,event: any) {
    this.ayaraUnbilledARLTDeferredReportService.getParameterValues(parameterName, event.query)
          .subscribe(results => this.valueResults = results);
  } 

}
