interface ILabels {
    [index: string]: string;
}

export class RmanArrgWaterfallReportVLabels {

    fieldLabels: ILabels;

    constructor() {
        this.fieldLabels = {};
        this.fieldLabels["dealArrangementNumber"] = "Arrangement Number";
        this.fieldLabels["currentPeriod"] = "Current Period";
        this.fieldLabels["dealName"] = "Deal Name";
        this.fieldLabels["dealLineNumber"] = "Deal Line#";
        this.fieldLabels["orderNumber"] = "Order#";
        this.fieldLabels["sourceLineNumber"] = "Source Line#";
        this.fieldLabels["glPeriodNew"] = "Period Name";
        this.fieldLabels["seqNumber"] = "Seq#";
        this.fieldLabels["glAmount"] = "Amount";

    }

}
