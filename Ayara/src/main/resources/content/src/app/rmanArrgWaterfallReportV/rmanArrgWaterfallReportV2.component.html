<div class="content-section implementation">
</div>

<div class="card-wrapper waterfall-detail-report">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card-block">

					<p-panel header="WaterFall Details Report" [toggleable]="false" (onBeforeToggle)=onBeforeToggle($event)>
						<p-header>
							<div class="pull-right icons-list">
								<a (click)="goToOperationReports()" class="add-column">
									<em class="fa fa-reply"></em>Back</a>
								<a (click)="onConfiguringColumns($event)" class="add-column">
									<em class="fa fa-cog"></em>Columns</a>
								<a (click)="showDialogToSearch()" title="Search">
									<em class="fa fa-search"></em>
								</a>
								<a (click)="reset(dt)" title="Reset">
									<em class="fa fa-refresh"></em>
								</a>
								<a (click)="dt.exportCSV()" title="Export">
									<em class="fa fa-external-link"></em>
								</a>
								
								<!-- <a (click)="exportCSVfile()" title="Export" *ngIf="!disableExport">
									<em class="fa fa-external-link"></em>
								</a> -->

								<div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
									<div class="user-popup">
										<div class="content overflow">
											<input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()" />
											<label for="selectall">Select All</label>
											<a class="close" title="Close" (click)="closeConfigureColumns($event)">&times;</a>
											<p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
												<ng-template let-col let-index="index" pTemplate="item">
													<div *ngIf="col.drag">
														<div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
															<div class="drag">
																<input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" />
																<label>{{col.header}}</label>
															</div>
														</div>
													</div>
													<div *ngIf="!col.drag">
														<div class="ui-helper-clearfix">
															<div>
																<input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"
																/>
																<label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
															</div>
														</div>
													</div>
												</ng-template>
											</p-listbox>
										</div>
										<div class="pull-right">
											<a class="configColBtn" (click)="saveColumns()">Save</a>
											<a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
										</div>
									</div>
								</div>
							</div>
						</p-header>

						<div class="x-scroll mb-2">
							<p-table class="ui-datatable arrangementMgrTbl"  #dt [loading]="loading" id="waterfall-dt" 
							exportFilename="Waterfall_Report_{{currentDate|date:'yyyyMMddhhmmss'}}" [value]="arrgWaterFallRepList"
							[paginator]="true" [rows]="10"
							[columns]="columns" [resizableColumns]="true" columnResizeMode="expand"  scrollable="true" >

								<ng-template pTemplate="colgroup" let-columns>
									<colgroup>
										<col *ngFor="let col of columns">
									</colgroup>
								</ng-template>

								<ng-template pTemplate="header" class="arrangementMgrTblHead">
									<tr>
										<th style="width:100px" *ngFor="let col of columns" pResizableColumn [ngStyle]="{'display': col.display, 'text-align':col.text}">
											{{col.header}}
										</th>

									</tr>
								</ng-template>
								<ng-template pTemplate="body" let-rowData let-arrgWaterFallRep>
									<tr [pSelectableRow]="rowData">
										<td style="width:100px" *ngFor="let col of columns" [ngStyle]="{'display': col.display, 'text-align': col.text}">
											<span title="{{arrgWaterFallRep[col.field]}}">{{(fieldType(arrgWaterFallRep[col.field])==
												'number' ? (arrgWaterFallRep[col.field]|number:'1.2-2') : arrgWaterFallRep[col.field])}}</span>
										</td>


									</tr>
								</ng-template>
								<ng-template pTemplate="emptymessage">
									<div class="no-results-data">
										<p>{{noData}}</p>
									</div>
								</ng-template>





							</p-table>
						</div>

					</p-panel>
				</div>
			</div>
		</div>
	</div>
</div>


<p-dialog header="Search" width="800" [visible]="displayDialog" [closable]="false" [draggable]="true" showEffect="fade"
 [modal]="true">
	<form>
		<div class="ui-grid ui-grid-responsive ui-fluid">
			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Revenue Contract Number From</span>
						<!--<input pInputText name="from" id="from" class="textbox" placeholder="Revenue Contract Number From" [ngModelOptions]="{standalone: true}"
						 [(ngModel)]="fromArrgId" />-->
						
						<p-autoComplete  inputStyleClass="textbox" [suggestions]="dealArrangementNumbers" 
										(completeMethod)="searchRCList($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								 placeholder="RC Number Type atleast 3 Characters" id="from" [(ngModel)]="fromArrgId"></p-autoComplete>
	 
					</span>
				</div>
				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">Revenue Contract Number To</span>
						<!--<input pInputText name="to" id="to" class="textbox" placeholder="Revenue Contract Number To" [ngModelOptions]="{standalone: true}"
						 [(ngModel)]="toArrgId" />-->
						 <p-autoComplete  inputStyleClass="textbox" [suggestions]="dealArrangementNumbers" 
										(completeMethod)="searchRCList($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								 placeholder="RC Number Type atleast 3 Characters" id="to" [(ngModel)]="toArrgId"></p-autoComplete>
	 
					</span>
				</div>
			</div>
			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">From Period</span>
						<p-autoComplete inputId="fromPeriodName" [(ngModel)]="fromPeriod" inputStyleClass="textbox" [ngModelOptions]="{standalone: true}"
						 [suggestions]="fromPeriodArray" appendTo="body" (completeMethod)="searchPeriod($event)" [minLength]='3'></p-autoComplete>
					</span>

				</div>


				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">To Period</span>
						<p-autoComplete inputId="toPeriodName" [(ngModel)]="toPeriod" inputStyleClass="textbox" [ngModelOptions]="{standalone: true}"
						 [suggestions]="fromPeriodArray" appendTo="body" (completeMethod)="searchPeriod($event)" [minLength]='3'></p-autoComplete>
					</span>
				</div>
			</div>
			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">PO Number</span>
						<input pInputText name="po" id="po" class="textbox" placeholder="PO Number" [ngModelOptions]="{standalone: true}" [(ngModel)]="po"
						/>
					</span>
				</div>
				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">Order Number</span>
						<input pInputText name="so" id="so" class="textbox" placeholder="Order Number" [ngModelOptions]="{standalone: true}" [(ngModel)]="so"
						/>
					</span>
				</div>
			</div>
		</div>
		<div class="ui-g-12">
			<div class="ui-g-6">
				<span class="selectSpan"> Billing Company </span>
				<p-dropdown [options]="entities" name="legalEntity" id="legalEntity" [(ngModel)]="legalEntity" appendTo="body"></p-dropdown>
			</div>
			<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">Billing Region</span>
						<!--<input pInputText name="region" id="region" class="textbox" placeholder="Billing Region" [ngModelOptions]="{standalone: true}" [(ngModel)]="region"/>-->
						<p-autoComplete inputStyleClass="textbox" [suggestions]="regions" 
										(completeMethod)="searchRegions($event)" styleClass="wid100"
       								appendTo="body" [minLength]="2" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Billing Region Type atleast 2 Characters" id="region"  [(ngModel)]="region" ></p-autoComplete>
                                    
					</span>
			</div>
		</div>
		<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Customer Name</span>
						<!--<input pInputText name="customerName" id="customerName" class="textbox" placeholder="Customer Name" [ngModelOptions]="{standalone: true}" [(ngModel)]="customerName"
						/>-->
						<p-autoComplete  inputStyleClass="textbox" [suggestions]="customers" 
				(completeMethod)="searchCustomerNames($event)" styleClass="wid100"
								   appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
									placeholder="Customer Name Type atleast 3 Characters" id="from" [(ngModel)]="selectedCustomer" field="customerName"></p-autoComplete>
						<!-- <p-autoComplete  inputStyleClass="textbox" [suggestions]="customerNames" 
										(completeMethod)="searchCustomerNames($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Customer Name Type atleast 3 Characters" id="customerName" [(ngModel)]="customerName" ></p-autoComplete> -->
                        
					</span>
				</div>
				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">Customer Number</span>
						<!--<input pInputText name="customerNumber" id="customerNumber" class="textbox" placeholder="Customer Number" [ngModelOptions]="{standalone: true}" [(ngModel)]="customerNumber"
						/>-->
						          <p-autoComplete  inputStyleClass="textbox" [suggestions]="customerNumbers" 
										(completeMethod)="searchCustomerNumbers($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Customer Number Type atleast 3 Characters" id="customerNumber" [(ngModel)]="customerNumber" ></p-autoComplete>
                                    
					</span>
				</div>
			</div>
		<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Product Family</span>
						<!--<input pInputText name="productFamily" id="productFamily" class="textbox" placeholder="Product Family" [ngModelOptions]="{standalone: true}" [(ngModel)]="productFamily"
						/>-->
						<p-autoComplete  inputStyleClass="textbox" [suggestions]="productFamilies" 
										(completeMethod)="searchProductFamilies($event)" styleClass="wid100"
       								appendTo="body" [minLength]="2" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Product Family Type atleast 2 Characters" id="productFamily" [(ngModel)]="productFamily"></p-autoComplete>
					</span>
				</div>
				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">Product Line</span>
						<!--<input pInputText name="productLine" id="productLine" class="textbox" placeholder="Product Line" [ngModelOptions]="{standalone: true}" [(ngModel)]="productLine"
						/>-->
						 <p-autoComplete  inputStyleClass="textbox" [suggestions]="productLines" 
										(completeMethod)="searchProductLines($event)" styleClass="wid100"
       								appendTo="body" [minLength]="2" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Product Line Type atleast 2 Characters" id="productLine" [(ngModel)]="productLine"></p-autoComplete>
					</span>
				</div>
		</div>	
		<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Asset ID</span>
						<input pInputText name="assetId" id="assetId" class="textbox" placeholder="Asset ID" [ngModelOptions]="{standalone: true}" [(ngModel)]="assetId"
						/>
					</span>
				</div>
				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">Sales Contract Number</span>
						<input pInputText name="salesContract" id="salesContract" class="textbox" placeholder="Sales Contract Number" [ngModelOptions]="{standalone: true}" [(ngModel)]="salesContract"
						/>
					</span>
				</div>
		</div>	
	</form>
	<p-footer>
		<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
			<button type="submit" pButton class="primary-btn" label="Search" (click)="search()"></button>
			<button type="reset" pButton class="secondary-btn" (click)="reset(dt)" label="Cancel"></button>
		</div>
	</p-footer>
</p-dialog>