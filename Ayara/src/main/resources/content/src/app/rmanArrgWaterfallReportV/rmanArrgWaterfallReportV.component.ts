import { Location } from '@angular/common';
import { Component, Injectable } from '@angular/core';
import { Table } from 'primeng/table';
import { RmanFiscalPeriodsService } from '../rmanFiscalPeriods/rmanFiscalPeriodsservice';
import { RmanLegalEntitiesService } from '../rmanLegalEntities/rmanLegalEntitiesservice';
import { CommonSharedService } from '../shared/common.service';
import { NotificationService } from '../shared/notifications.service';
import { RmanArrgWaterfallReportVService } from './rmanArrgWaterfallReportVservice';
import { RmanCustomersService } from '../rmanCustomers/rmanCustomersservice';
import { RmanLookupCodesService } from '../rmanLookupCodes/rmanLookupCodesservice';



declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Injectable()
@Component({
  templateUrl: './rmanArrgWaterfallReportV2.component.html',
  selector: 'rmanArrgWaterfallReportV-data',
  providers: [RmanArrgWaterfallReportVService, RmanFiscalPeriodsService, RmanLegalEntitiesService,RmanCustomersService,RmanLookupCodesService]
})


export class RmanArrgWaterfallReportVComponent {

  paginationOptions: any;
  rmanArrgWaterfallReportVList: any[];
  data: any[];
  columns: any[] = [];
  pageSize: number;
  totalElements: number;
  fromPeriod: any;
  toPeriod: any;
  fromArrgId: any;
  toArrgId: any;
  po: any;
  so: any;
  legalEntity: any;
  region: any;
  customerName: any;
  customerNumber: any;
  assetId: any;
  productFamily: any;
  productLine: any;
  salesContract: any;

  isSerached: number = 0;

  selectedCustomer: any = {};
  
  rowCount: any;
  arrgWaterFallRepList: any[];
  arrgWaterFallList: any[];
  displayDialog: boolean;
  legalEntitiesList: any[] = [];
  fiscalPeriodsList: any[] = [];
  fromPeriodArray: any[] = [];
  entities: any[] = [];
  totalRecords: any;
  customers:any[] = [];

  currentPeriod : any = {};
  

  noData = appSettings.noData;
  loading: boolean;
  collapsed: boolean = false;
  _dataTable: any;

  showAddColumns = true;
  isSelectAllChecked = true;
  globalCols: any[];
  clonedCols: any[];
  userId: number;
  showPaginator: boolean = true;
  startIndex: number;

  exportCols: string[] = [];
  disableExport: boolean = true;
  
  customerNames: String[]  = [];
  customerNumbers: String[]= [];
  regions: String[]        = [];
  legalEntities: String[]  = [];
  productFamilies: String[] = [];
  productLines: String[] = [];
  dealArrangementNumbers: String[]  = [];
  
  currentDate: Date = new Date();

  constructor(private rmanArrgWaterfallReportVService: RmanArrgWaterfallReportVService, private rmanFiscalPeriodsService: RmanFiscalPeriodsService, private rmanLegalEntitiesService: RmanLegalEntitiesService, private location: Location, private commonSharedService: CommonSharedService, private notificationService: NotificationService,
  private rmanCustomersService: RmanCustomersService,private rmanLookupCodesService:RmanLookupCodesService) {
    this.globalCols = [];
    this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };
    this.rmanFiscalPeriodsService.getCurrentfiscalperiod().then(response=>{
        this.currentPeriod = response;
    });
  }

  ngOnInit() {
    this.rowCount = 20;
    this.rmanLegalEntitiesService.getAllRmanLegalEntities(this.paginationOptions, {}).then((data: any) => {
      this.legalEntitiesList = data.content;
      this.getEntities();
    })
    this.getAllRmanArrgWaterfallReportV2();
  }

  getTableColumns(pageName: string, tableName: string) {
    this.isSelectAllChecked = true;
    const substrings = ["Jan-", "Feb-", "Mar-","Apr-","May-","Jun-","Jul-","Aug-","Sep-","Oct-","Nov-","Dec-"];
    this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
      if (response && response != null && response.userId) {
        this.columns = [];
        let colsList = response.tableColumns.split(",");
        if (colsList.length > 0) {
          colsList.forEach((item, index) => {
            if (item) {
              this.startIndex = this.globalCols.findIndex(col => col.field == item);
              this.onDrop(index);
            }
          });
        }
        this.globalCols.forEach(col => {
          if (response.tableColumns.indexOf(col.field) !== -1) {
            this.columns.push(col);
          } else {
            if(this.checkIfStringStartsWith(col.field,substrings)==true){ 
					this.columns.push(col);
				}else{
					col.showField = false;	
				}	
          }
        });
        if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
        this.showPaginator = this.columns.length !== 0;
        this.userId = response.userId;
      } else {
        this.columns = this.globalCols;
      }
    }).catch(() => {
      this.notificationService.showError('Error occured while getting table columns data');
      this.loading = false;
    });
  }
  
  checkIfStringStartsWith(str, substrs) {
        return substrs.some(substr => str.startsWith(substr));
  }
  	
  saveColumns() {
    let selectedCols = "";
    this.showAddColumns = !this.showAddColumns;
    const colLength = this.globalCols.length - 1;
    this.globalCols.forEach((col, index) => {
		const substrings = ["Jan-", "Feb-", "Mar-","Apr-","May-","Jun-","Jul-","Aug-","Sep-","Oct-","Nov-","Dec-"];
      if (col.showField) {
		if(this.checkIfStringStartsWith(col.field,substrings)==false){  
	        selectedCols += col.field;
	        if (index < colLength) {
	          selectedCols += ",";
	        }
	    }   
      }
    });
    this.loading = true;
    this.commonSharedService.saveOrUpdateTableColumns("rmanArrgWaterfallReportV", "WaterFall Details Report", selectedCols, this.userId).then((response) => {
      this.columns = this.globalCols.filter(item => item.showField);
      this.userId = response["userId"];
      this.showPaginator = this.columns.length !== 0;
      this.loading = false;
    }).catch(() => {
      this.notificationService.showError('Error occured while saving');
      this.loading = false;
    });
  }

  onDragStart(index: number) {
    this.startIndex = index;
  }

  onDrop(dropIndex: number) {
    const general = this.globalCols[this.startIndex]; // get element
    this.globalCols.splice(this.startIndex, 1);       // delete from old position
    this.globalCols.splice(dropIndex, 0, general);    // add to new position
  }

  selectColumns(col: any) {
    let cols = this.globalCols.filter(item => !item.showField);
    if (cols.length > 0) {
      this.isSelectAllChecked = false;
    } else {
      this.isSelectAllChecked = true;
    }
  }

  onSelectAll() {
    this.isSelectAllChecked = !this.isSelectAllChecked;
    this.globalCols.forEach(col => {
      if (this.isSelectAllChecked) {
        col.showField = true;
      } else {
        if (col.drag) {
          col.showField = false;
        }
      }
    });
  }

  onConfiguringColumns(event: any) {
    this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
    this.showAddColumns = false;
  }

  closeConfigureColumns(event: any) {
    this.showAddColumns = true;
    this.globalCols = this.clonedCols;
    let configCol = this.globalCols.filter(item => !item.showField);
    this.isSelectAllChecked = !(configCol.length > 0);
  }

  goToOperationReports() {
    this.location.back();
  }

  private buildPivot() {
    var sum = $.pivotUtilities.aggregatorTemplates.sum;
    var numberFormat = $.pivotUtilities.numberFormat;
    var intFormat = numberFormat({ digitsAfterDecimal: 2 });

    $("#pivot").pivot(this.data,
      {
        rows: ["dealArrangementNumber", "dealName", "dealLineNumber", "orderNumber", "sourceLineNumber"],
        cols: ["seqNumber", "glPeriodNew"],
        aggregator: sum(intFormat)(["glAmount"]),
        aggregatorName: "Sum",
        rendererName: "Table",
        hideTotals: true
      });

    $(".pvtTotal").hide();
    $(".colTotal").hide();
    $(".pvtTotalLabel").hide();
    $(".pvtGrandTotal").hide();

  }

  getAllRmanArrgWaterfallReportV() {

    this.rmanArrgWaterfallReportVService.getAllRmanArrgWaterfallReportV(this.paginationOptions, {}).then((rmanArrgWaterfallReportVList: any) => {
      this.data = rmanArrgWaterfallReportVList.content;
      this.totalElements = rmanArrgWaterfallReportVList.totalElements;
      this.pageSize = rmanArrgWaterfallReportVList.size;
      this.buildPivot();
      this.disableExport = false;
    });
  }
  onBeforeToggle(evt: any) {
    this.collapsed = evt.collapsed;
  }
  paginate(data) {
    this.loading = true;
    this.arrgWaterFallRepList = this.arrgWaterFallList.slice(data.first, data.first + 10);
    this.loading = false;
  }

  onRowSelect(data) {
  }

  getAllRmanArrgWaterfallReportV2() {
    this.globalCols = [];
    this.loading = true;
    
    if(this.selectedCustomer.customerNumber === undefined){
      this.customerNumber = this.customerNumber;
    }else{
      this.customerNumber = this.selectedCustomer.customerNumber;
    }
    
    this.rmanArrgWaterfallReportVService.getRmanWaterFallDetailsReport(this.fromArrgId, this.toArrgId, this.fromPeriod, this.toPeriod, this.po, this.so, this.legalEntity,
    this.customerName,this.customerNumber,this.region,this.productFamily,this.productLine,this.assetId,this.salesContract
    ).then((data: any) => {
      this.arrgWaterFallRepList = data.Content;
      this.arrgWaterFallList = data.Content;
      let dTemp: any = this.arrgWaterFallRepList[0];
      for (let prop in dTemp) {
        this.globalCols.push({
          field: prop,
          header: prop,
          style: { 'width': '100px', 'text-align': 'right' },
          display: 'table-cell',
          showField: true,
          text: "right",
          drag: true
        });

      }

      


      let leftAlignItems = ["NOTE", "Cont Apply", "Element Type", "Customer", "Arrangement Name", "Legal Entity", "Deal Name", "Product Name", "Product Line", "Division", "Product Family", "Current Period", "Delivered Date", "Rev SDate", "Rev EDate", "Revenue Contract Name"];

      for (let index = 0; index < this.globalCols.length; index++) {
        if (leftAlignItems.indexOf(this.globalCols[index].header) == -1) {
          this.globalCols[index].text = "right";
        } else {
          this.globalCols[index].text = "left";
        }
      }

      //this.arrgWaterFallRepList = data.Content.slice(0, 10);
      this.totalRecords = this.arrgWaterFallRepList.length;
      this.loading = false;

      this.columns = [];
      this.getTableColumns("rmanArrgWaterfallReportV", "WaterFall Details Report");
      //this.disableExport = false;

    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });
  }




  showDialogToSearch() {
    this.fromArrgId = null;
    this.toArrgId = null;
    this.po = '';
    this.so = null;
    this.legalEntity = null;
    this.region = '';
  	this.customerName = '';
  	this.customerNumber = '';
  	this.assetId = '';
  	this.productFamily = '';
  	this.productLine = '';
  	this.salesContract = '';
    this.selectedCustomer = {};
    this.displayDialog = true;
    if(this.isSerached==0){
      this.fromPeriod = this.currentPeriod.periodName;
      this.toPeriod = this.currentPeriod.periodName;
    }else{
      this.fromPeriod = '';
      this.toPeriod = '';
    }  
  }

  searchPeriod(event: any) {
    let emp = {
      'periodName': event.query
    }
    let temp: any = [];
    this.rmanFiscalPeriodsService.getAllRmanFiscalPeriods(this.paginationOptions, emp).then((data: any) => {
      this.fiscalPeriodsList = data.content;
      this.fiscalPeriodsList.forEach((obj: any) => {
        temp.push(obj.periodName);

      })
      this.fromPeriodArray = temp;

    })
  }

  exportCSVfile() {
    this.exportCols = [];

    for (let index = 0; index < this.columns.length; index++) {
      if (this.columns[index].showField) {
        var header = this.columns[index].header;
        this.exportCols.push(header.replace(/[#$/()]/g, ''));
      }
    }

    let exportServiceUrl = this.rmanArrgWaterfallReportVService.getRmanWaterFallDetailsReportFile(this.fromArrgId, this.toArrgId, this.fromPeriod, this.toPeriod, this.po, this.so, this.legalEntity, 
    this.customerName,this.customerNumber,this.region,this.productFamily,this.productLine,this.assetId,this.salesContract,this.exportCols);
    window.location.href = exportServiceUrl;
  }

  getEntities() {
    let entitiesTemp: any[] = [{ label: '--Select Entity Name--', value: null }];
    this.legalEntitiesList.forEach((obj: any) => {
      entitiesTemp.push({ label: obj.name, value: obj.legalEntityId })
    })
    this.entities = entitiesTemp;
  }

  

  search() {
    this.isSerached = 1;
    this.getAllRmanArrgWaterfallReportV2();
    this.displayDialog = false;
  }
  
  searchCustomerNames(event:any){
	  this.rmanCustomersService.getAllRmanCustomers(this.paginationOptions, { 'customerName': event.query },false).then((response: any) => {
   //   this.customerNames= response.content.map(item => item.customerName);
      this.customers = response.content;
	  }).catch(err => {
     // this.customerNames = [];
      this.customers = [];
    });  
  }
  
  searchCustomerNumbers(event:any){
	  this.rmanCustomersService.getAllRmanCustomers(this.paginationOptions, { 'customerNumber': event.query },false).then((response: any) => {
	  	this.customerNumbers= response.content.map(item => item.customerNumber);
	  }).catch(err => {
      this.customerNumbers = [];
    });  
  }
  
  searchRegions(event:any){
	  this.rmanLookupCodesService.getAllRmanLookupCodes(this.paginationOptions, { 'lookupTypeCode': 'REGIONS','enabledFlag':'Y','lookupCode': event.query }).then((response: any) => {
	  	this.regions= response.content.map(item => item.lookupCode);
	  }).catch(err => {
      this.regions = [];
    });  
  }
  
  searchLegalEntities(event:any){
	  this.rmanLegalEntitiesService.getAllRmanLegalEntities(this.paginationOptions, { 'name': event.query }).then((response: any) => {
	  	this.legalEntities= response.content.map(item => item.name);
	  }).catch(err => {
      this.legalEntities = [];
    });  
  }

  searchProductFamilies(event:any){
	  this.rmanLookupCodesService.getAllRmanLookupCodes(this.paginationOptions, { 'lookupTypeCode': 'PRODUCT_FAMILIES','enabledFlag':'Y','description': event.query }).then((response: any) => {
	  	this.productFamilies= response.content.map(item => item.description);
	  }).catch(err => {
      this.productFamilies = [];
    });  
  }

  searchProductLines(event:any){
	  this.rmanLookupCodesService.getAllRmanLookupCodes(this.paginationOptions, { 'lookupTypeCode': 'PRODUCT_LINES','enabledFlag':'Y','description': event.query }).then((response: any) => {
	  	this.productLines= response.content.map(item => item.description);
	  }).catch(err => {
      this.productLines = [];
    });  
  }


  reset(dt: Table) {
    this.fromArrgId = null;
    this.toArrgId = null;
    this.fromPeriod = this.currentPeriod.periodName;
    this.toPeriod = this.currentPeriod.periodName;
    this.po = '';
    this.so = null;
    this.legalEntity = null;
    this.region = '';
  	this.customerName = '';
  	this.customerNumber = '';
  	this.assetId = '';
  	this.productFamily = '';
  	this.productLine = '';
  	this.salesContract = '';
    this.selectedCustomer = {};
    this.getAllRmanArrgWaterfallReportV2();
    this.displayDialog = false;
    this.isSerached = 0;
  }

  fieldType(fieldValue: any) {
    return typeof fieldValue;
  }

  isDateField(fieldName: any) {

    if (fieldName.search('Date') == -1) {
      return false;
    } else {
      return true;
    }
  }

  searchRCList(event:any){
	  this.rmanArrgWaterfallReportVService.fetchRCNumbers(event.query).then((response: any) => {
	  	this.dealArrangementNumbers= response.map(item => item);
	  }).catch(err => {
      this.dealArrangementNumbers = [];
    });  
   }		

}
