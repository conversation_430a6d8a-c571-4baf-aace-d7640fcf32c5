export interface RmanArrangementsAllV {
    dealFlag: any;
    endCustomerName: any;
    masterArrgName: any;
    arrangementSource: any;
    revManagerId: any;
    arrangementCurrency: any;
    msaName: any;
    contractFlag: any;
    endCustomerNumber: any;
    lastUpdateDate: any;
    customerContact: any;
    changeReason: any;
    arrangementTotal: any;
    arrangementName: any;
    masterArrgId: any;
    dealNumber: any;
    arrangementBasis: any;
    dealName: any;
    repCurrCode: any;
    createdBy: any;
    salesNodeLevel4: any;
    lastUpdatedBy: any;
    msaNumber: any;
    salesNodeLevel2: any;
    calcuationMethod: any;
    salesNodeLevel3: any;
    salesContact: any;
    arrangementNumber: any;
    legalEntityId: any;
    revAccountantId: any;
    reasonCode: any;
    creationDate: any;
    guidanceType: any;
    arrangementKey: any;
    masterArrgFlag: any;
    customerPoNum: any;
    arrangementId: any;
    salesNodeLevel1: any;
    arrangementType: any;
    allocationEligible: any;
    guidanceName: any;
    legalEntityName: any;
    arrangementStatus: any;
    orderNumber: any;
    arrangementQtr: any;
    contApplied: any;
}
