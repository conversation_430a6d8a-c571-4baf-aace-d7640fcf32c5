export interface RmanOrderLinesBookingsV {
  listPrice: any;
  revrecAcctScope: any;
  so: any;
  priceList: any;
  shipeedDate: any;
  elementType: any;
  milestone: any;
  revrecEndDate: any;
  shipDate: any;
  shippedQuantity: any;
  endCustomer: any;
  lineAmount: any;
  pobTemplate: any;
  pobGrouping: any;
  revrecDelay: any;
  billedDate: any;
  bookingCurrency: any;
  netPrice: any;
  revrecAcctRule: any;
  parentLineId: any;
  customerPoNum: any;
  productName: any;
  lineStatus: any;
  arrangementId: any;
  funcCurrencyCode: any;
  bookedDate: any;
  accountingRuleName: any;
  accountingScope: any;
  uom: any;
  ouName: any;
  orderBookedDate: any;
  productFamily: any;
  revrecAccount: any;
  productCategory: any;
  rmanLineId: any;
  orderedQuantity: any;
  da: any;
  contractStartDate: any;
  contractEndDate: any;
  revrecStDate: any;
  bundleFlag: any;
  soLineNum: any;
  sourceLineId: any;
  amortRuleRevenue: any;
  sourceHeaderId: any;
  refOrderNumber: any;
  serviceRefOrder: any;
  serviceRefOrdLineNum: any;
  contractModifed: any;
  contractModifiedDate: any;
  lineCost: any;
  manufacturingCost: any;
}
