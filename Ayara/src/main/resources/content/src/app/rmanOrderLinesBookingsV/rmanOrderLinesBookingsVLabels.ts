interface ILabels {
  [index: string]: string;
}

export class RmanOrderLinesBookingsVLabels {

  fieldLabels: ILabels;

  constructor() {

    this.fieldLabels = {};

    this.fieldLabels["listPrice"] = "Unit List Price";
    this.fieldLabels["revrecAcctScope"] = "Revrec Acct Scope";
    this.fieldLabels["so"] = "So#";
    this.fieldLabels["priceList"] = "Price List";
    this.fieldLabels["shipeedDate"] = "Delivered Date";
    this.fieldLabels["elementType"] = "Element Type";
    this.fieldLabels["revrecEndDate"] = "Revrec End Date";
    this.fieldLabels["shippedQuantity"] = "Shipped Quantity";
    this.fieldLabels["endCustomer"] = "End Customer";
    this.fieldLabels["revrecDelay"] = "Revrec Delay";
    this.fieldLabels["bookingCurrency"] = "Booking Currency";
    this.fieldLabels["netPrice"] = "Unit Selling Price";
    this.fieldLabels["revrecAcctRule"] = "Revrec Acct Rule";
    this.fieldLabels["parentLineId"] = "Parent Line Id";
    this.fieldLabels["customerPoNum"] = "Customer Po Num";
    this.fieldLabels["productName"] = "Product Name";
    this.fieldLabels["lineStatus"] = "Line Status";
    this.fieldLabels["arrangementId"] = "Arrangement Id";
    this.fieldLabels["funcCurrencyCode"] = "Func Currency Code";
    this.fieldLabels["accountingRuleName"] = "Accounting Rule";
    this.fieldLabels["accountingScope"] = "Accounting Scope";
    this.fieldLabels["ouName"] = "Ou Name";
    this.fieldLabels["orderBookedDate"] = "Order Booked Date";
    this.fieldLabels["productFamily"] = "Product Family";
    this.fieldLabels["revrecAccount"] = "Revrec Account";
    this.fieldLabels["productCategory"] = "Product Line";
    this.fieldLabels["orderedQuantity"] = "Ordered Quantity";
    this.fieldLabels["da"] = "Da";
    this.fieldLabels["contractStartDate"] = "Contract Start Date";
    this.fieldLabels["contractEndDate"] = "Contract End Date";
    this.fieldLabels["revrecStDate"] = "Revrec St Date";
    this.fieldLabels["bundleFlag"] = "Bundle Flag";
    this.fieldLabels["orderLineId"] = "Order Line ID";
    this.fieldLabels["uom"] = "UOM";
    this.fieldLabels["lineAmount"] = "Line Amount";
    this.fieldLabels["bookedDate"] = "Booked Date";
    this.fieldLabels["shipDate"] = "Delivered Date";
    this.fieldLabels["milestone"] = "Milestone";
    this.fieldLabels["pobGrouping"] = "POB ID";
    this.fieldLabels["pobTemplate"] = "POB Template";
    this.fieldLabels["billedDate"] = "Billed Date";
    this.fieldLabels["soLineNum"] = "SO Line#";
    this.fieldLabels["sourceLineId"] = "Source Line Id";
    this.fieldLabels["amortRuleRevenue"] = "Amortization Rule";
    this.fieldLabels["sourceHeaderId"] = "Source Header Id";
    this.fieldLabels["refOrderNumber"] = "Reference SO#";
    this.fieldLabels["serviceRefOrder"] = "Service Ref SO#";
    this.fieldLabels["serviceRefOrdLineNum"] = "Service Ref Line#";
    this.fieldLabels["contractModifed"] = "Contract Modified";
    this.fieldLabels["contractModifiedDate"] = "Contract Modified Date";
    this.fieldLabels["lineCost"] = "Line Cost";
    this.fieldLabels["manufacturingCost"] = "Manufacturing Cost";


  }

}
