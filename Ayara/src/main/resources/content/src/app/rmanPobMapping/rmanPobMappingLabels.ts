interface ILabels {
    [index: string]: string;
}

export class RmanPobMappingLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["pobGrouping"] = "POB Grouping";
        this.fieldLabels["parentSku"] = "Parent Product";
        this.fieldLabels["attribute3"] = "Attribute3";
        this.fieldLabels["attribute2"] = "Attribute2";
        this.fieldLabels["attribute1"] = "Legal Entity";
        this.fieldLabels["standaloneSubSku"] = "Product Name";
        this.fieldLabels["endDate"] = "End Date";
        this.fieldLabels["startDate"] = "Start Date";
        this.fieldLabels["split"] = "Split";
        this.fieldLabels["attribute5"] = "Attribute5";
        this.fieldLabels["attribute4"] = "Attribute4";
        this.fieldLabels["splitBasis"] = "Split Basis";
        this.fieldLabels["status"] = "Status";
        this.fieldLabels["pobMapId"] = "POB Map ID";
        this.fieldLabels["skuType"] = "Mapping Type";
        this.fieldLabels["legalEntityName"] = "Legal Entity"
    }

}
