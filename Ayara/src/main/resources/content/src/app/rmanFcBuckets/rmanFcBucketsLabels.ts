interface ILabels {
    [index: string]: string;
}

export class RmanFcBucketsLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["attribute10"] = "Attribute10";
        this.fieldLabels["attribute14"] = "Attribute14";
        this.fieldLabels["attribute13"] = "Attribute13";
        this.fieldLabels["createdDate"] = "Created Date";
        this.fieldLabels["attribute12"] = "Attribute12";
        this.fieldLabels["attribute11"] = "Attribute11";
        this.fieldLabels["bucketId"] = "Bucket Id";
        this.fieldLabels["endDate"] = "End Date";
        this.fieldLabels["numberOfYears"] = "Number Of Years";
        this.fieldLabels["attribute3"] = "Attribute3";
        this.fieldLabels["createdBy"] = "Created By";
        this.fieldLabels["attribute2"] = "Attribute2";
        this.fieldLabels["lastUpdatedBy"] = "Last Updated By";
        this.fieldLabels["attribute1"] = "Attribute1";
        this.fieldLabels["numberOfBuckets"] = "Number Of Buckets";
        this.fieldLabels["startDate"] = "Start Date";
        this.fieldLabels["attribute9"] = "Attribute9";
        this.fieldLabels["attribute8"] = "Attribute8";
        this.fieldLabels["attribute7"] = "Attribute7";
        this.fieldLabels["latestFlag"] = "Latest Flag";
        this.fieldLabels["attribute6"] = "Attribute6";
        this.fieldLabels["attribute5"] = "Attribute5";
        this.fieldLabels["name"] = "Name";
        this.fieldLabels["attribute4"] = "Attribute4";
        this.fieldLabels["lastUpdatedDate"] = "Last Updated Date";
        this.fieldLabels["numberOfQtrs"] = "Number Of Qtrs";
        this.fieldLabels["version"] = "Version";
        this.fieldLabels["numberOfPeriods"] = "Number Of Periods";
        this.fieldLabels["attribute15"] = "Attribute15";
    }

}
