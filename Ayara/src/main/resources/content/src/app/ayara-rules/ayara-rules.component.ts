import {Component, OnInit, ViewChild} from '@angular/core';
import {ConfirmationService, Message} from 'primeng/api';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import { RmanLookupCodesService } from '../rmanLookupCodes/rmanLookupCodesservice';
import {RmanLookupsVService} from '../rmanLookupsV/rmanLookupsVservice';
import {NotificationService} from '../shared/notifications.service';
import {CommonSharedService} from '../shared/common.service';
import {Table} from 'primeng/table';
import { AyaraRules } from './ayara-rules';
import { AyaraRulesService } from './ayara-rules-service';
import { RuleEngineConditionsComponent } from '../rule-engine-conditions/rule-engine-conditions.component';

declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');
@Component({
  selector: 'ayara-rules',
  templateUrl: './ayara-rules.component.html',
  providers: [AyaraRulesService, ConfirmationService, RmanLookupCodesService]
})
export class AyaraRulesComponent implements OnInit {

    constructor(private ayaraRulesService: AyaraRulesService, private rmanLookupsVService: RmanLookupsVService,
                private rmanLookupCodesService: RmanLookupCodesService, private formBuilder: FormBuilder, private confirmationService: ConfirmationService,
                private notificationService: NotificationService, private commonSharedService: CommonSharedService) {
        this.paginationOptions = {'pageNumber': 0, 'pageSize': '10000'};
        this.loading = true;

        
        this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, {'lookupTypeName': 'RULE_TYPE'})
            .then((rmanLookupsVList: any) => {
            this.loading = false;
            this.rmanRuleTypeList = rmanLookupsVList.content;
            this.prepareRmanRuleTypeObject();
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting "Rule Type" data');
            this.loading = false;
        });

        this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, {'lookupTypeName': 'ENABLED_FLAG'})
            .then((rmanLookupsVList: any) => {
            this.loading = false;
            this.rmanActivatedFlagList = rmanLookupsVList.content;
            this.prepareRmanActivatedFlagObject();
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting "Activated Flag" data');
            this.loading = false;
        });


    }

    displayDialog: boolean;

    showMsg: any;

    displaySearchDialog: boolean;

    ayaraRules: any = new AyaraRulesImpl();

    ayaraRulesSearch: any = new AyaraRulesImpl();

    isSerached = 0;

    selectedAyaraRules: AyaraRules;

    newAyaraRules: boolean;

    pRuleId: number;
    pRuleCategory: string;

    ayaraRulesList: any[];

    cols: any[];
    columns: any[];

    columnOptions: any[];

    msgs: Message[] = [];


    paginationOptions: any;

    pages: {};

    datasource: any[];
    pageSize: number;
    totalElements: number;

    globalCols: any[];
    clonedCols: any[];

    showAddColumns = true;
    isSelectAllChecked = true;

    userId: number;
    showPaginator = true;
    startIndex: number;


    rmanCostBasisList: any[] = []; 
    rmanRuleBasisList: any[] = []; 
    rmanActivatedFlagList = []; 
    rmanRuleTypeList: any[] = []; 
    rmanRuleAdjTypeList: any[] = []; 
    rmanApplicationLevelList: any[] = []; 
    rmanVCBasisList: any[] = []; 
    rmanICBasisList: any[] = []; 
    rmanCostBasisV: any[]; 
    rmanRuleBasisV: any[]; 
    rmanActivatedFlagsV: any[];
    rmanRuleTypeV: any[]; 
    rmanRuleAdjTypeV: any[]; 
    rmanApplicationLevelV: any[]; 
    filteredRuleBasisV: any[] = []; 

    noData = appSettings.noData;
    collapsed = true;
    arrangementRulesForm: FormGroup;
    loading: boolean;
    isApplicationLevelEnabled = false;

    private ruleAdjTypeDataLoaded: boolean = false;
    private applicationLevelDataLoaded: boolean = false;


    @ViewChild(RuleEngineConditionsComponent) private childTable: RuleEngineConditionsComponent;

    formErrors = {
        'ruleName': '', 'ruleType': '', 'ruleStartDate': '', 'ruleEndDate': '', 'ruleAdjValue': '', 'ruleAdjType': '', 'ruleApplyLevel': '', 'enabledFlag': '', 'ruleBasis': ''
    };

    validationMessages = {
        'ruleName': {
            'required': 'Rule Name is required (minimum length is 4 and maximum length is 65 characters)',
            'minlength': 'Rule Name must be at least 4 characters long.',
            'maxlength': 'Rule Name cannot be more than 65 characters long.',
        },
        'ruleType': {
            'required': 'Rule Type is required'
        },
        'ruleBasis': {
            'required': 'Rule Basis is required'
        },
        'ruleAdjType': {
            'required': 'Adjustment Type is required'
        },
        'ruleApplyLevel': {
            'required': 'Application Level is required when Rule Type is Variable Consideration'
        },
        'ruleStartDate': {
            'required': 'Rule Start Date is Required'
        },
        'category': {
            'required': 'Rule Category is required'
        },
        'ruleAdjValue': {
            'required': 'Cost Adjustment % is required (Only number values are allowed [0-9])',
        'minlength': 'Cost Adjustment % must be at least 1 characters long.',
            'pattern': 'Only number values are allowed [0-9]',
            'min': 'Minimum number allowed is -100.00',
            'percentageExceeded': 'Percentage value cannot exceed 100%',
        }

    };

    hideColumnMenu = true;

    showFilter = false;

    ngOnInit(): void {
        this.globalCols = [
            {field: 'ruleName', header: 'Rule Name', showField: true, display: 'table-cell', type: 'text', drag: false},
            {field: 'ruleType', header: 'Rule Type', showField: true, display: 'table-cell', type: 'text', drag: false},
            {field: 'ruleBasis', header: 'Rule Basis', showField: true, display: 'table-cell', type: 'text', drag: true},
            {field: 'ruleAdjType', header: 'Adjustment Type', showField: true, display: 'table-cell', type: 'text', drag: true},
            {field: 'ruleAdjValue', header: 'Adjustment Value', showField: true, display: 'table-cell', type: 'number', drag: true},
            {field: 'ruleApplyLevel', header: 'Application Level', showField: true, display: 'table-cell', type: 'text', drag: true},
            {field: 'ruleStartDate', header: 'Start Date', showField: true, display: 'table-cell', type: 'date', drag: true},
            {field: 'ruleEndDate', header: 'End Date', showField: true, display: 'table-cell', type: 'date', drag: true},
            {field: 'enabledFlag', header: 'Activated Flag', showField: true, display: 'table-cell', type: 'text', drag: true},

        ];

        this.columns = [];
        this.getTableColumns('AyaraRules', 'Ayara Rules');

        this.buildForm();
       
    }


    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
            if (response && response != null && response.userId) {
                this.columns = [];
                const colsList = response.tableColumns.split(',');
                if (colsList.length > 0) {
                    colsList.forEach((item, index) => {
                        if (item) {
                            this.startIndex = this.globalCols.findIndex(col => col.field === item);
                            this.onDrop(index);
                        }
                    });
                }
                this.globalCols.forEach(col => {
                    if (response.tableColumns.indexOf(col.field) !== -1) {
                        this.columns.push(col);
                    } else {
                        col.showField = false;
                    }
                });
                if (this.columns.length !== this.globalCols.length) {
                    this.isSelectAllChecked = false;
                }
                this.showPaginator = this.columns.length !== 0;
                this.userId = response.userId;
            } else {
                this.columns = this.globalCols;
            }
        }).catch(() => {
            this.notificationService.showError('Error occured while getting table columns data');
            this.loading = false;
        });
    }

    saveColumns() {
        let selectedCols = '';
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
            if (col.showField) {
                selectedCols += col.field;
                if (index < colLength) {
                    selectedCols += ',';
                }
            }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns('AyaraRules', 'Ayara Rules', selectedCols, this.userId).then((response) => {
            this.columns = this.globalCols.filter(item => item.showField);
            this.userId = response['userId'];
            this.showPaginator = this.columns.length !== 0;
            this.loading = false;
        }).catch(() => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    onDragStart(index: number) {
        this.startIndex = index;
    }

    onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
        // console.log(this.globalCols);
    }

    selectColumns(col: any) {
        const cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
            this.isSelectAllChecked = false;
        } else {
            this.isSelectAllChecked = true;
        }
    }

    onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
            if (this.isSelectAllChecked) {
                col.showField = true;
            } else {
                if (col.drag) {
                    col.showField = false;
                }
            }
        });
    }

    reset(dt: Table) {
        this.paginationOptions = {};
        this.ayaraRules = new AyaraRulesImpl();
        dt.reset();
    }


    onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
    }

    closeConfigureColumns(event: any) {
        this.showAddColumns = true;
        this.globalCols = this.clonedCols;
        const configCol = this.globalCols.filter(item => !item.showField);
        this.isSelectAllChecked = !(configCol.length > 0);
    }

    buildForm() {
        this.arrangementRulesForm = this.formBuilder.group({
            'ruleName': ['', [Validators.required, Validators.minLength(4), Validators.maxLength(65)]],
            'ruleType': ['', [Validators.required]],
            'ruleStartDate': ['', [Validators.required]],
            'ruleAdjType': ['', [Validators.required]],
            'ruleAdjValue': ['', [Validators.required, Validators.minLength(1), Validators.pattern(/^\-?\d+(\.\d+)?$/), Validators.min(-100.00), this.percentageValidator.bind(this)]],
            'ruleBasis': ['', [Validators.required]],
            'ruleApplyLevel': [''],
            'enabledFlag': ['', [Validators.required]],
            'ruleEndDate': [''],
        });
        this.arrangementRulesForm.valueChanges
            .subscribe(data => this.onValueChanged(data));
        this.onValueChanged();

        // Application Level based on Rule Type
        this.updateApplicationLevelValidation();
    }

    updateApplicationLevelValidation() {
        if (!this.arrangementRulesForm) {
            return;
        }

        const ruleTypeControl = this.arrangementRulesForm.get('ruleType');
        const ruleApplyLevelControl = this.arrangementRulesForm.get('ruleApplyLevel');

        if (ruleTypeControl && ruleApplyLevelControl) {
            const ruleTypeValue = ruleTypeControl.value;
            const isVariableConsideration = ruleTypeValue === 'VC';


            this.isApplicationLevelEnabled = isVariableConsideration;

            if (isVariableConsideration) {

                ruleApplyLevelControl.enable();
                ruleApplyLevelControl.setValidators([Validators.required]);
            } else {

                ruleApplyLevelControl.disable();
                ruleApplyLevelControl.clearValidators();
                ruleApplyLevelControl.setValue(null);
            }

            ruleApplyLevelControl.updateValueAndValidity();
        }
    }

    onValueChanged(data?: any) {
        if (!this.arrangementRulesForm) {
            return;
        }
        const form = this.arrangementRulesForm;

        for (const field in this.formErrors) {

            this.formErrors[field] = '';
            const control = form.get(field);


            if (control && !control.valid) {
                const shouldShowError = control.dirty || control.touched ||
                    (control.errors && control.errors['percentageExceeded']);

                if (shouldShowError) {
                    const messages = this.validationMessages[field];
                    for (const key in control.errors) {
                        this.formErrors[field] += messages[key] + ' ';
                    }
                }
            }
        }
    }

    transformRmanLookupsV(rmanLookupsV: any) {
        if (rmanLookupsV) {
            return rmanLookupsV.lookupDescription;
        }
    }

    transformRuleAdjType(ruleAdjTypeCode: any) {


        if (!ruleAdjTypeCode || !this.rmanRuleAdjTypeList) {
            return ruleAdjTypeCode;
        }


        const lookupItem = this.rmanRuleAdjTypeList.find(item => item.lookupCode === ruleAdjTypeCode);

        if (lookupItem) {

            let description = lookupItem.attribute1;
            if (!description || description === null) {
                description = lookupItem.description;
            }
            if (!description || description === null) {
                description = lookupItem.lookupCode;
            }
            return description;
        }

        return ruleAdjTypeCode;
    }

    transformRuleApplyLevel(ruleApplyLevelCode: any) {
        if (!ruleApplyLevelCode || !this.rmanApplicationLevelList) {
            return ruleApplyLevelCode;
        }


        const lookupItem = this.rmanApplicationLevelList.find(item => item.lookupCode === ruleApplyLevelCode);

        if (lookupItem) {

            let description = lookupItem.attribute1;
            if (!description || description === null) {
                description = lookupItem.description;
            }
            if (!description || description === null) {
                description = lookupItem.lookupCode;
            }
            return description;
        }


        return ruleApplyLevelCode;
    }


    getAllAyaraRules() {
        this.loading = true;
        this.ayaraRulesService.getAllAyaraRules(this.paginationOptions, this.ayaraRules)
            .then((ayaraRulesList: any) => {
            this.loading = false;
            this.datasource = ayaraRulesList.content;
            this.ayaraRulesList = ayaraRulesList.content;
            if (this.ayaraRulesList.length > 0) {

                this.selectedAyaraRules = this.ayaraRulesList[0];
                this.pRuleId = this.selectedAyaraRules.ruleId;
                this.childTable.parentCall(this.selectedAyaraRules);

            } else {

                this.selectedAyaraRules = null;
                this.pRuleId = null;

            }


            this.totalElements = ayaraRulesList.totalElements;
            this.pageSize = ayaraRulesList.size;
            this.displaySearchDialog = false;
        }).catch((err: any) => {
            console.error(err);
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }


    getAyaraRules(event: any) {

        const first: number = event.first;
        const rows: number = event.rows;
        const pageNumber: number = first / rows;
        this.paginationOptions = {
            'pageNumber': pageNumber,
            'pageSize': this.pageSize,
            'sortField': event.sortField,
            'sortOrder': event.sortOrder
        };
        this.loading = true;
        this.ayaraRulesService.getAllAyaraRules(this.paginationOptions, this.ayaraRules).then((ayaraRulesList: any) => {
            this.loading = false;
            this.datasource = ayaraRulesList.content;
            this.ayaraRulesList = ayaraRulesList.content;
            if (this.ayaraRulesList.length > 0) {
                /*Begining of Code used for default First Row Selected*/
                this.selectedAyaraRules = this.ayaraRulesList[0];
                this.pRuleId = this.selectedAyaraRules.ruleId;
                this.childTable.parentCall(this.selectedAyaraRules);
                /*End of Code used for default First Row Selected*/
            } else {
                // No records found - clear selection
                this.selectedAyaraRules = null;
                this.pRuleId = null;
                // Don't call childTable.parentCall('') here - let it show "No records found" naturally
            }
            this.totalElements = ayaraRulesList.totalElements;
            this.pageSize = ayaraRulesList.size;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });

    }


    showDialogToAdd() {
        console.log('Opening add rule dialog...');

        this.newAyaraRules = true;
        this.ayaraRules = new AyaraRulesImpl();
        this.displayDialog = true;

      
        this.ruleAdjTypeDataLoaded = false;
        this.applicationLevelDataLoaded = false;
        this.rmanRuleBasisList = []; 

        this.prepareRmanCostBasisObject();
        this.prepareRmanActivatedFlagObject();
        this.prepareRmanRuleTypeObject();

        
        this.prepareRmanRuleAdjTypeObject(); // This will now include fallback if API is empty
        this.prepareRmanApplicationLevelObject(); // This will now include fallback if API is empty
        this.filteredRuleBasisV = [{label: '--Select Rule Basis(*)--', value: null}];


        this.buildForm();

        // Initialize Application Level validation for new rule
        setTimeout(() => {
            this.updateApplicationLevelValidation();
        }, 0);

    }

   
    loadRuleAdjTypeDataOnFocus(): Promise<void> {
        return new Promise((resolve, reject) => {
           
            if (!this.ruleAdjTypeDataLoaded) { // Removed || this.rmanRuleAdjTypeList.length === 0 here to prevent redundant calls if API returns empty
                console.log('API Call: Fetching Adjustment Type data on focus...');
                this.loading = true;
                this.rmanLookupCodesService.getAllRmanLookupCodes(this.paginationOptions, {'lookupTypeCode': 'RuleAdjType'})
                    .then((rmanLookupCodesList: any) => {
                    this.rmanRuleAdjTypeList = rmanLookupCodesList.content || [];
                   
                    this.prepareRmanRuleAdjTypeObject(); // Re-prepare options AFTER data is fetched
                    this.ruleAdjTypeDataLoaded = true; // Set flag to true
                    this.loading = false;
                    
                    resolve();
                }).catch((err: any) => {
                    console.error('Error loading RuleAdjType on focus:', err);
                    this.notificationService.showError('Error occurred while loading Adjustment Type data');
                    this.loading = false;
                    reject(err);
                });
            } else {
                console.log('Adjustment Type data already loaded (no API call).');
                this.prepareRmanRuleAdjTypeObject(); 
                resolve();
            }
        });
    }

   
    loadApplicationLevelDataOnFocus(): Promise<void> {
        return new Promise((resolve, reject) => {
            
            if (!this.applicationLevelDataLoaded) { 
              
                this.loading = true;
                this.rmanLookupCodesService.getAllRmanLookupCodes(this.paginationOptions, {'lookupTypeCode': 'APPLICATION_LEVEL'})
                    .then((rmanLookupCodesList: any) => {
                    this.rmanApplicationLevelList = rmanLookupCodesList.content || [];
                    
                    this.prepareRmanApplicationLevelObject(); 
                    this.applicationLevelDataLoaded = true; 
                    this.loading = false;
                    console.log('Application Level data loaded on focus:', this.rmanApplicationLevelList);
                    resolve();
                }).catch((err: any) => {
                    
                    this.notificationService.showError('Error occurred while loading Application Level data');
                    this.loading = false;
                    reject(err);
                });
            } else {
               
                this.prepareRmanApplicationLevelObject(); 
                resolve();
            }
        });
    }

    
    loadRuleBasisDataForType(lookupTypeCode: string): Promise<void> {
        return new Promise((resolve, reject) => {
            
            const isDataAlreadyLoadedForType = this.rmanRuleBasisList.length > 0 && 
                                               this.rmanRuleBasisList.every(item => item.lookupTypeCode === lookupTypeCode);

            if (!isDataAlreadyLoadedForType) {
                console.log(`API Call: Fetching Rule Basis data for type: ${lookupTypeCode}...`);
                this.loading = true;
                this.rmanLookupCodesService.getAllRmanLookupCodes(this.paginationOptions, {'lookupTypeCode': lookupTypeCode})
                    .then((rmanLookupCodesList: any) => {
                        this.rmanRuleBasisList = rmanLookupCodesList.content || []; 
                        console.log(`API Response for Rule Basis (${lookupTypeCode}):`, this.rmanRuleBasisList); 
                        this.loading = false;
                      
                        this.filterRuleBasisOptions(lookupTypeCode); 
                        resolve();
                    })
                    .catch((err: any) => {
                        console.error(`Error loading Rule Basis data for type ${lookupTypeCode}:`, err);
                        this.notificationService.showError(`Error occurred while loading Rule Basis data for ${lookupTypeCode}`);
                        this.loading = false;
                        reject(err);
                    });
            } else {
                
                this.filterRuleBasisOptions(lookupTypeCode); 
                resolve();
            }
        });
    }


    saveOrUpdate(msg: any) {
        this.notificationService.showSuccess(msg);
        this.loading = false;
        this.getAllAyaraRules();
    }

    save() {
        if (this.newAyaraRules) {
            this.loading = true;
            this.ayaraRulesService.saveAyaraRules(this.ayaraRules).then((response: any) => {
                this.saveOrUpdate('Saved successfully');
            }).catch((err: any) => {
                this.notificationService.showError('Error occurred while saving data');
                this.loading = false;
            });
        } else {
            this.loading = true;
            const obj = this.prepareObjectToUpdate();
            this.ayaraRulesService.updateAyaraRules(obj).then((response: any) => {
                this.saveOrUpdate('Updated successfully');
            }).catch((err: any) => {
                this.notificationService.showError('Error occurred while updating data');
                this.loading = false;
            });
        }
        this.ayaraRules = new AyaraRulesImpl();
        this.displayDialog = false;
    }

    delete(ayaraRules: any) {
        this.ayaraRules = ayaraRules;
        this.ayaraRules.ruleEndDate = new Date(this.ayaraRules.ruleEndDate);
        this.ayaraRules.ruleStartDate = new Date(this.ayaraRules.ruleStartDate);

        console.log('Delete - ayaraRules:', this.ayaraRules);
        console.log('Delete - ruleId:', this.ayaraRules.ruleId);

        this.displayDialog = false;
        this.confirmationService.confirm({
            message: 'Are you sure you want to delete this record?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
                this.loading = true;
                this.ayaraRulesService.deleteAyaraRules(this.ayaraRules).then((response: any) => {
                    this.loading = false;
                    this.ayaraRulesList.splice(this.findSelectedAyaraRulesIndex(), 1);
                    this.notificationService.showSuccess('Deleted successfully');
                    this.ayaraRules = new AyaraRulesImpl();
                    this.getAllAyaraRules();
                }, error => {
                    console.error('Delete error:', error);
                    this.notificationService.showError('Error occurred while deleting data');
                    this.loading = false;
                });
            },
            reject: () => {
                this.notificationService.showWarning('You have rejected');
            }
        });
    }


    editRow(ayaraRules: any) {
        this.newAyaraRules = false;
        this.ayaraRules = this.cloneAyaraRules(ayaraRules);
        if (this.ayaraRules.ruleEndDate) {
            this.ayaraRules.ruleEndDate = new Date(this.ayaraRules.ruleEndDate);
        }
        if (this.ayaraRules.ruleStartDate) {
            this.ayaraRules.ruleStartDate = new Date(this.ayaraRules.ruleStartDate);
        }

        this.displayDialog = true;
        this.prepareRmanCostBasisObject();
        this.prepareRmanActivatedFlagObject();
        this.prepareRmanRuleTypeObject();

         
        this.loadRuleAdjTypeDataOnFocus();
        this.loadApplicationLevelDataOnFocus();

       
        if (this.ayaraRules.ruleType) {
            this.loadRuleBasisDataForType(this.ayaraRules.ruleType);
        } else {
           
            this.filteredRuleBasisV = [{label: '--Select Rule Basis(*)--', value: null}];
        }

        this.buildForm();
        setTimeout(() => {
            this.updateApplicationLevelValidation();
        }, 0);
    }


    findSelectedAyaraRulesIndex(): number {
        return this.ayaraRulesList.indexOf(this.selectedAyaraRules);
    }

    onRowUnSelect() {
        this.childTable.parentCall('');
    }


    onRowSelect(event: any) {
        this.selectedAyaraRules = event.data;
        this.pRuleId = this.selectedAyaraRules.ruleId;
        this.childTable.parentCall(event.data);

    }

    cloneAyaraRules(c: AyaraRules): AyaraRules {
        const ayaraRules: any
            = new AyaraRulesImpl();
        for (const prop in c) {
            ayaraRules[prop] = c[prop];
        }
        return ayaraRules;
    }

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {

        this.ayaraRulesSearch = new AyaraRulesImpl();

        if (this.isSerached == 0) {
            this.ayaraRulesSearch = new AyaraRulesImpl();
        }
        this.displaySearchDialog = true;

    }

    cancelSearch() {
        this.displaySearchDialog = false;
        this.ayaraRulesSearch = new AyaraRulesImpl();

    }

    cancelEdit() {
        this.displayDialog = false;
        this.ayaraRules = new AyaraRulesImpl();
    }

    search() {
        this.isSerached = 1;
        this.ayaraRules = this.ayaraRulesSearch;
        this.paginationOptions = {};
        this.getAllAyaraRules();
    }

    prepareRmanCostBasisObject() {
        const rmanCostBasisTempObj: any = [{label: '--Select Cost Basis(*)--', value: null}];
        this.rmanCostBasisList.forEach((rmanLookupsV) => {
            rmanCostBasisTempObj.push({label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode});
        });

        this.rmanCostBasisV = rmanCostBasisTempObj;

    }

    
    prepareRmanRuleBasisObject() {
        const rmanRuleBasisTempObj: any = [{label: '--Select Rule Type(*)--', value: null}];
      
        this.rmanRuleBasisList.forEach((rmanLookupsV) => {
            rmanRuleBasisTempObj.push({label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode});
        });

        this.rmanRuleBasisV = rmanRuleBasisTempObj;

    }

    prepareRmanActivatedFlagObject() {
        const rmanActivatedFlagTempObj: any = [{label: '--Select Activated Flag(*)--', value: null}];
        this.rmanActivatedFlagList.forEach((rmanLookupsV) => {
            rmanActivatedFlagTempObj.push({label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode});
        });

        this.rmanActivatedFlagsV = rmanActivatedFlagTempObj;

    }

    prepareRmanRuleTypeObject() {
        const rmanRuleTypeTempObj: any = [{label: '--Select Rule Type(*)--', value: null}];
        this.rmanRuleTypeList.forEach((rmanLookupsV) => {
            rmanRuleTypeTempObj.push({label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode});
        });

        this.rmanRuleTypeV = rmanRuleTypeTempObj;

    }

    prepareRmanRuleAdjTypeObject() {
        const rmanRuleAdjTypeTempObj: any = [{label: '--Select Adjustment Type(*)--', value: null}];

      
        if (this.rmanRuleAdjTypeList && this.rmanRuleAdjTypeList.length > 0) {
            this.rmanRuleAdjTypeList.forEach((rmanLookupCode) => {
               
                const displayLabel = rmanLookupCode.attribute1 || rmanLookupCode.description || rmanLookupCode.lookupCode;
                rmanRuleAdjTypeTempObj.push({
                    label: displayLabel,
                    value: rmanLookupCode.lookupCode
                });
            });
        } else {
            
            rmanRuleAdjTypeTempObj.push({label: 'AMOUNT', value: 'AMOUNT'});
            rmanRuleAdjTypeTempObj.push({label: 'PERCENT', value: 'PERCENT'});
            
        }

        this.rmanRuleAdjTypeV = rmanRuleAdjTypeTempObj;
       
    }


    prepareRmanApplicationLevelObject() {
        const rmanApplicationLevelTempObj: any = [{label: '--Select Application Level(*)--', value: null}];

        
        if (this.rmanApplicationLevelList && this.rmanApplicationLevelList.length > 0) {
            this.rmanApplicationLevelList.forEach((rmanLookupCode) => {
                let description = rmanLookupCode.attribute1;
                if (!description || description === null) {
                    description = rmanLookupCode.description;
                }
                if (!description || description === null) {
                    description = rmanLookupCode.lookupCode;
                }
                rmanApplicationLevelTempObj.push({
                    label: description,
                    value: rmanLookupCode.lookupCode
                });
            });
        } else {
           
            rmanApplicationLevelTempObj.push({label: 'No options available', value: null, disabled: true});
        }

        this.rmanApplicationLevelV = rmanApplicationLevelTempObj;
        
    }


    percentageValidator(control: any) {

        if (!control.value) {

            return null; // Don't validate empty values, let required validator handle that
        }


        const adjType = this.arrangementRulesForm?.get('ruleAdjType')?.value;


        if (adjType && adjType.toString().toUpperCase() === 'PERCENT') {
            const value = parseFloat(control.value);

            if (value > 100) {

                return { 'percentageExceeded': true };
            } else {

            }
        } else {

        }


        return null; // Valid
    }


    onAdjustmentTypeChange(_selectedAdjType: any) {

        const adjValueControl = this.arrangementRulesForm?.get('ruleAdjValue');
        if (adjValueControl) {
            adjValueControl.updateValueAndValidity();

            this.onValueChanged();
        }
    }


    onAdjustmentValueChange(_value: any) {

        const adjValueControl = this.arrangementRulesForm?.get('ruleAdjValue');
        if (adjValueControl) {
            adjValueControl.updateValueAndValidity();

            this.onValueChanged();
        }
    }

    onRuleTypeChange(selectedRuleType: any) {
        this.ayaraRules.ruleBasis = null;

       
        if (selectedRuleType) {
            this.loadRuleBasisDataForType(selectedRuleType);
        } else {
            
            this.filteredRuleBasisV = [{label: '--Select Rule Basis(*)--', value: null}];
            this.rmanRuleBasisList = []; 
        }

        this.updateApplicationLevelValidation();
    }


    filterRuleBasisOptions(ruleType: string) {
        if (!this.rmanRuleBasisList || this.rmanRuleBasisList.length === 0) {
            this.filteredRuleBasisV = [{label: '--Select Rule Basis(*)--', value: null}];
            return;
        }
        if (!ruleType) {

            this.filteredRuleBasisV = [{label: '--Select Rule Basis(*)--', value: null}];
            return;
        }


        
        const filteredBasisList = this.rmanRuleBasisList.filter(item => {
            return item.lookupTypeCode === ruleType;
        });
        const filteredBasisTempObj: any = [{label: '--Select Rule Basis(*)--', value: null}];

        if (filteredBasisList.length === 0) {


            filteredBasisTempObj.push({label: 'No options available for ' + ruleType, value: null, disabled: true});
        } else {
            filteredBasisList.forEach((rmanLookupCode) => {

                let description = rmanLookupCode.attribute1;

                if (!description || description === null) {
                    description = rmanLookupCode.description;
                }
                if (!description || description === null) {
                    description = rmanLookupCode.lookupCode;
                }
                filteredBasisTempObj.push({
                    label: description,
                    value: description
                });
            });
        }
        this.filteredRuleBasisV = filteredBasisTempObj;
    }

    onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }

    prepareObjectToUpdate() {
        const objToUpdate = new AyaraRulesImpl();
        objToUpdate.ruleName = this.ayaraRules.ruleName;
        objToUpdate.ruleEndDate = this.ayaraRules.ruleEndDate;
        objToUpdate.ruleType = this.ayaraRules.ruleType;
        objToUpdate.ruleAdjType = this.ayaraRules.ruleAdjType;
        objToUpdate.ruleAdjValue = this.ayaraRules.ruleAdjValue;
        objToUpdate.ruleBasis = this.ayaraRules.ruleBasis;
        objToUpdate.enabledFlag = this.ayaraRules.enabledFlag;
        objToUpdate.ruleStartDate = this.ayaraRules.ruleStartDate;
        objToUpdate.ruleId = this.ayaraRules.ruleId;
        objToUpdate.ruleApplyLevel = this.ayaraRules.ruleApplyLevel;

        return objToUpdate;
    }
}

class AyaraRulesImpl {
    constructor(
      public ruleId?: any,
      public ruleName?: any,
      public ruleType?: any,
      public ruleBasis?: any,
      public ruleAdjType?: any,
      public ruleAdjValue?: any,
      public ruleApplyLevel?: any,
      public ruleStartDate?: any,
      public ruleEndDate?: any,
      public enabledFlag?: any
        ) {
    }
}
