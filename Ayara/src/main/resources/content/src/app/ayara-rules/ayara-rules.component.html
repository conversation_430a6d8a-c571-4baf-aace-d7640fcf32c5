<div class="content-section implementation">
</div>
<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>
<div class="card-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block">
                    <h2>Ayara Rules</h2>
                    <div class="pull-right icons-list">
                        <a  (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
                        <a (click)="showDialogToAdd()" title="Add"><em class="fa fa-plus-circle"></em></a>
                        <a  (click)="showDialogToSearch()" title="Search"><em class="fa fa-search"></em></a>
                        <a  (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
                        <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
                            <div class="user-popup">
                                <div class="content overflow">
                                    <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()"/>
                                    <label for="selectall">Select All</label>
                                    <a class="close" title="Close" (click)="closeConfigureColumns($event)" >&times;</a>
                                    <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                                        <ng-template let-col let-index="index" pTemplate="item">
                                            <div *ngIf="col.drag">
                                                <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens"
                                                     (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                                                    <div class="drag">
                                                        <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)"/>
                                                        <label>{{col.header}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div *ngIf="!col.drag">
                                                <div class="ui-helper-clearfix">
                                                    <div>
                                                        <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"/>
                                                        <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </ng-template>
                                    </p-listbox>

                                </div>

                                <div class="pull-right">
                                    <a class="configColBtn" (click)="saveColumns()">Save</a>
                                    <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="x-scroll">
                        <p-table class="ui-datatable arrangementMgrTbl" #dt id="arrangementRules-dt" [loading]="loading" [columns]="columns" [value]="ayaraRulesList" selectionMode="single" [(selection)]="selectedAyaraRules"
                                 (onRowSelect)="onRowSelect($event)"  (onRowUnselect)="onRowUnSelect()" (onLazyLoad)="getAyaraRules($event)" [lazy]="true" [paginator]="true" [rows]="pageSize"
                                 [totalRecords]="totalElements"  scrollable="true" [resizableColumns]="true" columnResizeMode="expand">

                            <ng-template pTemplate="colgroup" let-columns>
                                <colgroup>
                                    <col>
                                    <col *ngFor="let col of columns">
                                </colgroup>
                            </ng-template>

                            <ng-template pTemplate="header" class="arrangementMgrTblHead">
                                <tr>
                                    <th></th>
                                    <ng-container *ngFor="let col of columns">
                                        <th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                                        <th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                                    </ng-container>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-rowData let-ayaraRules let-columns="columns">
                                <tr [pSelectableRow]="rowData">
                                    <td>
                                        <a  (click)="editRow(ayaraRules)" class="icon-edit" title="Edit"> </a>
                                        <a  (click)="delete(ayaraRules)" class="icon-delete" title="Delete"> </a>
                                    </td>
                                    <ng-container *ngFor="let col of columns" >
                                        <td *ngIf="col.type == 'text' && col.field !== 'ruleAdjType' && col.field !== 'ruleApplyLevel'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field]}}
                                        </td>

                                       
                                        <td *ngIf="col.type == 'text' && col.field === 'ruleAdjType'" title="{{transformRuleAdjType(rowData[col.field])}}" [ngStyle]="{'display': col.display}">
                                            {{transformRuleAdjType(rowData[col.field])}}
                                        </td>

                                       
                                        <td *ngIf="col.type == 'text' && col.field === 'ruleApplyLevel'" title="{{transformRuleApplyLevel(rowData[col.field])}}" [ngStyle]="{'display': col.display}">
                                            {{transformRuleApplyLevel(rowData[col.field])}}
                                        </td>

                                        <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field]}}
                                        </td>

                                        <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                                        </td>

                                        <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field] | round}}
                                        </td>
                                    </ng-container>

                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage" let-columns>
                                <tr *ngIf="!columns">
                                    <td class="no-data">{{noData}}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<app-rule-engine-conditions [pRuleId]='pRuleId'></app-rule-engine-conditions>




<p-dialog header="Search" width="800" [(visible)]="displaySearchDialog"  showEffect="fade" [modal]="true" [draggable]="true" [blockScroll]="true" (onHide)="cancelSearch()">
    <form>
        <div class="ui-grid ui-grid-responsive ui-fluid">
            <div class="ui-g-12">
                <div class="ui-g-6">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Rule Name </span>
                            <input pInputText class="textbox" placeholder="Rule Name" name="ruleName" id="ruleName" [ngModelOptions]="{standalone: true}" [(ngModel)]="ayaraRulesSearch.ruleName"
                            />
                        </span>
                </div>
                
                <div class="ui-g-6 pull-right">
                        <span class="md-inputfield">
                            <span class="selectSpan">Rule Type  </span>
                            <p-dropdown [options]="rmanRuleTypeV" [(ngModel)]="ayaraRulesSearch.ruleType" name="ruleType" [filter]="true" appendTo="body">
                            </p-dropdown>
                        </span>
                </div>
            </div>
            <div class="ui-g-12">
                <div class="ui-g-6">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Activated Flag </span>
                            <p-dropdown [options]="rmanActivatedFlagsV" [(ngModel)]="ayaraRulesSearch.enabledFlag" name="enabledFlag" [filter]="true"
                                        appendTo="body">
                            </p-dropdown>
                        </span>
                </div>
            </div>
         
        </div>

    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" class="primary-btn" pButton label="Search" (click)="search()"></button>
            <button type="button" class="secondary-btn" pButton (click)="displaySearchDialog=false" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>
<p-dialog header="{{(newAyaraRules) ? 'Create Rule' : 'Edit Rule'}}" width="800" [(visible)]="displayDialog"
          showEffect="fade" [modal]="true" [draggable]="true" [blockScroll]="true" (onHide)="cancelEdit()">
    <form (ngSubmit)="save()" [formGroup]="arrangementRulesForm" novalidate>
        <div class="ui-grid ui-grid-responsive ui-fluid" *ngIf="ayaraRules">
            <div class="ui-g">
                <div class="ui-g-12">
                    <div class="ui-g-6">
                            <span class="md-inputfield">
                                <span class="selectSpan"> Rule Name  <span class="red-color">*</span> </span>
                                <input pInputText class="textbox" placeholder="Rule Name" name="ruleName" id="ruleName" [(ngModel)]="ayaraRules.ruleName" formControlName="ruleName"
                                />
                                <div *ngIf="formErrors.ruleName" class="ui-message ui-messages-error ui-corner-all">
                                    {{ formErrors.ruleName }}
                                </div>
                            </span>
                    </div>
                    <div class="ui-g-6">
                       
                        <span class="selectSpan"> Rule Type <span class="red-color">*</span> </span>
                        <p-dropdown [options]="rmanRuleTypeV" [(ngModel)]="ayaraRules.ruleType" name="ruleType" [filter]="true" formControlName="ruleType"
                                    (onChange)="onRuleTypeChange($event.value)" appendTo="body">

                        </p-dropdown>
                        <div *ngIf="formErrors.ruleType" class="ui-message ui-messages-error ui-corner-all">
                            {{ formErrors.ruleType }}
                        </div>
                    </div>
                </div>
                <div class="ui-g-12">
                    <div class="ui-g-6">
                        <span class="selectSpan"> Rule Basis<span class="red-color">*</span> </span>
                        <p-dropdown [options]="filteredRuleBasisV" [(ngModel)]="ayaraRules.ruleBasis" name="ruleBasis" [filter]="true" formControlName="ruleBasis"
                                    [disabled]="!ayaraRules.ruleType" appendTo="body">

                        </p-dropdown>
                        <div *ngIf="formErrors.ruleBasis" class="ui-message ui-messages-error ui-corner-all">
                            {{ formErrors.ruleBasis }}
                        </div>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan"> Adjustment Type <span class="red-color">*</span> </span>
                        <p-dropdown [options]="rmanRuleAdjTypeV" [(ngModel)]="ayaraRules.ruleAdjType" name="ruleAdjType" [filter]="true" formControlName="ruleAdjType"
                                    (onChange)="onAdjustmentTypeChange($event.value)" (onFocus)="loadRuleAdjTypeDataOnFocus()" appendTo="body">

                        </p-dropdown>
                        <div *ngIf="formErrors.ruleAdjType" class="ui-message ui-messages-error ui-corner-all">
                            {{ formErrors.ruleAdjType }}
                        </div>
                    </div>
                </div>
                <div class="ui-g-12">

                    <div class="ui-g-6">
                            <span class="md-inputfield">
                                <span class="selectSpan"> Adjustment Value<span class="red-color">*</span> </span>
                                <input pInputText name="ruleAdjValue" class="textbox" placeholder="Adjustment" id="ruleAdjValue" formControlName="ruleAdjValue"
                                       [(ngModel)]="ayaraRules.ruleAdjValue" (ngModelChange)="onAdjustmentValueChange($event)" />
                                    <div *ngIf="formErrors.ruleAdjValue" class="ui-message ui-messages-error ui-corner-all">
                                        {{ formErrors.ruleAdjValue }}
                                    </div>
                                    <small *ngIf="ayaraRules.ruleAdjType && ayaraRules.ruleAdjType.toString().toUpperCase() === 'PERCENT'"
                                           class="help-text" style="color: #666; font-style: italic;">
                                                Percentage values cannot exceed 100%.
                                    </small>

                            </span>
                    </div>

                    <div class="ui-g-6">
                            <span class="md-inputfield">
                                <span class="selectSpan"> Activated Flag <span class="red-color">*</span> </span>
                        <p-dropdown [options]="rmanActivatedFlagsV" [(ngModel)]="ayaraRules.enabledFlag" name="enabledFlag" [filter]="true" formControlName="enabledFlag"
                                    appendTo="body">

                        </p-dropdown>
                        <div *ngIf="formErrors.enabledFlag" class="ui-message ui-messages-error ui-corner-all">
                            {{ formErrors.enabledFlag }}
                        </div>
                            </span>
                    </div>

                </div>
                <div class="ui-g-12">

                    <div class="ui-g-6">
                        <span class="selectSpan"> Application Level <span class="red-color" *ngIf="isApplicationLevelEnabled">*</span> </span>
                        <p-dropdown [options]="rmanApplicationLevelV" [(ngModel)]="ayaraRules.ruleApplyLevel" name="ruleApplyLevel" [filter]="true" formControlName="ruleApplyLevel"
                                    [disabled]="!isApplicationLevelEnabled" (onFocus)="loadApplicationLevelDataOnFocus()" appendTo="body">

                        </p-dropdown>
                        <div *ngIf="formErrors.ruleApplyLevel" class="ui-message ui-messages-error ui-corner-all">
                            {{ formErrors.ruleApplyLevel }}
                        </div>
                        <small *ngIf="!isApplicationLevelEnabled" class="help-text" style="color: #666; font-style: italic;">
                            Application Level is only required when Rule Type is Variable Consideration.
                        </small>
                    </div>

                    <div class="ui-g-6">
                        <span class="selectSpan">Rule Start Date  <span class="red-color">*</span></span>
                        <p-calendar showAnim="slideDown" inputStyleClass="textbox" name="ruleStartDate" id="ruleStartDate" [(ngModel)]="ayaraRules.ruleStartDate" [monthNavigator]="true"
                                    [yearNavigator]="true" yearRange="1950:2030" appendTo="body" formControlName="ruleStartDate" dateFormat="yy-mm-dd"
                                    placeholder="Rule Start Date*">
                            <div *ngIf="formErrors.ruleStartDate" class="ui-message ui-messages-error ui-corner-all">
                                {{ formErrors.ruleStartDate }}
                            </div>
                        </p-calendar>
                    </div>

                </div>
                <div class="ui-g-12">

                    <div class="ui-g-6">
                        <span class="selectSpan">Rule End Date</span>
                        <p-calendar inputStyleClass="textbox" showAnim="slideDown" name="ruleEndDate" id="ruleEndDate" [ngModelOptions]="{standalone: true}" [(ngModel)]="ayaraRules.ruleEndDate"
                                    [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" appendTo="body" dateFormat="yy-mm-dd"
                                    placeholder="Rule End Date"></p-calendar>
                    </div>

                </div>
            </div>
        </div>
    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" class="primary-btn" pButton label="Save" (click)="save()" [disabled]="!arrangementRulesForm.valid"></button>
            <button type="button" class="secondary-btn" pButton (click)="displayDialog=false" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>


<div class="modal fade dialogueBox" id="FieldsMandatory" role="dialog" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">{{'Error'}}</h4>
            </div>
            <form class="form-horizontal">
                <div class="modal-body clearfix">
                    <div class="col-md-12 col-sm-12 col-xs-12 col-lg-12">

                        <p *ngIf="showMsg==5">Please enter Rule Name</p>
                        <p *ngIf="showMsg==6">Please enter Rule Category</p>
                        <p *ngIf="showMsg==7">Please enter Rule Start Date</p>


                    </div>

                </div>
                <div class="modal-footer" style="padding:3px;">
                    <button class="btn btn-primary pull-right" data-dismiss="modal">OK</button>
                </div>
            </form>
        </div>
    </div>
</div>
