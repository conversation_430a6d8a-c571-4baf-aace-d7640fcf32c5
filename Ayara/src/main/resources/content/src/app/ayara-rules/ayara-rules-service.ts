import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {DatePipe} from '@angular/common';
declare var require: any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type': 'application/json',
  })
};


@Injectable({
  providedIn: 'root'
})
export class AyaraRulesService {

  constructor(private http: HttpClient, private datePipe: DatePipe) { }

  getAllAyaraRules(paginationOptions: any, ayaraRulesSearchObject: any): Promise<any[]> {

    let serviceUrl = appSettings.apiUrl + '/ayaraRulesSearch?';

    let searchString = '';
    const searchFeilds = ['ruleId', "ruleName", "ruleType","ruleBasis", "ruleAdjType", "ruleAdjValue", "ruleApplyLevel", "ruleStartDate", "ruleEndDate",  "enabledFlag"];
    searchFeilds.forEach((obj) => {
      if (ayaraRulesSearchObject[obj] != undefined && ayaraRulesSearchObject[obj] != "") {
        if ( ayaraRulesSearchObject[obj] instanceof Date) {
          searchString = searchString + obj + ':' + this.datePipe.transform(ayaraRulesSearchObject[obj],'yyyyMMdd') + ',';
        } else {
          searchString = searchString + obj + ':' + ayaraRulesSearchObject[obj] + ',';
        }
      }
    });

    if (searchString == '') {
      serviceUrl = serviceUrl + 'search=%25';
    }
    else {
      serviceUrl = serviceUrl + 'search=' + searchString;
    }

    if (paginationOptions.pageNumber != undefined && paginationOptions.pageNumber != "" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber != 0) {
      serviceUrl = serviceUrl + '&page=' + paginationOptions.pageNumber + '&size=' + paginationOptions.pageSize;
    }

    return this.http.get(serviceUrl).toPromise().then((data: any) => {
      return data;
    });
  }

  saveAyaraRules(ayaraRules: any): Promise<any[]> {
    let body = JSON.stringify(ayaraRules);
    return this.http.post<any[]>(appSettings.apiUrl + '/AYARA_RULES', body, httpOptions).toPromise().then(data => {
      return data;
    });
  }

  updateAyaraRules(ayaraRules: any): Promise<any[]> {
    const ruleId = ayaraRules.ruleId;
    delete ayaraRules._links;
    delete ayaraRules.interests;
    delete ayaraRules.ruleId;
    let body = JSON.stringify(ayaraRules);
    return this.http.put<any[]>(appSettings.apiUrl + '/AYARA_RULES/' + ruleId, body, httpOptions).toPromise().then(data => {
      return data;
    });

  }

  deleteAyaraRules(ayaraRules: any): Promise<any[]> {
    let deleteUrl = appSettings.apiUrl + '/AYARA_RULES/' + ayaraRules.ruleId;
    return this.http.delete(deleteUrl, httpOptions).toPromise().then((data: any) => {
      return data;
    });
  }
}
