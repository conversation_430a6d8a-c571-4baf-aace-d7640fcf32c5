<div class="card-wrapper">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card-block">

          <p-tabView (onChange)="handleChange($event)">
            <ng-container *ngIf="exceptionType=='all'">
              <all-exceptions></all-exceptions>
            </ng-container>
            <ng-container *ngIf="exceptionType=='customers'">
              <rmanCustomersInterface-data></rmanCustomersInterface-data>
            </ng-container>
			<ng-container *ngIf="exceptionType=='products'">
              <rmanProductsInterface-data></rmanProductsInterface-data>
            </ng-container>
			<ng-container *ngIf="exceptionType=='conversions'">
              <rmanConversionRatesInt-data></rmanConversionRatesInt-data>
            </ng-container>
			<ng-container *ngIf="exceptionType=='fiscalPeriods'">
              <rmanFiscalPeriodsInterface-data></rmanFiscalPeriodsInterface-data>
            </ng-container>
			<ng-container *ngIf="exceptionType=='usages'">
              <rmanUsageSummaryInterface-data></rmanUsageSummaryInterface-data>
            </ng-container>
            <ng-container *ngIf="exceptionType=='quotes'">
              <rmanIntfDealDetails-data></rmanIntfDealDetails-data>
            </ng-container>
            <ng-container *ngIf="exceptionType=='bookings'">
              <rmanOrdersInterface-data [otype]="orderType"></rmanOrdersInterface-data>
            </ng-container>
            <ng-container *ngIf="exceptionType=='shipments'">
              <rmanOrdersInterface-data [otype]="orderType"></rmanOrdersInterface-data>
            </ng-container>
            <ng-container *ngIf="exceptionType=='billings'">
              <rmanInvoiceLinesInterface-data></rmanInvoiceLinesInterface-data>
            </ng-container>
            <ng-container *ngIf="exceptionType=='pobMappings'">
              <ayaraPobMappingsInterface-data></ayaraPobMappingsInterface-data>
            </ng-container>
            <ng-container *ngIf="exceptionType=='manage-adjustments'">
              <app-manage-adjustments></app-manage-adjustments>
            </ng-container>
           
          </p-tabView>

        </div>
      </div>
    </div>
  </div>
</div>