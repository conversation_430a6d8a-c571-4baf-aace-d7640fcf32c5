import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Table } from 'primeng/table';
import { CommonSharedService } from '../../shared/common.service';
import { NotificationService } from '../../shared/notifications.service';
import { RmanDealDetailsService } from '../../rmanDealDetails/rmanDealDetailsservice';
import { HttpClient } from '@angular/common/http';
import { privateDecrypt } from 'crypto';

@Component({
  selector: 'app-manage-adjustments',
  templateUrl: './manage-adjustments.component.html',
  styleUrls: ['./manage-adjustments.component.css'],
  providers: [RmanDealDetailsService]
})
export class ManageAdjustmentsComponent implements OnInit {

  adjustmentsList: any[] = [];
  adjustment: any;
  arrangementId: string;
  editingRowId: any = null;
  columns: any[] = [];
  globalCols: any[] = [];
  clonedCols: any[] = [];
  showAddColumns = true;
  isSelectAllChecked = true;
  userId: number;
  loading: boolean = false;
  noData: string = 'No data available';
  showPaginator: boolean = true;
  startIndex: number;
  displaySearchDialog: boolean = false;
  processDialog: boolean = false;
  globalSearchTerm: string;
  isGlobalSearch: boolean = false;
  pageSize: number = 10;
 totalElements: number;
   processId: string;
  searchCriteria: any = {
    quoteNumber: '',
    opportunityNumber: '',
    rcNumber: ''
  };
  allocationOptions: any[] = [
    { label: 'Y', value: 'Y' },
    { label: 'N', value: 'N' }
  ];
  
  clonedAdjustments: { [s: string]: any } = {};
  selectedRow: any; // Add this line to declare the selectedRow property

  constructor(
    private commonSharedService: CommonSharedService,
    private notificationService: NotificationService,
    private rmanDealDetailsService: RmanDealDetailsService, 
    private http: HttpClient
  ) { }

  ngOnInit(): void {
    this.globalCols = [
      { field: 'dealArrangementId', header: 'Revenue Contract Number', showField: true, display: "table-cell", type: 'number', drag: true },
      { field: 'dealLineNumber', header: 'Line Number', showField: true, display: "table-cell", type: 'number', drag: true },
      { field: 'productName', header: 'Product Name', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'quantity', header: 'Quantity', showField: true, display: "table-cell", type: 'number', drag: true },
      { field: 'unitListPrice', header: 'Unit List Price', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'unitSellingPrice', header: 'Unit Selling Price', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'lineAmount', header: 'Net Price', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'elementType', header: 'Element Type', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'bundleFlag', header: 'Bundle Flag', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'parentLineNumber', header: 'Parent Line Number', showField: true, display: "table-cell", type: 'number', drag: true },
      { field: 'serviceStartDate', header: 'Service Start Date', showField: true, display: "table-cell", type: 'date', drag: true },
      { field: 'serviceEndDate', header: 'Service End Date', showField: true, display: "table-cell", type: 'date', drag: true },
      { field: 'unitCost', header: 'Unit Cost', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'dealLineCost', header: 'Total Cost', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'accRuleRevenue', header: 'Accounting Rule', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'adjUnitCost', header: 'Adjustment Unit Cost', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'adjDealLineCost', header: 'Adjustment Deal Line Cost', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'pobGroup', header: 'POB Group', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'quoteAssetLineId', header: 'Asset ID', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'chargeType', header: 'Charge Type', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'dealLineStatus', header: 'Line Status', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'attribute4', header: 'Revenue Policy Line', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'attribute1', header: 'Line Type', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'attribute3', header: 'Asset Number', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'attribute9', header: 'Attribute9', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'attribute12', header: 'Primary Line Number', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'attribute13', header: 'Item Sequence Number', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'attribute15', header: 'Quote Reference Line Number', showField: true, display: "table-cell", type: 'text', drag: true },
	  { field: 'attribute30', header: 'Bundle Parent Line Number', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'deliveredDate', header: 'Delivered Date', showField: true, display: "table-cell", type: 'date', drag: true },
      { field: 'expectedStartDate', header: 'Forecast Date', showField: true, display: "table-cell", type: 'date', drag: true },
      { field: 'bundlePercent', header: 'POB Split %', showField: true, display: "table-cell", type: 'number', drag: true },
      { field: 'revrecStartDate', header: 'Revrec Start Date', showField: true, display: "table-cell", type: 'date', drag: true },
      { field: 'revrecEndDate', header: 'Revrec End Date', showField: true, display: "table-cell", type: 'date', drag: true },
      { field: 'opportunityNumber', header: 'Opportunity Number', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'oppotunityName', header: 'Opportunity Name', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'quoteNumber', header: 'Quote Number', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'orderNumber', header: 'Order Number', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'orderLineNumber', header: 'Order Line Number', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'bookedDate', header: 'Ready for Activation Date', showField: true, display: "table-cell", type: 'date', drag: true },
      { field: 'proposalDescription', header: 'Proposal Description', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'orderType', header: 'Order Type', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'legalEntity', header: 'Billing Company', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'customerName', header: 'Customer Name', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'customerNumber', header: 'Customer Number', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'salesContractNumber', header: 'Sales Contract Number', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'opportunityType', header: 'Opportunity Type', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'customerTypeName', header: 'Customer Type', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'signatureDate', header: 'Signature Date', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'orderStatus', header: 'Order Status', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'region', header: 'Billing Region', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'revenueTermMonths', header: 'Revenue Term in Months', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'revenueTermDays', header: 'Revenue Term in Days', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'childListPrice', header: 'Bundle Attribution Extended List Price', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'childSellPrice', header: 'Bundle Attribution Net Price', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'revenueTemplateName', header: 'Revenue Policy', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'lineDiscount', header: 'Discount or Premium %', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'extendedListAmount', header: 'Extended List Amount', showField: true, display: "table-cell", type: 'round', drag: true },
      { field: 'argBasisLineNumber', header: 'Revenue Contract Line Number', showField: true, display: "table-cell", type: 'number', drag: true },
      { field: 'allocationFlag', header: 'Allocation Flag', showField: true, display: "table-cell", type: 'text', drag: true },
      { field: 'gmLineAmount', header: 'GM Line Amount', showField: true, display: "table-cell", type: 'number', drag: true },
      { field: 'ssp', header: 'SSP', showField: true, display: "table-cell", type: 'number', drag: true },
      {field: 'sspLow', header: 'SSP Low', showField: true, display: "table-cell", type: 'number', drag: true},
      {field: 'sspHigh', header: 'SSP High', showField: true, display: "table-cell", type: 'number', drag: true},
      {field: 'allocableSellingPrice', header: 'Allocable Selling Price', showField: true, display: "table-cell", type: 'text', drag: true},
      {field: 'dealStatus', header: 'Deal Status', showField: true, display: "table-cell", type: 'text', drag: true},
      {field: 'terminatedFlag', header: 'Terminated Flag', showField: true, display: "table-cell", type: 'text', drag: true},
	  {field: 'postedFlag', header: 'Posted Flag', showField: true, display: "table-cell", type: 'text', drag: true},  
	  {field: 'pobId', header: 'POB ID', showField: true, display: "table-cell", type: 'text', drag: true},
	  {field:  'dealNumber', header: 'Deal Number', showField: true, display: "table-cell", type: 'text', drag: true},
	  {field: 'uomCode'	, header: 'UOM Code', showField: true, display: "table-cell", type: 'text', drag: true},
	  {field: 'dealCurrencyCode', header: 'Deal Currency', showField: true, display: "table-cell", type: 'text', drag: true},
	  {field:'vc', header: 'Variable Consideration', showField: true, display: "table-cell", type: 'text', drag: true},
	//  {field: 'allocableAmount', header: 'Allocable Net Price', showField: true, drag: true, display: "table-cell", type: 'round' },
	  {field: 'allocationAmount', header: 'Allocation Amount', showField: true, drag: true, display: "table-cell", type: 'round' },
	  {field: 'cvInOutAmount', header: 'Carve In/Carve Out Amount ', showField: true, drag: true, display: "table-cell", type: 'round' },
	  {field: 'fmvAmount', header: 'Extended SSP', showField: true, drag: true, display: "table-cell", type: 'round' },
	  {field: 'fxRate', header: 'Fx Rate', showField: true, drag: true, display: "table-cell", type: 'round', footer: 'empty' },
	  {field: 'fxCurrency', header: 'Fx Currency', showField: true, drag: true, display: "table-cell", type: 'number', footer: 'empty' },
      //{ field: 'fxDate', header: 'Fx Date', showField: true, drag: true, display: "table-cell", type: 'date', footer: 'empty' },
	  {field: 'grossMargin', header: 'Gross Margin', drag: true, showField: true, display: 'table-cell', type: 'round'},
	  { field: 'gmLinePercentage', header: 'Gross Margin %', drag: true, showField: true, display: "table-cell", type: 'round' },
     
     
     

    ];
    this.getTableColumns("manageAdjustments", "Adjustments");
  
  }
 

  getTableColumns(pageName: string, tableName: string) {
    this.isSelectAllChecked = true;
    this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
      if (response && response != null && response.userId) {
        this.columns = [];
        let colsList = response.tableColumns.split(",");
        if (colsList.length > 0) {
          colsList.forEach((item, index) => {
            if (item) {
              this.startIndex = this.globalCols.findIndex(col => col.field == item);
              this.onDrop(index);
            }
          });
        }
        this.globalCols.forEach(col => {
          if (colsList.indexOf(col.field) !== -1) {
            this.columns.push(col);
          } else {
            col.showField = false;
          }
        });
        this.isSelectAllChecked = this.columns.length == this.globalCols.length;
        this.showPaginator = this.columns.length !== 0;
        this.userId = response.userId;
      } else {
        this.columns = this.globalCols;
      }
    }).catch(() => {
      this.notificationService.showError('Error occured while getting table columns data');
      this.loading = false;
    });
  }
  
 

  saveColumns() {
    let selectedCols = "";
    this.showAddColumns = !this.showAddColumns;
    const colLength = this.globalCols.length - 1;
    this.globalCols.forEach((col, index) => {
      if (col.showField) {
        selectedCols += col.field;
        if (index < colLength) {
          selectedCols += ",";
        }
      }
    });
    this.loading = true;
    this.commonSharedService.saveOrUpdateTableColumns("manageAdjustments", "Adjustments", selectedCols, this.userId).then((response) => {
      this.columns = this.globalCols.filter(item => item.showField);
      this.userId = response["userId"];
      this.showPaginator = this.columns.length !== 0;
      this.loading = false;
    }).catch(() => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });
  }

  onDragStart(index: number) {
    this.startIndex = index;
  }

  onDrop(dropIndex: number) {
    const general = this.globalCols[this.startIndex]; // get element
    this.globalCols.splice(this.startIndex, 1);       // delete from old position
    this.globalCols.splice(dropIndex, 0, general);    // add to new position
  }

  selectColumns(col: any) {
    let cols = this.globalCols.filter(item => !item.showField);
    if (cols.length > 0) {
      this.isSelectAllChecked = false;
    } else {
      this.isSelectAllChecked = true;
    }
  }

  onSelectAll() {
    this.isSelectAllChecked = !this.isSelectAllChecked;
    this.globalCols.forEach(col => {
      if (this.isSelectAllChecked) {
        col.showField = true;
      } else {
        if (col.drag) {
          col.showField = false;
        }
      }
    });
  }

  onConfiguringColumns(event: any) {
    this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
    this.showAddColumns = false;
  }

  closeConfigureColumns(event: any) {
    this.showAddColumns = true;
    this.globalCols = this.clonedCols;
    let configCol = this.globalCols.filter(item => !item.showField);
    this.isSelectAllChecked = !(configCol.length > 0);
  }

  reset(dt: Table) {
    dt.reset();
  }
  globalSearch() {
    if (!this.globalSearchTerm || this.globalSearchTerm.trim() === '') {
      this.notificationService.showInfo('Please enter a search term');
      return;
    }
    this.isGlobalSearch = true;
    this.searchCriteria = {
      quoteNumber: '',
      opportunityNumber: '',
      rcNumber: this.globalSearchTerm
    };
    
    // Call your existing search method
    this.search();
  }
  resetGlobalSearch() {
    // Clear the search input
    this.globalSearchTerm = '';
    
    // Reset the search flag
    this.isGlobalSearch = false;
    
    // Reset search criteria
    this.searchCriteria = {
      quoteNumber: '',
      opportunityNumber: '', 
      rcNumber: ''
    };
    
    // Show loading indicator
    this.loading = true;
    
    // Fetch the default data or clear the list
    // Option 1: If you want to clear the data
    this.adjustmentsList = [];
    this.loading = false;
  }

  search() {
    this.displaySearchDialog = false;
    this.loading = true;
    this.adjustmentsList = [];
    
    // Determine which search term to use
    let searchTerm = '';
    if (this.searchCriteria.rcNumber) {
      searchTerm = this.searchCriteria.rcNumber;
    } else if (this.searchCriteria.quoteNumber) {
      searchTerm = this.searchCriteria.quoteNumber;
    } else if (this.searchCriteria.opportunityNumber) {
      searchTerm = this.searchCriteria.opportunityNumber;
    }
    
    if (searchTerm) {
      this.rmanDealDetailsService.fetchContractLines(searchTerm).then((data) => {
        this.adjustmentsList = data;
        this.loading = false;
        if (data.length === 0) {
          this.notificationService.showInfo('No results found for the search criteria');
        }
      }).catch((error) => {
        this.notificationService.showError('Error occurred while fetching contract lines: ' + error);
        this.loading = false;
      });
    } else {
      this.notificationService.showInfo('Please enter at least one search term');
      this.loading = false;
    }
  }
  showDialogToSearch() {
    this.searchCriteria = {
      quoteNumber: '',
      opportunityNumber: '',
      rcNumber: ''
    };
    this.displaySearchDialog = true;
  }
  onRowSelect(event: any) {
    this.selectedRow = event.data;
  }
  onRowEditInit(adjustment: any) {
    this.clonedAdjustments[adjustment.dealLineId] = {...adjustment};
}

onRowEditSave(adjustment: any) {
  this.loading = true;
  
  this.rmanDealDetailsService.updateAllocationFlag(adjustment).subscribe({
    next: (response) => {
      this.notificationService.showSuccess('Allocation flag updated successfully');
      delete this.clonedAdjustments[adjustment.dealLineId];
      this.loading = false;
    },
    error: (error) => {
      console.error('Error updating allocation flag:', error);
      this.notificationService.showError('Error updating allocation flag: ' + (error.message || error));
      
      // Restore the original data on error
      const index = this.adjustmentsList.findIndex(item => item.dealLineId === adjustment.dealLineId);
      if (index >= 0 && this.clonedAdjustments[adjustment.dealLineId]) {
        this.adjustmentsList[index] = this.clonedAdjustments[adjustment.dealLineId];
      }
      delete this.clonedAdjustments[adjustment.dealLineId];
      
      this.loading = false;
    }
  });
}

refreshData() {
    this.search(); // Refresh the data by calling the search method again
}

onRowEditCancel(adjustment: any, rowIndex: number) {
    this.adjustmentsList[rowIndex] = this.clonedAdjustments[adjustment.dealLineId];
    delete this.clonedAdjustments[adjustment.dealLineId];
}
  // onRowEditInit(adjustment: any) {
  //   // Store the current editing row ID
  //   this.editingRowId = adjustment.dealLineId;
  //   // Store the original in case user cancels
  //   this.clonedAdjustments[adjustment.dealLineId] = {...adjustment};
  // }
  
  // onRowEditSave(adjustment: any) {
  //   this.loading = true;
    
  //   if (!adjustment.dealLineId) {
  //     this.notificationService.showError('Error: dealLineId is missing.');
  //     this.loading = false;
  //     return;
  //   }
    
  //   this.rmanDealDetailsService.updateAllocationFlag(adjustment).subscribe(
  //     (response) => {
  //       this.notificationService.showSuccess('Allocation flag updated successfully');
  //       delete this.clonedAdjustments[adjustment.dealLineId];
  //       this.editingRowId = null;
  //       this.loading = false;
  //     },
  //     (error) => {
  //       this.notificationService.showError('Error updating allocation flag: ' + (error.message || error));
  //       this.loading = false;
  //     }
  //   );
  // }
  // onRowEditCancel(adjustment: any, rowIndex: number) {
  //   // Restore the original data if edit is canceled
  //   this.adjustmentsList[rowIndex] = this.clonedAdjustments[adjustment.dealLineId];
  //   delete this.clonedAdjustments[adjustment.dealLineId];
  //   this.editingRowId = null; // Clear editing state
    
  //   // Force the table to refresh
  //   this.table.reset();
  // }
  // onRowEditCancel(adjustment: any, rowIndex: number) {
  //   // Restore the original data if edit is canceled
  //   this.adjustmentsList[rowIndex] = this.clonedAdjustments[adjustment.dealLineId];
  //   delete this.clonedAdjustments[adjustment.dealLineId];
  // }
  // onReSubmitDocument() {
  //   // Use a fixed arrangement ID or get it from somewhere in your component
  //  // const arrangementId = "YOUR_ARRANGEMENT_ID_HERE"; // Replace with actual ID or get it from your component
  //  const arrangementId = this.selectedRow.dealArrangementId;
  //   this.loading = true;
    
  //   this.rmanDealDetailsService.reAllocateDeals(arrangementId)
  //     .subscribe(
  //       (res) => {
  //         this.notificationService.showSuccess('Reallocation job submitted successfully');
  //         this.loading = false;
  //       },
  //       (err) => {
  //         this.notificationService.showError('Error occurred during reallocation: ' + (err.error?.message || err.message || 'Unknown error'));
  //         this.loading = false;
  //       }
  //     );
  // }
  onReSubmitDocument() {
    if (!this.selectedRow) {
      this.notificationService.showInfo('Please select a row first');
      return;
    }
    
    if (!this.selectedRow.dealArrangementId) {
      this.notificationService.showInfo('Selected row does not have an arrangement ID');
      return;
    }
    
    this.loading = true;
    
    this.rmanDealDetailsService.reAllocateDeals(this.selectedRow.dealArrangementId)
      .subscribe(
        (res) => {
          this.notificationService.showSuccess('Reallocation job submitted successfully');
          this.loading = false;
        },
        (err) => {
          this.notificationService.showError('Error occurred during reallocation: ' + (err.error?.message || err.message || 'Unknown error'));
          this.loading = false;
        }
      );
  }
}

