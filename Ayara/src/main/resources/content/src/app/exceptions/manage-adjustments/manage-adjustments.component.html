<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h2>Manage Adjustments</h2>
            <div class="pull-right icons-list">
              <!-- <div class="search__container">                    
                    <input pInputText class="textbox" name="searchKey" id="searchKey"
                           [(ngModel)]="globalSearchTerm" 
                           placeholder="Search here..." value=""/>
                    <a class="globalSearchBtn">
                        <em class="fa fa-search" aria-hidden="true" (click)="globalSearch()"></em>
                    </a>
                    <a *ngIf="isGlobalSearched" class="globalSearchBtnx" (click)="resetGlobalSearch()">
                        <em class="fa fa-times" aria-hidden="true"></em>
                    </a>
                </div> -->

                <a (click)="onConfiguringColumns($event)" class="add-column">
                    <em class="fa fa-cog"></em>Columns</a>
              
                <a (click)="showDialogToSearch()" title="Search">
                    <em class="fa fa-search"></em>
                </a>

                <a  (click)="onReSubmitDocument()" title="Submit">
                    <em class="fa fa-hand-pointer-o"></em>
                </a>
                
                
                <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
                    <div class="user-popup">
                        <div class="content overflow">
                            <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()" />
                            <label for="selectall">Select All</label>
                            <a class="close" title="Close" (click)="closeConfigureColumns($event)">&times;</a>
                            <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                                <ng-template let-col let-index="index" pTemplate="item">
                                    <div *ngIf="col.drag">
                                        <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                                            <div class="drag">
                                                <input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" />
                                                <label>{{col.header}}</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="!col.drag">
                                        <div class="ui-helper-clearfix">
                                            <div>
                                                <input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag" />
                                                <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                                            </div>
                                        </div>
                                    </div>
                                </ng-template>
                            </p-listbox>
                        </div>
                        <div class="pull-right">
                            <a class="configColBtn" (click)="saveColumns()">Save</a>
                            <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="x-scroll">
                <!-- <p-table #dt class="ui-datatable arrangementMgrTbl" id="adjustments-dt" [loading]="loading" [value]="adjustmentsList"
                    selectionMode="single" (onRowSelect)="onRowSelect($event)" [lazy]="true" [paginator]="true"
                    [rows]="pageSize" [totalRecords]="totalElements" scrollable="true" [columns]="columns" [resizableColumns]="true" columnResizeMode="expand" editMode="row" dataKey="dealArrangementId">
                    <ng-template pTemplate="colgroup" let-columns>
                        <colgroup>
                            <col>
                            <col *ngFor="let col of columns">
                        </colgroup>
                    </ng-template>
                    <ng-template pTemplate="header" let-columns>
                        <tr>
                            <th></th>
                            <ng-container *ngFor="let col of columns">
                                <th *ngIf="col.type=='text' || col.type=='date'" [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                                <th *ngIf="col.type=='number' || col.type=='round' || col.type=='link'" class="number" [ngStyle]="{'display': col.display}"
                                    title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                            </ng-container>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-rowData let-columns="columns" let-editing="editing" let-ri="rowIndex">
                        <tr [pSelectableRow]="rowData" [pSelectableRowIndex]="ri" [pEditableRow]="rowData">
                            <td style="text-align: center; width: 70px">
                                <div class="edit-buttons">
                                   
                                    <button *ngIf="!editing" pButton type="button" pInitEditableRow 
                                            icon="fa fa-pencil" class="edit-btn p-button-text p-button-rounded p-button-sm"
                                            (click)="onRowEditInit(rowData)"></button>
                                    <button *ngIf="editing" pButton type="button" pSaveEditableRow 
                                            icon="fa fa-check" class="save-btn p-button-text p-button-rounded p-button-sm p-button-success"
                                            (click)="onRowEditSave(rowData)"></button>
                                    <button *ngIf="editing" pButton type="button" pCancelEditableRow 
                                            icon="fa fa-times" class="cancel-btn p-button-text p-button-rounded p-button-sm p-button-danger"
                                            (click)="onRowEditCancel(rowData, ri)"></button>
                                </div>
                            </td>
                            <ng-container *ngFor="let col of columns">
                                <td *ngIf="col.type == 'text' && col.field != 'allocationFlag'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                                    {{rowData[col.field]}}
                                </td>
                                <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
                                    {{rowData[col.field]}}
                                </td>
                                <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
                                    {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                                </td>
                                <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
                                    {{rowData[col.field] | round}}
                                </td>
                                <td *ngIf="col.field == 'allocationFlag'" [pEditableColumn]="rowData" [pEditableColumnField]="col.field" 
                                title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                                <p-cellEditor>
                                    <ng-template pTemplate="output">
                                        {{rowData[col.field]}}
                                    </ng-template>
                                    <ng-template pTemplate="input">
                                        <p-dropdown [options]="allocationOptions" [(ngModel)]="rowData[col.field]" 
                                                  [style]="{'width':'100%'}" appendTo="body">
                                        </p-dropdown>
                                    </ng-template>
                                </p-cellEditor>
                            </td-->
							<p-table #dt class="ui-datatable arrangementMgrTbl" id="adjustments-dt" [loading]="loading" 
							         [value]="adjustmentsList" selectionMode="single" (onRowSelect)="onRowSelect($event)" 
							         [lazy]="true" [paginator]="true" [rows]="pageSize" [totalRecords]="totalElements" 
							         scrollable="true" [columns]="columns" [resizableColumns]="true" 
							         columnResizeMode="expand" editMode="row" dataKey="dealLineId">
    
    <ng-template pTemplate="colgroup" let-columns>
        <colgroup>
            <col style="width: 70px">
            <col *ngFor="let col of columns">
        </colgroup>
    </ng-template>
    
    <ng-template pTemplate="header" let-columns>
        <tr>
            <th style="width: 70px"></th>
            <ng-container *ngFor="let col of columns">
                <th *ngIf="col.type=='text' || col.type=='date'" [ngStyle]="{'display': col.display}" 
                    title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                <th *ngIf="col.type=='number' || col.type=='round' || col.type=='link'" 
                    class="number" [ngStyle]="{'display': col.display}"
                    title="{{col.header}}" pResizableColumn>{{col.header}}</th>
            </ng-container>
        </tr>
    </ng-template>
    
    <ng-template pTemplate="body" let-rowData let-editing="editing" let-ri="rowIndex">
        <tr [pSelectableRow]="rowData" [pSelectableRowIndex]="ri" [pEditableRow]="rowData">
            <td style="width:5px;text-align:center; position: sticky; left: 0; background: inherit">
                <button *ngIf="!editing && !(rowData.dealStatus?.toUpperCase() === 'CLOSED' || rowData.terminatedFlag === 'Y' || rowData.postedFlag === 'Y')"	
                        pButton type="button" pInitEditableRow 
                        icon="fa fa-pencil" class="table-edit-btn"
                        (click)="onRowEditInit(rowData)"></button>
                <button *ngIf="editing" pButton type="button" pSaveEditableRow 
                        icon="fa fa-check" class="table-edit-btn"
                        (click)="onRowEditSave(rowData)"></button>
                <button *ngIf="editing" pButton type="button" pCancelEditableRow 
                        icon="fa fa-times" class="table-edit-btn"
                        (click)="onRowEditCancel(rowData, ri)"></button>
            </td>
            
            <ng-container *ngFor="let col of columns">
                <td *ngIf="col.type == 'text' && col.field != 'allocationFlag'" 
                    title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                    {{rowData[col.field]}}
                </td>
                <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" 
                    class="number" [ngStyle]="{'display': col.display}">
                    {{rowData[col.field]}}
                </td>
                <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" 
                    [ngStyle]="{'display': col.display}">
                    {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                </td>
                <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" 
                    class="number" [ngStyle]="{'display': col.display}">
                    {{rowData[col.field] | round}}
                </td>
                <td *ngIf="col.field == 'allocationFlag'" [pEditableColumn]="rowData" 
                    [pEditableColumnField]="col.field" [ngStyle]="{'display': col.display}">
                    <p-cellEditor>
                        <ng-template pTemplate="input">
                            <p-dropdown [options]="allocationOptions" [(ngModel)]="rowData[col.field]" 
                                      [style]="{'width':'100%'}" appendTo="body"
									  [disabled]="rowData.postedFlag === 'Y'">
                            </p-dropdown>
                        </ng-template>
                        <ng-template pTemplate="output">
                            {{rowData[col.field]}}
                        </ng-template>
                    </p-cellEditor>
                </td>
            </ng-container>
        </tr>
    </ng-template>
	
	<ng-template pTemplate="emptymessage" let-columns>
	  <tr>
	    <!-- Add +1 for the action column -->
	    <td [attr.colspan]="columns.length + 1" style="text-align: center; padding: 1rem;">
	      No records found.
	    </td>
	  </tr>
	</ng-template>
</p-table>
            </div>
        </div>
    </div>
</div>

<p-dialog header="Search" width="auto" [(visible)]="displaySearchDialog" showEffect="fade"
    [modal]="true" [blockScroll]="true" [draggable]="true">
    <form>
        <div class="ui-grid ui-grid-responsive ui-fluid">
            <div class="ui-g-12">
                <div class="ui-g-6">
                    <span class="md-inputfield">
                        <span class="selectSpan">{{columns['quoteNumber']}}</span>
                        <input pInputText name="quoteNumber" class="textbox" placeholder="Quote Number" id="quoteNumber"
                            [ngModelOptions]="{standalone: true}"
                            [(ngModel)]="searchCriteria.quoteNumber" />
                    </span>
                </div>
                <div class="ui-g-6 pull-right">
                    <span class="md-inputfield">
                        <span class="selectSpan">{{columns['opportunityNumber']}}</span>
                        <input pInputText name="opportunityNumber" id="opportunityNumber" class="textbox" placeholder="Opportunity Number"
                            [ngModelOptions]="{standalone: true}"
                            [(ngModel)]="searchCriteria.opportunityNumber" />
                    </span>
                </div>
            </div>
            <div class="ui-g-12">
                <div class="ui-g-6">
                    <span class="md-inputfield">
                        <span class="selectSpan">{{columns['rcNumber']}}</span>
                        <input pInputText name="rcNumber" class="textbox" placeholder="RC Number" id="rcNumber"
                            [ngModelOptions]="{standalone: true}"
                            [(ngModel)]="searchCriteria.rcNumber" />
                    </span>
                </div>
            </div>
        </div>
    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" pButton class="primary-btn" label="Search" (click)="search()"></button>
            <button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>

<p-dialog header="Resubmit" width="auto" [(visible)]="processDialog" showEffect="fade"
    [modal]="true" [blockScroll]="true" [draggable]="true">
    <form>
        <div class="ui-grid ui-grid-responsive ui-fluid">
            <div class="ui-g-12">
                <div class="ui-g-6">
                    <span class="md-inputfield">
                        <span class="selectSpan">Old Process ID</span>
                        <input pInputText name="processId" class="textbox" placeholder="Old Process Id" id="processId"
                            [ngModelOptions]="{standalone: true}"
                            [(ngModel)]="processId" />
                    </span>
                </div>
            </div>
        </div>
    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" pButton class="primary-btn" label="ReSubmit" (click)="onReSubmitDocument()"></button>
            <button type="button" pButton class="secondary-btn" (click)="processDialog=false" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>

<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>