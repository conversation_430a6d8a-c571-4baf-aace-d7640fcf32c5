// import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RmanIntfDealDetailsComponent } from '../rmanIntfDealDetails/rmanIntfDealDetails.component';
import { RmanInvoiceLinesInterfaceComponent } from '../rmanInvoiceLinesInterface/rmanInvoiceLinesInterface.component';
import { RmanOrdersInterfaceComponent } from '../rmanOrdersInterface/rmanOrdersInterface.component';
import { RmanCustomersInterfaceComponent } from '../rmanCustomersInterface/rmanCustomersInterface.component';
import { RmanConversionRatesIntComponent } from '../rmanConversionRatesInt/rmanConversionRatesInt.component';
import { RmanFiscalPeriodsInterfaceComponent } from '../rmanFiscalPeriodsInterface/rmanFiscalPeriodsInterface.component';
import { RmanProductsInterfaceComponent } from '../rmanProductsInterface/rmanProductsInterface.component';
import { RmanUsageSummaryInterfaceComponent } from '../rmanUsageSummaryInterface/rmanUsageSummaryInterface.component';
import { AyaraPobMappingsInterfaceComponent } from '../ayaraPobMappingsInterface/ayaraPobMappingsInterface.component';
import { NewSharedModule } from '../shared/shared.module';
import { ExceptionJobMonitorComponent } from './exception-job-monitor/exception-job-monitor.component';
import { ExceptionsComponent } from './exceptions/exceptions.component';
import { AllExceptionsComponent } from './allExceptions/allExceptions.component';
import { ManageAdjustmentsComponent } from  './manage-adjustments/manage-adjustments.component';
import { ConfirmationService } from 'primeng/api';
import { CommonSharedService } from '../shared/common.service';

const routes: Routes =[
    {path:':exceptionType',component:ExceptionsComponent},
   
    
];
@NgModule({
    imports:[NewSharedModule,RouterModule.forChild(routes)],
    declarations:[RmanInvoiceLinesInterfaceComponent,RmanOrdersInterfaceComponent,RmanIntfDealDetailsComponent, ExceptionsComponent, ExceptionJobMonitorComponent, AllExceptionsComponent,RmanCustomersInterfaceComponent, RmanConversionRatesIntComponent,RmanFiscalPeriodsInterfaceComponent,RmanProductsInterfaceComponent,RmanUsageSummaryInterfaceComponent,AyaraPobMappingsInterfaceComponent,ManageAdjustmentsComponent],
    providers: [
        ConfirmationService,
        CommonSharedService
    ]
  })
  
  export class ExceptionsModule{
      
  }