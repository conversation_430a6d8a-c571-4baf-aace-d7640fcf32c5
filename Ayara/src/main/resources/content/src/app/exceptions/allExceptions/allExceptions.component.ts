import { Component, OnInit } from '@angular/core';
import { ConfirmationService } from 'primeng/api';
import { Table } from 'primeng/table';
import { NotificationService } from 'src/app/shared/notifications.service';
import { AllExceptionsLabels } from './allExceptionLabels';
import { AllExceptionsService } from './allExceptions.service';
import { AllExceptions } from './exceptions';
import { CommonSharedService } from 'src/app/shared/common.service';
import { ViewChild } from '@angular/core';
import { ChangeDetectorRef } from '@angular/core';

declare var $: any;
declare var require: any;
const appSettings = require('../../appsettings');

@Component({
  selector: 'all-exceptions',
  templateUrl: './allExceptions.component.html',
  providers:[AllExceptionsService, ConfirmationService]
})
export class AllExceptionsComponent implements OnInit {

    displayDialog: boolean;

    displaySearchDialog: boolean;
    selectedLines: any[] = [];
    exceptionsList: any[] = [];
    allExceptions: AllExceptions = new AllExceptionsImpl();

    allExceptionsSearch: AllExceptions = new AllExceptionsImpl();

    isSearched: number = 0;
    selectedRmanOrdersInterface: AllExceptions;

    newRmanOrdersInterface: boolean;

    allExceptionsList: AllExceptions[];
    columns: ILabels;
    paginationOptions:any;
    pages: {};
    datasource: any[];
    pageSize: number;
    totalElements: number;
    hideColumnMenu: boolean = true;
    showFilter: boolean = false;
    cellEditedIntfDealDetails: any[] = [];
    collapsed: boolean = true;
    loading: boolean;
    noData = appSettings.noData;


  	sortField: string;
  	sortOrder: string;

    allExceptionsTypes: any[] = [];
    exceptionsType:any = 'QUOTE';

    quoteExportsColumns:any[] = [];
    bookExportsColumns:any[] = [];
    shipExportsColumns:any[] = [];
    invExportsColumns:any[] = [];
    vcExportsColumns:any[] = [];
    modifiedExceptions: AllExceptions[] = [];
    deletedExceptions: AllExceptions[] =[];

	processId:any;
    processDialog: boolean = false;
    valueResults: string[];
    swicthClickRecognizer: boolean = false;
    allExceptionCurrentOpenPeriodSwitch: boolean = true;
    isSearch: boolean = false;
    showAddColumns: boolean = true;
    clonedCols: any;
    columnsList: any [] = [];
    startIndex: number;
    isSelectAllChecked = true;
    userId: number;
    showPaginator: boolean = true;
    columnsToBeDisplayed: any[];
    exceptions: AllExceptions = new AllExceptionsImpl();
    currentOpenPeriodEndDate: any;
  	exportCols: any[];
  	 showTable: boolean = true;
    @ViewChild('dt') dt!: Table;


    clonedExceptions: { [id: number]: AllExceptions; } = {};

    constructor(private allExceptionsService: AllExceptionsService, private commonSharedService: CommonSharedService,
        private notificationService: NotificationService, private confirmationService: ConfirmationService, private cdRef: ChangeDetectorRef) {

        this.allExceptionsTypes.push({ label: 'Quotes', value: 'QUOTE' },
                { label: 'Bookings', value: 'BOOK' },
                { label: 'Shipments', value: 'SHIP' },
                { label: 'Invoices', value: 'INV' },
                { label: 'VC Release', value: 'CONT_RELEASE' });
       this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10', 'sortField': 'documentNumber', 'sortOrder': 'desc' };
    }

    onChange(dt?:Table){
		this.showAddColumns = true;
		this.forceTableReset();
		this.reloadScreen();
		this.allExceptionsList = [];
        this.totalElements = 0;
        if (dt) {
          dt.first = 0;
        }
         this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10', 'sortField': 'documentNumber', 'sortOrder': 'desc' };
        if(this.isSearch){
          this.isSearch = false;
        } else {
          this.allExceptions.documentSource = '';
          this.allExceptions.documentLineId = '';
          this.allExceptions.processId = '';
          this.allExceptions.documentNumber = '';
        }
        if(this.exceptionsType === 'QUOTE' || this.exceptionsType === 'INV'){
            this.allExceptions.documentCategory = this.exceptionsType;
            this.allExceptions.eventType = '';
        }
        else if(this.exceptionsType === 'BOOK' || this.exceptionsType === 'SHIP' || this.exceptionsType === 'CONT_RELEASE'){
            this.allExceptions.eventType = this.exceptionsType;
            this.allExceptions.documentCategory = '';
        }

        this.selectedLines = [];
        setTimeout(() => {
          this.getAllExceptions();
      }, 100);
    }

    ngOnInit() {
        let allExceptionsLabels = new AllExceptionsLabels();
        this.columns = allExceptionsLabels.fieldLabels;
        this.selectedLines = [];

        this.quoteExportsColumns.push("documentCategory", "documentSource","dealNumber",
        "projectNumber", "opportunityNumber", "opportunityName","documentNumber",
        "documentDescription", "documentHeaderId", "entityName", "entityId", "customerName",
        "customerNumber", "documentDate", "currencyCode", "SalesRep", "salesTheater", "region",
        "salesTerritory", "documentLineNumber", "documentLineId", "productName", "quantity", "uom",
        "unitListPrice", "unitSellingPrice", "parentLineNumber", "serviceStartDate", "serviceEndDate",
        "unitCost", "contingnecyCode", "contingnecyPercentage", "revenueTemplateName",
        "refDocumentNumber", "exceptionMessage");

        this.bookExportsColumns.push("documentCategory", "documentSource", "eventType",
        "dealNumber", "projectNumber", "opportunityNumber", "customerPo", "documentNumber",
        "documentHeaderId", "documentType", "entityName", "entityId", "customerName", "customerNumber",
        "documentDate", "documentStatus", "currencyCode", "SalesRep", "salesTheater", "region",
        "salesTerritory", "documentLineId", "documentLineNumber", "productName", "inventoryItemId",
        "quantity", "uom", "unitListPrice", "unitSellingPrice", "serviceStartDate", "serviceEndDate",
        "unitCost", "documentLineStatus", "contingencyCode","contApplyType", "contingnecyPercentage", "revenueTemplateName", "refLineId",
        "refLineNumber", "refDocumentNumber", "parentLineId", "exceptionMessage");

        this.shipExportsColumns.push("documentCategory", "documentSource", "eventType",
        "documentLineId", "quantity", "documentLineStatus","documentDate", "contingencyCode","contApplyType", "contingnecyPercentage",
        "exceptionMessage");

        this.invExportsColumns.push("documentCategory", "documentSource", "documentType", "documentHeaderId",
        "documentNumber", "documentDate", "documentLineId", "documentLineNumber", "quantity",
        "unitSellingPrice", "currencyCode", "amount", "refDocumentNumber", "refLineNumber", "refLineId",
        "exceptionMessage");

        this.vcExportsColumns.push("documentCategory", "documentSource", "eventType", "documentLineId","contingencyCode",
        "contReleaseType","contReleaseRevenue", "exceptionMessage");

		 this.reloadScreen();

    }

	  reloadScreen(){
	      let allExceptionsLabels = new AllExceptionsLabels();
	      const labelData = allExceptionsLabels.getLabels(this.exceptionsType);
	      if(this.exceptionsType == 'QUOTE'){
	        this.columnsList = allExceptionsLabels.mergeFields("quote");
	      } else if(this.exceptionsType == 'BOOK'){
	        this.columnsList = allExceptionsLabels.mergeFields("book");
	      } else if(this.exceptionsType == 'SHIP'){
	        this.columnsList = allExceptionsLabels.mergeFields("ship");
	      } else if(this.exceptionsType == 'INV'){
	        this.columnsList = allExceptionsLabels.mergeFields("inv");
	      } else if(this.exceptionsType == 'CONT_RELEASE'){
	        this.columnsList = allExceptionsLabels.mergeFields("cont_release");
	      }
	      this.columnsToBeDisplayed = [];
	      this.getTableColumns(labelData.pageName,labelData.tableName);
	  }

     onAllExceptionsCurrentOpenPeriodSwitch(){
      this.swicthClickRecognizer = true;
      this.allExceptions.documentSource = '';
      this.allExceptions.documentLineId = '';
      this.allExceptions.processId = '';
      this.allExceptions.documentNumber = '';
      this.paginationOptions = { pageNumber: 0, pageSize: this.pageSize,sortField:this.sortField, sortOrder:this.sortOrder };
      this.allExceptionsList = [];
      this.totalElements = 0;
      if (this.dt) {
        this.dt.first = 0;
      }
      this.getAllTypeExceptions({ first: 0, rows: this.pageSize, sortField: this.sortField, sortOrder: this.sortOrder });
    }

    getAllExceptions() {
		this.sortField = this.sortField ? this.sortField : 'documentNumber';
        this.sortOrder = this.sortOrder ? this.sortOrder : 'desc';
		this.paginationOptions = { pageNumber: 0, pageSize: this.pageSize, sortField: this.sortField, sortOrder: this.sortOrder };
        if(this.exceptionsType === undefined || this.exceptionsType === 'QUOTE'){
            this.allExceptions.documentCategory = 'QUOTE';
        }
        this.loading = true;
        this.allExceptionsService.getAllExceptions(this.paginationOptions, this.allExceptions).then((rmanOrdersInterfaceList: any) => {
            this.datasource = rmanOrdersInterfaceList.content;
            this.allExceptionsList = rmanOrdersInterfaceList.content;
            this.totalElements = rmanOrdersInterfaceList.totalElements;
            this.pageSize = rmanOrdersInterfaceList.size;
            this.displaySearchDialog = false;
            this.loading = false;
        });
    }

    getAllTypeExceptions(event: any) {
        if(this.exceptionsType === undefined || this.exceptionsType === 'QUOTE'){
            this.allExceptions.documentCategory = 'QUOTE';
        }
        let first: number = event.first;
        let rows: number = event.rows;
        let pageNumber: number = first / rows;
        let sortField = event.sortField ? event.sortField : 'documentNumber';
        let sortOrder = this.sortOrder ?? (event?.sortOrder === 1 ? 'desc' : event?.sortOrder ?? 'desc');

      	this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': sortField, 'sortOrder': sortOrder };
        this.loading = true;
        this.allExceptionsService.getAllExceptions(this.paginationOptions, this.allExceptions).then((rmanOrdersInterfaceList: any) => {
            this.allExceptionsList = rmanOrdersInterfaceList.content;
            this.totalElements = rmanOrdersInterfaceList.totalElements;
            this.pageSize = rmanOrdersInterfaceList.size;
            this.loading = false;
        });

    }

	onsorting(key: any, sortType: any,dt:Table) {
	    this.paginationOptions.sortField = key;
	    this.paginationOptions.sortOrder = sortType;
	    this.sortField = key;
	    this.sortOrder = sortType;
	    if(dt){
        	dt.first = 0;
      	}
	    this.getAllExceptions();
  	}

    deleteSelected(dt: Table) {

        this.exceptionsList = [];

        for (let line of this.selectedLines) {
            this.exceptionsList.push(line.trxId);
            this.deletedExceptions.push(line);
        }
        if (this.exceptionsList.length > 0) {

        this.confirmationService.confirm({
            message: 'Are you sure you want to delete?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
              this.loading = true;
          console.log(this.deletedExceptions);
              this.allExceptionsService.deleteSelectedLines(this.exceptionsList).then((response: any) => {
                for (let exception of this.deletedExceptions) {
                    this.allExceptionsService.updateExceptionLogs(exception,'deleted').subscribe();
                }
                this.notificationService.showSuccess('Selected records are deleted Successfully  ');
                this.deletedExceptions=[];
            }).then(() => {
                this.reset(dt);
            }).catch((err: any) => {
                this.loading = false;
                this.notificationService.showError('Getting error While delete records');
            });
            },
            reject: () => {
              this.notificationService.showWarning('You have rejected');
            }
          });

        } else {
            this.notificationService.showInfo('Select at least one record to delete');
        }
    }

    exportExcel() {
      $.blockUI();
      this.exportCols = [];
      for (let index = 0; index < this.columnsToBeDisplayed.length; index++) {
        if (this.columnsToBeDisplayed[index].showField) {
          this.exportCols.push(this.columnsToBeDisplayed[index].fieldName);
        }
      }
      let serviceUrl = this.allExceptionsService.getServiceUrl(this.paginationOptions, this.allExceptions,1,this.exportCols,this.exceptionsType);
      $.unblockUI();
      window.location.href = serviceUrl;
    }

    showDialogToAdd() {
        this.newRmanOrdersInterface = true;
        this.allExceptions = new AllExceptionsImpl();
        this.displayDialog = true;
    }
    reset(dt: Table) {

        this.paginationOptions = {};
        this.allExceptions = new AllExceptionsImpl();
        this.onChange(dt);
        this.selectedLines = [];
        dt.reset();
    }

    onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }

    editRow(allExceptions: any) {
        this.newRmanOrdersInterface = false;
        this.allExceptions = this.cloneRmanOrdersInterface(allExceptions);
        this.displayDialog = true;

    }
    findSelectedRmanOrdersInterfaceIndex(): number {
        return this.allExceptionsList.indexOf(this.selectedRmanOrdersInterface);
    }

    onRowSelect(event: any) {

    }
    cloneRmanOrdersInterface(c: AllExceptions): AllExceptions {
        let allExceptions = new AllExceptionsImpl();
        for (let prop in c) {
            allExceptions[prop] = c[prop];
        }
        return allExceptions;
    }

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {
        this.paginationOptions = {};
        this.allExceptionsSearch = new AllExceptionsImpl();
        if (this.isSearched == 0) {
            this.allExceptionsSearch = new AllExceptionsImpl();
        }
        this.displaySearchDialog = true;

    }

    search(dt?:Table) {
		this.isSearch = true;
        this.isSearched = 1;
        this.displaySearchDialog = false;
        this.allExceptions = this.allExceptionsSearch;
        this.onChange(dt);
    }
    cancelSearch() {
        this.displaySearchDialog = false;
        this.allExceptionsSearch = new AllExceptionsImpl();
    }

    onRowEditInit(exception: AllExceptions) {
        this.clonedExceptions[exception.trxId] = {...exception};
    }

    onRowEditSave(exception: AllExceptions) {
        delete this.clonedExceptions[exception.trxId];
        exception.exceptionMessage='';
        /*
        if(exception.documentDate!=null){
            let y = new Date(exception.documentDate).getFullYear();
            let m = new Date(exception.documentDate).getMonth()+1;
            let d = new Date(exception.documentDate).getDate();
            let dDate = y+'-'+m+'-'+d;
            exception.documentDate = dDate;
        }
        if(exception.serviceStartDate!=null){
            let y = new Date(exception.serviceStartDate).getFullYear();
            let m = new Date(exception.serviceStartDate).getMonth()+1;
            let d = new Date(exception.serviceStartDate).getDate();
            let sDate = y+'-'+m+'-'+d;
            exception.serviceStartDate = sDate;
        }
        if(exception.serviceEndDate!=null){
            let y = new Date(exception.serviceEndDate).getFullYear();
            let m = new Date(exception.serviceEndDate).getMonth()+1;
            let d = new Date(exception.serviceEndDate).getDate();
            let eDate = y+'-'+m+'-'+d;
            exception.serviceEndDate = eDate;
        }
        */
        this.allExceptionsService.updateExceptionLogs(exception,'modified').subscribe();
        this.allExceptionsService.updateException(exception).subscribe();
        if(!this.modifiedExceptions.includes(exception)){
            this.modifiedExceptions.push(exception);
        }
    }

    onRowEditCancel(exception: AllExceptions, rowIndex: number) {
        this.allExceptionsList[rowIndex] = this.clonedExceptions[exception.trxId];
    }

	submitdocument(){
        this.processDialog= true;
    }

    onReSubmitDocument(){
        this.loading = true;
        for (let exception of this.modifiedExceptions) {
            this.allExceptionsService.updateExceptionLogs(exception,'reprocessed').subscribe();
        }
        this.modifiedExceptions=[];
        this.allExceptionsService.submitToReProcessDocuments(this.exceptionsType,this.allExceptions.documentNumber)
        .subscribe(
			(res)=>{
                this.notificationService.showSuccess('Job Submitted Successfully and Job ID is '+res);
                this.loading=true;
                this.allExceptions.documentNumber='';
                this.getAllExceptions();
                this.loading=false;
            },
			(err)=>{
				this.notificationService.showError(err.error.message);
				this.loading = false;
			}
		);
        this.processDialog=false;
    }

    searchValues(documentType:any,parameterName:any,event: any) {
    this.allExceptionsService.getParameterValues(documentType,parameterName, event.query)
          .subscribe(results => this.valueResults = results);
  }

	onDragStart(index: number) {
		this.startIndex = index;
	}

  	onDrop(dropIndex: number) {
	    const general = this.columnsList[this.startIndex];
	    this.columnsList.splice(this.startIndex, 1);
	    this.columnsList.splice(dropIndex, 0, general);
  	}

  	selectColumns(col: any) {
	    let cols = this.columnsList.filter(item => !item.showField);
	    if (cols.length > 0) {
	      this.isSelectAllChecked = false;
	    } else {
	      this.isSelectAllChecked = true;
	    }
  	}

  	onSelectAll() {
	    this.isSelectAllChecked = !this.isSelectAllChecked;
	    this.columnsList.forEach(col => {
	      if (this.isSelectAllChecked) {
	        col.showField = true;
	      } else {
	        if (col.drag) {
	          col.showField = false;
	        }
	      }
	    });
  	}

  	saveColumns() {
	    let selectedCols = "";
	    let allExceptionsLabels = new AllExceptionsLabels();
	    const labelData = allExceptionsLabels.getLabels(this.exceptionsType);
	    this.showAddColumns = !this.showAddColumns;
	    const colLength = this.columnsList.length - 1;
	    this.columnsList.forEach((col, index) => {
	      if (col.showField) {
	        selectedCols += col.fieldName;
	        if (index < colLength) {
	          selectedCols += ",";
	        }
	      }
	    });
	    this.loading = true;
	    this.commonSharedService.saveOrUpdateTableColumns(labelData.pageName, labelData.tableName, selectedCols, this.userId).then((response) => {
	      this.columnsToBeDisplayed = this.columnsList.filter(item => item.showField);
	      console.log("columns",this.columnsToBeDisplayed)
	      this.userId = response["userId"];
	      this.showPaginator = this.columnsToBeDisplayed.length !== 0;
	      this.loading = false;
	    }).catch(() => {
	      this.notificationService.showError('Error occured while getting data');
	      this.loading = false;
	    });
  	}

  	getTableColumns(pageName:string,tableName:string) {
	    this.isSelectAllChecked = true;
	    this.commonSharedService.getConfiguredColDetails(pageName,tableName).then((response) => {
	      if (response && response != null && response.userId) {
	        this.columnsToBeDisplayed = [];
	        let colsList = response.tableColumns.split(",");
	        if (colsList.length > 0) {
	          colsList.forEach((item, index) => {
	            if (item) {
	              this.startIndex = this.columnsList.findIndex(col => col.fieldName == item);
	              this.onDrop(index);
	            }
	          });
	        }
	        this.columnsList.forEach(col => {
	          if (response.tableColumns.indexOf(col.fieldName) !== -1) {
	            this.columnsToBeDisplayed.push(col);
	          } else {
	            col.showField = false;
	          }
	        });
	        if (this.columnsToBeDisplayed.length != this.columnsList.length) this.isSelectAllChecked = false;
	        this.showPaginator = this.columnsToBeDisplayed.length !== 0;
	        this.userId = response.userId;
	      } else {
	        this.columnsToBeDisplayed = this.columnsList;
	      }
	    }).catch(() => {
	      this.notificationService.showError('Error occured while getting table columns data');
	      this.loading = false;
	    });
  	}

  	onConfiguringColumns(event: any) {
	    this.clonedCols = JSON.parse(JSON.stringify(this.columnsList));
	    this.showAddColumns = false;
  	}

  	closeConfigureColumns(event: any) {
	    this.showAddColumns = true;
	    this.columnsList = this.clonedCols;
	    let configCol = this.columnsList.filter(item => !item.showField);
	    this.isSelectAllChecked = !(configCol.length > 0);
  	}

  	forceTableReset() {
	    this.showTable = false;
	    setTimeout(() => {
	        this.showTable = true;
	        this.cdRef.detectChanges();
	    	}, 50);
	}


}


class AllExceptionsImpl implements AllExceptions {
    constructor(public documentCategory?,public documentSource?,public dealNumber?,public projectNumber?,public opportunityNumber?,public opportunityName?,public documentNumber?,public documentDescription?,public documentHeaderId?,public entityName?,public entityId?,public customerName?,public customerNumber?,public documentDate?,public currencyCode?,public salesRep?,public salesTheater?,public region?,public salesTerritory?,public documentLineNumber?,public documentLineId?,public productName?,public quantity?,public uom?,public unitListPrice?,public unitSellingPrice?,public parentLineNumber?,public serviceStartDate?,public serviceEndDate?,public unitCost?,public contingnecyCode?,public contingnecyPercentage?,public revenueTemplateName?,public refDocumentNumber?,public processFlag?,public eventType?, public trxId?,
        public customerPo?,
        public documentType?,
        public inventoryItemId?,
        public refLineId?,
        public refLineNumber?,
        public amount?,
        public exceptionMessage?,
        public documentLineStatus?,
        public processId?,
        public documentStatus?,
        public contApplyType?,
        public contReleaseType?,
        public contReleaseRevenue?) { }
}

interface ILabels {
    [index: string]: string;
}


interface IField {
    fieldName: string;
    label: string;
}