export interface AllExceptions { 
    trxId?:any;
    documentCategory?:any;
    documentSource?:any;
    dealNumber?:any;
    projectNumber?:any;
    opportunityNumber?:any;
    opportunityName?:any;
    documentNumber?:any;
    documentDescription?:any;
    documentHeaderId?:any;
    entityName?:any;
    entityId?:any;
    customerName?:any;
    customerNumber?:any;
    documentDate?:any;
    currencyCode?:any;
    salesRep?:any;
    salesTheater?:any;
    region?:any;
    salesTerritory?:any;
    documentLineNumber?:any;
    documentLineId?:any;
    productName?:any;
    quantity?:any;
    uom?:any;
    unitListPrice?:any;
    unitSellingPrice?:any;
    parentLineNumber?:any;
    serviceStartDate?:any;
    serviceEndDate?:any;
    unitCost?:any;
    contingnecyCode?:any;
    contingnecyPercentage?:any;
    revenueTemplateName?:any;
    refDocumentNumber?:any;
    processFlag?:any;
    eventType?:any;
    customerPo?:any;
    documentType?:any;
    inventoryItemId?:any;
    refLineId?:any;
    refLineNumber?:any;
    amount?:any;
    exceptionMessage?:any;
    documentLineStatus?:any;
    processId?:any;
    documentStatus?:any;
    contApplyType?:any;
    contReleaseType?:any;
    contReleaseRevenue?:any;
}