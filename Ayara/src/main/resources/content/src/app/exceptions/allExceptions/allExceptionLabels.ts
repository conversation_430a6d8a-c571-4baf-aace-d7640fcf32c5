interface ILabels {
    [index: string]: string;
}

interface IField {
    fieldName: string;
    label: string;
    showField: boolean;
    drag: boolean;
    editable: boolean;
    sortable: boolean;
    type: string;
    showForExceptions: string[];
  }

export class AllExceptionsLabels {

    fieldLabels: ILabels;
    private fields: IField[];

    constructor() {

        this.fieldLabels = {};
        this.fieldLabels["documentCategory"] = "Document Category";
        this.fieldLabels["documentSource"] = "Document Source";
        this.fieldLabels["dealNumber"] = "Deal Number";
        this.fieldLabels["projectNumber"] = "Project Number";
        this.fieldLabels["opportunityNumber"] = "Oppurtunity Number";
        this.fieldLabels["opportunityName"] = "Oppurtunity Name";
        this.fieldLabels["documentNumber"] = "Document Number";
        this.fieldLabels["documentDescription"] = "Document Description";
        this.fieldLabels["documentHeaderId"] = "Document Header Id";
        this.fieldLabels["entityName"] = "Entity Name";
        this.fieldLabels["entityId"] = "Entity Id";
        this.fieldLabels["customerName"] = "Customer Name";
        this.fieldLabels["customerNumber"] = "Customer Number";
        this.fieldLabels["documentDate"] = "Document Date";
        this.fieldLabels["currencyCode"] = "Currency Code";
        this.fieldLabels["salesRep"] = "Sales Rep";
        this.fieldLabels["salesTheater"] = "Sales Theater";
        this.fieldLabels["region"] = "Region";
        this.fieldLabels["salesTerritory"] = "Sales Territory";
        this.fieldLabels["documentLineNumber"] = "Document Line Number";
        this.fieldLabels["documentLineId"] = "Document Line Id";
        this.fieldLabels["productName"] = "Product Name";
        this.fieldLabels["quantity"] = "Quantity";
        this.fieldLabels["uom"] = "UOM";
        this.fieldLabels["unitListPrice"] = "Unit List Price";
        this.fieldLabels["unitSellingPrice"] = "Unit Selling Price";
        this.fieldLabels["serviceStartDate"] = "Service Start Date";
        this.fieldLabels["serviceEndDate"] = "Service End Date";
        this.fieldLabels["unitCost"] = "Unit Cost";
        this.fieldLabels["contingnecyCode"] = "Contingency Code";
        this.fieldLabels["contingnecyPercentage"] = "Contingency Percentage";
        this.fieldLabels["revenueTemplateName"] = "Revenue Template Name";
        this.fieldLabels["refDocumentNumber"] = "Ref Document Number";
        this.fieldLabels["eventType"] = "Event Type";
        this.fieldLabels["customerPo"] = "Customer PO";
        this.fieldLabels["documentType"] = "Document Type";
        this.fieldLabels["inventoryItemId"] = "Inventory Item Id";
        this.fieldLabels["refLineId"] = "Ref Line Id";
        this.fieldLabels["refLineNumber"] = "Ref Line Number";
        this.fieldLabels["amount"] = "Amount";
        this.fieldLabels["exceptionMessage"] = "Exception Message";
        this.fieldLabels["documentLineStatus"] = "Document Line Status";
        this.fieldLabels["processId"] = "Process Id";
        this.fieldLabels["parentLineNumber"] = "Parent Line Number";
        this.fieldLabels["documentStatus"] = "Document Status";
        this.fieldLabels["contApplyType"] = "Cont Apply Type";
        this.fieldLabels["contReleaseType"] = "Cont Release Type";
        this.fieldLabels["contReleaseRevenue"] = "Cont Release Revenue";

        this.fields = [
            { fieldName: "documentCategory", label: "Document Category", showField: true, drag: true,editable: false,sortable:false, type:'text',showForExceptions: ["quote", "book", "ship", "inv", "cont_release"] },
            { fieldName: "documentSource", label: "Document Source", showField: true, drag: true,editable: false,sortable:false, type:'text', showForExceptions: ["quote", "book", "ship", "inv", "cont_release"] },
            { fieldName: "eventType", label: "Event Type", showField: true, drag: true,editable: false,sortable:false,  type:'text',showForExceptions: ["book","ship","cont_release"] },
            { fieldName: "dealNumber", label: "Deal Number", showField: true, drag: true,editable: false,sortable:false,  type:'text',showForExceptions: ["quote","book"] },
            { fieldName: "projectNumber", label: "Project Number", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote","book"] },
            { fieldName: "opportunityNumber", label: "Opportunity Number", showField: true, drag: true,editable: false, sortable:false, type:'text',showForExceptions: ["quote","book"] },
            { fieldName: "customerPo", label: "Customer PO", showField: true, drag: true,editable: false,sortable:false, type:'text', showForExceptions: ["book"] },
            { fieldName: "opportunityName", label: "Opportunity Name", showField: true, drag: true,editable: false, sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "documentNumber", label: "Document Number", showField: true, drag: true,editable: false,sortable:true, type:'text', showForExceptions: ["quote", "book","ship","inv"] },
            { fieldName: "documentDescription", label: "Document Description", showField: true, drag: true,editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "documentHeaderId", label: "Document Header Id", showField: true, drag: true,editable: false,sortable:false, type:'text', showForExceptions: ["quote", "book","inv"] },
            { fieldName: "documentType", label: "Document Type", showField: true, drag: true, editable: true,sortable:false,  type:'text',showForExceptions: ["inv","book","ship"] },
            { fieldName: "entityName", label: "Entity Name", showField: true, drag: true, editable: true,sortable:false, type:'text', showForExceptions: ["quote", "book"] },
            { fieldName: "entityId", label: "Entity Id", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote","book"] },
            { fieldName: "customerName", label: "Customer Name", showField: true, drag: true, editable: true, sortable:false, type:'text',showForExceptions: ["quote", "book"] },
            { fieldName: "customerNumber", label: "Customer Number", showField: true, drag: true,editable: true,sortable:false,  type:'text',showForExceptions: ["quote", "book"] },
            { fieldName: "documentDate", label: "Document Date", showField: true, drag: true, editable: true,sortable:false,  type:'date',showForExceptions: ["quote", "book","ship","inv"] },
            { fieldName: "documentStatus", label: "Document Status", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["book"] },
            { fieldName: "currencyCode", label: "Currency Code", showField: true, drag: true, editable: true,sortable:false,  type:'text',showForExceptions: ["quote", "book","inv"] },
            { fieldName: "salesRep", label: "Sales Rep", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote", "book"] },
            { fieldName: "salesTheater", label: "Sales Theater", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote", "book"] },
            { fieldName: "region", label: "Region", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote", "book"] },
            { fieldName: "salesTerritory", label: "Sales Territory", showField: true, drag: true,editable: false,sortable:false,  type:'text',showForExceptions: ["quote", "book"] },
            { fieldName: "documentLineNumber", label: "Document Line Number", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote", "book","inv"] },
            { fieldName: "documentLineId", label: "Document Line Id", showField: true, drag: true,editable: false,sortable:false,  type:'text',showForExceptions: ["quote", "book", "ship", "inv", "cont_release"] },
            { fieldName: "productName", label: "Product Name", showField: true, drag: true, editable: true,sortable:false,  type:'text',showForExceptions: ["quote", "book"] },
            { fieldName: "quantity", label: "Quantity", showField: true, drag: true, editable: true,sortable:false,  type:'text',showForExceptions: ["quote", "book","ship","inv"] },
            { fieldName: "documentLineStatus", label: "Document Line Status", showField: true, drag: true, editable: true, sortable:false, type:'text',showForExceptions: ["book","ship"] },
            { fieldName: "uom", label: "UOM", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote", "book"] },
            { fieldName: "unitListPrice", label: "Unit List Price", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote", "book"] },
            { fieldName: "unitSellingPrice", label: "Unit Selling Price", showField: true, drag: true, editable: true, sortable:false, type:'text',showForExceptions: ["quote", "book","inv"] },
            { fieldName: "parentLineNumber", label: "Parent Line Number", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote","book"] },
            { fieldName: "serviceStartDate", label: "Service Start Date", showField: true, drag: true, editable: true, sortable:false, type:'date',showForExceptions: ["quote", "book"] },
            { fieldName: "serviceEndDate", label: "Service End Date", showField: true, drag: true, editable: true,sortable:false,  type:'date',showForExceptions: ["quote", "book"] },
            { fieldName: "unitCost", label: "Unit Cost", showField: true, drag: true,editable: false, sortable:false, type:'round',showForExceptions: ["quote", "book"] },
            { fieldName: "contingnecyCode", label: "Contingency Code", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote", "book","ship","cont_release"] },
            { fieldName: "contReleaseType", label: "Contingency Release Type", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["cont_release"] },
            { fieldName: "contReleaseRevenue", label: "Contingency Release Revenue", showField: true, drag: true,editable: false, sortable:false, type:'text',showForExceptions: ["cont_release"] },
            { fieldName: "contApplyType", label: "Cont Apply Type", showField: true, drag: true,editable: false, sortable:false, type:'text',showForExceptions: ["book","ship"] },
            { fieldName: "contingnecyPercentage", label: "Contingency Percentage", showField: true, drag: true,editable: false, sortable:false, type:'text',showForExceptions: ["quote", "book","ship"] },
            { fieldName: "revenueTemplateName", label: "Revenue Template Name", showField: true, drag: true, editable: true, sortable:false, type:'text',showForExceptions: ["quote", "book"] },
            { fieldName: "refDocumentNumber", label: "Reference Document Number", showField: true, drag: true, editable: true, sortable:false, type:'text',showForExceptions: ["quote", "book","inv"] },
            { fieldName: "inventoryItemId", label: "Inventory Item Id", showField: true, drag: true, editable: true, sortable:false, type:'text',showForExceptions: ["book"] },
            { fieldName: "refLineId", label: "Reference Line Id", showField: true, drag: true, editable: true, sortable:false, type:'text',showForExceptions: ["book","inv","ship"] },
            { fieldName: "refLineNumber", label: "Reference Line Number", showField: true, drag: true, editable: false, sortable:false, type:'text',showForExceptions: ["ship"] },
            { fieldName: "refLineNumber", label: "Reference Line Number", showField: true, drag: true, editable: true, sortable:false, type:'text',showForExceptions: ["book","inv"] },
            { fieldName: "inventoryItemId", label: "Extended List Amount", showField: true, drag: true, editable: true, sortable:false, type:'round',showForExceptions: ["quote"] },
            { fieldName: "extendedListAmount", label: "Extended List Amount", showField: true, drag: true, editable: true, sortable:false, type:'round',showForExceptions: ["book"] },
            { fieldName: "amount", label: "Amount", showField: true, drag: true, editable: true, sortable:false, type:'round',showForExceptions: ["quote","book","ship","inv"] },
            { fieldName: "parentLineId", label: "Parent Line Id", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote","book"] },
            { fieldName: "headerAttribute1", label: "Header Attribute1", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "headerAttribute1", label: "Conga Order Header Id", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "headerAttribute2", label: "Header Attribute2", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "headerAttribute2", label: "Sales Contract Number", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["book"] },
            { fieldName: "headerAttribute3", label: "Header Attribute3", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "headerAttribute3", label: "Opportunity Type", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            // { fieldName: "headerAttribute4", label: "Grouping Id", showField:true, drag:true, editable: false, sortable:false, type:'text',showForExceptions:["book"]},
            { fieldName: "headerAttribute5", label: "Sales Contract Id", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote","book"] },
            { fieldName: "headerAttribute7", label: "Header Attribute7", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "headerAttribute7", label: "Customer Type", showField: true, drag: true,editable: false, sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "headerAttribute8", label: "Header Attribute8", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "headerAttribute8", label: "T4C Included", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "headerAttribute9", label: "Header Attribute9", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "headerAttribute9", label: "T4C Type", showField: true, drag: true,editable: false, sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "headerAttribute10", label: "Header Attribute10", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "headerAttribute10", label: "Legacy", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "headerAttribute11", label: "Header Attribute11", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "headerAttribute11", label: "Signature Date", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "headerAttribute12", label: "Header Attribute12", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "headerAttribute12", label: "T4C Other Details", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "headerAttribute13", label: "Header Attribute13", showField: true, drag: true ,editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "headerAttribute13", label: "Proposal Description", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute1", label: "line Attribute 1", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute2", label: "line Attribute 2", showField: true, drag: true, editable: false, sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute3", label: "line Attribute 3", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute4", label: "line Attribute 4", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute5", label: "line Attribute 5", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute6", label: "line Attribute 6", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute7", label: "line Attribute 7", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute8", label: "line Attribute 8", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute9", label: "line Attribute 9", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute10", label: "line Attribute 10", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute11", label: "line Attribute 11", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute12", label: "line Attribute 12", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute13", label: "line Attribute 13", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute14", label: "line Attribute 14", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute15", label: "line Attribute 15", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote"] },
            { fieldName: "lineAttribute1", label: "Conga Order Line Id", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute2", label: "Asset Id", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute1", label: "Asset Id", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["inv"] },
            { fieldName: "lineAttribute3", label: "Asset Number", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute2", label: "Asset Number", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["inv"] },
            { fieldName: "lineAttribute4", label: "Selling Term", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute5", label: "Charge Type", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute6", label: "Revenue Account", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute7", label: "Revenue Category", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute8", label: "SD Flag", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute9", label: "POB Mapping", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute10", label: "Status", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute11", label: "Contract Term Type", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute12", label: "Primary Line Number", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute13", label: "Item Sequence", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute14", label: "Custom Pricing", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute15", label: "Quote Line Reference Number", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute1", label: "Delievered Percent", showField: true, drag: true, editable: true,sortable:false, type:'round',showForExceptions:["ship"] },
            { fieldName: "lineAttribute2", label: "SD Flag", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["ship"] },
            { fieldName: "lineAttribute3", label: "Proof Posted Flag", showField: true, drag: true, editable: true,sortable:false, type:'text',showForExceptions:["ship"] },
            { fieldName: "lineAttribute4", label: "Revenue Policy Line", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["ship"] },
            { fieldName: "lineAttribute5", label: "Conga Invoice Line Id", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["inv"] },
            { fieldName: "lineAttribute7", label: "Xpedient Invoice Description", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["inv"] },
            { fieldName: "lineAttribute16", label: "Sub Fee Year1 Price", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute17", label: "Sub Fee Year2 Price", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute18", label: "Sub Fee Year3 Price", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute19", label: "Sub Fee Year4 Price", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute20", label: "Sub Fee Year5 Price", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute21", label: "Sub Fee Year6 Price", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute22", label: "Sub Fee Year7 Price", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute23", label: "Sub Fee Year8 Price", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute24", label: "Sub Fee Year9 Price", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute25", label: "Sub Fee Year10 Price", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute26", label: "Date Depended Flag", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute27", label: "Allocation Amount", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute28", label: "Line Attribute28", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute29", label: "Line Attribute29", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "lineAttribute30", label: "Order Parent Line Number", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions:["book"] },
            { fieldName: "entityName", label: "Company Code", showField:true, drag:true, editable:false,sortable:false, type:'text',showForExceptions:["ship"]},
            { fieldName: "entityName", label: "Company Code", showField:true, drag:true, editable:false,sortable:false, type:'text',showForExceptions:["inv"]},
            { fieldName: "creationDate", label: "Creation Date", showField: true, drag: true, editable: false,sortable:false, type:'time',showForExceptions: ["quote", "book", "ship", "inv", "cont_release"] },
            { fieldName: "processId", label: "Process Id", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote", "book", "ship", "inv", "cont_release"] },
            { fieldName: "exceptionMessage", label: "Exception Message", showField: true, drag: true, editable: false,sortable:false, type:'text',showForExceptions: ["quote", "book", "ship", "inv", "cont_release"] },

        ];


    }

    mergeFields(exceptionType: string): IField[] {
        const filteredFields = this.fields.filter(field =>
            field.showForExceptions.includes(exceptionType)
        );
        return filteredFields;
    }

    getLabels(exceptionType: string) {
        const labels = {
          QUOTE: {
            pageName: 'quotePage',
            tableName: 'quoteTable'
          },
          BOOK: {
            pageName: 'bookingPage',
            tableName: 'bookingTable'
          },
          SHIP: {
            pageName: 'shipmentPage',
            tableName: 'shipmentTable'
          },
          INV: {
            pageName: 'invPage',
            tableName: 'invTable'
          },
          CONT_RELEASE: {
            pageName: 'contReleasePage',
            tableName: 'contReleaseTable'
          }
        };

        return labels[exceptionType];
      }
 }