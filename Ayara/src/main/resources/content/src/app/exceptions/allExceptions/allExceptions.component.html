<div class="content-section implementation">
</div>


<p-panel header="Exceptions">
    <p-header>
        <div class="pull-right icons-list">
            <!-- <p-toggleButton [ngStyle]="{'margin-left': '10px', 'position': 'relative', 'top': '-2px'}" class="ui-inputswitch" onLabel="CurrentPeriodExceptions" offLabel="FuturePeriodExceptions" onIcon="fa fa-toggle-on fa-sm" offIcon="fa fa-toggle-off fa-sm" [(ngModel)]="allExceptionCurrentOpenPeriodSwitch" (ngModelChange)="onAllExceptionsCurrentOpenPeriodSwitch()"></p-toggleButton> -->
            <p-dropdown class="ml-auto ssp-books-status" [options]="allExceptionsTypes" [(ngModel)]="exceptionsType" [ngModelOptions]="{standalone: true}" (onChange)="onChange(dt)" name="exeType" [filter]="false" appendTo="body"></p-dropdown>
            <a (click)="onConfiguringColumns($event)"><em class="fa fa-cog"></em>
                <span style="position: relative;bottom: -1px;">Columns</span>
            </a>
<!--            <a  *isAuthorized="['write','QUOTEEX']" (click)="deleteSelected(dt)" title="Delete Selected"><em class="fa fa-trash"></em></a>-->
            <a title="Delete Selected" (click)="deleteSelected(dt)"><em class="fa fa-trash"></em></a>
            <a  (click)="showDialogToSearch()" title="Search"><em class="fa fa-search"></em></a>
            <a  (click)="reset(dt)"  title="Reset"><em class="fa fa-refresh"></em></a>
            <a  (click)="exportExcel()" title="Export"><em class="fa fa-external-link"></em></a>
<!--            <a  *isAuthorized="['write','QUOTEEX']" (click)="onReSubmitDocument()" title="Submit"><em class="fa fa-hand-pointer-o"></em></a>-->
            <a  (click)="onReSubmitDocument()" title="Submit"><em class="fa fa-hand-pointer-o"></em></a>
        </div>
    </p-header>

    <div class="x-scroll">
        <p-table *ngIf="showTable" class="ui-datatable arrangementMgrTbl" [loading]="loading" #dt [value]="allExceptionsList" id="rmanOrdersInt-dt" [(selection)]="selectedLines"
                 (onRowSelect)="onRowSelect($event)" [paginator]="true" [lazy]="true" [rows]="pageSize" 	scrollable="true"
                 [totalRecords]="totalElements" (onLazyLoad)="getAllTypeExceptions($event)" editMode="row" dataKey="trxId" [resizableColumns]="true" columnResizeMode="expand" >

            <ng-template pTemplate="colgroup" let-columns>
                <colgroup>
                    <col>
                    <col>
                    <col *ngFor="let col of columnsToBeDisplayed">
                </colgroup>
            </ng-template>

            <ng-template pTemplate="header" class="arrangementMgrTblHead">
                <tr>
                    <th></th>
                    <th></th>
                    <ng-container *ngFor="let col of columnsToBeDisplayed">
                        <th [ngStyle]="{'display': col.display}" title="{{col.label}}" pResizableColumn>
                            <a>{{col.label}}
                                <span *ngIf="col.sortable" class="sorting-icons">
                               <em class="fa fa-angle-up" (click)="onsorting(col.fieldName, 'asc',dt)"></em>
                               <em class="fa fa-angle-down" (click)="onsorting(col.fieldName, 'desc',dt)"></em>
                           </span>
                            </a>
                        </th>
                    </ng-container>
                </tr>
            </ng-template>

            <ng-template let-allExceptions let-rowData pTemplate="body" let-editing="editing" let-ri="rowIndex" let-columns="columnsToBeDisplayed">
                <tr [pSelectableRow]="rowData" [pEditableRow]="rowData">
                    <td style="text-align: center">
<!--                        <div *isAuthorized="['write','QUOTEEX']" >-->
                            <div >
                            <p-tableCheckbox [value]="rowData"></p-tableCheckbox>
                        </div>
                    </td>
                    <td style="text-align: center;position: sticky;background: inherit;left: 0;">
<!--                        <div *isAuthorized="['write','QUOTEEX']">-->
                            <div >
                            <button *ngIf="!editing" pButton type="button" pInitEditableRow icon="fa fa-pencil" (click)="onRowEditInit(allExceptions)" class="table-edit-btn"></button>
                            <button *ngIf="editing" pButton type="button" pSaveEditableRow icon="fa fa-check" (click)="onRowEditSave(allExceptions)" class="table-edit-btn"></button>
                            <button *ngIf="editing" pButton type="button" pCancelEditableRow icon="fa fa-times"(click)="onRowEditCancel(allExceptions, ri)"  class="table-edit-btn"></button>
                        </div>
                    </td>
                    <ng-container *ngFor="let col of columnsToBeDisplayed">
                        <td [title]="rowData[col.fieldName]" [ngStyle]="{'display': 'table-cell'}">
                            <p-cellEditor *ngIf="col.editable">
                                <ng-template pTemplate="output">
                                    <ng-container *ngIf="col.type === 'round'">
                                        {{ rowData[col.fieldName] | round }}
                                    </ng-container>
                                    <ng-container *ngIf="col.type === 'time'">
                                        {{ rowData[col.fieldName] | date:'MM/dd/yyyy' }}
                                    </ng-container>
                                    <ng-container *ngIf="col.type !== 'round' && col.type !== 'time'">
                                        {{ rowData[col.fieldName] }}
                                    </ng-container>
                                </ng-template>
                                <ng-template pTemplate="input">
                                    <ng-container *ngIf="col.type === 'date'">
                                        <p-calendar
                                                [monthNavigator]="true" class="table-cell-calendar" dataType="string" dateFormat="yy-mm-dd" [(ngModel)]="rowData[col.fieldName]" [yearNavigator]="true"
                                                yearRange="2000:2100" [showIcon]="false" appendTo="body">
                                        </p-calendar>
                                    </ng-container>
                                    <ng-container *ngIf="col.type !== 'date'">
                                        <input class="table-edit-input" pInputText [(ngModel)]="rowData[col.fieldName]">
                                    </ng-container>
                                </ng-template>
                            </p-cellEditor>
                            <ng-container *ngIf="!col.editable">
                                <ng-container *ngIf="col.type === 'round'">
                                    {{ rowData[col.fieldName] | round }}
                                </ng-container>
                                <ng-container *ngIf="col.type === 'time'">
                                    {{ rowData[col.fieldName] | date:'MM/dd/yyyy' }}
                                </ng-container>
                                <ng-container *ngIf="col.type !== 'round' && col.type !== 'time'">
                                    {{ rowData[col.fieldName] }}
                                </ng-container>
                            </ng-container>
                        </td>
                    </ng-container>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage" let-columns>
                <tr *ngIf="!allExceptionsList || allExceptionsList.length === 0">
                    <td class="no-data" >{{noData}}</td>
                </tr>
            </ng-template>

            <!--   <ng-template pTemplate="header" class="arrangementMgrTblHead">
                   <tr>
                       <th class= "column-resize" *isAuthorized="['write','QUOTEEX']" style="width: 60px" pResizableColumn><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
                       <th class= "column-resize" *isAuthorized="['write','QUOTEEX']" style="width: 60px; position: sticky; left: 0; background: #d9e1e7" pResizableColumn></th>
                       <th class= "column-resize" style="width:170px;text-align:left" pResizableColumn><a >{{columns['documentCategory']}}</a></th>
                       <th class= "column-resize" style="width:170px;text-align:left" pResizableColumn><a >{{columns['documentSource']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'SHIP' || exceptionsType === 'BOOK' || exceptionsType === 'CONT_RELEASE'" style="width:150px;text-align:left" pResizableColumn ><a >{{columns['eventType']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['dealNumber']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['projectNumber']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['opportunityNumber']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:right" 	pResizableColumn><a >{{columns['customerPo']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" 	pResizableColumn><a >{{columns['opportunityName']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType !== 'CONT_RELEASE'" style="width:170px;text-align:left" pResizableColumn>
                           <a >{{columns['documentNumber']}}
                               <span>
                                   <em class="fa fa-angle-up" (click)="onsorting('documentNumber','asc')"></em>
                                   <em class="fa fa-angle-down" (click)="onsorting('documentNumber','desc')"></em>
                               </span>
                           </a>
                       </th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:180px;text-align:left" pResizableColumn><a >{{columns['documentDescription']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType !== 'SHIP' && exceptionsType !== 'CONT_RELEASE'" style="width:180px;text-align:left" pResizableColumn><a >{{columns['documentHeaderId']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'INV' || exceptionsType === 'BOOK'" style="width:170px;text-align:right" 	pResizableColumn><a >{{columns['documentType']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['entityName']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:180px;text-align:left" pResizableColumn><a >{{columns['entityId']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['customerName']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['customerNumber']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType !== 'CONT_RELEASE'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['documentDate']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:center" pResizableColumn><a >{{columns['documentStatus']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType !== 'SHIP' && exceptionsType !== 'CONT_RELEASE'" style="width:170px;text-align:center" pResizableColumn><a >{{columns['currencyCode']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:center" pResizableColumn><a >{{columns['salesRep']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:center" pResizableColumn><a >{{columns['salesTheater']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['region']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['salesTerritory']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType !== 'SHIP' && exceptionsType !== 'CONT_RELEASE'" style="width:170px;text-align:center" pResizableColumn><a >{{columns['documentLineNumber']}}</a></th>
                       <th class= "column-resize" style="width:150px;text-align:left" pResizableColumn><a >{{columns['documentLineId']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:150px;text-align:center" pResizableColumn><a >{{columns['productName']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType !== 'CONT_RELEASE'"style="width:170px;text-align:left" pResizableColumn><a >{{columns['quantity']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'SHIP' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['documentLineStatus']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['uom']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:150px;text-align:right" pResizableColumn><a >{{columns['unitListPrice']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType !== 'SHIP' && exceptionsType !== 'CONT_RELEASE'" style="width:170px;text-align:right" pResizableColumn><a >{{columns['unitSellingPrice']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:right" pResizableColumn><a >{{columns['parentLineNumber']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:150px;text-align:left" pResizableColumn><a >{{columns['serviceStartDate']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:right" pResizableColumn><a >{{columns['serviceEndDate']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:right" pResizableColumn><a >{{columns['unitCost']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType !== 'INV'" style="width:170px;text-align:right" pResizableColumn><a >{{columns['contingnecyCode']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'CONT_RELEASE'" style="width:170px;text-align:right" pResizableColumn><a >{{columns['contReleaseType']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'CONT_RELEASE'" style="width:170px;text-align:right" pResizableColumn><a >{{columns['contReleaseRevenue']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'SHIP' || exceptionsType === 'BOOK'" style="width:170px;text-align:right" pResizableColumn><a >{{columns['contApplyType']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType !== 'INV' && exceptionsType !== 'CONT_RELEASE'" style="width:170px;text-align:right" pResizableColumn><a >{{columns['contingnecyPercentage']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:right" pResizableColumn><a >{{columns['revenueTemplateName']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType !== 'SHIP' && exceptionsType !== 'CONT_RELEASE'" style="width:170px;text-align:left" pResizableColumn><a >{{columns['refDocumentNumber']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:right" pResizableColumn><a >{{columns['inventoryItemId']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'INV' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP' " style="width:170px;text-align:right" pResizableColumn><a >{{columns['refLineId']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'INV' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP'" style="width:170px;text-align:left" 	pResizableColumn><a >{{columns['refLineNumber']}}</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:right" pResizableColumn><a >Extended List Amount</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'INV' || exceptionsType === 'BOOK'|| exceptionsType === 'SHIP'" style="width:170px;text-align:right" pResizableColumn><a >Amount</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Parent Line ID</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Parent Line Number</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute1</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >Conga Order Header Id</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute2</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >Sales Contract Number</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute3</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >Opportunity Type</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute5</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >Header Attribute5</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute7</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >Customer type</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute8</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >T4C Included</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute9</a></th>
                       <th class= "column-resize"*ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >T4C Type</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute10</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >Legacy</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute11</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >Signature Date</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute12</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >T4C Other Details</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute13</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >Proposal Description</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a >Header Attribute14</a></th>
                       <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'"  style="width:170px;text-align:left" pResizableColumn><a >Header Attribute14</a></th>


                       <!--<th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute1</a></th>
                       <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute2</a></th>
                       <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute3</a></th>
                       <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute5</a></th>
                       <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute7</a></th>
                       <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute8</a></th>
                       <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute9</a></th>
                       <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute10</a></th>
                       <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute11</a></th>
                       <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute12</a></th>
                       <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute13</a></th>
                       <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Header Attribute14</a></th>
                       -->
            <!--     <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute1</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute2</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute3</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute4</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute5</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute6</a></th>
                 <th class= "column-resize"*ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute7</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute8</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute9</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute10</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute11</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute12</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute13</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute14</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'QUOTE'" style="width:170px;text-align:left" pResizableColumn><a>Line Attribute15</a></th>

                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Conga Order Line Id</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Asset Id</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Asset Number</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Selling Term</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Charge Type</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Revenue Account</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Revenue Category</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>SD Flag</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>POB Grouping</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Status</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Contract Term Type</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Primary Line Number</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Item Sequence</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Custom Pricing</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a>Quote Line Reference Number</a></th>

                 <th class= "column-resize" *ngIf="exceptionsType === 'SHIP'" style="width:170px;text-align:left" pResizableColumn><a>Delivered Percent</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'SHIP'" style="width:170px;text-align:left" pResizableColumn><a>SD Flag</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'SHIP'" style="width:170px;text-align:left" pResizableColumn><a>Proof Posted Flag</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'SHIP'" style="width:170px;text-align:left" pResizableColumn><a>Revenue Policy Line</a></th>


                 <th class= "column-resize" *ngIf="exceptionsType === 'INV'" style="width:170px;text-align:left" pResizableColumn><a>Asset Id</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'INV'" style="width:170px;text-align:left" pResizableColumn><a>Asset Number</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'INV'" style="width:170px;text-align:left" pResizableColumn><a>Conga Invoice Line Id</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'INV'" style="width:170px;text-align:left" pResizableColumn><a>Xpedient Invoice Description</a></th>


                 <!--<th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP' || exceptionsType === 'INV'" style="width:170px;text-align:left"><a>Line Attribute1</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP' || exceptionsType === 'INV'" style="width:170px;text-align:left"><a>Line Attribute2</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP'" style="width:170px;text-align:left"><a >Line Attribute3</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP'" style="width:170px;text-align:left"><a >Line Attribute4</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'INV'" style="width:170px;text-align:left"><a >Line Attribute5</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Line Attribute6</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'INV'" style="width:170px;text-align:left"><a >Line Attribute7</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Line Attribute8</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Line Attribute9</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Line Attribute10</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Line Attribute11</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Line Attribute12</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Line Attribute13</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Line Attribute14</a></th>
                 <th *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a >Line Attribute15</a></th>
                 -->
            <!--     <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Sub Fee Year1 Price</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Sub Fee Year2 Price</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Sub Fee Year3 Price</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Sub Fee Year4 Price</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Sub Fee Year5 Price</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Sub Fee Year6 Price</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Sub Fee Year7 Price</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Sub Fee Year8 Price</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Sub Fee Year9 Price</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Sub Fee Year10 Price</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Date Depended Flag(JSON)</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Allocation Amount</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Line Attribute28</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Line Attribute29</a></th>
                 <th class= "column-resize" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left" pResizableColumn><a >Order Parent Line Number</a></th>
                 <th class= "column-resize" style="width:170px;text-align:left" pResizableColumn><a >Creation Date</a></th>
                 <th class= "column-resize" style="width:170px;text-align:left" pResizableColumn><a >{{columns['processId']}}</a></th>
                 <th class= "column-resize" style="width:340px;text-align:left" pResizableColumn><a >{{columns['exceptionMessage']}}</a></th>

             </tr>
         </ng-template>
         <ng-template let-allExceptions let-rowData pTemplate="body" let-editing="editing" let-ri="rowIndex">
             <tr [pSelectableRow]="rowData" [pEditableRow]="rowData">
                 <td *isAuthorized="['write','QUOTEEX']" style="width:5px">
                     <p-tableCheckbox [value]="rowData"></p-tableCheckbox>
                 </td>
                 <td *isAuthorized="['write','QUOTEEX']" style="width:5px;text-align:center; position: sticky; left: 0; background: inherit">
                     <button *ngIf="!editing" pButton type="button" pInitEditableRow icon="fa fa-pencil" (click)="onRowEditInit(allExceptions)" class="table-edit-btn"></button>
                     <button *ngIf="editing" pButton type="button" pSaveEditableRow icon="fa fa-check" (click)="onRowEditSave(allExceptions)" class="table-edit-btn"></button>
                     <button *ngIf="editing" pButton type="button" pCancelEditableRow icon="fa fa-times"(click)="onRowEditCancel(allExceptions, ri)"  class="table-edit-btn"></button>
                 </td>
                 <td title="{{allExceptions.documentCategory}}" style="width:170px;text-align:left">{{allExceptions.documentCategory}}</td>
                 <td title="{{allExceptions.documentSource}}" style="width:170px;text-align:left" pEditableColumn>{{allExceptions.documentSource}}</td>
                 <td *ngIf="exceptionsType === 'SHIP' || exceptionsType === 'BOOK' || exceptionsType === 'CONT_RELEASE'" title="{{allExceptions.eventType}}" style="width:150px;text-align:left">{{allExceptions.eventType}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.dealNumber}}" style="width:170px;text-align:left">{{allExceptions.dealNumber}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.projectNumber}}" style="width:170px;text-align:left">{{allExceptions.projectNumber}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.opportunityNumber}}" style="width:170px;text-align:left">{{allExceptions.opportunityNumber}}</td>
                 <td *ngIf="exceptionsType === 'BOOK'" title="{{allExceptions.customerPo}}" style="width:170px;text-align:left">{{allExceptions.customerPo}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE'" title="{{allExceptions.opportunityName}}" style="width:170px;text-align:left">{{allExceptions.opportunityName}}</td>
                 <td *ngIf="exceptionsType !== 'CONT_RELEASE'" title="{{allExceptions.documentNumber}}" style="width:170px;text-align:left">{{allExceptions.documentNumber}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE'" title="{{allExceptions.documentDescription}}" style="width:180px;text-align:left">{{allExceptions.documentDescription}}</td>
                 <td *ngIf="exceptionsType !== 'SHIP' && exceptionsType !== 'CONT_RELEASE'" title="{{allExceptions.documentHeaderId}}" style="width:180px;text-align:left">{{allExceptions.documentHeaderId}}</td>
                 <td *ngIf="exceptionsType === 'INV' || exceptionsType === 'BOOK'" title="{{allExceptions.documentType}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.documentType}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.documentType">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.entityName}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.entityName}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.entityName">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.entityId}}" style="width:180px;text-align:left">{{allExceptions.entityId}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.customerName}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.customerName}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.customerName">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.customerNumber}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.customerNumber}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.customerNumber">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType !== 'CONT_RELEASE'" title="{{allExceptions.documentDate}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.documentDate}}
                         </ng-template>
                         <ng-template pTemplate="input">
                                 <p-calendar [monthNavigator]="true" class="table-cell-calendar" dataType="string" dateFormat="yy-mm-dd" [(ngModel)]="allExceptions.documentDate"
                                             [yearNavigator]="true" yearRange="2000:2100" [showIcon]="false" appendTo="body" >
                                 </p-calendar>
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'BOOK'" title="{{allExceptions.documentStatus}}" style="width:170px;text-align:left">{{allExceptions.documentStatus}}</td>
                 <td *ngIf="exceptionsType !== 'SHIP' && exceptionsType !== 'CONT_RELEASE'" title="{{allExceptions.currencyCode}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.currencyCode}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.currencyCode">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.salesRep}}" style="width:170px;text-align:left">{{allExceptions.salesRep}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.salesTheater}}" style="width:170px;text-align:left">{{allExceptions.salesTheater}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.region}}" style="width:170px;text-align:left">{{allExceptions.region}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.salesTerritory}}" style="width:170px;text-align:left">{{allExceptions.salesTerritory}}</td>
                 <td *ngIf="exceptionsType !== 'SHIP' && exceptionsType !== 'CONT_RELEASE'" title="{{allExceptions.documentLineNumber}}" style="width:170px;text-align:left">{{allExceptions.documentLineNumber}}</td>
                 <td title="{{allExceptions.documentLineId}}" style="width:150px;text-align:left">{{allExceptions.documentLineId}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.productName}}" style="width:150px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.productName}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.productName">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType !== 'CONT_RELEASE'" title="{{allExceptions.quantity}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.quantity}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.quantity">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'SHIP' || exceptionsType === 'BOOK'" title="{{allExceptions.documentLineStatus}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.documentLineStatus}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.documentLineStatus">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.uom}}" style="width:170px;text-align:left">{{allExceptions.uom}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.unitListPrice}}" style="width:150px;text-align:left">{{allExceptions.unitListPrice}}</td>
                 <td *ngIf="exceptionsType !== 'SHIP' && exceptionsType !== 'CONT_RELEASE'" title="{{allExceptions.unitSellingPrice}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.unitSellingPrice}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.unitSellingPrice">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'QUOTE'" title="{{allExceptions.parentLineNumber}}" style="width:170px;text-align:left">{{allExceptions.parentLineNumber}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.serviceStartDate}}" style="width:150px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.serviceStartDate}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <p-calendar [monthNavigator]="true" class="table-cell-calendar" dataType="string" dateFormat="yy-mm-dd" [(ngModel)]="allExceptions.serviceStartDate"
                                         [yearNavigator]="true" yearRange="2000:2100" [showIcon]="false" appendTo="body" >
                             </p-calendar>
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.serviceEndDate}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.serviceEndDate}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <p-calendar [monthNavigator]="true" class="table-cell-calendar" dataType="string" dateFormat="yy-mm-dd" [(ngModel)]="allExceptions.serviceEndDate"
                                         [yearNavigator]="true" yearRange="2000:2100" [showIcon]="false" appendTo="body" >
                             </p-calendar>
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.unitCost}}" style="width:170px;text-align:left">{{allExceptions.unitCost | round}}</td>
                 <td *ngIf="exceptionsType !== 'INV'" title="{{allExceptions.contingnecyCode}}" style="width:170px;text-align:left">{{allExceptions.contingnecyCode}}</td>
                 <td *ngIf="exceptionsType === 'CONT_RELEASE'" title="{{allExceptions.contReleaseType}}" style="width:170px;text-align:left">{{allExceptions.contReleaseType}}</td>
                 <td *ngIf="exceptionsType === 'CONT_RELEASE'" title="{{allExceptions.contReleaseRevenue}}" style="width:170px;text-align:left">{{allExceptions.contReleaseRevenue}}</td>
                 <td *ngIf="exceptionsType === 'SHIP' || exceptionsType === 'BOOK'" title="{{allExceptions.contApplyType}}" style="width:170px;text-align:left">{{allExceptions.contApplyType}}</td>
                 <td *ngIf="exceptionsType !== 'INV' && exceptionsType !== 'CONT_RELEASE'" title="{{allExceptions.contingnecyPercentage}}" style="width:170px;text-align:left">{{allExceptions.contingnecyPercentage}}</td>
                 <td *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" title="{{allExceptions.revenueTemplateName}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.revenueTemplateName}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.revenueTemplateName">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType !== 'SHIP' && exceptionsType !== 'CONT_RELEASE'" title="{{allExceptions.refDocumentNumber}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.refDocumentNumber}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.refDocumentNumber">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'BOOK'" title="{{allExceptions.inventoryItemId}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.inventoryItemId}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.inventoryItemId">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'INV' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP'" title="{{allExceptions.refLineId}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.refLineId}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.refLineId">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'INV'|| exceptionsType === 'SHIP' || exceptionsType === 'BOOK'" title="{{allExceptions.refLineNumber}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.refLineNumber}}
                         </ng-template>
                         <ng-template pTemplate="input" *ngIf="exceptionsType === 'INV' || exceptionsType === 'BOOK'">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.refLineNumber">
                         </ng-template>
                     </p-cellEditor>
                 </td>
                 <td *ngIf="exceptionsType === 'BOOK'||exceptionsType === 'QUOTE'" title="{{allExceptions.extendedListAmount}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.extendedListAmount | round}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.extendedListAmount" required>
                         </ng-template>
                     </p-cellEditor>
                 </td>

                 <td *ngIf="exceptionsType === 'BOOK'||exceptionsType === 'QUOTE'||exceptionsType === 'SHIP' || exceptionsType === 'INV'" title="{{allExceptions.amount}}" style="width:170px;text-align:left">
                     <p-cellEditor>
                         <ng-template pTemplate="output">
                             {{allExceptions.amount | round}}
                         </ng-template>
                         <ng-template pTemplate="input">
                             <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.amount" required>
                         </ng-template>
                     </p-cellEditor>
                 </td>

                 <td title="{{allExceptions.parentLineId}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a> {{allExceptions.parentLineId}}</a></td>
                 <td title="{{allExceptions.parentLineNumber}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a> {{allExceptions.parentLineNumber}}</a></td>
                 <td title="{{allExceptions.headerAttribute1}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a> {{allExceptions.headerAttribute1}}</a></td>
                 <td title="{{allExceptions.headerAttribute2}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a> {{allExceptions.headerAttribute2}}</a></td>
                 <td title="{{allExceptions.headerAttribute3}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a> {{allExceptions.headerAttribute3}}</a></td>
                 <td title="{{allExceptions.headerAttribute5}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a> {{allExceptions.headerAttribute5}}</a></td>
                 <td title="{{allExceptions.headerAttribute7}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a> {{allExceptions.headerAttribute7}}</a></td>
                 <td title="{{allExceptions.headerAttribute8}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a> {{allExceptions.headerAttribute8}}</a></td>
                 <td title="{{allExceptions.headerAttribute9}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a> {{allExceptions.headerAttribute9}} </a></td>
                 <td title="{{allExceptions.headerAttribute10}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.headerAttribute10}}</a></td>
                 <td title="{{allExceptions.headerAttribute11}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.headerAttribute11}}</a></td>
                 <td title="{{allExceptions.headerAttribute12}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.headerAttribute12}}</a></td>
                 <td title="{{allExceptions.headerAttribute13}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.headerAttribute13}}</a></td>
                 <td title="{{allExceptions.headerAttribute14}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.headerAttribute14}}</a></td>
                 <td title="{{allExceptions.lineAttribute1}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP' || exceptionsType === 'INV'" style="width:170px;text-align:left">
                         <p-cellEditor>
                                 <ng-template pTemplate="output" *ngIf="exceptionsType === 'SHIP'">
                                     {{allExceptions.lineAttribute1 | round}}
                                 </ng-template>
                                 <ng-template pTemplate="output" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'INV'">
                                         {{allExceptions.lineAttribute1}}
                                 </ng-template>
                                 <ng-template pTemplate="input" *ngIf="exceptionsType === 'SHIP'">
                                     <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.lineAttribute1" required>
                                 </ng-template>
                         </p-cellEditor>
                 </td>

                 <td title="{{allExceptions.lineAttribute2}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP' || exceptionsType === 'INV'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute2}}</a></td>
                 <td title="{{allExceptions.lineAttribute3}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP'" style="width:170px;text-align:left">
                     <p-cellEditor>
                                 <ng-template pTemplate="output" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP'">
                                         {{allExceptions.lineAttribute3}}
                                     </ng-template>
                                 <ng-template pTemplate="input" *ngIf="exceptionsType === 'SHIP'">
                                     <input class="table-edit-input" pInputText [(ngModel)]="allExceptions.lineAttribute3" required>
                                 </ng-template>
                         </p-cellEditor>
                 </td>
                 <td title="{{allExceptions.lineAttribute4}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'SHIP'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute4}}</a></td>
                 <td title="{{allExceptions.lineAttribute5}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'INV'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute5}}</a></td>
                 <td title="{{allExceptions.lineAttribute6}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute6}}</a></td>
                 <td title="{{allExceptions.lineAttribute7}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK' || exceptionsType === 'INV'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute7}}</a></td>
                 <td title="{{allExceptions.lineAttribute8}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute8}}</a></td>
                 <td title="{{allExceptions.lineAttribute9}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute9}}</a></td>
                 <td title="{{allExceptions.lineAttribute10}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute10}}</a></td>
                 <td title="{{allExceptions.lineAttribute11}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute11}}</a></td>
                 <td title="{{allExceptions.lineAttribute12}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute12}}</a></td>
                 <td title="{{allExceptions.lineAttribute13}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute13}}</a></td>
                 <td title="{{allExceptions.lineAttribute14}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute14}}</a></td>
                 <td title="{{allExceptions.lineAttribute15}}" *ngIf="exceptionsType === 'QUOTE' || exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute15}}</a></td>
                 <td title="{{allExceptions.lineAttribute16}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute16}}</a></td>
                 <td title="{{allExceptions.lineAttribute17}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute17}}</a></td>
                 <td title="{{allExceptions.lineAttribute18}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute18}}</a></td>
                 <td title="{{allExceptions.lineAttribute19}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute19}}</a></td>
                 <td title="{{allExceptions.lineAttribute20}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute20}}</a></td>
                 <td title="{{allExceptions.lineAttribute21}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute21}}</a></td>
                 <td title="{{allExceptions.lineAttribute22}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute22}}</a></td>
                 <td title="{{allExceptions.lineAttribute23}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute23}}</a></td>
                 <td title="{{allExceptions.lineAttribute24}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute24}}</a></td>
                 <td title="{{allExceptions.lineAttribute25}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute25}}</a></td>
                 <td title="{{allExceptions.lineAttribute26}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute26}}</a></td>
                 <td title="{{allExceptions.lineAttribute27}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute27}}</a></td>
                 <td title="{{allExceptions.lineAttribute28}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute28}}</a></td>
                 <td title="{{allExceptions.lineAttribute29}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute29}}</a></td>
                 <td title="{{allExceptions.lineAttribute30}}" *ngIf="exceptionsType === 'BOOK'" style="width:170px;text-align:left"><a>{{allExceptions.lineAttribute30}}</a></td>
                 <td title="{{allExceptions.creationDate}}" style="width:170px;text-align:left">{{allExceptions.creationDate| date:'MM/dd/yyyy'}}</td>
                 <td title="{{allExceptions.processId}}" style="width:170px;text-align:left">{{allExceptions.processId}}</td>
                 <td title="{{allExceptions.exceptionMessage}}" style="width:340px!important;text-align:left">{{allExceptions.exceptionMessage}}</td>
                 </tr>
         </ng-template>
         <ng-template pTemplate="emptymessage" let-columns>
             <tr *ngIf="!columns">
                 <td class="no-data">{{noData}}</td>
             </tr>
         </ng-template> -->

        </p-table>
    </div>
    <div id="add-column-popup" *ngIf="!showAddColumns">
        <div class="user-popup" style ="right:auto;left:610px;top:21px !important;">
            <div class="content overflow">
                <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()" />
                <label for="selectall">Select All</label>
                <a class="close" style="padding: 6px;"  title="Close" (click)="closeConfigureColumns($event)">&times;</a>
                <p-listbox [options]="columnsList" [style]="{ width: '100%', height: '100%' }">
                    <ng-template let-col let-index="index" pTemplate="item">
                        <div *ngIf="col.drag">
                            <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                                <div class="drag">
                                    <input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" />
                                    <label>{{col.label}}</label>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="!col.drag">
                            <div class="ui-helper-clearfix">
                                <div>
                                    <input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"
                                    />
                                    <label [ngStyle]="{'color': 'grey'}">{{col.label}}</label>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                </p-listbox>

            </div>
            <div class="pull-right icons-list">
                <a class="configColBtn" (click)="saveColumns()">Save</a>
                <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
            </div>
        </div>
    </div>
</p-panel>


<p-dialog header="Search" width="auto" [(visible)]="displaySearchDialog"  showEffect="fade"
          [modal]="true" [blockScroll]="true" [draggable]="true">
    <form>
        <div class="ui-grid ui-grid-responsive ui-fluid">
            <div class="ui-g-12">
                <div class="ui-g-6">
                              <span class="md-inputfield">
                                   <span class="selectSpan">{{columns['documentSource']}}</span>
                                  <input pInputText name="documentSource" class="textbox" placeholder="Document Source" id="documentSource"
                                       [ngModelOptions]="{standalone: true}"
                                       [(ngModel)]="allExceptionsSearch.documentSource" />
                                  <!--<p-autoComplete [(ngModel)]="allExceptionsSearch.documentSource"
                                                  appendTo="body" placeholder="Type Document Source" [suggestions]="valueResults" (completeMethod)="searchValues(exceptionsType,'DOCUMENT_SOURCE',$event)"
                                                  [ngModelOptions]="{standalone: true}" minLength="3">
                                    </p-autoComplete>-->
                            </span>
              </div>
              <div class="ui-g-6 pull-right">
                            <span class="md-inputfield">
                                 <span class="selectSpan">{{columns['documentLineId']}}</span>
                                <input pInputText name="documentLineId" id="documentLineId" class="textbox" placeholder="Document Line Id"
                                     [ngModelOptions]="{standalone: true}"
                                     [(ngModel)]="allExceptionsSearch.documentLineId" />
                                <!--<p-autoComplete [(ngModel)]="allExceptionsSearch.documentLineId"
                                                appendTo="body" placeholder="Type Document Line ID" [suggestions]="valueResults" (completeMethod)="searchValues(exceptionsType,'DOCUMENT_LINE_ID',$event)"
                                                [ngModelOptions]="{standalone: true}" minLength="3">
                                   </p-autoComplete>-->
                           </span>
             </div>
         </div>
         <div class="ui-g-12">
             <div class="ui-g-6">
                           <span class="md-inputfield">
                                <span class="selectSpan">{{columns['processId']}}</span>
                               <input pInputText name="processId" class="textbox" placeholder="Process id" id="processId"
                                    [ngModelOptions]="{standalone: true}"
                                    [(ngModel)]="allExceptionsSearch.processId" />
                               <!--<p-autoComplete [(ngModel)]="allExceptionsSearch.processId"
                                               appendTo="body" placeholder="Type Process ID" [suggestions]="valueResults" (completeMethod)="searchValues(exceptionsType,'PROCESS_ID',$event)"
                                               [ngModelOptions]="{standalone: true}" minLength="3">
                                  </p-autoComplete>-->
                          </span>
            </div>
            <div class="ui-g-6 pull-right">
                        <span class="md-inputfield">
                             <span class="selectSpan">Document Number</span>
                            <input pInputText name="documentNumber" id="documentNumber" class="textbox" placeholder="Document Number"
                                 [ngModelOptions]="{standalone: true}"
                                 [(ngModel)]="allExceptionsSearch.documentNumber" />
                            <!--<p-autoComplete [(ngModel)]="allExceptionsSearch.documentNumber"
                                            appendTo="body" placeholder="Type Document Number" [suggestions]="valueResults" (completeMethod)="searchValues(exceptionsType,'DOCUMENT_NUMBER',$event)"
                                            [ngModelOptions]="{standalone: true}" minLength="3">
                                 </p-autoComplete>-->
                       </span>
           </div>
       </div>
   </div>
</form>
<p-footer>
   <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
       <button type="submit" pButton class="primary-btn" label="Search" (click)="search(dt)"></button>
       <button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>
   </div>
</p-footer>

</p-dialog>

<p-dialog header="Resubmit" width="auto" [(visible)]="processDialog"  showEffect="fade"
     [modal]="true" [blockScroll]="true" [draggable]="true">
<form>
   <div class="ui-grid ui-grid-responsive ui-fluid">
       <div class="ui-g-12">
           <div class="ui-g-6">
                         <span class="md-inputfield">
                              <span class="selectSpan">Old Process ID</span>
                              <input pInputText name="processId" class="textbox" placeholder="Old Process Id" id="processId"
                                     [ngModelOptions]="{standalone: true}"
                                     [(ngModel)]="processId" />
                         </span>
           </div>
       </div>
   </div>
</form>
<p-footer>
   <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
       <button type="submit" pButton class="primary-btn" label="ReSubmit" (click)="onReSubmitDocument()"></button>
       <button type="button" pButton class="secondary-btn" (click)="processDialog=false" label="Cancel"></button>
   </div>
</p-footer>

</p-dialog>

<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>