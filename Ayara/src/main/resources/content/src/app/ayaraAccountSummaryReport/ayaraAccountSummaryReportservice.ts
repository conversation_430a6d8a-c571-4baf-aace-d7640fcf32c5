import { DatePipe } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require:any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type':  'application/json',
  })
};




@Injectable()
export class AyaraAccountSummaryReportService {

    constructor(private http: HttpClient,private datePipe: DatePipe) {}
    
    getServiceUrl(paginationOptions:any ,searchFields:any,  exportFlag: any, exportCols: any) {
        
        let serviceUrl = appSettings.apiUrl + '/ayaraSummaryReportExport?';

    	 if (exportFlag == 0) {
      	 	serviceUrl = appSettings.apiUrl + '/ayaraSummaryReport?';
    	 }
		
		if(searchFields.groupBy){
			serviceUrl =  serviceUrl + '&groupBy='+searchFields.groupBy;
		}
		if(searchFields.rcNumber){
			serviceUrl = serviceUrl + '&rcNumber='+searchFields.rcNumber;
		}
		if(searchFields.salesContract){
			serviceUrl = serviceUrl + '&salesContract='+searchFields.salesContract;
		}
		if(searchFields.so){
			serviceUrl = serviceUrl + '&so='+searchFields.so;
		}
		if(searchFields.orderLine){
			serviceUrl = serviceUrl + '&orderLine='+searchFields.orderLine;
		}
		
		if(searchFields.assetId){
			serviceUrl = serviceUrl + '&assetId='+searchFields.assetId;
		}
		
		if(searchFields.startDate){
			serviceUrl = serviceUrl + '&startDate='+this.datePipe.transform(searchFields.startDate,'yyyyMMdd');
		}
		
		if(searchFields.endDate){
			serviceUrl = serviceUrl + '&endDate='+this.datePipe.transform(searchFields.endDate,'yyyyMMdd');
		}

		if (paginationOptions.pageNumber!=undefined && paginationOptions.pageNumber!="" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber!=0) {
           serviceUrl=serviceUrl+'&page='+paginationOptions.pageNumber+'&size='+paginationOptions.pageSize;
        }
                
        if (exportCols != undefined && exportCols != "") {
      		serviceUrl = serviceUrl + '&exportCols=' + exportCols;
    	}
   		
   		return serviceUrl;
    }
    
	getAllayaraAccountSummaryReport(paginationOptions:any,searchFields:any, exportCols: any): Promise<any[]> {
        
        let serviceUrl = this.getServiceUrl(paginationOptions, searchFields, 0, exportCols);
        return this.http.get(serviceUrl).toPromise().then((data:any) => {
            return data;
        });
        
    }
    
    fetchRCNumbers(rc: any) {
        let unpostedRCUrl = appSettings.apiUrl + '/fetchAllRC?rc='+rc;

        return this.http.get(unpostedRCUrl).toPromise().then((data: any) => {
               return data;
         });
    } 

	
}
