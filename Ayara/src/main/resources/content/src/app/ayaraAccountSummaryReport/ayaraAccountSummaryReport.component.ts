import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { Table } from 'primeng/table';
import { NotificationService } from '../shared/notifications.service';
import { UploadService } from '../shared/upload.service';
import { AyaraAccountSummaryReport } from './ayaraAccountSummaryReport';
import { AyaraAccountSummaryReportService } from './ayaraAccountSummaryReportservice';
import { CommonSharedService } from '../shared/common.service';
import { RmanLookupsVService } from '../rmanLookupsV/rmanLookupsVservice';
import { RmanFiscalPeriodsService } from '../rmanFiscalPeriods/rmanFiscalPeriodsservice';
declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Component({
    templateUrl: './ayaraAccountSummaryReport.component.html',
    selector: 'ayaraAccountSummaryReport-data',
    providers: [AyaraAccountSummaryReportService, ConfirmationService, RmanLookupsVService, RmanFiscalPeriodsService]
})

export class AyaraAccountSummaryReportComponent {

    displayDialog: boolean;

    displaySearchDialog: boolean;
    
    ayaraAccountSummaryReport: AyaraAccountSummaryReport = new AyaraAccountSummaryReportImpl();

    ayaraAccountSummaryReportSearch: AyaraAccountSummaryReport = new AyaraAccountSummaryReportImpl();

    isSerached: number = 0;

    selectedAyaraAccountSummaryReport: AyaraAccountSummaryReport;

    newAyaraAccountSummaryReport: boolean;
	
	displayCustomersInterfaceDialog: boolean = false;
	
    ayaraAccountSummaryReportList: AyaraAccountSummaryReport[];

    cols: any[];
    //columns: ILabels;

    columnOptions: any[];

    paginationOptions = {};
	
	searchFields:any = {};

    pages: {};


    datasource: any[];
    pageSize: number;
    totalElements: number;
    collapsed: boolean = true;
    
    customerForm: FormGroup;
    noData = appSettings.noData;
    loading: boolean;
    ayaraAccountSummaryReporttatus: any[];
    regions:any[];
    customerTypes:any[];
    isSelectAllChecked = true;
    globalCols: any[];
    clonedCols: any[];
    userId: number;
    showPaginator: boolean = true;
    startIndex: number;
    showAddColumns = true;
    columns: any[];
    groupByList:any[] = [];
    groupBy:any[];
    exportCols: string[] = [];
    disableExport: boolean = true;
    
    searched: number = 0;
	
	dealArrangementNumbers: String[]  = [];
	
	currentPeriod : any = {};

    constructor(
        private ayaraAccountSummaryReportService: AyaraAccountSummaryReportService,
        private confirmationService: ConfirmationService,
        private formBuilder: FormBuilder,
        private notificationService:NotificationService,
        public _uploadService:UploadService,  private commonSharedService: CommonSharedService,
        private rmanLookupsVService: RmanLookupsVService,
        private rmanFiscalPeriodsService: RmanFiscalPeriodsService
    ) {

        // generate list code
        this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10' };

       this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, { 'lookupTypeName': 'ACCT_SUMMARY_GROUP' }).then((rmanLookupsVList: any) => {
            this.groupByList = rmanLookupsVList.content;
            this.prepareGroupByObject();
 		 });
 		
 		this.rmanFiscalPeriodsService.getCurrentfiscalperiod().then(response=>{
			this.currentPeriod = response;
		});
	 
  
    }

    ngOnInit() {
        this.globalCols = [
            
            { field: 'postedFlag', header: 'Posted Flag', showField: true, drag: false, display: "table-cell",type:'text'},
            { field: 'groupId', header: 'Group ID', showField: true, drag: false, display: "table-cell",type:'text'},
            { field: 'entityName', header: 'Entity Name', showField: true, drag: false, display: "table-cell",type:'text'},
            /*{ field: 'dealArrangementNumber', header: 'RC Number', showField: true, drag: false, display: "table-cell",type:'number'},
            { field: 'salesContract', header: 'Sales Contract#', showField: true, drag: true, display: "table-cell",type:'text' },
            { field: 'orderNumber', header: 'Order Number', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'orderLineNumber', header: 'Order Line Number', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 't4cFlag', header: 'T4C Flag', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 't4cFrequency', header: 'T4C Frequency', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'assetId', header: 'Asset ID#', showField: true, drag: true, display: "table-cell",type:'text'},
			*/
			{ field: 'assetNumber', header: 'Asset Number', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'periodName', header: 'Period Name', showField: true, drag: false, display: "table-cell",type:'number'},
            { field: 'currencyCode', header: 'Currency Code', showField: true, drag: true, display: "table-cell",type:'text' },
            { field: 'account', header: 'Account', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'accountDescription', header: 'Account Description', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'enteredDebitAmount', header: 'Entered Debit Amount', showField: true, drag: true, display: "table-cell",type:'round'},
            { field: 'enteredCreditAmount', header: 'Entered Credit Amount', showField: true, drag: true, display: "table-cell",type:'round'},
			{ field: 'converetedDebitAmount', header: 'Converted Debit Amount', showField: true, drag: true, display: "table-cell",type:'round'},
			{ field: 'converetedCreditAmount', header: 'Converted Credit Amount', showField: true, drag: true, display: "table-cell",type:'round'},
			{ field: 'segment1', header: 'Company', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'segment2', header: 'Region', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'segment3', header: 'Department', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'segment4', header: 'Account', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'segment5', header: 'Project', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'segment6', header: 'Revenue Category', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'segment7', header: 'Product Family', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'segment8', header: 'Product Line', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'segment9', header: 'Customer Type', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'segment10', header: 'Future1', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'segment11', header: 'Future2', showField: true, drag: true, display: "table-cell",type:'text'}
        ];

        this.columns = [];
        this.getTableColumns("ayaraAccountSummaryReport", "AyaraAccountSummaryReport");
    }

    
    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
          if (response && response != null && response.userId) {
            this.columns = [];
            let colsList = response.tableColumns.split(",");
            if (colsList.length > 0) {
              colsList.forEach((item, index) => {
                if (item) {
                  this.startIndex = this.globalCols.findIndex(col => col.field == item);
                  this.onDrop(index);
                }
              });
            }
            this.globalCols.forEach(col => {
              if (response.tableColumns.indexOf(col.field) !== -1) {
                this.columns.push(col);
              } else {
                col.showField = false;
              }
            });
            if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
            this.showPaginator = this.columns.length !== 0;
            this.userId = response.userId;
          } else {
            this.columns = this.globalCols;
          }
        }).catch(() => {
          this.notificationService.showError('Error occured while getting table columns data');
          this.loading = false;
        });
      }
    
      saveColumns() {
        let selectedCols = "";
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
          if (col.showField) {
            selectedCols += col.field;
            if (index < colLength) {
              selectedCols += ",";
            }
          }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns("ayaraAccountSummaryReport", "AyaraAccountSummaryReport", selectedCols, this.userId).then((response) => {
          this.columns = this.globalCols.filter(item => item.showField);
          this.userId = response["userId"];
          this.showPaginator = this.columns.length !== 0;
          this.loading = false;
        }).catch(() => {
          this.notificationService.showError('Error occured while getting data');
          this.loading = false;
        });
      }
    
      onDragStart(index: number) {
        this.startIndex = index;
      }
    
      onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
        //console.log(this.globalCols);
      }
    
      selectColumns(col: any) {
        let cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
          this.isSelectAllChecked = false;
        } else {
          this.isSelectAllChecked = true;
        }
      }
    
      onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
          if (this.isSelectAllChecked) {
            col.showField = true;
          } else {
            if (col.drag) {
              col.showField = false;
            }
          }
        });
      }
    
      onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
      }
    
      closeConfigureColumns(event: any) {
      this.showAddColumns = true;
      this.globalCols = this.clonedCols;
      let configCol = this.globalCols.filter(item => !item.showField);
      this.isSelectAllChecked = !(configCol.length > 0);
      }



    getAllAyaraAccountSummaryReport() {
        this.loading = true;
        this.ayaraAccountSummaryReportService.getAllayaraAccountSummaryReport(this.paginationOptions, this.searchFields, this.exportCols).then((ayaraAccountSummaryReportList: any) => {
            this.loading = false;
            this.datasource = ayaraAccountSummaryReportList.content;
            this.ayaraAccountSummaryReportList = ayaraAccountSummaryReportList.content;
            this.totalElements = ayaraAccountSummaryReportList.totalElements;
            this.pageSize = ayaraAccountSummaryReportList.size;
            this.displaySearchDialog = false;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    getAyaraAccountSummaryReport(event: any) {
        this.loading = true;
        let first: number = event.first;
        let rows: number = event.rows;
        let pageNumber: number = first / rows;
        this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
        this.ayaraAccountSummaryReportService.getAllayaraAccountSummaryReport(this.paginationOptions, this.searchFields, this.exportCols).then((ayaraAccountSummaryReportList: any) => {
            this.loading = false;
            this.datasource = ayaraAccountSummaryReportList.content;
            this.ayaraAccountSummaryReportList = ayaraAccountSummaryReportList.content;
            this.totalElements = ayaraAccountSummaryReportList.totalElements;
            this.pageSize = ayaraAccountSummaryReportList.size;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });

    }
    
    exportExcel() {
	    this.exportCols = [];
	    for (let index = 0; index < this.columns.length; index++) {
	      if (this.columns[index].showField) {
	        this.exportCols.push(this.columns[index].field);
	      }
	    }
	    
	    let serviceUrl = this.ayaraAccountSummaryReportService.getServiceUrl(this.paginationOptions, this.searchFields, 1, this.exportCols);
	    window.location.href = serviceUrl;
   }


    
   onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }


    reset(dt: Table) {
        this.paginationOptions = {};
        this.ayaraAccountSummaryReport = new AyaraAccountSummaryReportImpl();
        this.searchFields = {};
        dt.reset();
        this.searched = 0;
    }

	onRowSelect(event: any) {

    }

     prepareGroupByObject() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectGroupBy, value: null }];
        this.groupByList.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode });
        });

        this.groupBy = rmanLookupsVTempObj;

    }
    
    
    
    hideColumnMenu: boolean = true;

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    showFilter: boolean = false;

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }
    showDialogToSearch() {
        this.searchFields = {};
        this.displaySearchDialog = true;
        if(this.isSerached==0){
				this.searchFields.startDate = new Date(this.currentPeriod.startDate);
				this.searchFields.endDate = new Date(this.currentPeriod.endDate);
		}
    }
    
    searchRCList(event:any){
	  this.ayaraAccountSummaryReportService.fetchRCNumbers(event.query).then((response: any) => {
	  	this.dealArrangementNumbers= response.map(item => item);
	  }).catch(err => {
      this.dealArrangementNumbers = [];
    });  
   }									 
    
	search() {
		this.paginationOptions={};
		this.displaySearchDialog = false;
        this.getAllAyaraAccountSummaryReport();
        this.searched = 1;
    }
    
    
}


export class AyaraAccountSummaryReportImpl implements AyaraAccountSummaryReport {
    constructor(public dealArrangementNumber?: any,public salesContract?: any,public orderNumber?: any,public orderLineNumber?: any,public orderLineId?: any,public t4cFlag?: any,public t4cFrequency?: any,public assetId?: any,public assetNumber?: any,public periodName?: any,public currencyCode?: any,public account?: any,public accountDescription?: any,public enteredDebitAmount?: any,public enteredCreditAmount?: any,public converetedDebitAmount?: any,public converetedCreditAmount?: any,public segment1?: any,
	public segment2?: any,public segment3?: any,public segment4?: any,public segment5?: any,public segment6?: any,public segment7?: any,public segment8?: any,public segment9?: any,public segment10?: any,public segment11?: any) { }
}

interface ILabels {
    [index: string]: string;
}
