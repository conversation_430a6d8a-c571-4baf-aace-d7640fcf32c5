<div class="content-section implementation">
</div>
<!--
<div class="card-wrapper">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card-block"> -->
          <p-panel header="Account Summary Report" [toggleable]="false" (onBeforeToggle)="onBeforeToggle($event)">
            <p-header>

              <div class="pull-right icons-list" *ngIf="collapsed">
                  <a (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
                <a (click)="showDialogToSearch()" title="Search">
                  <em class="fa fa-search"></em>
                </a>
                <a (click)="reset(dt)" title="Reset">
                  <em class="fa fa-refresh"></em>
                </a>
                <ng-container *ngIf="!disableExport">
					<a (click)="exportExcel()" title="Export">
						<em class="fa fa-external-link"></em>
					</a>
				</ng-container>

                <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
                    <div class="user-popup">
                      <div class="content overflow">
                        <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()"/>
                        <label for="selectall">Select All</label>
                        <a class="close" title="Close" (click)="closeConfigureColumns($event)" >&times;</a>
                        <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                          <ng-template let-col let-index="index" pTemplate="item">
                            <div *ngIf="col.drag">
                            <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" 
                             (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                              <div class="drag">
                                <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)"/>
                                <label>{{col.header}}</label>
                              </div>
                            </div>
                            </div>
                            <div *ngIf="!col.drag">
                            <div class="ui-helper-clearfix">
                              <div>
                                <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"/>
                                <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                              </div>
                            </div>
                            </div>
                          </ng-template>
                          </p-listbox>
                
                      </div>
                
                      <div class="pull-right">
                        <a class="configColBtn" (click)="saveColumns()">Save</a>
                        <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
                      </div>
                
                    </div>
                  </div>

              </div>
            </p-header>

            <div class="x-scroll">
              <p-table class="ui-datatable arrangementMgrTbl" #dt id="ayaraAccountSummaryReport-dt" [loading]="loading" [value]="ayaraAccountSummaryReportList"
                selectionMode="single" (onRowSelect)="onRowSelect($event)" (onLazyLoad)="getAyaraAccountSummaryReport($event)" [lazy]="true"
                [paginator]="true" [rows]="pageSize" [totalRecords]="totalElements" scrollable="true" [columns]="columns" [resizableColumns]="true" columnResizeMode="expand" >
                
                <ng-template pTemplate="colgroup" let-columns>
                    <colgroup>
                      <col *ngFor="let col of columns">
                    </colgroup>
                  </ng-template>
    
                   
                  <ng-template pTemplate="header" class="arrangementMgrTblHead">
                    <tr>
                      <ng-container *ngFor="let col of columns">
                        <th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                        <th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                      </ng-container>
                    </tr>
                  </ng-template>
    
                  <ng-template pTemplate="body" let-rowData let-ayaraAccountSummaryReport let-columns="columns">
                    <tr [pSelectableRow]="rowData" [pSelectableRowIndex]="rowIndex">
                     
                      <ng-container *ngFor="let col of columns" >
                        <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                          {{rowData[col.field]}}
                        </td>
                      
                        <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
                          {{rowData[col.field]}}
                        </td>
                      
                        <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
                          {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                        </td>
                      
                        <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
                          {{rowData[col.field] | round}}
                        </td>
                      </ng-container>
                            
                    </tr>                 
                  </ng-template>
                <ng-template pTemplate="emptymessage" let-columns>
                  <div class="no-results-data">
						<p>{{noData}}</p>
				  </div>
                </ng-template>
              </p-table>
            </div>
          </p-panel>
 <!--       </div>
      </div>
    </div>
  </div>
</div> -->


<p-dialog header="Search" width="auto" [(visible)]="displaySearchDialog" showEffect="fade" [draggable]="true" [modal]="true" [blockScroll]="true">
  <form>
    <div class="ui-grid ui-grid-responsive ui-fluid">
      <div class="ui-g-12">
           <div class="ui-g-6">
              <span class="selectSpan">From Period</span>
              <p-calendar showAnim="slideDown" inputStyleClass="textbox" name="fromPeriod" id="fromPeriod" [monthNavigator]="true" [yearNavigator]="true"
                                    yearRange="1950:2030" appendTo="body" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.startDate"
                                    dateFormat="yy-mm-dd" placeholder="From Period"></p-calendar>
            </div>

            <div class="ui-g-6 pull-right">
                <span class="selectSpan">To Period</span>
                <p-calendar showAnim="slideDown" name="toPeriod" inputStyleClass="textbox" id="toPeriod" [monthNavigator]="true" [yearNavigator]="true"
                                    yearRange="1950:2030" appendTo="body" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.endDate"
                                    dateFormat="yy-mm-dd" placeholder="To Period"></p-calendar>
            </div>
	  </div>
      <!--<div class="ui-g-12">
        <div class="ui-g-6">
          <span class="md-inputfield">
            <span class="selectSpan">Account Summary Group By</span>
            <p-dropdown [options]="groupBy"  appendTo="body" [(ngModel)]="searchFields.groupBy" [ngModelOptions]="{standalone: true}"  name="geo" ></p-dropdown>
          </span>
        </div>
        <div class="ui-g-6 pull-right">
          <span class="md-inputfield">
            <span class="selectSpan">RC Number</span>
             <p-autoComplete  inputStyleClass="textbox" [suggestions]="dealArrangementNumbers" 
										(completeMethod)="searchRCList($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								 placeholder="RC Number Type atleast 3 Characters" id="rcNumber" [(ngModel)]="searchFields.rcNumber"></p-autoComplete>
 
          </span>
        </div>
      </div>
	  <div class="ui-g-12">
        <div class="ui-g-6">
          <span class="md-inputfield">
            <span class="selectSpan">Sales Contract#</span>
            <input pInputText name="salesContract" id="salesContract" class="textbox" placeholder="Sales Contract#" [ngModelOptions]="{standalone: true}"
              [(ngModel)]="searchFields.salesContract" />
          </span>
        </div>
		<div class="ui-g-6 pull-right">
          <span class="md-inputfield">
            <span class="selectSpan">Order Number</span>
            <input pInputText name="so" id="so" class="textbox" placeholder="Order Number" [ngModelOptions]="{standalone: true}"
              [(ngModel)]="searchFields.so" />
          </span>
        </div>
      </div>
	  <div class="ui-g-12">
        <div class="ui-g-6">
          <span class="md-inputfield">
            <span class="selectSpan">Order Line Number</span>
            <input pInputText name="orderLine" id="orderLine" class="textbox" placeholder="Order Line Number" [ngModelOptions]="{standalone: true}"
              [(ngModel)]="searchFields.orderLine" />
          </span>
        </div>
		<div class="ui-g-6 pull-right">
          <span class="md-inputfield">
            <span class="selectSpan">Asset ID#</span>
            <input pInputText name="assetId" id="assetId" class="textbox" placeholder="Asset ID#" [ngModelOptions]="{standalone: true}"
              [(ngModel)]="searchFields.assetId" />
          </span>
        </div>
      </div> -->
    </div>
  </form>
  <p-footer>
    <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
      <button type="submit" pButton class="primary-btn" label="Search" (click)="search()"></button>
      <button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>
    </div>
  </p-footer>

</p-dialog>

<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>