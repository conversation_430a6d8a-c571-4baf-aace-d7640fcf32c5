export interface RmanDealHeaders {
    endCustomerName :any;
    financeContactId :any;
    createdDate :any;
    priceList :any;
    analystId :any;
    customerType :any;
    partnerName :any;
    billToCustomerNum :any;
    dealType :any;
    salesChannel :any;
    salesTeritory :any;
    billToCountry :any;
    opportunityNum :any;
    dealCurrencyCode :any;
    approverId :any;
    conversionType :any;
    financeContact :any;
    paymentTerms :any;
    opportunityName :any;
    endCustomerNum :any;
    dealNumber :any;
    billToCustomerName :any;
    dealName :any;
    agreementName :any;
    createdBy :any;
    attribute3 :any;
    lastUpdatedBy :any;
    attribute2 :any;
    attribute1 :any;
    legalEntityId :any;
    conversionRate :any;
    dealHeaderId :any;
    dealArrangementId :any;
    attribute5 :any;
    forecastCode :any;
    attribute4 :any;
    dealStatus :any;
    agreementNumber :any;
    lastUpdatedDate :any;
    salesOpsName :any;
    shipToCountry :any;
    agreementCode :any;
    salesrepName :any;
    conversionDate :any;
    contractStartDate :any;
    contractEndDate :any;
}
