<div class="content-section implementation">
</div>
<div class="card-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block">

                    <h2>Accounting Periods</h2>

                    <div class="pull-right icons-list">
                        <a (click)="onConfiguringColumns($event)" class="add-column">
                            <em class="fa fa-cog"></em>Columns</a>
                        <a *isAuthorized="['write','MAP']" (click)="showDialogToAdd()" title="Add">
                            <em class="fa fa-plus-circle"></em>
                        </a>
                        <a *isAuthorized="['upload','MAP']" (click)="importFile()" title="Import">
                            <em class="fa fa-upload"></em>
                        </a>
                        <a (click)="showDialogToSearch()" title="Search">
                            <em class="fa fa-search"></em>
                        </a>
                        <a (click)="reset(dt)" title="Reset">
                            <em class="fa fa-refresh"></em>
                        </a>
                        <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
                            <div class="user-popup">
                                <div class="content overflow">
                                    <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()" />
                                    <label for="selectall">Select All</label>
                                    <a class="close" title="Close" (click)="closeConfigureColumns($event)">&times;</a>
                                    <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                                        <ng-template let-col let-index="index" pTemplate="item">
                                            <div *ngIf="col.drag">
                                                <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                                                    <div class="drag">
                                                        <input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" />
                                                        <label>{{col.header}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div *ngIf="!col.drag">
                                                <div class="ui-helper-clearfix">
                                                    <div>
                                                        <input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"
                                                        />
                                                        <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </ng-template>
                                    </p-listbox>

                                </div>

                                <div class="pull-right">
                                    <a class="configColBtn" (click)="saveColumns()">Save</a>
                                    <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="x-scroll">
                        <p-table [loading]="loading" #dt class="ui-datatable arrangementMgrTbl" id="accountingPeriods-dt" [value]="rmanFiscalPeriodsList"
                            selectionMode="single" (onRowSelect)="onRowSelect($event)" (onLazyLoad)="getRmanFiscalPeriods($event)"
                            [lazy]="true" [paginator]="true" [rows]="pageSize" [totalRecords]="totalElements" scrollable="true"
                            [columns]="columns" [resizableColumns]="true" columnResizeMode="expand">

                            <ng-template pTemplate="colgroup" let-columns>
                                <colgroup>
                                    <col>
                                    <col *ngFor="let col of columns">
                                </colgroup>
                            </ng-template>


                            <ng-template pTemplate="header" class="arrangementMgrTblHead">
                                <tr>
                                    <th></th>
                                    <ng-container *ngFor="let col of columns">
                                        <th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                                        <th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}"
                                            title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                                    </ng-container>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-rowData let-rmanFiscalPeriods let-columns="columns">
                                <tr [pSelectableRow]="rowData" [pSelectableRowIndex]="rowIndex">
                                    <td>
                                        <a *isAuthorized="['write','MAP']" (click)="editRow(rmanFiscalPeriods)" class="icon-edit" title="Edit"> </a>
                                        <a *isAuthorized="['write','MAP']" (click)="delete(rmanFiscalPeriods)" class="icon-delete" title="Delete"> </a>

                                    </td>
                                    <ng-container *ngFor="let col of columns">
                                        <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field]}}
                                        </td>

                                        <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field]}}
                                        </td>

                                        <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                                        </td>

                                        <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field] | round}}
                                        </td>
                                    </ng-container>

                                </tr>
                            </ng-template>

                            <ng-template pTemplate="emptymessage" let-columns>
                                <tr *ngIf="!columns">
                                    <td class="no-data">{{noData}}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<div class="lds-roller" style="top: 0px;" *ngIf="uploadLoading">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div>
<p-dialog header="Upload  Accounting Periods " width="500" [(visible)]="_uploadService.uploadDialog" [draggable]="true"
    showEffect="fade" [modal]="true" (onAfterHide)="cancelPeriodsUpload()">
    <p-fileUpload id="upload-bookings-input" name="file" customUpload="true" (uploadHandler)="fileUploadHandler($event, '/upload/periods')"
        accept=".csv" invalidFileTypeMessageSummary="{0}:Invalid file Type">
    </p-fileUpload>
    <progress-bar></progress-bar>
</p-dialog>
<p-dialog header="Search" width="800" [(visible)]="displaySearchDialog" [draggable]="true" showEffect="fade" [modal]="true"
    [blockScroll]="true" (onHide)="cancelSearch()">
    <form>
        <div class="ui-grid ui-grid-responsive ui-fluid">
            <div class="ui-g-12">
                <div class="ui-g-6">
                    <span class="md-inputfield">
                        <span class="selectSpan">Ledger Name</span>
                        <input class="textbox" placeholder="Ledger Name" pInputText name="ledgerName" id="ledgerName"
                            [(ngModel)]="rmanFiscalPeriodsSearch.ledgerName" />
                    </span>
                </div>
                <div class="ui-g-6 pull-right">
                    <span class="md-inputfield">
                        <span class="selectSpan">Period Name</span>
                        <input class="textbox" placeholder="Period Name" pInputText name="periodName" id="periodName"
                            [(ngModel)]="rmanFiscalPeriodsSearch.periodName" />
                    </span>
                </div>
            </div>
        </div>

    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" pButton class="primary-btn" label="Search" (click)="search()"></button>
            <button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>

<p-dialog header="{{(newRmanFiscalPeriods) ? 'Define Fiscal Period' : 'Edit Fiscal Period'}}" width="800" [(visible)]="displayDialog"
    [draggable]="true" showEffect="fade" [modal]="true" [blockScroll]="true" (onHide)="cancelAddEdit()">
    <form (ngSubmit)="save()" [formGroup]="fiscalPeriodsForm" novaldiate>
        <div class="ui-grid ui-grid-responsive ui-fluid" *ngIf="rmanFiscalPeriods">
            <div class="ui-g-12">
                <div class="ui-g-6">
                    <div class="ui-g-12">
                        <span class="selectSpan"> Period Status </span>
                        <p-dropdown [options]="rmanLookupsV" [(ngModel)]="rmanFiscalPeriods.periodStatus" [ngModelOptions]="{standalone: true}" (onChange)="onSelectStatus($event.value)"
                            name="periodStatus" [filter]="true"></p-dropdown>
                    </div>
                    <div class="ui-g-12">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Period Number
                                <span class="red-color">*</span>
                            </span>
                            <input pInputText class="textbox" placeholder="Period Number" name="periodNum" id="periodNum"
                                [(ngModel)]="rmanFiscalPeriods.periodNum" formControlName="periodNum" />
                            <div *ngIf="formErrors.periodNum" class="ui-message ui-messages-error ui-corner-all">
                                {{ formErrors.periodNum }}
                            </div>
                        </span>
                    </div>
                    <div class="ui-g-12">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Period Year
                                <span class="red-color">*</span>
                            </span>
                            <input pInputText class="textbox" placeholder="Period Year" name="periodYear" id="periodYear"
                                [(ngModel)]="rmanFiscalPeriods.periodYear" formControlName="periodYear" />
                            <div *ngIf="formErrors.periodYear" class="ui-message ui-messages-error ui-corner-all">
                                {{ formErrors.periodYear }}
                            </div>
                        </span>
                    </div>
                    <div class="ui-g-12">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Period Name
                                <span class="red-color">*</span>
                            </span>
                            <input pInputText class="textbox" placeholder="Period Name" name="periodName" id="periodName"
                                [(ngModel)]="rmanFiscalPeriods.periodName" formControlName="periodName" />
                            <div *ngIf="formErrors.periodName" class="ui-message ui-messages-error ui-corner-all">
                                {{ formErrors.periodName }}
                            </div>
                        </span>
                    </div>
                    <div class="ui-g-12">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Quarter Name
                                <span class="red-color">*</span>
                            </span>
                            <input pInputText class="textbox" placeholder="Quarter Name" name="quarterName" id="quarterName"
                                [(ngModel)]="rmanFiscalPeriods.quarterName" formControlName="quarterName" />
                            <div *ngIf="formErrors.quarterName" class="ui-message ui-messages-error ui-corner-all">
                                {{ formErrors.quarterName }}
                            </div>
                        </span>
                    </div>
                    <div class="ui-g-12">
                        <span class="selectSpan">Year Start Date</span>
                        <p-calendar showAnim="slideDown" inputStyleClass="textbox" appendTo="body" name="yearStartDate" id="yearStartDate" [monthNavigator]="true"
                            [yearNavigator]="true" yearRange="1950:2030" [(ngModel)]="rmanFiscalPeriods.yearStartDate" [ngModelOptions]="{standalone: true}"
                            dateFormat="yy-mm-dd" placeholder="Year Start Date"></p-calendar>
                    </div>
                </div>
                <div class="ui-g-6">
                    <div class="ui-g-12">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Ledger Name
                                <span class="red-color">*</span>
                            </span>
                            <input pInputText class="textbox" placeholder="Ledger Name" name="ledgerName" id="ledgerName"
                                [(ngModel)]="rmanFiscalPeriods.ledgerName" formControlName="ledgerName" />
                            <div *ngIf="formErrors.ledgerName" class="ui-message ui-messages-error ui-corner-all">
                                {{ formErrors.ledgerName }}
                            </div>
                        </span>
                    </div>
                    <div class="ui-g-12">
                        <span class="selectSpan">Start Date</span>
                        <p-calendar showAnim="slideDown" inputStyleClass="textbox" name="startDate" id="startDate" [monthNavigator]="true" appendTo="body"
                            [yearNavigator]="true" yearRange="1950:2030" [(ngModel)]="rmanFiscalPeriods.startDate" [ngModelOptions]="{standalone: true}"
                            dateFormat="yy-mm-dd" placeholder="Start Date"></p-calendar>
                    </div>
                    <div class="ui-g-12">
                        <span class="selectSpan">End Date</span>
                        <p-calendar showAnim="slideDown" inputStyleClass="textbox" appendTo="body" name="endDate" id="endDate" [monthNavigator]="true"
                            [yearNavigator]="true" yearRange="1950:2030" [(ngModel)]="rmanFiscalPeriods.endDate" [ngModelOptions]="{standalone: true}"
                            dateFormat="yy-mm-dd" placeholder="End Date"></p-calendar>
                    </div>
                    <div class="ui-g-12">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Adjustement Period Flag
                                <span class="red-color">*</span>
                            </span>
                            <input pInputText class="textbox" name="adjustmentPeriodFlag" id="adjustmentPeriodFlag" [(ngModel)]="rmanFiscalPeriods.adjustmentPeriodFlag"
                                formControlName="adjustmentPeriodFlag" />
                            <div *ngIf="formErrors.adjustmentPeriodFlag" class="ui-message ui-messages-error ui-corner-all">
                                {{ formErrors.adjustmentPeriodFlag }}
                            </div>
                        </span>
                    </div>
                    <div class="ui-g-12">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Quarter Number
                                <span class="red-color">*</span>
                            </span>
                            <input pInputText class="textbox" placeholder="Quarter Number" name="quarterNum" id="quarterNum"
                                [(ngModel)]="rmanFiscalPeriods.quarterNum" formControlName="quarterNum" />
                            <div *ngIf="formErrors.quarterNum" class="ui-message ui-messages-error ui-corner-all">
                                {{ formErrors.quarterNum }}
                            </div>
                        </span>
                    </div>
                    <div class="ui-g-12">
                        <span class="selectSpan">Quarter Start Date</span>
                        <p-calendar showAnim="slideDown" inputStyleClass="textbox" appendTo="body" name="quarterStartDate" id="quarterStartDate"
                            [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" [(ngModel)]="rmanFiscalPeriods.quarterStartDate"
                            [ngModelOptions]="{standalone: true}" dateFormat="yy-mm-dd" placeholder="Quarter Start Date"></p-calendar>
                    </div>
                </div>

            </div>
        </div>
    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" pButton class="primary-btn" label="Save" (click)="save()" [disabled]="!fiscalPeriodsForm.valid"></button>
            <button type="button" pButton class="secondary-btn" (click)="displayDialog=false" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>

<div class="modal fade dialogueBox" id="FieldsMandatory" role="dialog" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">{{'Error'}}</h4>
            </div>
            <form class="form-horizontal">
                <div class="modal-body clearfix">
                    <div class="col-md-12 col-sm-12 col-xs-12 col-lg-12">

                        <p *ngIf="showMsg==16">Please enter Period Number</p>
                        <p *ngIf="showMsg==17">Please enter Period Year</p>
                        <p *ngIf="showMsg==18">Please enter Period Name</p>
                        <p *ngIf="showMsg==19">Please enter Ledger Name</p>
                        <p *ngIf="showMsg==20">Please enter Start Date</p>
                        <p *ngIf="showMsg==21">Please enter End Date</p>


                    </div>

                </div>
                <div class="modal-footer" style="padding:3px;">
                    <button class="btn btn-primary pull-right" data-dismiss="modal">OK</button>
                </div>
            </form>
        </div>
    </div>
</div>
<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>