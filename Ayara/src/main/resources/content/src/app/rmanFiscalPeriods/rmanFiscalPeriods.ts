export interface RmanFiscalPeriods {
    quarterStartDate: any;
    createdBy: any;
    lastUpdatedBy: any;
    periodYear: any;
    adjustmentPeriodFlag: any;
    creationDate: any;
    endDate: any;
    startDate: any;
    quarterNum: any;
    sourcePeriodId: any;
    lastUpdateDate: any;
    periodNum: any;
    ledgerName: any;
    quarterName: any;
    periodName: any;
    enteredPeriodName: any;
    periodStatus: any;
    yearStartDate: any;
    rmanPeriodId: any;
    openFlag: any;
}
