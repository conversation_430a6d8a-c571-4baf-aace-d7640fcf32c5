interface ILabels {
    [index: string]: string;
}

export class RmanReconSosrcToBkingsRepLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["sourceLineNumber"] = "SO Line #";
        this.fieldLabels["sourceLineId"] = "Source Line Id";
        this.fieldLabels["arrangementNumber"] = "Linked Arrangement #";
        this.fieldLabels["exception"] = "Exceptions";
        this.fieldLabels["bookedDate"] = "SO Booked Date";
        this.fieldLabels["trgQty"] = "Quantity (Destination)";
        this.fieldLabels["srcUnitPrice"] = "Unit Price (Source)";
        this.fieldLabels["trgUnitPrice"] = "Unit Price (Destination)";
        this.fieldLabels["arrangementLineNum"] = "Linked Arrangement Line #";
        this.fieldLabels["srcQty"] = "Quantity (Source)";
        this.fieldLabels["srcAmount"] = "Amount (Source)";
        this.fieldLabels["bookingEntityName"] = "Entity";
        this.fieldLabels["trgAmount"] = "Amount (Destination)";
        this.fieldLabels["so"] = "SO #";
        this.fieldLabels["endCustomer"] = "Customer Name";
        this.fieldLabels["sku"] = "Product Name";
    }

}
