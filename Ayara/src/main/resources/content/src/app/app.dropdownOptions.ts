export const dropDownMessages = {
    selectentity: 'Select Entity Category(*)',
    selectobjectType: 'Select Object Type(*)',
    selectDeal: 'Select Deal Flag(*)',
    selectAudit: 'Select Audit Flag(*)',
    selectPost: 'Select Post Booking(*)',
    selectEnabledFlag: 'Select Enabled Flag(*)',
    selectParameter: 'Select Parameter Name(*)',
    selectLegalEntity: 'Select Legal Entity',
    selectManager: 'Select Manager',
    selectSource: 'Select Source',
    // selectResponsibility: 'Select Responsibility ID(*)',
    selectResponsibility: 'Select Responsibility Name(*)',
    selectAllocation: 'Select Allocation Flag',
    selectInvoice: 'Select Invoice Enabled Flag',
    selectRevTemp: 'Select Revenue Template Name',
    selectProdOrg: 'Select Product Organization(*)',
    selectBundled: 'Select Bundled Item(*)',
    selectInvoicable: 'Select Invoicable Item',
    selectAccounting: 'Select Accounting Trigger COGS',
    selectRuleRevenue: 'Select Accounting Rule Revenue(*)',
    selectTriggerRevenue: 'Select Accounting Trigger Revenue(*)',
    selectAmortization: 'Select Amortization Method COGS',
    selectAmortizationMethod: 'Select Amortization Method Revenue(*)',
    selectRuleCOGS: 'Select Accounting Rule COGS',
    selectAmortizationRule: 'Select Amortization Rule Revenue(*)',
    selectAmortizationRuleCOGS: 'Select Amortization Rule COGS',
    selectActiveFlag: 'Select Active Flag(*)',
    selectBundleFlag: 'Select Bundle Flag',
    LegalEntityID: 'Select Legal Entity ID(*)',
    selectStat: 'Select Status(*)',
    selectAllocationCurrency: 'Select Allocation Currency',
    selectCurrencyCode: 'Select Currency Code',
    selectReporting: 'Select Reporting Currency',
    selectFunctional: 'Select Functional Currency',
    selectFlagEnabled: 'Select Enabled Flag',
    selectFromCurrency: 'Select From Currency',
    selectToCurrency: 'Select To Currency',
    SelectStatus: 'Select Status (*)',
    SelectStandalone: 'Select Standalone Sub SKU',
    SelectMapping: 'Select Mapping Type',
    SelectSplit: 'Select Split Type',
    SelectPeriodStatus:'Select Period Status',
    SelectInvoiceHold:'Select Invoice Hold',
    SelectApplyType:'Select Apply Type',
    SelectRuleCategory:'Select Rule Category',
    SelectRuleApply:'Select Rule Apply Level',
    selectArrangementCurrency: 'Select Arrangement Currency',
    selectMasterArrgFlag: 'Select Master Arrangement Flag',
    selectSalesTheater: 'Select Sales Theater',
    selectSalesRegion: 'Select Sales Region',
    selectSalesTeritory: 'Select Sales Teritory',
    selectDealClosedFlag: 'Select Deal Closed Flag',
    selectDealArrangementSource: 'Select Deal Arrangement Source',
    selectDealArrangementBasis: 'Select Deal Arrangement Basis',
    // selectRevManagerID: 'Select Rev Manager ID',
    // selectRevAccountantID: 'Select Rev Accountant ID',
    selectRevManagerID: 'Select Rev Manager',
    selectRevAccountantID: 'Select Rev Accountant',
    selectCustomer: 'Select Customer Name',
    selectMasterArrangement: 'Select Master Arrangement',
    selectLegalEntityName: 'Select Legal Entity',
    selectFMVCategory: 'Select SSP Category(*)',
    selectFMVType: 'Select SSP Type(*)',
    selectFMVRuleStatus:'Select SSP Rule Status',
    SelectFMVCreateRuleParameterName:'Select Parameter Name',
    SelectFMVCreateRuleParameterAndOr:'Select AND/OR(*)',
    SelectFMVCreateRuleParameterQualifier:'Select Qualifier',
    SelectContingencyCode: 'Select Contingency Code',
    SelectRevRecHoldApplyLevel: 'Select Revrec Hold Apply Level',
    selectFunctionName: 'Select Function Name',
    selectModuleName: 'Select Module Name',
    selectYear: 'Select Year',
    selectPeriod: 'Select Period',
    selectedPeriodName: 'Select Month',
    selectQuarterName: 'Select Quarter',
    selectLtStFlag: 'Select LT/ST Flag',
    selectUom:'Select Unit of Measurement',
    selectItemStatus:'Select Item Status',
    selectInvoiceRule:'Select Invoicing Rule',
    selectProductType:'Select Product Type',
    selectCalculateSsp: 'Select SSP Calculation Type ',
    selectBSIDate:'Select Date Type',
    selectSSPAmount:'Select Amount Type',
    selectStatus:'Select Status',
    selectRuleName:'Select Rule Name',
    selectConversionType:'Select Conversion Type',
    selectItemCategory: 'Select Item Category',
    selectTriggeringEvent: 'Select Triggering Event',
	selectQuoteStatus:'Select Quote Status',
	selectDocumentType:'Select Document Type',
	selectUserType: 'Select User Type',
	selectGeo: 'Select Geo',
	selectProductGroup: 'Select Product Group',
	selectProductFamily: 'Select Product Family',
	selectProductLine: 'Select Product Line',
	selectProductModel: 'Select Product Model',
	selectRegion: 'Select Region',
	selectCustomerType: 'Select Customer Type',
	selectGroupBy: 'Select Group By'
}