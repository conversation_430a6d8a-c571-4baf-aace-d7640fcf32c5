/* ✅ General filter form field layout */
.filter-form {
    width: 100% !important;
    margin: 6px 0 !important;
    box-sizing: border-box;
}

/* ✅ Grid for 2 filters per row */
.form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3 per row */
    gap: 1rem;
    margin-bottom: 0.5rem;
}

/* ✅ Form group layout */
.form-group {
    display: flex;
    flex-direction: column;
    width: 100%;
}

/* ✅ Labels */
.form-label {
    margin-left: 6px !important;
    color: #262d74 !important;
    font-weight: 600 !important;
}

/* ✅ Error message styling */
.error-message {
    color: red;
    font-size: 0.9em;
    margin-top: 2px;
}

/* Ensure all input-like components share the same height, padding, and box model */
p-autoComplete,
p-dropdown,
p-calendar,
input[type="text"],
input.p-inputtext {
    width: 100% !important;
    box-sizing: border-box !important;
    display: block;
    font-size: 14px; /* Match text size */
    font-family: inherit;
}

/* Apply same styles to the actual input inside p-autoComplete */
.p-autoComplete .p-inputtext {
    width: 100% !important;
    padding: 0.5rem !important;
    font-size: 14px;
    box-sizing: border-box !important;
    min-height: 38px; /* Match height with dropdown and calendar */
    border-radius: 4px;
    border: 1px solid #ccc; /* Match border style */
}

/* Optional: hover/focus consistency */
p-autoComplete .p-inputtext:focus {
    border-color: #262d74;
    outline: none;
    box-shadow: 0 0 0 0.1rem rgba(38, 45, 116, 0.25);
}

/* ✅ Toggle Switch */
.outer {
    width: 45px;
    height: 20px;
    border-radius: 10px;
    background-color: #262d74;
    position: relative;
    display: flex;
    align-items: center;
    padding: 3px;
    box-shadow: inset 0px 0px 5px rgb(189, 189, 189);
    margin: 0px 15px;
}
.inner {
    width: 15px;
    height: 15px;
    border-radius: 10px;
    background-color: rgb(255, 255, 255);
    position: absolute;
    transition: transform 0.15s linear;
}
.logDetails {
    transform: translateX(23px);
}
.toggler {
    display: flex;
    padding: 0px 10px;
}
.no-data-found-message{
  color: #262d74;
  padding: 3px 600px;
  font-weight: 600;
}
::ng-deep .p-autocomplete-input{
  width: 100% !important;
}
::ng-deep .p-autocomplete{
  width: 100% !important;
}