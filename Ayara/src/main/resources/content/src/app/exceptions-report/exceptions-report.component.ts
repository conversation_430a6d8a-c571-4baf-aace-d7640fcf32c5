import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ReportsService } from '../reports/reportsservice';
import { Table } from 'primeng/table';
import { LoadingService } from '../shared/loading.service';
import { RmanUsersService } from '../rmanUsers/rmanUsersservice';
import { RmanLookupsVService } from '../rmanLookupsV/rmanLookupsVservice';
import { Location } from '@angular/common';

@Component({
  selector: 'app-exceptions-report',
  templateUrl: './exceptions-report.component.html',
  styleUrls: ['./exceptions-report.component.css']
})
export class ExceptionsReportComponent implements OnInit {
  filters: FormGroup;
  logDetails: boolean = false;
  filteredData: any;
  showPaginator: boolean = true;
  columns: any[] = [];
  displayAdvancedSearch: boolean = true;
  totalRmanUsersList: any;
  filteredUserList: any[] = [];
  tableVisible: boolean = false;
  exceptionEvents = ['Modified', 'Reprocessed', 'Deleted'].map(event => ({
    label: event,
    value: event.toLowerCase()
  }));
  recordTypes = [];
  sourceSystemDropdown=[];
  paginationOptions: { pageNumber: number; pageSize: number; first: number };
  totalElements: any;
  pageSize: any;
  userValueResults = [];
  usernames: any = [];
  data: any[] = [];
  noDataFoundMessage='';
  minDate:Date;
  maxDate:Date=new Date();

  constructor(
    private formBuilder: FormBuilder,
    private reportsService: ReportsService,
    private rmanUsersService: RmanUsersService,
    public loadingService: LoadingService,private location: Location,
    private cd: ChangeDetectorRef, private rmanLookupsVService:RmanLookupsVService
  ) {
    this.paginationOptions = { pageNumber: 0, pageSize: 10, first: 0 };
    this.filters = this.formBuilder.group({
      userName: [''],
      recordType: [''],
      startDate: [''],
      endDate: [''],
      sourceSystem: [''],
      exceptionEvent: ['']
    });
    this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, { 'lookupTypeName': 'ARRG_SOURCE' }).then((rmanLookupsVList: any) => {
      this.sourceSystemDropdown = rmanLookupsVList.content
        .map((item: any) => ({
          label: item.lookupCode,
          value: item.lookupCode
        }));
    });
    this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, { 'lookupTypeName': 'DOCUMENT_CATEGORY' }).then((rmanLookupsVList: any) => {
      this.recordTypes = rmanLookupsVList.content
        .map((item: any) => ({
          label: item.lookupDescription,
          value: item.lookupCode
        }));
    });


    this.filters.valueChanges.subscribe(data => this.onValueChanged(data));
    this.onValueChanged();
    this.getAllUsers();
  }
  goToOperationReports() {
    this.location.back();
  }

  getAllUsers() {
    this.rmanUsersService.getAllRmanUsers(this.paginationOptions, {}, true).then((totalRmanUsersList: any) => {
      this.totalRmanUsersList = totalRmanUsersList.content;
      for (let user of totalRmanUsersList.content) {
        this.usernames.push(user.userName);
      }
    }).catch((err: any) => {
      // handle error
    });
  }

  ngOnInit() {
    this.onValueChanged();
    this.noDataFoundMessage='';
  }

  onValueChanged(data?: any) {
    if (!this.filters) { return; }
    const form = this.filters;

    for (const field in this.formErrors) {
      this.formErrors[field] = '';
      const control = form.get(field);

      if (control && control.dirty && !control.valid) {
        const messages = this.validationMessages[field];
        for (const key in control.errors) {
          this.formErrors[field] += messages[key] + ' ';
        }
      }
    }
  }

  formErrors = {
    'userName': '', 'recordType': '', 'startDate': '', 'endDate': '', 'exceptionEvent': ''
  };

  validationMessages = {
    'recordType': {
      'required': 'Record Type is required'
    }
  };
  setMinDate(){
    this.minDate=this.filters.value.startDate;
  }
  setMaxDate(){
    this.maxDate=this.filters.value.endDate;
  }

  toggleFilterType() {
    this.logDetails = !this.logDetails;

    const recordTypeControl = this.filters.get('recordType');
    if (this.logDetails) {
      recordTypeControl?.setValidators([Validators.required]);
    } else {
      recordTypeControl?.clearValidators();
    }
    recordTypeControl?.updateValueAndValidity();
    this.onValueChanged();
  }

  applyFilters(flag:any) {
    let filterType = this.logDetails ? 'logDetails' : 'logSummary';

    if (this.logDetails && (!this.filters.value.recordType || this.filters.value.recordType === '')) {
      return;
    }

    this.filters.value.filterType = filterType;
    this.loadingService.setLoading(true);

    if (this.filters.value.startDate != null && this.filters.value.startDate !== '') {
      let startDate = this.filters.value.startDate;
      if (!(startDate instanceof Date)) {
        startDate = new Date(startDate);
      }
      if (!isNaN(startDate.getTime())) {
        startDate.setHours(0, 0, 0, 0);
        this.filters.value.startDate = startDate.toISOString();
      }
    }

    if (this.filters.value.endDate != null && this.filters.value.endDate !== '') {
      let endDate = this.filters.value.endDate;
      if (!(endDate instanceof Date)) {
        endDate = new Date(endDate);
      }
      if (!isNaN(endDate.getTime())) {
        endDate.setHours(23, 59, 59, 999);
        this.filters.value.endDate = endDate.toISOString();
      }
    }
    if(flag==0){
      this.paginationOptions = { pageNumber: 0, pageSize: 10, first: 0 };
    }

    this.reportsService.filterExceptionReports(this.paginationOptions, this.filters.value).then((response: any) => {
      if (response && response.status === 'success') {
        this.tableVisible = false;
        this.filteredData=[];
        this.data=[];
        this.totalElements = response.count;
        this.pageSize = this.paginationOptions.pageSize || 10;

        if (response.data.length > 0) {
          this.columns = response.columns.map(key => ({
            field: key,
            header: this.formatHeader(key)
          }));
          this.filteredData = response.data.map((item: any) => this.transformDates(item));
          this.data = response.allData.map((item: any) => this.transformDates(item));
          this.tableVisible = true;
          this.noDataFoundMessage='';
          this.cd.detectChanges();
        }
        if(response.data.length==0) this.noDataFoundMessage='No Data Found';
      } else {
        console.error('Failed to fetch data');
      }

      this.loadingService.setLoading(false);
    }).catch(error => {
      console.error('Error fetching data:', error);
      this.loadingService.setLoading(false);
    });
  }

  formatHeader(header: string): string {
    const headerMap: { [key: string]: string } = {
      "exceptionId": "Exception ID",
      "recordType": "Exception Record Type",
      "lastModified": "Modified Timestamp",
      "sourceSystem": "Source System",
      "lastUpdatedBy": "Modified By",
      "exceptionEvent": "Exception Event",
      "Attribute 13": "Asset ID",
      "Attribute 14": "Asset Number"
    };

    if (headerMap[header]) {
      return headerMap[header];
    }
    return header
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/_/g, ' ')
      .replace(/\b\w/g, char => char.toUpperCase());
  }

  isDate(value: any): boolean {
    return value instanceof Date && !isNaN(value.getTime());
  }

  transformDates(item: any): any {
    const dateFields = ['createdDate', 'lastModified'];

    dateFields.forEach(field => {
      const value = item[field];
      if (Array.isArray(value)) {
        item[field] = this.convertArrayToDate(value);
      } else if (typeof value === 'number') {
        item[field] = new Date(value);
      } else if (typeof value === 'string') {
        item[field] = new Date(value);
      }
    });

    return item;
  }

  convertArrayToDate(arr: number[]): Date {
    if (!Array.isArray(arr) || arr.length < 6) return null;
    return new Date(arr[0], arr[1] - 1, arr[2], arr[3], arr[4], arr[5], Math.floor(arr[6] / 1000000));
  }

  exportExcel() {
    this.reportsService.downloadCsv(this.data).subscribe((response: any) => {
      const blob = new Blob([response], { type: 'text/csv;charset=utf-8' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'exceptions_report.csv';
      link.click();
      window.URL.revokeObjectURL(url);
    });
  }

  onClickAdvanceSearch() {
    this.displayAdvancedSearch = !this.displayAdvancedSearch;
  }

  refreshTable(flag: number) {
    const emptyValues: any = {}; 
    Object.keys(this.filters.controls).forEach(key => {
      if (this.logDetails && key === 'recordType') {
        emptyValues[key] = this.filters.value[key];
      } else {
        emptyValues[key] = '';
      }
    });
    this.filters.setValue(emptyValues);
    if (flag == 1) {
      this.applyFilters(0);
    }
    if(flag===0){
      this.minDate=undefined;
      this.maxDate=new Date();
    }
  }

  onPageChange(event: any) {
    this.paginationOptions.pageNumber = event.first / event.rows;
    this.paginationOptions.pageSize = event.rows;
    this.paginationOptions.first = event.first;
    this.applyFilters(1);
  }

  searchUserValues(event: any) {
    const query = event.query.toLowerCase();
    this.userValueResults = this.usernames.filter(username =>
      username.toLowerCase().includes(query)
    );
  }
}
