<div class="content-section implementation">
</div>
<div class="card-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block" *ngIf="displayAdvancedSearch">
                    <form [formGroup]="filters" (ngSubmit)="applyFilters(0)">
                        <div class="toggler search_panel">
                            <label style="font-weight: bold;">Log Summary</label>
                            <div class="outer" (click)="toggleFilterType()">
                                <div [class]="logDetails ? 'inner logDetails' : 'inner'">
                                </div>
                            </div>
                            <label style="font-weight: bold;">Log Details</label>
                        </div>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label" for="userName">User Name</label>
                                <p-autoComplete formControlName="userName"
                                                appendTo="body"
                                                placeholder="User Name"
                                                [suggestions]="userValueResults"
                                                (completeMethod)="searchUserValues($event)"
                                                minLength="3"
                                                class="filter-form">
                                </p-autoComplete>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="recordType">Record Type<span *ngIf="logDetails" style="color: red;">*</span></label>
                                <!-- <input type="text" id="recordType" placeholder="Record Type" formControlName="recordType" name="exceptionRecordType" class="filter-form" /> -->
                                <p-dropdown [options]="recordTypes" formControlName="recordType" placeholder="Select Record Type" appendTo="body" [filter]="true" class="filter-form"></p-dropdown>
                                <div *ngIf="formErrors.recordType || (logDetails && filters.value.recordType=='')" class="ui-message ui-messages-error ui-corner-all">
                                    {{ formErrors.recordType }}
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="sourceSystem">Source System</label>
                                <!-- <input type="text" id="sourceSystem" placeholder="Source System" formControlName="sourceSystem" name="sourceSystem" class="filter-form" /> -->
                                 <p-dropdown [options]="sourceSystemDropdown" formControlName="sourceSystem" placeholder="Select Source System" appendTo="body" [filter]="true" class="filter-form"></p-dropdown>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="startDate">Start Date</label>
                                <p-calendar formControlName="startDate" [monthNavigator]="true" placeholder="Select Start Date" [yearNavigator]="true" yearRange="2000:2100" class="prosp-calendar filter-form" [showIcon]="false" appendTo="body" dateFormat="yy-mm-dd" (onSelect)="setMinDate()" [maxDate]="maxDate"></p-calendar>
                                <!-- <input type="date" id="startDate" placeholder="Start Date" formControlName="startDate" name="startDate" class="filter-form" /> -->
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="endDate">End Date</label>
                                <p-calendar formControlName="endDate" [monthNavigator]="true" placeholder="Select End Date" [yearNavigator]="true" yearRange="2000:2100" class="prosp-calendar filter-form" [showIcon]="false" appendTo="body" dateFormat="yy-mm-dd" [minDate]="minDate" (onSelect)="setMaxDate()"></p-calendar>
                                <!-- <input type="date" id="endDate" placeholder="End Date" formControlName="endDate" name="endDate" class="filter-form" /> -->
                            </div>



                            <div class="form-group">
                                <label class="form-label" for="exceptionEvent">Exception Event</label>
                                <p-dropdown [options]="exceptionEvents" formControlName="exceptionEvent" placeholder="Select Exception Event" appendTo="body" [filter]="true" class="filter-form"></p-dropdown>
                                <!-- <input type="text" id="exceptionEvent" placeholder="Exception Event" formControlName="exceptionEvent" name="exceptionEvent" class="filter-form" /> -->
                                <!-- <select id="exceptionEvent" formControlName="exceptionEvent" name="exceptionEvent" class="filter-form">
                                      <option value="">-- Select Exception Event --</option>
                                      <option *ngFor="let event of exceptionEvents" [value]="event">{{ event }}</option>
                                </select> -->
                                <!-- <p-dropdown [options]="exceptionEvents" optionLabel="label" appendTo="body" formControlName="exceptionEvent" placeholder="Select Exception Event" class="filter-form"></p-dropdown> -->
                            </div>
                        </div>


                        <div class="row d-content">
                            <div class="col-md-12" class="text-right">
                                <button type="submit" [disabled]="filters.invalid" pButton class="primary-btn" label="Search"></button>
                                <button type="reset" class="secondary-btn" pButton (click)="refreshTable(0)" label="Reset"></button>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
            <div class="col-md-12">

                <div class="card-block" style="display: flex; flex-direction: column;">
                    <h2>
                        Exceptions Report
                    </h2>
                    <div class="pull-right icons-list" style="align-self: flex-end">
                        <a  (click)="goToOperationReports()" class="add-column"><em class="fa fa-reply"></em>Back</a>
                        <a (click)="onClickAdvanceSearch()" class="add-column">
                            <em class="fa fa-search" *ngIf="!displayAdvancedSearch"></em>
                            <em class="fa fa-times" *ngIf="displayAdvancedSearch"></em>
                            Advanced Search
                        </a>
                        <a (click)="refreshTable(1)" title="Reset">
                            <em class="fa fa-refresh"></em>
                        </a>
                        <a (click)="exportExcel()" title="Export">
                            <em class="fa fa-external-link"></em>
                        </a>
                    </div>
                    <div class="no-data-found-message">{{noDataFoundMessage}}</div>

                    <p-table *ngIf="tableVisible" class="ui-datatable arrangementMgrTbl" [value]="filteredData" [paginator]="filteredData!=null || filteredData!=undefined" [(first)]="paginationOptions.first"
                             [lazy]="true" [rows]="pageSize" [totalRecords]="totalElements" [resizableColumns]="true" [rowsPerPageOptions]="[10, 20]" (onPage)="onPageChange($event)">

                        <ng-template pTemplate="header">
                            <tr>
                                <th *ngFor="let col of columns">{{ col.header }}</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-rowData>
                            <tr>
                                <td *ngFor="let col of columns">
                              <span *ngIf="isDate(rowData[col.field])" [title]="rowData[col.field] | date: 'yyyy-MM-dd HH:mm:ss'">
                              {{ rowData[col.field] | date: 'yyyy-MM-dd HH:mm:ss' }}
                              </span>
                                    <span *ngIf="!isDate(rowData[col.field])" [title]="rowData[col.field]">
                              {{ rowData[col.field] }}
                              </span>
                                </td>
                            </tr>
                        </ng-template>

                    </p-table>


                </div>
            </div>
        </div>
    </div>
</div>