import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require:any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type':  'application/json',
  })
};




@Injectable()
export class RmanFiscalPeriodsInterfaceService {

    constructor(private http: HttpClient) {}
    
    getServiceUrl(paginationOptions:any,rmanFiscalPeriodsInterfaceSearchObject:any,  exportFlag: any, exportCols: any) {
        
         let serviceUrl = appSettings.apiUrl + '/fiscalPeriodsInterfaceExport?';

    	 if (exportFlag == 0) {
      	 	serviceUrl = appSettings.apiUrl + '/rmanFiscalPeriodsInterfaceSearch?';
    	 }
    	 
    	 let searchString='';

        
        if (rmanFiscalPeriodsInterfaceSearchObject.periodName!=undefined && rmanFiscalPeriodsInterfaceSearchObject.periodName!="") {
            searchString=searchString+'periodName:'+rmanFiscalPeriodsInterfaceSearchObject.periodName+',';
        }

        if (rmanFiscalPeriodsInterfaceSearchObject.periodYear!=undefined && rmanFiscalPeriodsInterfaceSearchObject.periodYear!="") {
            searchString=searchString+'periodYear:'+rmanFiscalPeriodsInterfaceSearchObject.periodYear+',';
        }

        if (rmanFiscalPeriodsInterfaceSearchObject.interfaceProcessId!=undefined && rmanFiscalPeriodsInterfaceSearchObject.interfaceProcessId!="") {
            searchString=searchString+'interfaceProcessId:'+rmanFiscalPeriodsInterfaceSearchObject.interfaceProcessId+',';
        }

        searchString=searchString+'interfaceStatus:E';


        if (searchString == '') {
            serviceUrl=serviceUrl+'search=%25';
        }
        else {
            serviceUrl=serviceUrl+'search='+searchString;
        }
        
        if (paginationOptions.pageNumber!=undefined && paginationOptions.pageNumber!="" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber!=0) {
           serviceUrl=serviceUrl+'&page='+paginationOptions.pageNumber+'&size='+paginationOptions.pageSize;
        }
        
         if (exportCols != undefined && exportCols != "") {
      		serviceUrl = serviceUrl + '&exportCols=' + exportCols;
    	}
   		
   		return serviceUrl;
        
	}

    
    getAllRmanFiscalPeriodsInterface(paginationOptions:any,rmanFiscalPeriodsInterfaceSearchObject:any, exportCols:any): Promise<any[]> {
        let serviceUrl = this.getServiceUrl(paginationOptions, rmanFiscalPeriodsInterfaceSearchObject, 0, exportCols);
        return this.http.get(serviceUrl).toPromise().then((data:any) => {
            return data;
        });
    }

    deleteSelectedLines(exceptionLines: any[]){

        let delUrl = appSettings.apiUrl + '/bulkDelFiscalPeriodsExceptions?periodNames='+exceptionLines;
        return this.http.delete(delUrl,{responseType : "text"}).toPromise().then((data:any) => {
          return data;
        });

    }


	
}
