import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { Table } from 'primeng/table';
import { NotificationService } from '../shared/notifications.service';
import { UploadService } from '../shared/upload.service';
import { RmanFiscalPeriodsInterface } from './rmanFiscalPeriodsInterface';
import { RmanFiscalPeriodsInterfaceService } from './rmanFiscalPeriodsInterfaceservice';
import { CommonSharedService } from '../shared/common.service';

declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Component({
    templateUrl: './rmanFiscalPeriodsInterface.component.html',
    selector: 'rmanFiscalPeriodsInterface-data',
    providers: [RmanFiscalPeriodsInterfaceService, ConfirmationService]
})

export class RmanFiscalPeriodsInterfaceComponent {

    displayDialog: boolean;

    displaySearchDialog: boolean;
    
    uploadLoading: boolean = false;

    rmanFiscalPeriodsInterface: RmanFiscalPeriodsInterface = new RmanFiscalPeriodsInterfaceImpl();

    rmanFiscalPeriodsInterfaceSearch: RmanFiscalPeriodsInterface = new RmanFiscalPeriodsInterfaceImpl();

    isSerached: number = 0;

    selectedRmanFiscalPeriodsInterface: RmanFiscalPeriodsInterface;

    newRmanFiscalPeriodsInterface: boolean;
	
	displayFiscalPeriodsInterfaceDialog: boolean = false;
	
    rmanFiscalPeriodsInterfaceList: RmanFiscalPeriodsInterface[];

    cols: any[];
    //columns: ILabels;

    columnOptions: any[];

    paginationOptions = {};

    pages: {};


    datasource: any[];
    pageSize: number;
    totalElements: number;
    collapsed: boolean = true;
    
    customerForm: FormGroup;
    noData = appSettings.noData;
    loading: boolean;
    rmanFiscalPeriodsInterfacetatus: any[];
    regions:any[];
    customerTypes:any[];
    isSelectAllChecked = true;
    globalCols: any[];
    clonedCols: any[];
    userId: number;
    showPaginator: boolean = true;
    startIndex: number;
    showAddColumns = true;
    columns: any[];
    
    exportCols: string[] = [];
  	disableExport: boolean = true;
    selectedLines: any[] = [];
	  exceptionsList: any[] = [];




    constructor(
        private rmanFiscalPeriodsInterfaceService: RmanFiscalPeriodsInterfaceService,
        private confirmationService: ConfirmationService,
        private formBuilder: FormBuilder,
        private notificationService:NotificationService,
        public _uploadService:UploadService,  private commonSharedService: CommonSharedService
    ) {

        // generate list code
        this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };


    }

    ngOnInit() {
      this.selectedLines = [];
        this.globalCols = [
            { field: 'periodSetName', header: 'PERIOD_SET_NAME', showField: true, drag: false, display: "table-cell",type:'number'},
            { field: 'periodName', header: 'PERIOD_NAME', showField: true, drag: true, display: "table-cell",type:'text' },
            { field: 'startDate', header: 'START_DATE', showField: true, drag: true, display: "table-cell",type:'date'},
            { field: 'endDate', header: 'END_DATE', showField: true, drag: true, display: "table-cell",type:'date'},
            { field: 'yearStartDate', header: 'YEAR_START_DATE', showField: true, drag: true, display: "table-cell",type:'date'},
            { field: 'quarterStartDate', header: 'QUARTER_START_DATE', showField: true, drag: true, display: "table-cell",type:'date'},
			{ field: 'periodType', header: 'PERIOD_TYPE', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'periodYear', header: 'PERIOD_YEAR', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'periodNum', header: 'PERIOD_NUM', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'quarterNum', header: 'QUARTER_NUM', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'enteredPeriodName', header: 'ENTERED_PERIOD_NAME', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'adjustmentPeriodFlag', header: 'ADJUSTMENT_PERIOD_FLAG', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'interfaceProcessId', header: 'INTERFACE_PROCESS_ID', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'errorMessage', header: 'ERROR_MESSAGE', showField: true, drag: true, display: "table-cell",type:'text'}
        ];

        this.columns = [];
        this.getTableColumns("rmanFiscalPeriodsInterface", "FiscalPeriodsInterface");
    }

    prepareRmanStatus() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectStat, value: null }];
        this.rmanFiscalPeriodsInterfacetatus.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode });
        });
        this.rmanFiscalPeriodsInterfacetatus = rmanLookupsVTempObj;
    }

    prepareRegions() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectRegion, value: null }];
        this.regions.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupDescription });
        });
        this.regions = rmanLookupsVTempObj;
    }

    prepareCustomerTypes() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectCustomerType, value: null }];
        this.customerTypes.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupDescription });
        });
        this.customerTypes = rmanLookupsVTempObj;
    }

    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
          if (response && response != null && response.userId) {
            this.columns = [];
            let colsList = response.tableColumns.split(",");
            if (colsList.length > 0) {
              colsList.forEach((item, index) => {
                if (item) {
                  this.startIndex = this.globalCols.findIndex(col => col.field == item);
                  this.onDrop(index);
                }
              });
            }
            this.globalCols.forEach(col => {
              if (response.tableColumns.indexOf(col.field) !== -1) {
                this.columns.push(col);
              } else {
                col.showField = false;
              }
            });
            if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
            this.showPaginator = this.columns.length !== 0;
            this.userId = response.userId;
          } else {
            this.columns = this.globalCols;
          }
        }).catch(() => {
          this.notificationService.showError('Error occured while getting table columns data');
          this.loading = false;
        });
      }

      saveColumns() {
        let selectedCols = "";
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
          if (col.showField) {
            selectedCols += col.field;
            if (index < colLength) {
              selectedCols += ",";
            }
          }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns("rmanFiscalPeriodsInterface", "FiscalPeriodsInterface", selectedCols, this.userId).then((response) => {
          this.columns = this.globalCols.filter(item => item.showField);
          this.userId = response["userId"];
          this.showPaginator = this.columns.length !== 0;
          this.loading = false;
        }).catch(() => {
          this.notificationService.showError('Error occured while getting data');
          this.loading = false;
        });
      }

      onDragStart(index: number) {
        this.startIndex = index;
      }

      onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
        //console.log(this.globalCols);
      }

      selectColumns(col: any) {
        let cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
          this.isSelectAllChecked = false;
        } else {
          this.isSelectAllChecked = true;
        }
      }

      onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
          if (this.isSelectAllChecked) {
            col.showField = true;
          } else {
            if (col.drag) {
              col.showField = false;
            }
          }
        });
      }

      onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
      }

      closeConfigureColumns(event: any) {
      this.showAddColumns = true;
      this.globalCols = this.clonedCols;
      let configCol = this.globalCols.filter(item => !item.showField);
      this.isSelectAllChecked = !(configCol.length > 0);
      }



    getAllRmanFiscalPeriodsInterface() {
        this.loading = true;
        this.rmanFiscalPeriodsInterfaceService.getAllRmanFiscalPeriodsInterface(this.paginationOptions, this.rmanFiscalPeriodsInterface, this.exportCols).then((rmanFiscalPeriodsInterfaceList: any) => {
            this.loading = false;
            this.datasource = rmanFiscalPeriodsInterfaceList.content;
            this.rmanFiscalPeriodsInterfaceList = rmanFiscalPeriodsInterfaceList.content;
            this.totalElements = rmanFiscalPeriodsInterfaceList.totalElements;
            this.pageSize = rmanFiscalPeriodsInterfaceList.size;
            this.displaySearchDialog = false;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    getRmanFiscalPeriodsInterface(event: any) {
        this.loading = true;
        let first: number = event.first;
        let rows: number = event.rows;
        let pageNumber: number = first / rows;
        this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
        this.rmanFiscalPeriodsInterfaceService.getAllRmanFiscalPeriodsInterface(this.paginationOptions, this.rmanFiscalPeriodsInterface, this.exportCols).then((rmanFiscalPeriodsInterfaceList: any) => {
            this.loading = false;
            this.datasource = rmanFiscalPeriodsInterfaceList.content;
            this.rmanFiscalPeriodsInterfaceList = rmanFiscalPeriodsInterfaceList.content;
            this.totalElements = rmanFiscalPeriodsInterfaceList.totalElements;
            this.pageSize = rmanFiscalPeriodsInterfaceList.size;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });

    }

	exportExcel() {
	    this.exportCols = [];

	    for (let index = 0; index < this.columns.length; index++) {
	      if (this.columns[index].showField) {
	        this.exportCols.push(this.columns[index].field);
	      }
	    }

	    let serviceUrl = this.rmanFiscalPeriodsInterfaceService.getServiceUrl(this.paginationOptions, this.rmanFiscalPeriodsInterface, 1, this.exportCols);
	    window.location.href = serviceUrl;
   }

   onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }


    reset(dt: Table) {
        this.paginationOptions = {};
        this.rmanFiscalPeriodsInterface = new RmanFiscalPeriodsInterfaceImpl();
        this.rmanFiscalPeriodsInterfaceSearch = new RmanFiscalPeriodsInterfaceImpl();
        this.selectedLines = [];
        dt.reset();
    }
    deleteSelected(dt: Table) {

        this.exceptionsList = [];

        for (let line of this.selectedLines) {
            this.exceptionsList.push(line.periodName);
        }
        if (this.exceptionsList.length > 0) {

        this.confirmationService.confirm({
            message: 'Are you sure you want to delete?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
              this.loading = true;
              this.rmanFiscalPeriodsInterfaceService.deleteSelectedLines(this.exceptionsList).then((response: any) => {
                this.notificationService.showSuccess('Selected records are deleted Successfully  ');
            }).then(() => {
                this.reset(dt);
            }).catch((err: any) => {
                this.loading = false;
                this.notificationService.showError('Getting error While delete records');
            });
            },
            reject: () => {
              this.notificationService.showWarning('You have rejected');
            }
          });

        } else {
            this.notificationService.showInfo('Select at least one record to delete');
        }
    }




    
    findSelectedRmanFiscalPeriodsInterfaceIndex(): number {
        return this.rmanFiscalPeriodsInterfaceList.indexOf(this.selectedRmanFiscalPeriodsInterface);
    }

    onRowSelect(event: any) {

    }

    
    hideColumnMenu: boolean = true;

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    showFilter: boolean = false;

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {
        if (this.isSerached == 0) {
            this.rmanFiscalPeriodsInterfaceSearch = new RmanFiscalPeriodsInterfaceImpl();
        }
        this.rmanFiscalPeriodsInterfaceSearch = new RmanFiscalPeriodsInterfaceImpl();
        this.displaySearchDialog = true;

    }
    
	search() {

        this.isSerached = 1;
        this.rmanFiscalPeriodsInterface = this.rmanFiscalPeriodsInterfaceSearch;
        this.paginationOptions={};
        this.getAllRmanFiscalPeriodsInterface();
    }
    
    
    showUploadLoader(){
    	this.uploadLoading = true;
    }
	
}


export class RmanFiscalPeriodsInterfaceImpl implements RmanFiscalPeriodsInterface {
    constructor(public periodSetName?: any,
	public periodName?: any,
    public startDate?: any,
    public endDate?: any,
    public yearStartDate?: any,
	public quarterStartDate?: any,
	public periodType?: any,
	public periodYear?: any,
	public periodNum?: any,
	public quarterNum?: any,
	public enteredPeriodName?: any,
	public adjustmentPeriodFlag?: any,
    public interfaceProcessId? :any,
	public interfaceStatus?: any,
	public errorMessage?: any) { }
}

interface ILabels {
    [index: string]: string;
}
