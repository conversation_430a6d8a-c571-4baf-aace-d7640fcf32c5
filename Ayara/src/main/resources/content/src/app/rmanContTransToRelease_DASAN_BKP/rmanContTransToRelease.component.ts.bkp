
import { Component, EventEmitter, Output, Input, Injectable, OnInit, ChangeDetectorRef, ViewChild,OnDestroy } from '@angular/core';
import { FormControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MenuItem, Message, DataTable, ConfirmationService } from 'primeng/primeng';
import { RmanContTransToRelease } from './rmanContTransToRelease';
import { RmanContTransToReleaseService } from './rmanContTransToReleaseservice';
import { RmanContTransToReleaseLabels } from './rmanContTransToReleaseLabels';
import { RmanUsersService } from '../rmanUsers/rmanUsersservice';
import { RmanLookupsVService } from '../rmanLookupsV/rmanLookupsVservice';
import { RmanContTransLinesComponent } from '../rmanContTransLines/rmanContTransLines.component';
import { RmanContReleaseLinesComponent,RmanContReleaseLinesImpl } from '../rmanContReleaseLines/rmanContReleaseLines.component';
import { RmanContReleaseLinesVComponent,RmanContReleaseLinesVImpl } from '../rmanContReleaseLinesV/rmanContReleaseLinesV.component';
import { RmanContReleaseLinesService } from '../rmanContReleaseLines/rmanContReleaseLinesservice';
// import { RmanContLinkTemplateVService } from '../rmanContLinkTemplateV/rmanContLinkTemplateVservice';
import { RmanContLinkTemplateVService } from '../services/rmanContLinkTemplateVservice';
import { RmanContProdNameLovVService } from '../services/rmanContProdNameLovVservice';
// import { RmanContProdNameLovVService } from '../rmanContProdNameLovV/rmanContProdNameLovVservice';
//import { RmanContHeaderVService } from '../rmanContHeaderV/rmanContHeaderVservice';
import { RmanContHeaderVService } from '../services/rmanContHeaderVservice';
//import { RmanContSourceLovService } from '../rmanContSourceLov/rmanContSourceLovservice';
import { RmanContSourceLovService } from '../services/rmanContSourceLovservice';
import { Router } from "@angular/router";
import { ActivatedRoute, Params } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { CommonSharedService } from '../shared/common.service';
declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Component({
  templateUrl: './rmanContTransToRelease.component.html',
  selector: 'rmanContTransToRelease-data',
  providers: [RmanContTransToReleaseService, RmanContReleaseLinesService,ConfirmationService, RmanUsersService, RmanContLinkTemplateVService, RmanContProdNameLovVService, RmanContSourceLovService, RmanContHeaderVService]
})

export class RmanContTransToReleaseComponent implements OnInit,OnDestroy {
  noData = appSettings.noData;
  displayDialog: boolean;
  displaySearchDialog: boolean;
  displayConditionDialog : boolean;
  conditionFlag:boolean;
  pTransHeaderId :number;
  arrIdK: any;
  msgs: Message[] = [];
  rmanContTransToRelease: any = new RmanContTransToReleaseImpl();
  rmanContTransToReleaseSearch: any = new RmanContTransToReleaseImpl();
  isSerached: number = 0;
  selectedRmanContTransToRelease: RmanContTransToRelease;
  newRmanContTransToRelease: boolean = false;
  rmanContTransToReleaseList: any[] = [];
  cols: any[];
  columns: ILabels;
  columnOptions: any[];
  paginationOptions: any;
  pages: {};
  private items: MenuItem[];
  datasource: any[];
  pageSize: number;
  totalElements: number;
  
  subscription: Subscription;
  isReadOnly: boolean;
  
  addFlag = false;
  rmanUsersList: any[] = [];
  rmanUsers: any[];
  rmanUsers1List: any[] = [];
  rmanUsers1: any[];
  rmanLookupsVList: any[] = [];
  rmanLookupsV: any[];
  rmanLookupsV1List: any[] = [];
  rmanLookupsV1: any[];
  rmanLookupsV2List: any[] = [];
  rmanLookupsV2: any[];
  rmanContLinkTemplateVList: any[] = [];
  rmanContLinkTemplateVListHeaderId: any[];
  rmanContSourceLovApp: any[];
  rmanContSourceLovApplication: any[];
  rmanContLinkTemplateV: any[];
  rmanContProdNameLovVList: any[] = [];
  rmanContProdNameLovV: any[];
  rmanContHeaderVList: any[] = [];
  rmanContHeaderV: any[];
  rmanLookupsV3List: any[] = [];
  rmanLookupsV3: any[];
  rmanLookupsV6List: any[] = [];
  rmanLookupsV6: any[];
  rmanLookupsV7List: any[] = [];
  rmanLookupsV7: any[];
  contAdditionalInfo3LovVList: any[] = [];
  contAdditionalInfo3LovV: any[];
  rmanContSourceLovList: any[] = [];
  rmanContSourceLov: any[];
  rmanContSourceLov1List: any[] = [];
  rmanContSourceLov1: any[];
  rmanContProdNameLovV1List: any[] = [];
  rmanContProdNameLovV1: any[];
  rmanContProdNameLovV2List: any[] = [];
  rmanContProdNameLovV2: any[];
  rmanContProdGroupV1: any[];
  rmanContProdCategoryV1: any[];
  rmanContProdNameV1: any[];
  rmanContProdGroupList: any[] = [];
  rmanContProdCategoryList: any[] = [];
  rmanContProdNameList: any[] = [];
  collapsed: boolean = true;
  loading: boolean;
  transToReleaseForm: FormGroup;
  dt: DataTable;
  
  status:any;
  releaseCont:any[]=[];
  rmanContReleaseLines: any = new RmanContReleaseLinesImpl();

  @ViewChild(RmanContTransLinesComponent)
  private rmanContTransLinesComponent: RmanContTransLinesComponent;


  @ViewChild(RmanContReleaseLinesComponent)
  private rmanContReleaseLinesComponent: RmanContReleaseLinesComponent;

  @ViewChild(RmanContReleaseLinesVComponent)
  private rmanContReleaseLinesVComponent: RmanContReleaseLinesVComponent;


  constructor(private confirmationService: ConfirmationService, private ref: ChangeDetectorRef, private rmanContTransToReleaseService: RmanContTransToReleaseService, private rmanUsersService: RmanUsersService, private rmanLookupsVService: RmanLookupsVService, private rmanContLinkTemplateVService: RmanContLinkTemplateVService, private rmanContProdNameLovVService: RmanContProdNameLovVService, private rmanContHeaderVService: RmanContHeaderVService,private rmanContSourceLovService: RmanContSourceLovService, private router: Router, private route: ActivatedRoute, private formBuilder: FormBuilder
  ,private rmanContReleaseLinesService:RmanContReleaseLinesService
  ,private commonSharedService: CommonSharedService) {
    // generate list code
    this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };
	
	this.subscription = this.commonSharedService.getRAccess().subscribe(data => {
      //console.log('Read Access in Arrangement Manger : '+data);
      if (data == 'Y') {
      	this.isReadOnly = true;
      }else{
      	this.isReadOnly = false;
      }
     
    });
    
        

    this.route.params.subscribe((params: any) => {
      this.arrIdK = params['id'];
    });

   this.loading = true;
    this.rmanUsersService.getAllRmanUsers(this.paginationOptions, {}).then((rmanUsersList: any) => {
      this.rmanUsersList = rmanUsersList.content;
      this.prepareRmanUsersObject();


    this.rmanUsersService.getAllRmanUsers(this.paginationOptions, {}).then((rmanUsers1List: any) => {
      this.rmanUsers1List = rmanUsers1List.content;
      this.prepareRmanUsers1Object();


    this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, { 'lookupTypeName': 'ENABLED_FLAG' }).then((rmanLookupsVList: any) => {
      this.rmanLookupsVList = rmanLookupsVList.content;
      this.prepareRmanLookupsVObject();


    this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, { 'lookupTypeName': 'RELEASE_EVENT' }).then((rmanLookupsV1List: any) => {
      this.rmanLookupsV1List = rmanLookupsV1List.content;
      this.prepareRmanLookupsV1Object();


    this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, { 'lookupTypeName': 'APPLICATION_LEVEL' }).then((rmanLookupsV2List: any) => {
      this.rmanLookupsV2List = rmanLookupsV2List.content;
      this.prepareRmanLookupsV2Object();



    this.rmanContLinkTemplateVService.getAllRmanContLinkTemplateV(this.paginationOptions, {}).then((rmanContLinkTemplateVList: any) => {

      this.rmanContLinkTemplateVList = rmanContLinkTemplateVList.content;
      this.prepareRmanContLinkTemplateVObject();


    this.rmanContProdNameLovVService.getAllRmanContProdNameLovV(this.paginationOptions, { 'dealArrangementId': this.arrIdK }).then((rmanContProdNameLovVList: any) => {

      this.rmanContProdNameLovVList = rmanContProdNameLovVList.content;
      this.rmanContProdGroupList = rmanContProdNameLovVList.content;
      this.rmanContProdCategoryList = rmanContProdNameLovVList.content;
      this.rmanContProdNameList = rmanContProdNameLovVList.content;
      this.prepareRmanContProdNameLovVObject();


    this.rmanContHeaderVService.getAllRmanContHeaderV(this.paginationOptions, { 'lookupTypeName': 'APPLICATION_LEVEL' }).then((rmanContHeaderVList: any) => {
      this.rmanContHeaderVList = rmanContHeaderVList.content;
      this.prepareRmanContHeaderVObject();


    this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, { 'lookupTypeName': 'CONT_EVENT_TYPE' }).then((rmanLookupsV3List: any) => {
      this.rmanLookupsV3List = rmanLookupsV3List.content;
      this.prepareRmanLookupsV3Object();


    this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, { 'lookupTypeName': 'RMAN_ACCOUNT_STANDARDS' }).then((rmanLookupsV6List: any) => {
      this.rmanLookupsV6List = rmanLookupsV6List.content;
      this.prepareRmanLookupsV6Object();


    this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, { 'lookupTypeName': 'CONT_RELEASE_TYPE' }).then((rmanLookupsV7List: any) => {
      this.rmanLookupsV7List = rmanLookupsV7List.content;
      this.prepareRmanLookupsV7Object();



    this.rmanContSourceLovService.getAllRmanContSourceLov(this.paginationOptions, {}).then((rmanContSourceLovList: any) => {
      this.rmanContSourceLovList = rmanContSourceLovList.content;
      /* this.prepareRmanContSourceLovObject(); */


    this.rmanContSourceLovService.getAllRmanContSourceLov(this.paginationOptions, {}).then((rmanContSourceLov1List: any) => {
      this.rmanContSourceLov1List = rmanContSourceLov1List.content;
    /*  this.prepareRmanContSourceLov1Object();*/


    this.rmanContProdNameLovVService.getAllRmanContProdNameLovV(this.paginationOptions, {}).then((rmanContProdNameLovV1List: any) => {
      this.rmanContProdNameLovV1List = rmanContProdNameLovV1List.content;
      this.prepareRmanContProdNameLovV1Object();


    this.rmanContProdNameLovVService.getAllRmanContProdNameLovV(this.paginationOptions, {}).then((rmanContProdNameLovV2List: any) => {
      this.loading = false;
      this.rmanContProdNameLovV2List = rmanContProdNameLovV2List.content;
      this.prepareRmanContProdNameLovV2Object();
                                }).catch((err: any) => {
                                      this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
                                       this.loading = false;
                                   });
                               }).catch((err: any) => {
                                     this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
                                      this.loading = false;
                                  });
                              }).catch((err: any) => {
                                    this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting "Source Line ID" data'}];
                                     this.loading = false;
                                 });
                             }).catch((err: any) => {
                                   this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting "Standard" data'}];
                                    this.loading = false;
                                });
                            }).catch((err: any) => {
                                  this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting "Release Type" data'}];
                                   this.loading = false;
                               });
                           }).catch((err: any) => {
                                 this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting "Cont Event Type" data'}];
                                  this.loading = false;
                              });
                         }).catch((err: any) => {
                               this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting "Rule Header ID" data'}];
                                this.loading = false;
                            });
                       }).catch((err: any) => {
                             this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting "Element Type" data'}];
                              this.loading = false;
                          });
                     }).catch((err: any) => {
                           this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
                            this.loading = false;
                        });
                   }).catch((err: any) => {
                         this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting "Template ID" data'}];
                          this.loading = false;
                      });
                 }).catch((err: any) => {
                       this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting "Application Level" data'}];
                        this.loading = false;
                    });
               }).catch((err: any) => {
                     this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting "Release Event" data'}];
                      this.loading = false;
                  });
             }).catch((err: any) => {
                   this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting "Invoice Hold" data'}];
                    this.loading = false;
                });
           }).catch((err: any) => {
                 this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
                  this.loading = false;
              });
         }).catch((err: any) => {
               this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
                this.loading = false;
        });



  }

  // ngAfterViewChecked() {
  //   this.cref.detectChanges();
  // }



  ngOnInit() {

   // this.getAllRmanContTransToRelease();

    this.cols = [];
    let cols: any = [];
    cols["attribute30"] = "attribute30";
    cols["invoiceHold"] = "invoiceHold";
    cols["sourceLineId"] = "sourceLineId";
    cols["ruleHeaderId"] = "ruleHeaderId";
    cols["releaseCogs"] = "releaseCogs";
    cols["applyType"] = "applyType";
    cols["sourceHeaderId"] = "sourceHeaderId";
    cols["lastUpdateDate"] = "lastUpdateDate";
    cols["event"] = "event";
    cols["ranking"] = "ranking";
    cols["status"] = "status";
    cols["attribute29"] = "attribute29";
    cols["attribute28"] = "attribute28";
    cols["attribute27"] = "attribute27";
    cols["avgContAcceptDur"] = "avgContAcceptDur";
    cols["attribute26"] = "attribute26";
    cols["attribute3"] = "attribute3";
    cols["createdBy"] = "createdBy";
    cols["transHeaderId"] = "transHeaderId";
    cols["maxDuration"] = "maxDuration";
    cols["attribute2"] = "attribute2";
    cols["lastUpdatedBy"] = "lastUpdatedBy";
    cols["attribute1"] = "attribute1";
    cols["applicationLevel"] = "applicationLevel";
    cols["soHeaderId"] = "soHeaderId";
    cols["revenue"] = "revenue";
    cols["creationDate"] = "creationDate";
    cols["attribute9"] = "attribute9";
    cols["attribute8"] = "attribute8";
    cols["attribute7"] = "attribute7";
    cols["productName"] = "productName";
    cols["attribute6"] = "attribute6";
    cols["attribute5"] = "attribute5";
    cols["releaseType"] = "releaseType";
    cols["attribute4"] = "attribute4";
    cols["dealNum"] = "dealNum";
    cols["soLineId"] = "soLineId";
    cols["releaseRevenue"] = "releaseRevenue";
    cols["attribute10"] = "attribute10";
    cols["attribute14"] = "attribute14";
    cols["attribute13"] = "attribute13";
    cols["attribute12"] = "attribute12";
    cols["contEventType"] = "contEventType";
    cols["application"] = "application";
    cols["attribute11"] = "attribute11";
    cols["cogs"] = "cogs";
    cols["comments"] = "comments";
    cols["templateId"] = "templateId";
    cols["ruleCategory"] = "ruleCategory";
    cols["description"] = "description";
    cols["deferredMethod"] = "deferredMethod";
    cols["attribute21"] = "attribute21";
    cols["attribute20"] = "attribute20";
    cols["transLineId"] = "transLineId";
    cols["attribute25"] = "attribute25";
    cols["attribute24"] = "attribute24";
    cols["attribute23"] = "attribute23";
    cols["attribute22"] = "attribute22";
    cols["autoReleaseDays"] = "autoReleaseDays";
    cols["percentage"] = "percentage";
    cols["dealArrangementId"] = "dealArrangementId";
    cols["element"] = "element";
    cols["attribute18"] = "attribute18";
    cols["attribute17"] = "attribute17";
    cols["attribute16"] = "attribute16";
    cols["attribute15"] = "attribute15";
    cols["attribute19"] = "attribute19";
    cols["customerPayment"] = "customerPayment";



    //this.columns=cols;
    let rmanContTransToReleaseLabels = new RmanContTransToReleaseLabels();

    this.columns = rmanContTransToReleaseLabels.fieldLabels;

    this.columnOptions = [];

    this.cols = [];

    //for (let prop in cols) {
    for (let prop in this.columns) {
      this.cols.push({ field: prop, header: this.columns[prop] });
      this.columnOptions.push({ label: this.columns[prop], value: prop });
    }


    this.buildForm();

  }

  buildForm() {
    this.transToReleaseForm = this.formBuilder.group({
      'releaseType':['',[Validators.required]],
      'releaseRevenue':['',[Validators.required]],

    });



    this.transToReleaseForm.valueChanges
      .subscribe(data => this.onValueChanged(data));

    this.onValueChanged();
  }

  onValueChanged(data?: any) {
    if (!this.transToReleaseForm) { return; }
    const form = this.transToReleaseForm;

    for (const field in this.formErrors) {
      // clear previous error message (if any)
      this.formErrors[field] = '';
      const control = form.get(field);
      // //console.log(control.valid);
      if (control && control.dirty && !control.valid) {
        const messages = this.validationMessages[field];
        for (const key in control.errors) {
          this.formErrors[field] += messages[key] + ' ';
        }
      }
    }
  }

  formErrors = {
    'releaseType':'','releaseRevenue':''
  };

  validationMessages = {

      'releaseType':{
      'required': "Release Type  is required"
      },
      'releaseRevenue':{
      'required': "Release Revenue is required"
      // 'condition1': "Release Revenue is greater than Revenue,Release should be equal to Revenue",
      // 'condition2': "Release Revenue is less than Revenue,Release should be equal to Revenue"
      }


      /*,'workspacedbPort':{
'required': "DB Port is required",
// 'minlength': 'DB Port must be at least 4 characters long.',
// 'maxlength': 'DB Port cannot be more than 6 characters long.',
'pattern':'Please enter the valid "Port" number with minimum 4 and maximum 6 digit.'
},'workspacedbUser':{
'required': "DB User is required"
},'workspacedbPass':{
'required': "DB Password is required"
}
 */

  }




  transformRmanUsers(rmanUsers: any) {
    if (rmanUsers)
      return rmanUsers.fullName;
  }
  transformRmanUsers1(rmanUsers1: any) {
    if (rmanUsers1)
      return rmanUsers1.fullName;
  }
  transformRmanLookupsV(rmanLookupsV: any) {
    if (rmanLookupsV)
      return rmanLookupsV.lookupDescription;
  }
  transformRmanLookupsV1(rmanLookupsV1: any) {
    if (rmanLookupsV1)
      return rmanLookupsV1.lookupDescription;
  }

  /* To Remove Duplicate Rows in Array*/
  removeDuplicate(arr: any, prop: any) {
    var new_arr: any = [];
    var lookup = {};
    for (var i in arr) {
      lookup[arr[i][prop]] = arr[i];
    }
    for (i in lookup) {
      new_arr.push(lookup[i]);
    }
    // //console.log(new_arr);
    return new_arr;
  }

  reset(dt: DataTable) {
    this.paginationOptions = {};
    this.rmanContTransToRelease = new RmanContTransToReleaseImpl();
    dt.reset();
    //this.getAllRmanContTransToRelease();
  }



  getReleaseLines(){
    this.rmanContReleaseLinesVComponent.getAllRmanContReleaseLinesV();
  }


  onSelectApplicationLevel(applicationLevel: any) {
    // //console.log('***************************' + applicationLevel);

    this.rmanContSourceLovService.getAllRmanContSourceLov(this.paginationOptions, { 'dealArrangementId': this.arrIdK, 'applicationLevel': applicationLevel }).then((rmanContSourceLovList: any) => {
    // //console.log('**************Start**********');
      this.rmanContSourceLovList = rmanContSourceLovList.content.filter((item: any) => item.applicationLevel == applicationLevel, true);
      //this.rmanContSourceLovList = this.rmanContSourceLovList
     // //console.log('**************End**********');
    // //console.log(this.removeDuplicate(this.rmanContSourceLovList, 'sourceHeaderId'));
      this.rmanContSourceLovList = this.removeDuplicate(this.rmanContSourceLovList, 'sourceHeaderId');
      if (applicationLevel == 'ARRANGEMENT' && this.rmanContSourceLovList.length > 0) {

        this.rmanContTransToRelease.sourceHeaderId = this.rmanContSourceLovList[0].sourceHeaderId;
      } else if (!this.rmanContTransToRelease.sourceHeaderId)  {
        this.rmanContTransToRelease.sourceHeaderId = null;
      }
      else {
        this.onSelectSourceHeader(this.rmanContTransToRelease.sourceHeaderId);
      }



      this.prepareRmanContSourceLovObject();
    }).catch((err: any) => {
          this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
           this.loading = false;
       });

  }

  onSelectSourceHeader(sourceHeaderId: any) {
    this.rmanContSourceLov1List = [];
   this.loading = true;
    this.rmanContSourceLovService.getAllRmanContSourceLov(this.paginationOptions, { 'dealArrangementId': this.arrIdK
    , 'applicationLevel': this.rmanContTransToRelease.applicationLevel }).then((rmanContSourceLov1List: any) => {
    // //console.log('**************Start**********');
      this.rmanContSourceLov1List = rmanContSourceLov1List.content.filter((item: any) => item.sourceHeaderId == sourceHeaderId, true);
      //this.rmanContSourceLovList = this.rmanContSourceLovList
   // //console.log('**************End**********');
   // //console.log(this.removeDuplicate(this.rmanContSourceLov1List, 'sourceLineId'));
      this.rmanContSourceLov1List = this.removeDuplicate(this.rmanContSourceLov1List, 'sourceLineId');
   // //console.log('Source Lines');
   // //console.log(this.rmanContSourceLov1List);
     this.prepareRmanContSourceLov1Object();
      this.loading = false;
    }).catch((err: any) => {
          this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
           this.loading = false;
       });
  }

  onSelectContingency(contHeaderId: any) {
   // //console.log('Cont HeaderId : ' + contHeaderId);
    let rmanContHeaderFilterList: any = [];
    rmanContHeaderFilterList = this.rmanContHeaderVList.filter((item: any) => item.ruleHeaderId == contHeaderId);
 // //console.log('Size of HeaderList :' + rmanContHeaderFilterList.length);
    this.rmanContTransToRelease.ranking = rmanContHeaderFilterList[0].ranking;
    this.rmanContTransToRelease.ruleCategory = rmanContHeaderFilterList[0].ruleCategory;


    this.rmanContLinkTemplateVService.getAllRmanContLinkTemplateV(this.paginationOptions, { 'ruleHeaderId': contHeaderId }).then((rmanContLinkTemplateVList: any) => {
      this.rmanContLinkTemplateVList = rmanContLinkTemplateVList.content.filter((item: any) => item.ruleHeaderId == contHeaderId, true);
      this.prepareRmanContLinkTemplateVObject();
    }).catch((err: any) => {
          this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
           this.loading = false;
       });

  }

  onSelectContTemplate(contTemplateId: any) {
   // //console.log('Cont Template Id : ' + contTemplateId);
    let rmanContTemplateFilterList: any = [];
    rmanContTemplateFilterList = this.rmanContLinkTemplateVList.filter((item: any) => item.templateId == contTemplateId);
  // //console.log('Size of Cont Template List :' + rmanContTemplateFilterList.length);
    this.rmanContTransToRelease.revenue = rmanContTemplateFilterList[0].revenue;
    this.rmanContTransToRelease.cogs = rmanContTemplateFilterList[0].cogs;
    this.rmanContTransToRelease.invoiceHold = rmanContTemplateFilterList[0].invoiceHold;
    this.rmanContTransToRelease.applyType = rmanContTemplateFilterList[0].applyType;

    if (this.rmanContTransToRelease.applyType == 'PERCENT') {
      this.rmanContTransToRelease.deferredMethod = 'PRORATE';
    } else {
      this.rmanContTransToRelease.deferredMethod = 'FIFO';
    }

  }

  onSelecttAutoReleaseEvent(autoEvent: any) {
    // //console.log('Release Event :' + autoEvent);
    if (autoEvent != 'Auto') {
      this.rmanContTransToRelease.autoReleaseDays = null;
    }

  }

  onContRelease(releaseType: any, applyType: any) {
    if (releaseType == 'FULL') {
     // this.rmanContTransToRelease.releaseCogs = this.rmanContTransToRelease.cogs;
      this.rmanContTransToRelease.releaseRevenue = this.rmanContTransToRelease.revenue;
    } else {
      this.rmanContTransToRelease.releaseCogs = null;
      this.rmanContTransToRelease.releaseRevenue = null;
    }
  }



  getAllRmanContTransToRelease() {
    this.loading = true;
    this.rmanContTransToRelease.dealArrangementId = this.arrIdK;
    this.rmanContTransToReleaseService.getAllRmanContTransToRelease(this.paginationOptions, this.rmanContTransToRelease).then((rmanContTransToReleaseList: any) => {
      // //console.log(rmanContTransToReleaseList);
      this.loading = false;
      this.datasource = rmanContTransToReleaseList.content;
      this.rmanContTransToReleaseList = rmanContTransToReleaseList.content;
      if (this.rmanContTransToReleaseList.length > 0) {
        /*Begining of Code used for default First Row Selected*/
        this.selectedRmanContTransToRelease = this.rmanContTransToReleaseList[0];
        this.pTransHeaderId = this.selectedRmanContTransToRelease.transHeaderId;
        this.rmanContReleaseLinesVComponent.parentCall(this.selectedRmanContTransToRelease)
        // //console.log(this.pTransHeaderId);
        /*End of Code used for default First Row Selected*/
      }
      this.rmanContTransToReleaseList.forEach((releaseCont) => {
        this.status = releaseCont.status;
        // //console.log('this.status',this.status);
      });
      this.totalElements = rmanContTransToReleaseList.totalElements;
      this.pageSize = rmanContTransToReleaseList.size;
      this.displaySearchDialog = false;

    }).catch((err: any) => {
          this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
           this.loading = false;
           // //console.log('err at 623' + err)
       });
  }
  getAllRmanContTransToReleaseFilter() {
    this.loading = true;
    this.rmanContTransToReleaseService.getAllRmanContTransToRelease(this.paginationOptions, this.rmanContTransToRelease).then((rmanContTransToReleaseList: any) => {
      // //console.log(rmanContTransToReleaseList);
      this.loading = false;
      this.datasource = rmanContTransToReleaseList.content;
      this.rmanContTransToReleaseList = rmanContTransToReleaseList.content;
      if (this.rmanContTransToReleaseList.length > 0) {
        /*Begining of Code used for default First Row Selected*/
        this.selectedRmanContTransToRelease = this.rmanContTransToReleaseList[0];
        this.pTransHeaderId = this.selectedRmanContTransToRelease.transHeaderId;
        this.rmanContReleaseLinesVComponent.parentCall(this.selectedRmanContTransToRelease)
        // //console.log(this.pTransHeaderId);
        /*End of Code used for default First Row Selected*/
      }
      this.rmanContTransToReleaseList.forEach((releaseCont) => {
        this.status = releaseCont.status;
        // //console.log('this.status',this.status);
      });
      this.totalElements = rmanContTransToReleaseList.totalElements;
      this.pageSize = rmanContTransToReleaseList.size;
      this.displaySearchDialog = false;

    }).catch((err: any) => {
          this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
           this.loading = false;
           // //console.log('err at 623' + err)
       });
  }


  getRmanContTransToRelease(event: any) {
    this.loading = true;
    let status:any;
    let first: number = event.first;
    let rows: number = event.rows;
    let pageNumber: number = first / rows;
    this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
    // //console.log('*********************');
    // //console.log(this.paginationOptions);
    // //console.log('*********************');
    this.rmanContTransToRelease.dealArrangementId = this.arrIdK;
    this.rmanContTransToReleaseService.getAllRmanContTransToRelease(this.paginationOptions, this.rmanContTransToRelease).then((rmanContTransToReleaseList: any) => {
      // //console.log('Release Contingencies',rmanContTransToReleaseList);
      this.loading = false;
      this.datasource = rmanContTransToReleaseList.content;
      this.rmanContTransToReleaseList = rmanContTransToReleaseList.content;
      if (this.rmanContTransToReleaseList.length > 0) {
        /*Begining of Code used for default First Row Selected*/
        this.selectedRmanContTransToRelease = this.rmanContTransToReleaseList[0];
        this.pTransHeaderId = this.selectedRmanContTransToRelease.transHeaderId;
        this.rmanContReleaseLinesVComponent.parentCall(this.selectedRmanContTransToRelease)
        // //console.log(this.pTransHeaderId);
        /*End of Code used for default First Row Selected*/
      }
      this.rmanContTransToReleaseList.forEach((releaseCont) => {
        this.status = releaseCont.status;
        // //console.log('this.status',this.status);
      });
      this.status = this.status;
      // //console.log('status',this.status);
      this.totalElements = rmanContTransToReleaseList.totalElements;
      this.pageSize = rmanContTransToReleaseList.size;

    }).catch((err: any) => {
          this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
           this.loading = false;
       });

  }



  showDialogToAdd() {
  //  this.addFlag = true;
   // setTimeout(() => {
      this.rmanContTransLinesComponent.showDialogToAdd();
      
  //  }, 3000);
  }

  releaseRevenueChange(event:any){

    if(this.rmanContTransToRelease.releaseRevenue > this.rmanContTransToRelease.revenue){
      this.displayConditionDialog=true;
      this.conditionFlag=true;
    }

    if(this.rmanContTransToRelease.releaseRevenue <= this.rmanContTransToRelease.revenue ){
      this.displayConditionDialog=false;
      this.conditionFlag=false;
    }
  }



  release() {
   // //console.log(this.rmanContTransToRelease, 'this.rmanContTransToRelease')
   //console.log('Contingency Release new or exists Record : '+this.newRmanContTransToRelease)
    if (this.newRmanContTransToRelease) {
      this.rmanContTransToReleaseService.saveRmanContTransToRelease(this.rmanContTransToRelease).then((response: any) => {
        this.growlMsgs=[{severity:'success', summary:'Success Message', detail:'Released successfully'}];
        this.getAllRmanContTransToRelease();
      }).catch((err: any) => {
            this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while saving the data'}];
             this.loading = false;
         });
    }
    else {
      let headerId = this.rmanContTransToRelease.transHeaderId;
      let lineId = this.rmanContTransToRelease.transLineId;

      if (this.rmanContTransToRelease.ruleCategory == 'PROVISION') {
        this.confirmationService.confirm({
          message: 'Are you sure release for customer payment?',
          header: 'Confirmation',
          icon: 'fa fa-question-circle',
          accept: () => {
            this.rmanContTransToRelease.customerPayment = 'Y';
         
            this.rmanContTransToReleaseService.updateRmanContTransToRelease(this.rmanContTransToRelease).then((response: any) => {
              // //console.log(response, 'update sucess')
              if (response.transHeaderId != null) {
                this.rmanContTransToReleaseService.insertReleaseLinesRmanContengencies(headerId, lineId).then(response => {
                  // //console.log(response, 'insert success at if 715')
                  this.growlMsgs = [{ severity: 'success', summary: 'Confirm Release', detail: 'Contingency is successfully released.' }];
                  this.getAllRmanContTransToRelease();
                  this.getReleaseLines();
                }).catch((err: any) => {
                      this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
                       this.loading = false;
                   });

              }


            }).catch((err: any) => {
                  this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while getting data'}];
                   this.loading = false;
               });
            this.rmanContTransToRelease = new RmanContTransToReleaseImpl();
            this.displayDialog = false;
          },
          reject: () => {
            this.rmanContTransToRelease.customerPayment = 'N';
            this.rmanContTransToReleaseService.updateRmanContTransToRelease(this.rmanContTransToRelease).then((response: any) => {
             // //console.log(response, 'update at reject')
              if (response.transHeaderId != null) {
                // //console.log('740')
                this.rmanContTransToReleaseService.insertReleaseLinesRmanContengencies(headerId, lineId).then(response => {
                  // //console.log(response, 'insert at reject')
                  this.growlMsgs = [{ severity: 'warn', summary: 'Warn Release', detail: 'You have rejected' }];
                  this.getAllRmanContTransToRelease();
                 this.getReleaseLines();
                }).catch((err: any) => {
                  // //console.log('748')
                      this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while inserting the data'}];
                       this.loading = false;
                   });

              } else {
                // //console.log('752')
              }


            }).catch((err: any) => {
                  this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while updating the data'}];
                   this.loading = false;
               });
            this.rmanContTransToRelease = new RmanContTransToReleaseImpl();
            this.displayDialog = false;
          }
        });
      } else {
        this.rmanContTransToRelease.customerPayment = 'N';
        this.rmanContTransToReleaseService.updateRmanContTransToRelease(this.rmanContTransToRelease).then((response: any) => {
          // //console.log(response, 'update success on else case')
          if (response.transHeaderId != null) {
            this.rmanContTransToReleaseService.insertReleaseLinesRmanContengencies(headerId, lineId).then(response => {
              // //console.log(response, 'insertineg at else case')
              this.getAllRmanContTransToRelease();
              this.getReleaseLines();
            }).catch((err: any) => {
                let e:any = JSON.parse(err.error);
                this.growlMsgs=[{severity:'error', summary:'Error Message', detail: e.message }];
                this.loading = false;
               });

          }


        }).catch((err: any) => {
              this.growlMsgs=[{severity:'error', summary:'Error Message', detail:'Error occured while updating the data'}];
               this.loading = false;
           });
        this.rmanContTransToRelease = new RmanContTransToReleaseImpl();
        this.displayDialog = false;
      }



    }



    this.buildForm();

  }

  save() {
    this.rmanContTransLinesComponent.save();
  }


  delete(rmanContTransToRelease: any) {
    this.rmanContTransToRelease = rmanContTransToRelease;

    //this.rmanContTransToRelease = null;
    this.displayDialog = false;

    this.confirmationService.confirm({
      message: 'Are you sure you want to delete this record?',
      header: 'Confirmation',
      icon: '',
      accept: () => {
        this.rmanContTransToReleaseList.splice(this.findSelectedRmanContTransToReleaseIndex(), 1);
        this.rmanContTransToReleaseService.deleteRmanContTransToRelease(this.rmanContTransToRelease).then((response: any) => {
          this.rmanContTransToRelease = new RmanContTransToReleaseImpl();
          this.getAllRmanContTransToRelease();
          //this.rmanContTransToReleaseList.splice(this.findSelectedRmanContTransToReleaseIndex(), 1);
          //this.rmanContTransToRelease = null;
          // this.displayDialog = false;
        });
        this.growlMsgs=[{severity:'success', summary:'Success Message', detail:'Deleted successfully'}];
      },
      reject: () => {
        this.growlMsgs=[{severity:'warn', summary:'Warn Message', detail:'You have rejected'}];
      }
    })



  }

  editRow(rmanContTransToRelease: any) {
    this.newRmanContTransToRelease = false;
    this.rmanContTransToRelease = this.cloneRmanContTransToRelease(rmanContTransToRelease);
    setTimeout(()=>{
      this.onSelectApplicationLevel(this.rmanContTransToRelease.applicationLevel);
    },2000);
 
    this.prepareRmanUsersObject();
    this.prepareRmanUsers1Object();
    this.prepareRmanLookupsVObject();
    this.prepareRmanLookupsV1Object();
 //   this.prepareRmanContProdNameLovVObject();
    this.displayDialog = true;
    this.buildForm();

  }

  releaseLine(rmanContTransToRelease: any) {
    this.newRmanContTransToRelease = false;
    if (rmanContTransToRelease.releaseType != "FULL") {
      this.rmanContTransToRelease = this.cloneRmanContTransToRelease(rmanContTransToRelease);
      this.displayDialog = true;
    } else {
      this.growlMsgs = [{ severity: 'info', summary: 'Release Type Status', detail: 'This selected Contingency is Fully released..!' }];

    }

  }

  findSelectedRmanContTransToReleaseIndex(): number {
    return this.rmanContTransToReleaseList.indexOf(this.selectedRmanContTransToRelease);
  }

  onRowSelect(event: any) {
    this.selectedRmanContTransToRelease = event.data;
    this.pTransHeaderId = this.selectedRmanContTransToRelease.transHeaderId;
    this.rmanContReleaseLinesVComponent.parentCall(this.selectedRmanContTransToRelease)

  }

  onRowUnSelect (event) {
    this.rmanContReleaseLinesVComponent.parentCall('')
  }

  cloneRmanContTransToRelease(c: RmanContTransToRelease): RmanContTransToRelease {
    let rmanContTransToRelease: any
      = new RmanContTransToReleaseImpl();
    for (let prop in c) {
      rmanContTransToRelease[prop] = c[prop];
    }
    return rmanContTransToRelease;
  }

  hideColumnMenu: boolean = true;

  toggleColumnMenu() {
    if (this.hideColumnMenu) {
      this.hideColumnMenu = false;
    } else {
      this.hideColumnMenu = true;
    }
  }

  showFilter: boolean = false;

  toggleFilterBox() {
    if (this.showFilter) {
      this.showFilter = false;
    } else {
      this.showFilter = true;
    }
  }

  showDialogToSearch() {

    this.rmanContTransToReleaseSearch=new RmanContTransToReleaseImpl();

    if (this.isSerached == 0) {
      this.rmanContTransToReleaseSearch = new RmanContTransToReleaseImpl();
    }
    this.displaySearchDialog = true;

  }
  cancelSearch () {
    this.displaySearchDialog = false;
    this.rmanContTransToReleaseSearch = new RmanContTransToReleaseImpl();
}

  search() {

    this.isSerached = 1;
    this.rmanContTransToRelease = this.rmanContTransToReleaseSearch;
    this.rmanContTransToRelease.dealArrangementId = this.arrIdK;
   	this.paginationOptions={};
    this.getAllRmanContTransToRelease();
  }

  prepareRmanUsersObject() {
    let rmanUsersTempObj: any = [];
    this.rmanUsersList.forEach((rmanUsers) => {
      rmanUsersTempObj.push({ label: rmanUsers.fullName, value: rmanUsers.rmanUserId });
    });

    this.rmanUsers = rmanUsersTempObj;

  }

  prepareRmanUsers1Object() {
    let rmanUsers1TempObj: any = [];
    this.rmanUsers1List.forEach((rmanUsers1: any) => {
      rmanUsers1TempObj.push({ label: rmanUsers1.fullName, value: rmanUsers1.rmanUserId });
    });

    this.rmanUsers1 = rmanUsers1TempObj;

  }

  prepareRmanLookupsVObject() {
    let rmanLookupsVTempObj: any = [];
    rmanLookupsVTempObj.push({ label: 'Select Invoice Hold', value: null });
    this.rmanLookupsVList.forEach((rmanLookupsV: any) => {
      rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode });
    });

    this.rmanLookupsV = rmanLookupsVTempObj;

  }

  prepareRmanLookupsV1Object() {
    let rmanLookupsV1TempObj: any = [];
    rmanLookupsV1TempObj.push({ label: 'Select Event', value: null });
    this.rmanLookupsV1List.forEach((rmanLookupsV1: any) => {
      rmanLookupsV1TempObj.push({ label: rmanLookupsV1.lookupDescription, value: rmanLookupsV1.lookupCode });
    });

    this.rmanLookupsV1 = rmanLookupsV1TempObj;

  }
  
   exportExcel() {
	   this.rmanContTransToRelease.dealArrangementId = this.arrIdK;
      let serviceUrl = this.rmanContTransToReleaseService.getServiceUrl(this.paginationOptions, this.rmanContTransToRelease, 1);
      //console.log('serviceUrl', serviceUrl);
      window.location.href = serviceUrl;

    }

  prepareRmanLookupsV2Object() {
    let rmanLookupsV2TempObj: any = [];
    rmanLookupsV2TempObj.push({ label: 'Select Application Level', value: null });
    this.rmanLookupsV2List.forEach((rmanLookupsV2: any) => {
      rmanLookupsV2TempObj.push({ label: rmanLookupsV2.lookupDescription, value: rmanLookupsV2.lookupCode });
    });

    this.rmanLookupsV2 = rmanLookupsV2TempObj;

  }

  prepareRmanContLinkTemplateVObject() {
    let rmanContLinkTemplateVTempObj: any = [];
    rmanContLinkTemplateVTempObj.push({ label: 'Select Template ID', value: null });
    this.rmanContLinkTemplateVList.forEach((rmanContLinkTemplateV: any) => {
      rmanContLinkTemplateVTempObj.push({ label: rmanContLinkTemplateV.templateName, value: rmanContLinkTemplateV.templateId });
    });

    this.rmanContLinkTemplateV = rmanContLinkTemplateVTempObj;

  }

  prepareRmanContProdNameLovVObject() {

    this.rmanContProdNameLovVList = this.removeDuplicate(this.rmanContProdNameLovVList, 'elementType');
    this.rmanContProdGroupList = this.removeDuplicate(this.rmanContProdGroupList, 'productType');
    this.rmanContProdCategoryList = this.removeDuplicate(this.rmanContProdCategoryList, 'productCategory');
    this.rmanContProdNameList = this.removeDuplicate(this.rmanContProdNameList, 'productName');

    let rmanContProdNameLovVTempObj: any = [];
  //  rmanContProdNameLovVTempObj.push({ label: '--!!Select Element--', value: '' });
    this.rmanContProdNameLovVList.forEach((rmanContProdNameLovV) => {
      rmanContProdNameLovVTempObj.push({ label: rmanContProdNameLovV.elementType, value: rmanContProdNameLovV.elementType });
    });
    this.rmanContProdNameLovV = rmanContProdNameLovVTempObj;
    this.rmanContProdNameLovV.unshift({ label: 'Select Element', value: null})
   // //console.log(this.rmanContProdNameLovV, '!!rmanContProdNameLovVrmanContProdNameLovV')

    rmanContProdNameLovVTempObj = [];
   // rmanContProdNameLovVTempObj.push({ label: '--!!Select Product Group--', value: '' });
    this.rmanContProdGroupList.forEach((rmanContProdNameLovV: any) => {
      rmanContProdNameLovVTempObj.push({ label: rmanContProdNameLovV.productType, value: rmanContProdNameLovV.productType });
    });

    this.rmanContProdGroupV1 = rmanContProdNameLovVTempObj;
    this.rmanContProdGroupV1.unshift({ label: 'Select Product Group', value: null})

    rmanContProdNameLovVTempObj = [];
   // rmanContProdNameLovVTempObj.push({ label: '--Select Product Category--', value: '' });
    this.rmanContProdCategoryList.forEach((rmanContProdNameLovV: any) => {
      rmanContProdNameLovVTempObj.push({ label: rmanContProdNameLovV.productCategory, value: rmanContProdNameLovV.productCategory });
    });

    this.rmanContProdCategoryV1 = rmanContProdNameLovVTempObj;
    this.rmanContProdCategoryV1.push({label: 'Select Product Category', value: null})

    rmanContProdNameLovVTempObj = [];
 //   rmanContProdNameLovVTempObj.push({ label: '--Select Product Name--', value: '' });
    this.rmanContProdNameList.forEach((rmanContProdNameLovV: any) => {
      rmanContProdNameLovVTempObj.push({ label: rmanContProdNameLovV.productName, value: rmanContProdNameLovV.productName });
    });

    this.rmanContProdNameV1 = rmanContProdNameLovVTempObj;
    this.rmanContProdNameV1.unshift({ label: 'Select Product Name', value: null})


  }

  prepareRmanContHeaderVObject() {
    let rmanContHeaderVTempObj: any = [];
    rmanContHeaderVTempObj.push({ label: 'Select Rule Header ID', value: '' });
    this.rmanContHeaderVList.forEach((rmanContHeaderV: any) => {
      rmanContHeaderVTempObj.push({ label: rmanContHeaderV.ruleName, value: rmanContHeaderV.ruleHeaderId });
    });

    this.rmanContHeaderV = rmanContHeaderVTempObj;

  }

  prepareRmanLookupsV3Object() {
    let rmanLookupsV3TempObj: any = [];
    rmanLookupsV3TempObj.push({ label: 'Select Cont Event Type', value: null });
    this.rmanLookupsV3List.forEach((rmanLookupsV3: any) => {
      rmanLookupsV3TempObj.push({ label: rmanLookupsV3.lookupDescription, value: rmanLookupsV3.lookupCode });
    });

    this.rmanLookupsV3 = rmanLookupsV3TempObj;

  }

  prepareRmanLookupsV6Object() {
    let rmanLookupsV6TempObj: any = [];
    rmanLookupsV6TempObj.push({ label: 'Select Standard', value: null });
    this.rmanLookupsV6List.forEach((rmanLookupsV6: any) => {
      rmanLookupsV6TempObj.push({ label: rmanLookupsV6.lookupDescription, value: rmanLookupsV6.lookupCode });
    });

    this.rmanLookupsV6 = rmanLookupsV6TempObj;

  }

  prepareRmanLookupsV7Object() {
    let rmanLookupsV7TempObj: any = [];
    rmanLookupsV7TempObj.push({ label: 'Select Release Type(*)', value: null });
    this.rmanLookupsV7List.forEach((rmanLookupsV7: any) => {
      rmanLookupsV7TempObj.push({ label: rmanLookupsV7.lookupDescription, value: rmanLookupsV7.lookupCode });
    });

    this.rmanLookupsV7 = rmanLookupsV7TempObj;

  }

  prepareContAdditionalInfo3LovVObject() {
    let contAdditionalInfo3LovVTempObj: any = [];
    this.contAdditionalInfo3LovVList.forEach((contAdditionalInfo3LovV: any) => {
      contAdditionalInfo3LovVTempObj.push({ label: contAdditionalInfo3LovV.attribute3, value: contAdditionalInfo3LovV.attribute3 });
    });

    this.contAdditionalInfo3LovV = contAdditionalInfo3LovVTempObj;

  }

  prepareRmanContSourceLovObject() {
    let rmanContSourceLovTempObj: any = [];
    rmanContSourceLovTempObj.push({ label: 'Select SO Number', value: null });
    this.rmanContSourceLovList.forEach((rmanContSourceLov: any) => {
      rmanContSourceLovTempObj.push({ label: rmanContSourceLov.sourceName, value: rmanContSourceLov.sourceHeaderId });
    });

    this.rmanContSourceLov = rmanContSourceLovTempObj;
    // //console.log('testing', this.rmanContSourceLov);

  }


  prepareRmanContSourceLov1Object() {
    let rmanContSourceLov1TempObj: any = [];
    rmanContSourceLov1TempObj.push({ label: 'Select SO Line Number', value: null });
    this.rmanContSourceLov1List.forEach((rmanContSourceLov1: any) => {
      rmanContSourceLov1TempObj.push({ label: rmanContSourceLov1.sourceLine, value: rmanContSourceLov1.sourceLineId });
    });

    this.rmanContSourceLov1 = rmanContSourceLov1TempObj;
   // //console.log('mm', this.rmanContSourceLov1);

  }

  onBeforeToggle(evt: any) {
    // //console.log('event test', evt);
    this.collapsed = evt.collapsed;
  }
  prepareRmanContProdNameLovV1Object() {

    let rmanContProdNameLovV1TempObj: any = [];
    this.rmanContProdNameLovV1List.forEach((rmanContProdNameLovV1: any) => {
      rmanContProdNameLovV1TempObj.push({ label: rmanContProdNameLovV1.productType, value: rmanContProdNameLovV1.productType });
    });

    this.rmanContProdNameLovV1 = rmanContProdNameLovV1TempObj;

  }

  prepareRmanContProdNameLovV2Object() {
    let rmanContProdNameLovV2TempObj: any = [];
    this.rmanContProdNameLovV2List.forEach((rmanContProdNameLovV2: any) => {
      rmanContProdNameLovV2TempObj.push({ label: rmanContProdNameLovV2.productCategory, value: rmanContProdNameLovV2.productCategory });
    });

    this.rmanContProdNameLovV2 = rmanContProdNameLovV2TempObj;

  }
  
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }  



}


class RmanContTransToReleaseImpl {
  constructor(public attribute30?: any, public invoiceHold?: any, public sourceLineId?: any, public ruleHeaderId?: any, public releaseCogs?: any, public applyType?: any, public sourceHeaderId?: any, public lastUpdateDate?: any, public event?: any, public ranking?: any, public status?: any, public attribute29?: any, public attribute28?: any, public attribute27?: any, public avgContAcceptDur?: any, public attribute26?: any, public attribute3?: any, public createdBy?: any, public transHeaderId?: any, public maxDuration?: any, public attribute2?: any, public lastUpdatedBy?: any, public attribute1?: any, public applicationLevel?: any, public soHeaderId?: any, public revenue?: any, public creationDate?: any, public attribute9?: any, public attribute8?: any, public attribute7?: any, public productName?: any, public attribute6?: any, public attribute5?: any, public releaseType?: any, public attribute4?: any, public dealNum?: any, public soLineId?: any, public releaseRevenue?: any, public attribute10?: any, public attribute14?: any, public attribute13?: any, public attribute12?: any, public contEventType?: any, public application?: any, public attribute11?: any, public cogs?: any, public comments?: any, public templateId?: any, public ruleCategory?: any, public description?: any, public deferredMethod?: any, public attribute21?: any, public attribute20?: any, public transLineId?: any, public attribute25?: any, public attribute24?: any, public attribute23?: any, public attribute22?: any, public autoReleaseDays?: any, public percentage?: any, public dealArrangementId?: any, public element?: any, public attribute18?: any, public attribute17?: any, public attribute16?: any, public attribute15?: any, public attribute19?: any, public customerPayment?: any) { }
}

interface ILabels {
  [index: string]: string;
}
