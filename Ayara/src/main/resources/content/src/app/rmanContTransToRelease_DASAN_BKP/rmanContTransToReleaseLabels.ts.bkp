interface ILabels {
  [index: string]: string;
}

export class RmanContTransToReleaseLabels {

  fieldLabels: ILabels;

  constructor() {

    this.fieldLabels = {};

    this.fieldLabels["attribute30"] = "attribute30";
    this.fieldLabels["invoiceHold"] = "Invoice Hold";
    this.fieldLabels["sourceLineId"] = "SO Line Number";
    this.fieldLabels["ruleHeaderId"] = "Name";
    this.fieldLabels["releaseCogs"] = "Release Cogs";
    this.fieldLabels["applyType"] = "Type";
    this.fieldLabels["sourceHeaderId"] = "SO Number";
    this.fieldLabels["lastUpdateDate"] = "LAST UPDATE DATE";
    this.fieldLabels["event"] = "Release Event";
    this.fieldLabels["ranking"] = "Ranking";
    this.fieldLabels["status"] = "Status";
    this.fieldLabels["attribute29"] = "Post Allocation Contingency";
    this.fieldLabels["attribute28"] = "Include FV Allocation";
    this.fieldLabels["attribute27"] = "ATTRIBUTE27";
    this.fieldLabels["avgContAcceptDur"] = "AVG CONT ACCEPT DUR";
    this.fieldLabels["attribute26"] = "Standard";
    this.fieldLabels["attribute3"] = "Product Group";
    this.fieldLabels["createdBy"] = "CREATED BY";
    this.fieldLabels["transHeaderId"] = "TRANS HEADER ID";
    this.fieldLabels["maxDuration"] = "Forecasting Days";
    this.fieldLabels["attribute2"] = "Product Category";
    this.fieldLabels["lastUpdatedBy"] = "LAST UPDATED BY";
    this.fieldLabels["attribute1"] = "ATTRIBUTE1";
    this.fieldLabels["applicationLevel"] = "Release Level";
    this.fieldLabels["soHeaderId"] = "SO HEADER ID";
    this.fieldLabels["revenue"] = "Revenue";
    this.fieldLabels["creationDate"] = "CREATION DATE";
    this.fieldLabels["attribute9"] = "ATTRIBUTE9";
    this.fieldLabels["attribute8"] = "Template";
    this.fieldLabels["attribute7"] = "Name";
    this.fieldLabels["productName"] = "Product Name";
    this.fieldLabels["attribute6"] = "SO Line Number";
    this.fieldLabels["attribute5"] = "SO Number";
    this.fieldLabels["releaseType"] = "Release Type";
    this.fieldLabels["attribute4"] = "Contingency Type";
    this.fieldLabels["dealNum"] = "DEAL NUM";
    this.fieldLabels["soLineId"] = "SO LINE ID";
    this.fieldLabels["releaseRevenue"] = "Release Revenue";
    this.fieldLabels["attribute10"] = "ATTRIBUTE10";
    this.fieldLabels["attribute14"] = "Additional Info";
    this.fieldLabels["attribute13"] = "ATTRIBUTE13";
    this.fieldLabels["attribute12"] = "ATTRIBUTE12";
    this.fieldLabels["contEventType"] = "Current/Future";
    this.fieldLabels["application"] = "APPLICATION";
    this.fieldLabels["attribute11"] = "ATTRIBUTE11";
    this.fieldLabels["cogs"] = "COGS";
    this.fieldLabels["comments"] = "Comments";
    this.fieldLabels["templateId"] = "Template ID";
    this.fieldLabels["ruleCategory"] = "Rule Category";
    this.fieldLabels["description"] = "DESCRIPTION";
    this.fieldLabels["deferredMethod"] = "Method";
    this.fieldLabels["attribute21"] = "ATTRIBUTE21";
    this.fieldLabels["attribute20"] = "ATTRIBUTE20";
    this.fieldLabels["transLineId"] = "TRANS LINE ID";
    this.fieldLabels["attribute25"] = "ATTRIBUTE25";
    this.fieldLabels["attribute24"] = "ATTRIBUTE24";
    this.fieldLabels["attribute23"] = "ATTRIBUTE23";
    this.fieldLabels["attribute22"] = "ATTRIBUTE22";
    this.fieldLabels["autoReleaseDays"] = "Release Days";
    this.fieldLabels["percentage"] = "PERCENTAGE";
    this.fieldLabels["dealArrangementId"] = "DEAL ARRANGEMENT ID";
    this.fieldLabels["element"] = "Element Type";
    this.fieldLabels["attribute18"] = "ATTRIBUTE18";
    this.fieldLabels["attribute17"] = "ATTRIBUTE17";
    this.fieldLabels["attribute16"] = "ATTRIBUTE16";
    this.fieldLabels["attribute15"] = "ATTRIBUTE15";
    this.fieldLabels["attribute19"] = "ATTRIBUTE19";
    this.fieldLabels["customerPaymeny"] = "Customer Payment";
  }

}
