export interface RmanReconInvoiceReportV {
    dealArrangementNumber: any;
    endCustomerName: any;
    fxDate: any;
    acctdCurrency: any;
    repUnitSellingPrice: any;
    invoicedDate: any;
    dealArrangementName: any;
    invoiceCurrency: any;
    invoicedQuantity: any;
    revenueAmount: any;
    productDescription: any;
    dealArrangementBasis: any;
    fxRate: any;
    sno: any;
    shippedQuantity: any;
    dealNumber: any;
    itemType: any;
    actualFulfilledDate: any;
    salesNodeLevel2: any;
    salesNodeLevel3: any;
    dealLineNumber: any;
    sourceLineNumber: any;
    legalEntityId: any;
    dealHeaderId: any;
    invoiceNumber: any;
    acctdRevAmount: any;
    productName: any;
    customerPoNum: any;
    salesNodeLevel1: any;
    bookedDate: any;
    dealArrangementId: any;
    transactionPrice: any;
    legalEntityName: any;
    repUnitListPrice: any;
    invoiceLineNumber: any;
    orderNumber: any;
    orderedDate: any;
    orderedQuantity: any;
}
