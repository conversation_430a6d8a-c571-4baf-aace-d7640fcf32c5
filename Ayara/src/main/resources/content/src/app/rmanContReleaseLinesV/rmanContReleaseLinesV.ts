export interface RmanContReleaseLinesV {
    transHeaderId?: any;
    transLineId?: any;
    productGroup?: any;
    sourceLineNumber?: any;
    sourceHeader?: any;
    revenue?: any;
    releaseCogs?: any;
    cogs?: any;
    ruleName?: any;
    releaseLevel?: any;
    elementType?: any;
    applyType?: any;
    productName?: any;
    dealArrangementId?: any;
    event?: any;
    releaseType?: any;
    templateName?: any;
    productCategory?: any;
    releaseRevenue?: any;
    releaseLineId?: any;
    customerPayment?: any;
    releaseDate?: any;
}
