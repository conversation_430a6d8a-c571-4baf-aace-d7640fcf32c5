<div class="ContentSideSections Implementation">


<!--<p-panel header="Release Events" [toggleable]="true" [style]="{'margin-bottom':'20px'}" (onBeforeToggle)=onBeforeToggleReleaseEvents($event)>
	<p-header>
	<div class="pull-right" *ngIf="collapsedReleaseEvents">
	<a href="javascript:void(0)" (click)="showDialogToAdd()" class="icon-add" title="Add"></a>
	<a href="javascript:void(0)" (click)="showDialogToSearch()" class="icon-search" title="Search"></a>
	<a href="javascript:void(0)" (click)="reset(dt)" class="icon-reset" title="Reset"></a>
	</div>
	</p-header>



	 <p-dataTable [value]="rmanContReleaseEventsVList" selectionMode="single"     (onRowSelect)="onRowSelect($event)" scrollable="true"  [paginator]="true" [lazy]="true" [rows]="pageSize" [totalRecords]="totalElements" (onLazyLoad)="getRmanContReleaseEventsV($event)"   [globalFilter]="gb">

				<p-column styleClass="col-button" [style]="{'width':'100px'}">
		        <ng-template let-rmanFmvRulesDefParaValue="rowData" pTemplate="body">
							<a href="javascript:void(0)" (click)="editRow(rmanContReleaseEventsV)" class="icon-edit" title="Edit"> </a>
							<a href="javascript:void(0)" (click)="delete(rmanContReleaseEventsV)" class="icon-delete" title="Delete"> </a>
		        </ng-template>
		    </p-column>

		    <p-column field=sno header="{{columns['sno']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=dealArrangementId header="{{columns['dealArrangementId']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="false"></p-column>
        <p-column field=dealArrangementNumber header="{{columns['dealArrangementNumber']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=dealArrangementName header="{{columns['dealArrangementName']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="false"></p-column>
        <p-column field=lineNum header="{{columns['lineNum']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=dealNumber header="{{columns['dealNumber']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="false"></p-column>
        <p-column field=dealLineNumber header="{{columns['dealLineNumber']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="false"></p-column>
        <p-column field=so header="{{columns['so']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="false"></p-column>
        <p-column field=sourceLineId header="{{columns['sourceLineId']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=sourceLineNumber header="{{columns['sourceLineNumber']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=bookedAmount header="{{columns['bookedAmount']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=deliveredAmount header="{{columns['deliveredAmount']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=contingencyName header="{{columns['contingencyName']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="false"></p-column>
        <p-column field=templateName header="{{columns['templateName']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="false"></p-column>
        <p-column field=ranking header="{{columns['ranking']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=applyType header="{{columns['applyType']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=revenue header="{{columns['revenue']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=deferredAmount header="{{columns['deferredAmount']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=deferredReleaseAmount header="{{columns['deferredReleaseAmount']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>
        <p-column field=undeferredAmount header="{{columns['undeferredAmount']}}"  [style]="{'width':'100px'}" [hidden]="false" [sortable]="false" required="true"></p-column>

	</p-dataTable>
</p-panel> -->
	<p-dialog header="Search" width="auto" [(visible)]="displaySearchDialog"  showEffect="fade" [modal]="true">
		<form (ngSubmit)="search()">
			<div class="ui-grid ui-grid-responsive ui-fluid">
                                                    <div class="ui-grid-row">
                         <div class="ui-grid-col-4"><label for="sno">{{columns['sno']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="sno"  name="sno" [(ngModel)]="rmanContReleaseEventsVSearch.sno" /></div>
                    </div>
                    <div class="ui-grid-row">
                         <div class="ui-grid-col-4"><label for="dealArrangementId">{{columns['dealArrangementId']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="dealArrangementId"  name="dealArrangementId" [(ngModel)]="rmanContReleaseEventsVSearch.dealArrangementId" /></div>
                    </div>
                    <div class="ui-grid-row">
                         <div class="ui-grid-col-4"><label for="dealArrangementNumber">{{columns['dealArrangementNumber']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="dealArrangementNumber"  name="dealArrangementNumber" [(ngModel)]="rmanContReleaseEventsVSearch.dealArrangementNumber" /></div>
                    </div>

			</div>
			<footer>
				<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
                    <button type="button" pButton icon="fa-close" (click)="displaySearchDialog=false" label="Cancel"></button>
                    <button type="submit" pButton icon="fa-check" label="Search"></button>
				</div>
			</footer>
		</form>
	</p-dialog>
	<p-dialog header="RmanContReleaseEventsV" width="500" [(visible)]="displayDialog"  showEffect="fade" [modal]="true">
	<form (ngSubmit)="save()">
		<div class="ui-grid ui-grid-responsive ui-fluid" *ngIf="rmanContReleaseEventsV">
                                          <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="sno">{{columns['sno']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="sno" name="sno" required [(ngModel)]="rmanContReleaseEventsV.sno" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="dealArrangementId">{{columns['dealArrangementId']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="dealArrangementId" name="dealArrangementId" required [(ngModel)]="rmanContReleaseEventsV.dealArrangementId" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="dealArrangementNumber">{{columns['dealArrangementNumber']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="dealArrangementNumber" name="dealArrangementNumber" required [(ngModel)]="rmanContReleaseEventsV.dealArrangementNumber" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="dealArrangementName">{{columns['dealArrangementName']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="dealArrangementName" name="dealArrangementName" required [(ngModel)]="rmanContReleaseEventsV.dealArrangementName" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="lineNum">{{columns['lineNum']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="lineNum" name="lineNum" required [(ngModel)]="rmanContReleaseEventsV.lineNum" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="dealNumber">{{columns['dealNumber']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="dealNumber" name="dealNumber" required [(ngModel)]="rmanContReleaseEventsV.dealNumber" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="dealLineNumber">{{columns['dealLineNumber']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="dealLineNumber" name="dealLineNumber" required [(ngModel)]="rmanContReleaseEventsV.dealLineNumber" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="so">{{columns['so']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="so" name="so" required [(ngModel)]="rmanContReleaseEventsV.so" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="sourceLineId">{{columns['sourceLineId']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="sourceLineId" name="sourceLineId" required [(ngModel)]="rmanContReleaseEventsV.sourceLineId" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="sourceLineNumber">{{columns['sourceLineNumber']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="sourceLineNumber" name="sourceLineNumber" required [(ngModel)]="rmanContReleaseEventsV.sourceLineNumber" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="bookedAmount">{{columns['bookedAmount']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="bookedAmount" name="bookedAmount" required [(ngModel)]="rmanContReleaseEventsV.bookedAmount" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="deliveredAmount">{{columns['deliveredAmount']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="deliveredAmount" name="deliveredAmount" required [(ngModel)]="rmanContReleaseEventsV.deliveredAmount" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="contingencyName">{{columns['contingencyName']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="contingencyName" name="contingencyName" required [(ngModel)]="rmanContReleaseEventsV.contingencyName" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="templateName">{{columns['templateName']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="templateName" name="templateName" required [(ngModel)]="rmanContReleaseEventsV.templateName" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="ranking">{{columns['ranking']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="ranking" name="ranking" required [(ngModel)]="rmanContReleaseEventsV.ranking" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="applyType">{{columns['applyType']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="applyType" name="applyType" required [(ngModel)]="rmanContReleaseEventsV.applyType" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="revenue">{{columns['revenue']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="revenue" name="revenue" required [(ngModel)]="rmanContReleaseEventsV.revenue" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="deferredAmount">{{columns['deferredAmount']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="deferredAmount" name="deferredAmount" required [(ngModel)]="rmanContReleaseEventsV.deferredAmount" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="deferredReleaseAmount">{{columns['deferredReleaseAmount']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="deferredReleaseAmount" name="deferredReleaseAmount" required [(ngModel)]="rmanContReleaseEventsV.deferredReleaseAmount" /></div>
                    </div>
                    <div class="ui-grid-row">
     <div class="ui-grid-col-4"><label for="undeferredAmount">{{columns['undeferredAmount']}}</label></div>
                         <div class="ui-grid-col-8"><input pInputText id="undeferredAmount" name="undeferredAmount" required [(ngModel)]="rmanContReleaseEventsV.undeferredAmount" /></div>
                    </div>

		</div>
		</form>
		<footer>
			<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
                <button type="button" pButton icon="fa-close" (click)="displayDialog=false" label="Cancel"></button>
                <button type="submit" pButton icon="fa-check" label="Save" (click)="save()"></button>
			</div>
		</footer>
	</p-dialog>
</div>
