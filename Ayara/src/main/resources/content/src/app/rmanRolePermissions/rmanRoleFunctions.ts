import { RmanFunctions } from './rmanFunctions';

export interface RmanRoleFunctions {
    read: any;
    upload: any;
    write: any;
    rfid: any;
    roleid: any;
    functionId: any;
    rmanFunctions: RmanFunctions;
    enabledFlag: any;
    startDateActive: any;
    endDateActive: any
    createdBy: any;
    creationDate: any;
    lastUpdatedBy: any;
    lastUpdateDate: any;
    createdUser: any;
    updatedUser: any;
}
