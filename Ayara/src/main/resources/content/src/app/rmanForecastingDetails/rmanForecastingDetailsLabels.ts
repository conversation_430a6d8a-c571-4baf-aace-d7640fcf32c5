
interface ILabels {
    [index: string]: string;
}

export class RmanForecastingDetailsLabels {

    fieldLabels: ILabels;
    constructor() {


        this.fieldLabels = {};

        this.fieldLabels["bucket18"] = "BUCKET18";
        this.fieldLabels["applyContingencies"] = "APPLY CONTINGENCIES";
        this.fieldLabels["bucket17"] = "BUCKET17";
        this.fieldLabels["createdDate"] = "CREATED DATE";
        this.fieldLabels["bucket9Period"] = "BUCKET9 PERIOD";
        this.fieldLabels["bucket3Period"] = "BUCKET3 PERIOD";
        this.fieldLabels["bucket4Period"] = "BUCKET4 PERIOD";
        this.fieldLabels["bucket5Period"] = "BUCKET5 PERIOD";
        this.fieldLabels["bucket6Period"] = "BUCKET6 PERIOD";
        this.fieldLabels["bucket7Period"] = "BUCKET7 PERIOD";
        this.fieldLabels["bucket8Period"] = "BUCKET8 PERIOD";
        this.fieldLabels["bucket19"] = "BUCKET19";
        this.fieldLabels["level1Name"] = "Revenue Contract";
        this.fieldLabels["future"] = "FUTURE";
        this.fieldLabels["productOrgId"] = "PRODUCT ORG ID";
        this.fieldLabels["bucket1Period"] = "BUCKET1 PERIOD";
        this.fieldLabels["amount"] = "Amount";
        this.fieldLabels["bucket2Period"] = "BUCKET2 PERIOD";
        this.fieldLabels["bucket14"] = "BUCKET14";
        this.fieldLabels["bucket13"] = "BUCKET13";
        this.fieldLabels["expectedStartDate"] = "EXPECTED START DATE";
        this.fieldLabels["level5Value"] = "LEVEL5 VALUE";
        this.fieldLabels["bucket16"] = "BUCKET16";
        this.fieldLabels["bucket15"] = "BUCKET15";
        this.fieldLabels["bucket10"] = "BUCKET10";
        this.fieldLabels["type"] = "TYPE";
        this.fieldLabels["bucket12"] = "BUCKET12";
        this.fieldLabels["bucket11"] = "BUCKET11";
        this.fieldLabels["createdBy"] = "CREATED BY";
        this.fieldLabels["lastUpdatedBy"] = "LAST UPDATED BY";
        this.fieldLabels["bucket18Period"] = "BUCKET18 PERIOD";
        this.fieldLabels["arrangementId"] = "Revenue Contract Id";
        this.fieldLabels["bucket4"] = "BUCKET4";
        this.fieldLabels["level4Value"] = "LEVEL4 VALUE";
        this.fieldLabels["level5Name"] = "Product Name";
        this.fieldLabels["bucket5"] = "BUCKET5";
        this.fieldLabels["bucket21Period"] = "BUCKET21 PERIOD";
        this.fieldLabels["bucket2"] = "BUCKET2";
        this.fieldLabels["bucket3"] = "BUCKET3";
        this.fieldLabels["bucket8"] = "BUCKET8";
        this.fieldLabels["bucket9"] = "BUCKET9";
        this.fieldLabels["applyAllocations"] = "APPLY ALLOCATIONS";
        this.fieldLabels["level2Name"] = "Element";
        this.fieldLabels["bucket6"] = "BUCKET6";
        this.fieldLabels["bucket12Period"] = "BUCKET12 PERIOD";
        this.fieldLabels["bucket14Period"] = "BUCKET14 PERIOD";
        this.fieldLabels["bucket16Period"] = "BUCKET16 PERIOD";
        this.fieldLabels["bucket7"] = "BUCKET7";
        this.fieldLabels["bucket10Period"] = "BUCKET10 PERIOD";
        this.fieldLabels["bucket24"] = "BUCKET24";
        this.fieldLabels["bucket21"] = "BUCKET21";
        this.fieldLabels["bucket1"] = "BUCKET1";
        this.fieldLabels["bucket20"] = "BUCKET20";
        this.fieldLabels["bucket23Period"] = "BUCKET23 PERIOD";
        this.fieldLabels["bucket23"] = "BUCKET23";
        this.fieldLabels["level1Value"] = "LEVEL1 VALUE";
        this.fieldLabels["bucket22"] = "BUCKET22";
        this.fieldLabels["fcBucketId"] = "FC BUCKET ID";
        this.fieldLabels["remainingAmount"] = "REMAINING AMOUNT";
        this.fieldLabels["level4Name"] = "Product Type";
        this.fieldLabels["fcCurrency"] = "FC CURRENCY";
        this.fieldLabels["fcConversionType"] = "FC CONVERSION TYPE";
        this.fieldLabels["level3Value"] = "LEVEL3 VALUE";
        this.fieldLabels["remainingFutureFlag"] = "REMAINING FUTURE FLAG";
        this.fieldLabels["arrangementName"] = "Revenue Contract Name";
        this.fieldLabels["fcStartDate"] = "FC START DATE";
        this.fieldLabels["fcMethodId"] = "FC Method";
        this.fieldLabels["expectedSplit"] = "EXPECTED SPLIT";
        this.fieldLabels["level3Name"] = "Product Group";
        this.fieldLabels["bucket19Period"] = "BUCKET19 PERIOD";
        this.fieldLabels["level2Value"] = "LEVEL2 VALUE";
        this.fieldLabels["expectedDuration"] = "EXPECTED DURATION";
        this.fieldLabels["masterArrangementId"] = "MASTER REVENUE CONTRACT ID";
        this.fieldLabels["bucketType"] = "BUCKET TYPE";
        this.fieldLabels["bucket13Period"] = "BUCKET13 PERIOD";
        this.fieldLabels["lastUpdatedDate"] = "LAST UPDATED DATE";
        this.fieldLabels["bucket22Period"] = "BUCKET22 PERIOD";
        this.fieldLabels["shipmentDelay"] = "SHIPMENT DELAY";
        this.fieldLabels["customerName"] = "CUSTOMER NAME";
        this.fieldLabels["bucket11Period"] = "BUCKET11 PERIOD";
        this.fieldLabels["bucket15Period"] = "BUCKET15 PERIOD";
        this.fieldLabels["inoviceDelay"] = "INOVICE DELAY";
        this.fieldLabels["expectedEndDate"] = "EXPECTED END DATE";
        this.fieldLabels["bookingDelay"] = "BOOKING DELAY";
        this.fieldLabels["bucket17Period"] = "BUCKET17 PERIOD";
        this.fieldLabels["fcLevelName"] = "FC LEVEL NAME";
        this.fieldLabels["bucket20Period"] = "BUCKET20 PERIOD";
        this.fieldLabels["bucket24Period"] = "BUCKET24 PERIOD";
    }

}
