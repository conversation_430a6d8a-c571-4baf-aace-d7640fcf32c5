export interface RmanForecastingDetails {
    bucket18: any;
    applyContingencies: any;
    bucket17: any;
    createdDate: any;
    bucket9Period: any;
    bucket3Period: any;
    bucket4Period: any;
    bucket5Period: any;
    bucket6Period: any;
    bucket7Period: any;
    bucket8Period: any;
    bucket19: any;
    level1Name: any;
    future: any;
    productOrgId: any;
    bucket1Period: any;
    amount: any;
    bucket2Period: any;
    bucket14: any;
    bucket13: any;
    expectedStartDate: any;
    level5Value: any;
    bucket16: any;
    bucket15: any;
    bucket10: any;
    type: any;
    bucket12: any;
    bucket11: any;
    createdBy: any;
    lastUpdatedBy: any;
    bucket18Period: any;
    arrangementId: any;
    bucket4: any;
    level4Value: any;
    level5Name: any;
    bucket5: any;
    bucket21Period: any;
    bucket2: any;
    bucket3: any;
    bucket8: any;
    bucket9: any;
    applyAllocations: any;
    level2Name: any;
    bucket6: any;
    bucket12Period: any;
    bucket14Period: any;
    bucket16Period: any;
    bucket7: any;
    bucket10Period: any;
    bucket24: any;
    bucket21: any;
    bucket1: any;
    bucket20: any;
    bucket23Period: any;
    bucket23: any;
    level1Value: any;
    bucket22: any;
    fcBucketId: any;
    remainingAmount: any;
    level4Name: any;
    fcCurrency: any;
    fcConversionType: any;
    level3Value: any;
    remainingFutureFlag: any;
    arrangementName: any;
    fcStartDate: any;
    fcMethodId: any;
    expectedSplit: any;
    level3Name: any;
    bucket19Period: any;
    level2Value: any;
    expectedDuration: any;
    masterArrangementId: any;
    bucketType: any;
    bucket13Period: any;
    lastUpdatedDate: any;
    bucket22Period: any;
    shipmentDelay: any;
    customerName: any;
    bucket11Period: any;
    bucket15Period: any;
    inoviceDelay: any;
    expectedEndDate: any;
    bookingDelay: any;
    bucket17Period: any;
    fcLevelName: any;
    bucket20Period: any;
    bucket24Period: any;
}
