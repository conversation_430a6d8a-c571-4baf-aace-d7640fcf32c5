<div class="card-wrapper">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card-block">
					<p-panel header="Generate SSP" [toggleable]="false" [style]="{'margin-bottom':'20px'}">
						<p-header>
							<div class="pull-right icons-list">
                                <a [routerLink]="['/fairValues','sspBooks']" title="Go to SSP Books"><em class="fa fa-reply"></em></a>
                            </div>
						</p-header>
						<div>
                        <form [formGroup]="generateSspForm" novalidate>
                            <div class="ui-g ui-fluid">
                                <div class="generate-ssp ui-g-12 form-group">
                                    <div class="ui-grid-row">
                                        <div class="ui-g-12">
                                            <div class="ui-g-6">
                                                <span class="selectSpan"> Rule Name <span class="red-color">*</span></span>
                                                <p-dropdown class="ml-auto" [options]="sspGRules" name="sspGrule" id="sspGrule" [(ngModel)]="sspGrule"
                                                 [filter]="true" formControlName="sspGrule">
                                                </p-dropdown>
                                                <div *ngIf="formErrors.sspGrule" class="ui-message ui-messages-error ui-corner-all">
                                                    {{ formErrors.sspGrule }}
                                                </div>
                                            </div>
                                            <div class="ui-g-6 pull-right">
                                                <span class="selectSpan"> SSP Type <span class="red-color">*</span></span>
                                                <p-dropdown class="ml-auto" [options]="rmanSspTypes" name="sspType" id="sspType" [disabled]="" [(ngModel)]="sspType"
                                                 [filter]="true" formControlName="type" (onChange)="onSelectingSSPType($event.value)">
                                                </p-dropdown>
                                                <div *ngIf="formErrors.type" class="ui-message ui-messages-error ui-corner-all">
                                                    {{ formErrors.type }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="ui-g-12">
                                            <div class="ui-g-6">
                                                <span class="selectSpan"> Calculate SSP <span class="red-color">*</span></span>
                                                <p-dropdown class="ml-auto" [options]="rmanCalculateSspVals" name="calculatessp" id="calculatessp" [disabled]="" [(ngModel)]="calculateSsp" (onChange)="onSSPCalculationChange()"
                                                 [filter]="true" formControlName="calculateSsp">
                                                </p-dropdown>
                                                <div *ngIf="formErrors.calculateSsp" class="ui-message ui-messages-error ui-corner-all">
                                                    {{ formErrors.calculateSsp }}
                                                </div>
                                            </div>
                                            <div class="ui-g-2">
                                                <span class="md-inputfield">
                                                    <span class="selectSpan">Lower Band<span class="red-color" *ngIf="generateSspForm.controls['minimum'].validator != null">*</span></span>
                                                    <input pInputText name="min" id="min" class="textbox generate-ssp-input" placeholder="Lower Band" required [(ngModel)]="min" formControlName="minimum" />
                                                    <div *ngIf="formErrors.minimum" class="ui-message ui-messages-error ui-corner-all">
                                                        {{ formErrors.minimum }}
                                                    </div>
                                                </span>
                                            </div>
                                            <div class="ui-g-2 pull-right">
                                                <span class="md-inputfield">
                                                    <span class="selectSpan upper-band">Upper Band<span class="red-color" *ngIf="generateSspForm.controls['maximum'].validator != null">*</span></span>
                                                    <input pInputText name="max" id="max" class="textbox generate-ssp-input max-input" placeholder="Upper Band" required [(ngModel)]="max" formControlName="maximum" />
                                                    <div *ngIf="formErrors.maximum" class="ui-message ui-messages-error ui-corner-all">
                                                        {{ formErrors.maximum }}
                                                    </div>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ui-g-12">
                                            <div class="ui-g-6">
                                                <span class="selectSpan"> Date <span class="red-color">*</span></span>
                                                <p-dropdown class="ml-auto" [options]="rmanbsiDates" name="bsiDate" id="bsiDate" [(ngModel)]="bsiDate"
                                                 [filter]="true" formControlName="bsiDate">
                                                </p-dropdown>
                                                <div *ngIf="formErrors.bsiDate" class="ui-message ui-messages-error ui-corner-all">
                                                    {{ formErrors.bsiDate }}
                                                </div>
                                            </div>
                
                                            <div class="ui-g-6 pull-right">
                                                <span class="selectSpan"> Amount <span class="red-color">*</span></span>
                                                <p-dropdown class="ml-auto" [options]="rmanAmounts" name="amount" id="amount" [(ngModel)]="amountType"
                                                 [filter]="true" formControlName="amount">
                                                </p-dropdown>
                                                <div *ngIf="formErrors.amount" class="ui-message ui-messages-error ui-corner-all">
                                                    {{ formErrors.amount }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="ui-g-12">
                                            <div class="ui-g-6">
                                                <span class="selectSpan">Source From <span class="red-color">*</span></span>
                                                <p-calendar showAnim="slideDown" name="sourceFrom" inputStyleClass="textbox" id="sourceFrom" [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030"
                                                 appendTo="body" [(ngModel)]="sourceFrom" dateFormat="yy-mm-dd" placeholder="Source From" formControlName="sourceFrom"></p-calendar>
                                                <div *ngIf="formErrors.sourceFrom" class="ui-message ui-messages-error ui-corner-all">
                                                    {{ formErrors.sourceFrom }}
                                                </div>
                                            </div>
                                            <div class="ui-g-6 pull-right">
                                                    <span class="selectSpan">Source To<span class="red-color">*</span></span>
                                                    <p-calendar showAnim="slideDown" name="sourceTo" inputStyleClass="textbox" id="sourceTo" [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" formControlName="sourceTo"
                                                     appendTo="body" [(ngModel)]="sourceTo" dateFormat="yy-mm-dd" placeholder="Source To" ></p-calendar>
                                                        <div *ngIf="formErrors.sourceTo" class="ui-message ui-messages-error ui-corner-all">
                                                    {{ formErrors.sourceTo }}
                                            </div>
                                            </div>
                                        </div>
                                        <div class="ui-g-12">
                                            <div class="ui-g-6">
                                                <span class="selectSpan">Target From <span class="red-color">*</span></span>
                                                <p-calendar showAnim="slideDown" name="targetFrom" inputStyleClass="textbox" id="targetFrom" [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030"
                                                 appendTo="body" [(ngModel)]="targetFrom" dateFormat="yy-mm-dd" placeholder="Target From" formControlName="targetFrom"></p-calendar>
                                                <div *ngIf="formErrors.targetFrom" class="ui-message ui-messages-error ui-corner-all">
                                                    {{ formErrors.targetFrom }}
                                                </div>
                                            </div>
                                            <div class="ui-g-6 pull-right">
                                                    <span class="selectSpan">Target To<span class="red-color">*</span></span>
                                                    <p-calendar showAnim="slideDown" name="targetTo" inputStyleClass="textbox" id="targetTo" [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" formControlName="targetTo"
                                                     appendTo="body" [(ngModel)]="targetTo" dateFormat="yy-mm-dd" placeholder="Target To" ></p-calendar>
                                                        <div *ngIf="formErrors.targetTo" class="ui-message ui-messages-error ui-corner-all">
                                                    {{ formErrors.targetTo }}
                                            </div>
                                            </div>
                                        </div>
                                        
                                        <div class="ui-g-12">
                                            <div class="ui-g-6">
                                                <span class="selectSpan">Item Category </span>
                                                <p-dropdown class="ml-auto" [options]="itemCategories" name="itemCategory" id="itemCategory" [(ngModel)]="itemCategory"
                                                 [filter]="true" formControlName="itemCategory">
                                                </p-dropdown>
                                               
                                            </div>
                                        </div>
                                        
                                    </div>
                                    
                                </div>
                                <div class="generate-ssp-actions">
                                    <span class="ssp-buttons">
                                    <button type="submit" pButton class="primary-btn" label="Generate" (click)="save()" [disabled]="!generateSspForm.valid"></button>
                                    <button type="button" pButton class="secondary-btn" (click)="cancel()" label="Reset"></button>
                                    <button type="button" pButton class="secondary-btn" [routerLink]="['/fairValues','sspBooks']" label="Cancel"></button>
                                    </span>
                                </div>
                            </div>
                        </form>
                    </div>
					</p-panel>
				</div>
			</div>
		</div>
	</div>
</div>