interface ILabels {
    [index: string]: string;
}

export class RmanFmvRulesDefLabels {

    fieldLabels: ILabels;

    constructor() {
        this.fieldLabels = {};
        this.fieldLabels["arrangementId"] = "Arrangement Id";
        this.fieldLabels["arrangementName"] = "Arrangement Name";
        this.fieldLabels["sku"] = "SKU";
        this.fieldLabels["qty"] = "Quatity";
        this.fieldLabels["elementType"] = "Element Type";
        this.fieldLabels["netPrice"] = "Net Price";
        this.fieldLabels["expectedBookingAmount"] = "Expected Booking Amount";
        this.fieldLabels["recordType"] = "Recored Type";
        this.fieldLabels["amount"] = "Amount";
        this.fieldLabels["quarterName"] = "Quarter Name";
        this.fieldLabels["glPeriod"] = "GL Period";
        this.fieldLabels["startDate"] = "Start Date";
    }

}
