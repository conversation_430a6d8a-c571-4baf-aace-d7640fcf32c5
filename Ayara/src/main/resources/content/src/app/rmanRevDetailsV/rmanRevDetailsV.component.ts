import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Table } from 'primeng/table';
import { ArrgContractAllocationsVService } from '../arrgContractAllocationsV/arrgContractAllocationsVservice';
import { RmanContReleaseEventsVComponent, RmanContReleaseEventsVImpl } from '../rmanContReleaseEventsV/rmanContReleaseEventsV.component';
import { RmanContReleaseEventsVLabels } from '../rmanContReleaseEventsV/rmanContReleaseEventsVLabels';
import { RmanContReleaseEventsVService } from '../rmanContReleaseEventsV/rmanContReleaseEventsVservice';
import { RmanDealArrangementsService } from '../rmanDealArrangements/rmanDealArrangementsservice';
import { RmanGlAccountingVImpl } from '../rmanGlAccountingV/rmanGlAccountingV.component';
import { RmanGlAccountingVLabels } from '../rmanGlAccountingV/rmanGlAccountingVLabels';
import { RmanGlAccountingVService } from '../rmanGlAccountingV/rmanGlAccountingVservice';
import { CommonSharedService } from '../shared/common.service';
import { NotificationService } from '../shared/notifications.service';
import { RmanRevDetailsV } from './rmanRevDetailsV';
import { RmanRevDetailsVService } from './rmanRevDetailsVservice';
import { NewSharedModule } from '../shared/shared.module';
import { RoundCurrencyPipe } from '../shared/round-currency.pipe';


declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');
@Component({
  templateUrl: './rmanRevDetailsV.component.html',
  selector: 'rmanRevDetailsV-data',
  providers: [RmanRevDetailsVService, RmanGlAccountingVService, RmanContReleaseEventsVService, ArrgContractAllocationsVService, NewSharedModule]
})

export class RmanRevDetailsVComponent implements OnInit {
  noData = appSettings.noData;
  displayDialog: boolean;

  displaySearchDialog: boolean;

  arrId: any;

  @ViewChild(RmanContReleaseEventsVComponent) private rmanContReleaseEventsVComponent: RmanContReleaseEventsVComponent;

  rmanRevDetailsV: any = new RmanRevDetailsVImpl();

  rmanGlAccountingV: any = new RmanGlAccountingVImpl();

  rmanGlAccountingVSearch: any = new RmanGlAccountingVImpl();

  rmanContReleaseEventsV: any = new RmanContReleaseEventsVImpl();

  rmanContReleaseEventsVSearch: any = new RmanContReleaseEventsVImpl();

  rmanRevDetailsVSearch: any = new RmanRevDetailsVImpl();

  showRevSchedulesDialog: boolean = false;
  rmanDealUsageSummaryList: any[] = [];
  
  displayPageSize:any = 20;
  
  isSerached: number = 0;

  cref: ChangeDetectorRef;

  selectedRmanRevDetailsV: RmanRevDetailsV;

  newRmanRevDetailsV: boolean;

  rmanRevDetailsVList: any[] = [];

  //cols: any[];
  columns: any[];
  accColumns: ILabels;
  contColumns: ILabels;

  columnOptions: any[];

  paginationOptions: any;
  
  delPaginationOptions: any;

  pages: {};

  datasource: any[];
  pageSize: number;
  totalRevElements: number;
  totalAccElements: number;
  totalContElements: number;
  totalUsagesElements: number;
  so: any;
  soLine: any;
  collapsed: boolean = true;

  showContingenciesDialog: boolean = false;
  showAccountingDialog: boolean = false;
  showUsageDialog: boolean = false;

  rmanGlAccountingVList: any[] = [];
  rmanContReleaseEventsVList: any[] = [];
  rmanDealArrangementsList: any[] = [];
  arrgContractAllocationsVList: any[] = [];

  rmanUsagesVList: any[] = [];
  paramArrangementName: any;
  paramArrangementNumber: any;
  parrangementId: any;
  sku: any;
  lineNumber: any;
  pDealLineId: any;
  sourceLineId: any;
  
  totalAllocAmountTotal: any;
  deliveredAllocRspTotal: any;
  postBillingProvisionTotal: any;
  postBillingDeferralsTotal: any;
  postBillingAllocationAmountTotal: any;
  begBalanceTotal: any;
  currentPeriodTotal: any;
  endingBalanceTotal: any;
  totalAllocAmountFcTotal: any;
  deliveredAllocRspFcTotal: any;
  pbDeferralsFcTotal: any;
  totalBookedAmount: any;
  totalShippedAmount: any;
  totalBilledAmount: any;
  totalUnamortizedAmount: any;
  totalBilledAmountFc: any;
  totalPbAllocAmountTc: any;
  totalRevBegBalTc: any;
  totalRevCurrBalTc: any;
  totalRevEndBalTc: any;
  totalUnamortizedAmountTc: any;
  totalRevenueAdjustment: any;
  loading: boolean = true;
  pageContSize: number;

  uDealLineId: any;

  showAddColumns = true;
  isSelectAllChecked = true;
  globalCols: any[];
  clonedCols: any[];
  userId: number;
  showPaginator: boolean = true;
  startIndex: number;
 
  exportCols: string[] =[];
  disableExport: boolean = true;

  isRevType:boolean;
  revType:any = "D";

  revenueContractBasis:any;
  showDeliveryDetailsDialog: boolean
  rmanDeliveryDetailsList: any[] = [];
  totalDeliveryDetailsElements: number;
  rmanActualRevenueSchedulesList: any[] = [];
  rmanActualRevSchedulesList:any[];
  totalRevScheduleElements: number;
  revSchedulesRowSize: number;
  showActualRevSchedulesDialog: boolean;
  revcolumns: any[];
  isTCSelected:boolean = true;
  tcFc: any = true;
  filteredColumns: any[];
  currencyCode:any;


  totalArrgLineAmountFc: any;
  totalAttributedNetPriceFc: any;
  totalDeferredRevenueFc: any;
  totalArrgLineCostFc: any;
  totalBookedAmountFc: any;
  totalShippedAmountFc: any;
  totalDeliveredAttributedNetPriceFc: any;
  totalPostBillingContAmountFc: any;
  totalCogsBegBalCogsFc: any;
  totalCurrentMonthCogsFc: any;
  totalEndingBalanceFc: any;

  tcFields = [
    'currencyCode','arrgLineAmount', 'arrgLineCost','attributedNetPrice','attributedListPrice', 'totalAllocation', 'bookedAmount', 'shippedAmount',
    'deliveredAttributedNetPrice', 'deliveredAllocRsp', 'billedAmount', 'postBillingContAmount',
    'postBillingDeferrals', 'pbAllocAmountTc' , 'revBegBalTc', 'revCurrMonthBalTc', 'revEndBalTc',
    'gaapUnamortizedTc', 'cogsBegiBalCogs', 'currentMonthCogs', 'endingBalance',
    'revAdjustment', 'extendedListAmount', 'deferredRevenue'
  ];

  fcFields = [
    'fcCurrencyCode', 'arrgLineAmountFc', 'arrgLineCostFc', 'attributedNetPriceFc', 'totalAllocationFc', 'bookedAmountFc',
    'shippedAmountFc', 'deliveredAttributedNetPriceFc', 'deliveredAllocFcRsp', 'billedAmountFc', 'postBillingContAmountFc',
    'pbDeferralFc', 'postBillingAllocationAmount', 'cumGaapBegBalance', 'netGappCurrMonth', 'cumGaapEndingBalance',
    'gaapUnamortized', 'cogsBegiBalCogsFc', 'currentMonthCogsFc', 'endingBalanceFc', 
    'deferredRevenueFc'
  ];



  constructor(private router: Router, private rmanDealArrangementsService: RmanDealArrangementsService, private rmanContReleaseEventsVService: RmanContReleaseEventsVService, private rmanGlAccountingVService: RmanGlAccountingVService, private rmanRevDetailsVService: RmanRevDetailsVService, private commonSharedService: CommonSharedService,
    private notificationService: NotificationService) {

    // generate list code
    this.paginationOptions = { 'pageNumber': 0, 'pageSize': '100' };

    this.delPaginationOptions = { 'pageNumber': 0, 'pageSize': '10' };

    let url = router.url;
    this.arrId = url.split('/')[3];

    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.arrId = event.url.split('/')[3];
      }
    });
    //console.log(this.arrId);

  }


  ngOnInit() {
    this.getRmanDealArrangementsById();
    let rmanGlAccountingVLabels = new RmanGlAccountingVLabels();
    let rmanContReleaseEventsVLabels = new RmanContReleaseEventsVLabels();
    this.accColumns = rmanGlAccountingVLabels.fieldLabels;
    this.contColumns = rmanContReleaseEventsVLabels.fieldLabels;
    this.globalCols = [
      { field: 'pobGroup', header: 'POB ID', showField: true, drag: false, display: "table-cell", type: 'text', footer: 'empty' },
      { field: 'arrgLineNumber', header: 'Revenue Contract Line Number', showField: true, drag: false, display: "table-cell", type: 'number', footer: 'empty' },
      { field: 'dealNumber', header: 'Deal Number', showField: true, drag: true, display: "table-cell", type: 'number', footer: 'empty' },
      { field: 'dealLineNumber', header: 'Line Number', showField: true, drag: true, display: "table-cell", type: 'number', footer: 'empty' },
      { field: 'bundleFlag', header: 'Bundle Flag', showField: true, drag: true, display: "table-cell", type: 'text', footer: 'empty' },
      { field: 'parentLineId', header: 'Parent Line Number', showField: true, drag: true, display: "table-cell", type: 'number', footer: 'empty' },
      { field: 'customerPoNum', header: 'PO#', showField: true, drag: true, display: "table-cell", type: 'number', footer: 'empty' },
      { field: 'orderNumber', header: 'SO#', drag: true, showField: true, display: "table-cell", type: 'number', footer: 'empty' },
      { field: 'sourceLineNumber', header: 'SO Line#', drag: true, showField: true, display: "table-cell", type: 'number', footer: 'empty' },
      { field: 'productName', header: 'Product Name', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
      { field: 'productDescription', header: 'Product Description', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
      { field: 'element', header: 'Element Type', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
      { field: 'productGroup', header: 'Product Group', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
      { field: 'productType', header: 'Product Type', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
      { field: 'arrgLineAmount', header: 'Net Price', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'empty' },
      { field: 'arrgLineCost', header: 'Revenue Contract Line Cost (TC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'empty' },
      { field: 'attributedNetPrice', header: 'Bundle Attributed Net Price', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'empty' },
      { field: 'totalAllocation', header: 'Allocation Amount (TC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'totalAllocAmountTotal' },
      { field: 'totalAllocationFc', header: 'Total Allocation Amount (FC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'totalAllocAmountFcTotal' },
      { field: 'bookedAmount', header: 'Booked Amount', showField: true, drag: true, display: "table-cell", type: 'link', footer: 'totalBookedAmount' },
      { field: 'shippedAmount', header: 'Delivered Amount', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'totalShippedAmount' },
      { field: 'deliveredAttributedNetPrice', header: 'Delivered Attributed Net Price', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'empty' },
      { field: 'deliveredAllocRsp', header: 'Delivered Allocation (TC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'deliveredAllocRspTotal' },
      { field: 'deliveredAllocFcRsp', header: 'Delivered Allocation RSP(FC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'deliveredAllocRspFcTotal' },
      { field: 'billedAmount', header: 'Invoice Amount', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'totalBilledAmount' },
      { field: 'billedAmountFc', header: 'Billed Amount (FC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'totalBilledAmountFc' },
      { field: 'postBillingContAmount', header: 'Post Billing Provisions', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'postBillingProvisionTotal' },
      { field: 'postBillingDeferrals', header: 'Post Billing Deferrals (TC)', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'postBillingDeferralsTotal' },
      { field: 'pbDeferralFc', header: 'Post Billing Deferrals (FC)', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'pbDeferralsFcTotal' },
      { field: 'pbAllocAmountTc', header: 'Post Billing Alloc Amt (TC)', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalPbAllocAmountTc' },
      { field: 'postBillingAllocationAmount', header: 'Post Billing Allocation Amount (FC)', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'postBillingAllocationAmountTotal' },
      { field: 'revenueStartDate', header: 'Service Start Date', drag: true, showField: true, display: "table-cell", type: 'date', footer: 'empty' },
      { field: 'revenueEndDate', header: 'Service End Date', drag: true, showField: true, display: "table-cell", type: 'date', footer: 'empty' },
      { field: 'revBegBalTc', header: 'Revenue Beginning Balance (TC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'totalRevBegBalTc' },
      { field: 'revCurrMonthBalTc', header: 'Revenue Current Period (TC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'totalRevCurrBalTc' },
      { field: 'revEndBalTc', header: 'Revenue Ending Balance (TC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'totalRevEndBalTc' },
      { field: 'cumGaapBegBalance', header: 'Revenue - Beginning Balance (FC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'begBalanceTotal' },
      { field: 'netGappCurrMonth', header: 'Revenue-Current Period (FC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'currentPeriodTotal' },
      { field: 'cumGaapEndingBalance', header: 'Revenue-Ending Balance (FC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'endingBalanceTotal' },
      { field: 'accountingRuleName', header: 'Accounting Rule', showField: true, drag: true, display: "table-cell", type: 'text', footer: 'empty' },
      { field: 'amortRuleRevenue', header: 'Amortization Rule', showField: true, drag: true, display: "table-cell", type: 'text', footer: 'empty' },
      { field: 'amortMethodRevenue', header: 'Amortization Method', showField: true, drag: true, display: "table-cell", type: 'text', footer: 'empty' },
      { field: 'gaapUnamortizedTc', header: 'Unamortized Amount (TC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'totalUnamortizedAmountTc' },
      { field: 'gaapUnamortized', header: 'Unamortized Amount (FC)', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'totalUnamortizedAmount' },
      { field: 'cogsBegiBalCogs', header: 'COGS-Beg Balance', showField: true, drag: true, display: "table-cell", type: 'roundCurrency', footer: 'empty' },
      { field: 'currentMonthCogs', header: 'COGS-Current Period', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'empty' },
      { field: 'endingBalance', header: 'COGS-End Balance', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'empty' },
      { field: 'revAdjustment', header: 'Revenue Adjustment', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalRevenueAdjustment' },
      { field: 'productFamily', header: 'Product Family', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
      { field: 'productLine', header: 'Product Line', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	  { field: 'salesContract', header: 'Sales Contract Number', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	  { field: 'chargeType', header: 'Charge Type', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	  { field: 'assetId', header: 'Asset ID', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	  { field: 'assetNumber', header: 'Asset Number', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	  { field: 'primaryLineNumber', header: 'Primary Line Number', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	  { field: 'itemSequenceNumber', header: 'Item Sequence Number', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	 // { field: 'dateDependedFlag', header: 'Date Dependent?', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	 // { field: 'lineReferenceEventDate', header: 'Line Reference for Go Live Date', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	 // { field: 'revrecHoldFlag', header: 'Revrec Hold Flag', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
		{ field: 'forecastDate', header: 'Forecast Date', drag: true, showField: true, display: "table-cell", type: 'date', footer: 'empty' },
	    { field: 'deliveredDate', header: 'Delivered Date', drag: true, showField: true, display: "table-cell", type: 'date', footer: 'empty' },
	//  { field: 'proofPostedFlag', header: 'Proof Posted Flag', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	//  { field: 'signatureDate', header: 'Signature Date', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	 	{ field: 'revrecStartDate', header: 'Revrec Start Date', drag: true, showField: true, display: "table-cell", type: 'date', footer: 'empty' },
	    { field: 'revrecEndDate', header: 'Revrec End Date', drag: true, showField: true, display: "table-cell", type: 'date', footer: 'empty' },
	    { field: 'revenueTerm', header: 'Revenue Term in Months', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	//  { field: 'contractTermType', header: 'Contract Term Type', drag: true, showField: true, display: "table-cell", type: 'text', footer: 'empty' },
	  	{ field: 'bundlePercent', header: 'POB Split %', drag: true, showField: true, display: "table-cell", type: 'round', footer: 'empty' },
	  	{ field: 'productNumber', header: 'Product Code', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'sourceProductId', header: 'Product Case Safe Id', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'opportunityNumber', header: 'Opportunity Number', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'opportunityName', header: 'Opportunity Name', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'proposalDescription', header: 'Proposal Description', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'orderType', header: 'Order Type', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'legalEntityName', header: 'Billing Company', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'customerName', header: 'Customer Name', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'customerNumber', header: 'Customer Number', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'bookedDate', header: 'Ready for Activation Date', drag: true, showField: true, display: "table-cell", type: 'date',footer: 'empty'},
		{ field: 'orderStatus', header: 'Order Status', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'currencyCode', header: 'Currency', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
        { field: 'fcCurrencyCode', header: 'Currency', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'linestatus', header: 'Line Status', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'extendedListAmount', header: 'Extended List Price', drag: true, showField: true, display: "table-cell", type: 'roundCurrency',footer: 'empty'},
		{ field: 'discountPercent', header: 'Discount or Premium%', drag: true, showField: true, display: "table-cell", type: 'round',footer: 'empty'},
		{ field: 'quoteNumber', header: 'Quote Number', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'arrgLineAmountFc', header: 'Arrangement Line Amount FC', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalArrgLineAmountFc' },
    { field: 'attributedNetPriceFc', header: 'Attributed Net Price FC', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalAttributedNetPriceFc' },
    { field: 'deferredRevenueFc', header: 'Deferred Revenue FC', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalDeferredRevenueFc' },
    { field: 'arrgLineCostFc', header: 'Revenue Contract Line Cost FC', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalArrgLineCostFc' },
    { field: 'bookedAmountFc', header: 'Booked Amount FC', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalBookedAmountFc' },
    { field: 'shippedAmountFc', header: 'Shipped Amount FC', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalShippedAmountFc' },
    { field: 'deliveredAttributedNetPriceFc', header: 'Delivered Attributed Net Price FC', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalDeliveredAttributedNetPriceFc' },
    { field: 'postBillingContAmountFc', header: 'Post Billing Contingency Amount FC', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalPostBillingContAmountFc' },
    { field: 'cogsBegiBalCogsFc', header: 'COGS Beginning Balance FC', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalCogsBegBalCogsFc' },
    { field: 'currentMonthCogsFc', header: 'Current Month COGS FC', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalCurrentMonthCogsFc' },
    { field: 'endingBalanceFc', header: 'Ending Balance FC', drag: true, showField: true, display: "table-cell", type: 'roundCurrency', footer: 'totalEndingBalanceFc' },
    { field: 'region', header: 'Billing Region', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'revenueTemplate', header: 'Revenue Policy', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
		{ field: 'opportunityType', header: 'Opportunity Type', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
	//	{ field: 'customerType', header: 'Customer Type', drag: true, showField: true, display: "table-cell", type: 'text'},
	//	{ field: 't4cFlag', header: 'T4C Included', drag: true, showField: true, display: "table-cell", type: 'text'},
	//	{ field: 't4cFrequency', header: 'T4C Type', drag: true, showField: true, display: "table-cell", type: 'text'},
	//	{ field: 'legacy', header: 'Legacy', drag: true, showField: true, display: "table-cell", type: 'text'},
	//	{ field: 't4cOtherDetails', header: 'T4C Other Details', drag: true, showField: true, display: "table-cell", type: 'text'},
	//	{ field: 'revenueAccount', header: 'Revenue Account', drag: true, showField: true, display: "table-cell", type: 'text'},
	//	{ field: 'revenueCategory', header: 'Revenue Category', drag: true, showField: true, display: "table-cell", type: 'text'},
	//	{ field: 'sdFlag', header: 'Send to SD', drag: true, showField: true, display: "table-cell", type: 'text'},
//		{ field: 'customPricing', header: 'Custom Pricing', drag: true, showField: true, display: "table-cell", type: 'text'},
		{ field: 'quoteLineReference', header: 'Quote Reference Line Number', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
	//	{ field: 'year1Amt', header: 'Sub Fee Year 1 Price', drag: true, showField: true, display: "table-cell", type: 'round'},
	//	{ field: 'year2Amt', header: 'Sub Fee Year 2 Price', drag: true, showField: true, display: "table-cell", type: 'round'},
	//	{ field: 'year3Amt', header: 'Sub Fee Year 3 Price', drag: true, showField: true, display: "table-cell", type: 'round'},
	//	{ field: 'year4Amt', header: 'Sub Fee Year 4 Price', drag: true, showField: true, display: "table-cell", type: 'round'},
	//	{ field: 'year5Amt', header: 'Sub Fee Year 5 Price', drag: true, showField: true, display: "table-cell", type: 'round'},
	//	{ field: 'year6Amt', header: 'Sub Fee Year 6 Price', drag: true, showField: true, display: "table-cell", type: 'round'},
	//	{ field: 'year7Amt', header: 'Sub Fee Year 7 Price', drag: true, showField: true, display: "table-cell", type: 'round'},
	//	{ field: 'year8Amt', header: 'Sub Fee Year 8 Price', drag: true, showField: true, display: "table-cell", type: 'round'},
	//	{ field: 'year9Amt', header: 'Sub Fee Year 9 Price', drag: true, showField: true, display: "table-cell", type: 'round'},
	//	{ field: 'year10Amt', header: 'Sub Fee Year 10 Price', drag: true, showField: true, display: "table-cell", type: 'round'},
	//	{ field: 'subscriptionLink', header: 'Line for Subscription Fee Link', drag: true, showField: true, display: "table-cell", type: 'text'},
		{ field: 'revenueTermDays', header: 'Revenue Term in Days', drag: true, showField: true, display: "table-cell", type: 'round',footer: 'empty'},
	//	{ field: 'revopsLine', header: 'Revenue Policy Line', drag: true, showField: true, display: "table-cell", type: 'text'},
		{ field: 'attributedListPrice', header: 'Bundle Attribution Extended List Price', drag: true, showField: true, display: "table-cell", type: 'roundCurrency',footer: 'empty'},
		{ field: 'deferredRevenue', header: 'Deferred Revenue Amount', drag: true, showField: true, display: "table-cell", type: 'roundCurrency',footer: 'empty'},
		{ field: 'lineType', header: 'Line Type', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
	//	{ field: 'acceptanceLanguage', header: 'Acceptance Language', drag: true, showField: true, display: "table-cell", type: 'text'},
		{ field: 'bundleParentLineNumber', header: 'Bundle parent Line Number', drag: true, showField: true, display: "table-cell", type: 'text',footer: 'empty'},
	//	{ field: 'opsRevenueName', header: 'OpsRevenue Name', drag: true, showField: true, display: "table-cell", type: 'text'},
	//	{ field: 'refBsId', header: 'Ref BS ID for Amex/Visa', drag: true, showField: true, display: "table-cell", type: 'text'},
	//	{ field: 'driverRecord', header: 'Driver Record', drag: true, showField: true, display: "table-cell", type: 'text'}
    ];
    this.columns = [];
    this.getTableColumns("rmanRevDetailsV", "Deal Revenue Snapshot");
  }

  getTableColumns(pageName: string, tableName: string) {
    this.isSelectAllChecked = true;
    this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
      if (response && response != null && response.userId) {
        this.columns = [];
        let colsList = response.tableColumns.split(",");
        this.userId = response.userId;
        if (colsList.length > 0) {
          colsList.forEach((item, index) => {
            if (item) {
              this.startIndex = this.globalCols.findIndex(col => col.field == item);
              this.onDrop(index);
            }
          });
        }
        this.globalCols.forEach(col => {
          if (colsList.indexOf(col.field) !== -1) {
            this.columns.push(col);
          } else {
            col.showField = false;
          }
        });
        if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
        this.showPaginator = this.columns.length !== 0;
        this.userId = response.userId;
        this.onTCFCSwitch(1);
      } else {
        this.columns = this.globalCols;
        this.onTCFCSwitch(0);
      }
    }).catch(() => {
      this.notificationService.showError('Error occured while getting table columns data');
      this.loading = false;
    });

  }

  saveColumns() {
    let selectedCols = "";
    this.showAddColumns = !this.showAddColumns;
    const colLength = this.globalCols.length - 1;
    this.globalCols.forEach((col, index) => {
      if (col.showField && !(this.isTCSelected && this.tcFields.includes(col.field) || !this.isTCSelected && this.fcFields.includes(col.field))) {
        selectedCols += col.field;
        if (index < colLength) {
          selectedCols += ",";
        }
      }
    });
    this.loading = true;
    this.commonSharedService.saveOrUpdateTableColumns("rmanRevDetailsV", "Deal Revenue Snapshot", selectedCols, this.userId).then((response) => {
      this.columns = this.globalCols.filter(item => item.showField);
      this.userId = response["userId"];
      this.showPaginator = this.columns.length !== 0;
      this.loading = false;
    }).catch(() => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });
  }

  onDragStart(index: number) {
    this.startIndex = index;
  }

  onDrop(dropIndex: number) {
    const draggedCol = this.globalCols[this.startIndex];

    this.globalCols.splice(this.startIndex, 1);
    this.globalCols.splice(dropIndex, 0, draggedCol);

    const selectedFields = this.columns.map(c => c.field); // keep only user-selected
    this.columns = this.globalCols.filter(col => selectedFields.includes(col.field));
  }

  selectColumns(col: any) {
    // col.showField = !col.showField;

    // Update columns array with only selected columns (i.e., showField = true)
    // this.columns = this.globalCols.filter(c => c.showField);
    // this.columns = this.columns.filter(c => c.showField);
    if(col.showField){
      this.globalCols.forEach((column,index)=>{
        if(col.field==column.field){
          this.columns.splice(index, 0, col);
        }
      });
    }
    this.getToggledColumns();
  }

  onSelectAll() {
    this.isSelectAllChecked = !this.isSelectAllChecked;
    this.globalCols.forEach(col => {
      if (this.isSelectAllChecked) {
        col.showField = true;
      } else {
        if (col.drag) {
          col.showField = false;
        }
      }
    });
  }

  onConfiguringColumns(event: any) {
    this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
    this.showAddColumns = false;
  }

  closeConfigureColumns(event: any) {
    this.showAddColumns = true;
    this.globalCols = this.clonedCols;
    let configCol = this.globalCols.filter(item => !item.showField);
    this.isSelectAllChecked = !(configCol.length > 0);
  }

  swicthClickRecognizer:boolean = false;
  onQuoteOrderSwitch(){
    this.swicthClickRecognizer = true;
    if(this.revType == "D"){
      this.revType = "SO"
    }else{
      this.revType = "D"
    }
    this.getAllRmanRevDetailsV();
    this.onTCFCSwitch(1);
  }
  onTCFCSwitch(flag:any){
    // this.getTableColumns("rmanRevDetailsV", "Deal Revenue Snapshot");
    this.getAllRevenuesTotal();
    this.getToggledColumns();
    if(flag==1) this.setCurrencyCode();

  }
  setCurrencyCode(){
    if (Array.isArray(this.rmanRevDetailsVList) && this.rmanRevDetailsVList.length > 0) {
      this.currencyCode = !this.tcFc
        ? this.rmanRevDetailsVList[0]['fcCurrencyCode'] || 'USD'
        : this.rmanRevDetailsVList[0]['currencyCode'] || 'USD';
    } else {
      this.currencyCode = 'USD';
    }
  }
  getToggledColumns() {
    const selectedStandardFields = this.columns
      .map(col => col.field)
      .filter(field => !this.tcFields.includes(field) && !this.fcFields.includes(field));

    const standardCols = this.globalCols.filter(col =>
      selectedStandardFields.includes(col.field)
    );
    const currencyField = this.tcFc ? 'currencyCode' : 'fcCurrencyCode';
    const currencyCol = this.globalCols.find(col => col.field === currencyField);
    const currencyCols = currencyCol ? [currencyCol] : [];

    // 3. Add selected TC/FC columns
    const activeFields = this.tcFc ? this.tcFields : this.fcFields;
    const inactiveFields = this.tcFc ? this.fcFields : this.tcFields;

    const selectedActiveTCFCCols = this.globalCols.filter(col =>
      activeFields.includes(col.field) && col.showField
    );



    const allToggleFields = [...this.tcFields, ...this.fcFields];
        this.globalCols.forEach(col => {
      if (activeFields.includes(col.field)) {
        col.showField = true;
      } else if (inactiveFields.includes(col.field)) {
        col.showField = false;
      }
    });

    // 1. Shown standard columns
    const shownStandardCols = this.globalCols.filter(col =>
      !allToggleFields.includes(col.field) && col.showField
    );

    // 2. Unshown standard columns
    const unshownStandardCols = this.globalCols.filter(col =>
      !allToggleFields.includes(col.field) && !col.showField
    );

    // 3. Shown TC/FC columns (active toggle group)
    const shownTcFcCols = this.globalCols.filter(col =>
      activeFields.includes(col.field) && col.showField
    );

    // 4. Inactive TC/FC columns (always at end)
    const inactiveTcFcCols = this.globalCols.filter(col =>
      inactiveFields.includes(col.field)
    );

    // ✅ Final ordered assignment
    this.globalCols = [
      ...shownStandardCols,
      ...unshownStandardCols,
      ...shownTcFcCols,
      ...inactiveTcFcCols
    ];
    this.columns = [...shownStandardCols, ...shownTcFcCols];



  }

  exportExcel() {
    this.exportCols = [];
    for (let index = 0; index < this.columns.length; index++) {
      if (this.columns[index].showField) {
        this.exportCols.push(this.columns[index].field);
      }
    }

    let serviceUrl = this.rmanRevDetailsVService.getServiceUrl(this.paginationOptions, this.arrId, this.so, this.soLine, this.revType, 1, this.exportCols);
    window.location.href = serviceUrl;

  }

  reset(dt: Table) {
    this.paginationOptions = {};
    this.delPaginationOptions= {};
    this.rmanRevDetailsV = new RmanRevDetailsVImpl();
    this.rmanRevDetailsVSearch = new RmanRevDetailsVImpl();
    this.so = '';
    this.soLine = '';
    this.pDealLineId = null;
    dt.reset();
  }

  resetAccounts(dt1: Table) {

    this.paginationOptions = {};
    this.rmanGlAccountingV = new RmanGlAccountingVImpl();
    this.rmanGlAccountingVSearch = new RmanGlAccountingVImpl();
    this.rmanGlAccountingV.dealArrangementId = this.arrId;
    this.rmanGlAccountingV.dealLineId = this.pDealLineId;
    dt1.reset();
  }

  resetContingencies(dt3: Table) {

    this.paginationOptions = {};
    this.rmanContReleaseEventsV = new RmanContReleaseEventsVImpl();
    this.rmanContReleaseEventsVSearch = new RmanContReleaseEventsVImpl();
    this.rmanContReleaseEventsV.dealArrangementId = this.arrId;
    this.rmanContReleaseEventsV.dealLineId = this.pDealLineId;
    dt3.reset();

  }

  getRmanDealArrangementsById() {

    this.rmanDealArrangementsService.getAllRmanDealArrangements(this.paginationOptions, { 'dealArrangementId': this.arrId }).then((rmanDealArrangementsList: any) => {
      this.rmanDealArrangementsList = rmanDealArrangementsList.content;
      this.paramArrangementName = this.rmanDealArrangementsList[0].dealArrangementName;
      this.paramArrangementNumber = this.rmanDealArrangementsList[0].dealArrangementNumber;

    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });
  }

  getAllRmanContReleaseEventsVTest() {

    this.rmanContReleaseEventsVComponent.getAllRmanContReleaseEventsV();

  }




  getAllRmanContReleaseEventsV() {
    this.rmanContReleaseEventsVService.getAllRmanContReleaseEventsV(this.paginationOptions, this.rmanContReleaseEventsV).then((rmanContReleaseEventsVList: any) => {
      this.datasource = rmanContReleaseEventsVList.content;
      if (this.pDealLineId != null) {
        this.rmanContReleaseEventsVList = rmanContReleaseEventsVList.content.filter((item: any) => item.dealLineId == this.pDealLineId);
      } else {
        this.rmanContReleaseEventsVList = rmanContReleaseEventsVList.content;
      }
      this.totalContElements = rmanContReleaseEventsVList.totalElements;
      this.pageSize = rmanContReleaseEventsVList.size;
	      this.disableExport = false;
    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });

  }

  getRmanContReleaseEventsV(event: any) {

    let first: number = event.first;
    let rows: number = event.rows;
    let pageNumber: number = first / rows;
    this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageContSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
    this.rmanContReleaseEventsVService.getAllRmanContReleaseEventsV(this.paginationOptions, { 'dealArrangementId': this.arrId, 'dealLineId': this.pDealLineId }).then((rmanContReleaseEventsVList: any) => {
      this.rmanContReleaseEventsVList = rmanContReleaseEventsVList.content;
      this.totalContElements = rmanContReleaseEventsVList.totalElements;
      this.pageSize = rmanContReleaseEventsVList.size;
		      this.disableExport = false;
    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });

  }

  getRmanDealUsageSummary(event: any) {
    let first: number = event.first;
    let rows: number = event.rows;
    let pageNumber: number = first / rows;
    this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageContSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
    this.rmanRevDetailsVService.getUsageSummaryDetails(this.paginationOptions, this.pDealLineId).then((rmanDealUsageSummaryList: any) => {
      this.datasource = rmanDealUsageSummaryList.content;
      this.rmanDealUsageSummaryList = rmanDealUsageSummaryList.content;
      this.loading = false;
      this.totalContElements = rmanDealUsageSummaryList.totalElements;
      this.pageSize = rmanDealUsageSummaryList.size;

    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });

  }


  exportContingencies() {
    let searchParam = {};
    if (this.pDealLineId != null) {
      searchParam = { 'dealArrangementId': this.arrId, 'so': this.rmanContReleaseEventsVSearch.so, 'sourceLineNumber': this.rmanContReleaseEventsVSearch.sourceLineNumber, 'dealLineId': this.rmanContReleaseEventsVSearch.dealLineId };
    } else {
      searchParam = { 'dealArrangementId': this.arrId, 'so': this.rmanContReleaseEventsVSearch.so, 'sourceLineNumber': this.rmanContReleaseEventsVSearch.sourceLineNumber };
    }

    let serviceUrl = this.rmanContReleaseEventsVService.getServiceUrl(this.paginationOptions, searchParam, 1);
    window.location.href = serviceUrl;

  }


  searchAccounts() {
    this.rmanGlAccountingV = this.rmanGlAccountingVSearch;
    this.rmanGlAccountingV.dealArrangementId = this.arrId;
    this.rmanGlAccountingV.dealLineId = this.pDealLineId;
    this.paginationOptions = {};
    this.getAllRmanGlAccountingVFilter();
  }

  searchContigencies() {
    this.rmanContReleaseEventsV = this.rmanContReleaseEventsVSearch
    this.rmanContReleaseEventsV.dealArrangementId = this.arrId;
    this.rmanContReleaseEventsV.dealLineId = this.pDealLineId;
    this.paginationOptions = {};
    this.getAllRmanContReleaseEventsV();

  }

  contingencies(dt3: Table) {
    this.rmanContReleaseEventsV = {};
    this.rmanContReleaseEventsV = new RmanContReleaseEventsVImpl();
    this.rmanContReleaseEventsVSearch = new RmanContReleaseEventsVImpl();
    let searchParams = {};
    if (this.pDealLineId != null) {
      searchParams = { 'dealArrangementId': this.arrId, 'dealLineId': this.pDealLineId };
    } else {
      searchParams = { 'dealArrangementId': this.arrId };
    }

    this.loading = true;
    this.rmanContReleaseEventsVService.getAllRmanContReleaseEventsV(this.paginationOptions, searchParams).then((rmanContReleaseEventsVList: any) => {
      this.datasource = rmanContReleaseEventsVList.content;
      this.rmanContReleaseEventsVList = rmanContReleaseEventsVList.content;
      this.loading = false;
      this.totalContElements = rmanContReleaseEventsVList.totalElements;
      this.pageSize = rmanContReleaseEventsVList.size;
      if (this.rmanContReleaseEventsVList.length > 0) {
        this.getAllRmanContReleaseEventsVTest();
        this.showContingenciesDialog = true;
      } else {
        this.notificationService.showWarning('Holds & Events are not applied for this deal line.');
      }

    });
  }

  deliveryDetails(dd: Table){
    this.loading = true;
    if(this.sourceLineId != null && this.sourceLineId != undefined && this.pDealLineId !=null && this.pDealLineId !=undefined){
      this.rmanRevDetailsVService.getDeliveryDetails(this.delPaginationOptions, this.pDealLineId, this.sourceLineId).then((rmanDeliveryDetailsList: any) =>{
        this.rmanDeliveryDetailsList = rmanDeliveryDetailsList.content;
        this.loading = false;
        this.totalDeliveryDetailsElements = rmanDeliveryDetailsList.totalElements;
        this.pageSize = rmanDeliveryDetailsList.size;
        if (this.rmanDeliveryDetailsList.length > 0) {
          this.showDeliveryDetailsDialog = true;
        } else {
          this.notificationService.showWarning('No Delivery Details associated with corresponding Deal Line Id and Source Line Id');
        }

      });
    }else if(this.sourceLineId == null && this.pDealLineId == null){
      //warning
      this.notificationService.showWarning('Please select a record to View Delivery Details');
      this.loading = false;
    }else if(this.sourceLineId == null || this.pDealLineId == null){
      this.notificationService.showWarning('Either Deal Line Id or Source Line Id is empty, Please check and try again');
      this.loading = false;
    }
  }

  revenueSchedules(ars: Table){
    this.loading = true;
    if(this.arrId !=null && this.arrId !=undefined){
      this.rmanRevDetailsVService.getActualRevSchedules(this.paginationOptions, this.arrId, this.isTCSelected).then((rmanActualRevenueSchedulesList: any)=>{
        this.rmanActualRevenueSchedulesList = rmanActualRevenueSchedulesList.Content;
        this.rmanActualRevSchedulesList = rmanActualRevenueSchedulesList.Content;
        this.totalRevScheduleElements = rmanActualRevenueSchedulesList.Content.length;
        this.revSchedulesRowSize = 10;
        if(this.totalRevScheduleElements > 0){
          let dTemp: any = this.rmanActualRevenueSchedulesList[0];
          this.revcolumns = [];
          for (let prop in dTemp) {
            this.revcolumns.push({
              field: prop,
              header: prop,
              display: 'table-cell',
              text: 'right'
              //style: { 'width': '100px', 'text-align': 'right' },
              //display: 'table-cell',

            });

          }

          let leftAlignItems = ["Product Name"];

          for (let index = 0; index < this.revcolumns.length; index++) {
            if (leftAlignItems.indexOf(this.revcolumns[index].header) == -1) {
              this.revcolumns[index].text = "right";
            } else {
              this.revcolumns[index].text = "left";
            }
          }



          this.rmanActualRevenueSchedulesList = rmanActualRevenueSchedulesList.Content.slice(0, 10);
          this.showActualRevSchedulesDialog = true;
          this.loading = false;
        }else{
          this.loading = false;
          this.notificationService.showWarning('No Actual Revenue Schedules associated with corresponding Revenue Contract Number');
        }


      });
    }
  }

  cancel(){
    this.revcolumns = [];
  }

  paginate(data) {
    this.loading = true;
    this.rmanActualRevenueSchedulesList = this.rmanActualRevSchedulesList.slice(data.first, data.first + 10);
    this.loading = false;
  }


  getDeliveryDetails(event: any) {
    let first: number = event.first;
    let rows: number = event.rows;
    let pageNumber: number = first / rows;
    this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageContSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
    this.rmanRevDetailsVService.getDeliveryDetails(this.paginationOptions, this.pDealLineId, this.sourceLineId).then((rmanDeliveryDetailsList: any) => {
      this.datasource = rmanDeliveryDetailsList.content;
      this.rmanDeliveryDetailsList = rmanDeliveryDetailsList.content;
      this.loading = false;
      this.totalContElements = rmanDeliveryDetailsList.totalElements;
      this.pageSize = rmanDeliveryDetailsList.size;

    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });

  }



  revSchedules() {
    if (this.pDealLineId != null) {
      this.loading = true;
      this.rmanRevDetailsVService.getUsageSummaryDetails(this.paginationOptions, this.pDealLineId).then((rmanDealUsageSummaryList: any) => {
        this.datasource = rmanDealUsageSummaryList.content;
        this.rmanDealUsageSummaryList = rmanDealUsageSummaryList.content;
        this.loading = false;
        this.disableExport = false;
        this.totalContElements = rmanDealUsageSummaryList.totalElements;
        this.pageSize = rmanDealUsageSummaryList.size;
        if (this.rmanDealUsageSummaryList.length > 0) {
          this.showRevSchedulesDialog = true;
        } else {
          this.notificationService.showWarning('No Revenue Schedules for selected deal line.');
        }

      });

    } else {
      this.notificationService.showWarning('Select deal to view Revenue Schedules.');
    }
  }


  accounting(dt1: Table) {
    this.rmanGlAccountingV = new RmanGlAccountingVImpl();
    this.rmanGlAccountingVSearch = new RmanGlAccountingVImpl();
    this.showAccountingDialog = true;
    dt1.reset();
  }

  getAllRmanGlAccountingV() {
    this.loading = true;
    this.rmanGlAccountingVService.getAllRmanGlAccountingV(this.paginationOptions, this.rmanGlAccountingVSearch, this.exportCols).then((rmanGlAccountingVList: any) => {
      this.datasource = rmanGlAccountingVList.content;
      this.rmanGlAccountingVList = rmanGlAccountingVList.content;
      this.totalAccElements = rmanGlAccountingVList.totalElements;
      this.pageSize = rmanGlAccountingVList.size;
      this.loading = false;
      this.disableExport = false;
      this.displaySearchDialog = false;
    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });
  }

  getAllRmanGlAccountingVFilter() {
    this.loading = true;
    this.rmanGlAccountingVService.getAllRmanGlAccountingV(this.paginationOptions, this.rmanGlAccountingV, this.exportCols).then((rmanGlAccountingVList: any) => {
      this.loading = false;
      this.datasource = rmanGlAccountingVList.content;
      this.rmanGlAccountingVList = rmanGlAccountingVList.content;
      this.rmanGlAccountingVList = this.rmanGlAccountingVList.slice();
      this.totalAccElements = rmanGlAccountingVList.totalElements;
      this.pageSize = rmanGlAccountingVList.size;
      this.displaySearchDialog = false;
      this.disableExport = false;
    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });
  }

  getRmanGlAccountingV(event: any) {

    let first: number = event.first;
    let rows: number = event.rows;
    let pageNumber: number = first / rows;
    this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };

    this.rmanGlAccountingV = this.rmanGlAccountingVSearch;
    this.rmanGlAccountingV.dealArrangementId = this.arrId;
    if (this.pDealLineId != null) {
      this.rmanGlAccountingV.dealLineId = this.pDealLineId;
    }
    this.loading = true;
    this.rmanGlAccountingVService.getAllRmanGlAccountingV(this.paginationOptions, this.rmanGlAccountingV, this.exportCols).then((rmanGlAccountingVList: any) => {

      this.datasource = rmanGlAccountingVList.content;
      this.rmanGlAccountingVList = rmanGlAccountingVList.content;
      this.totalAccElements = rmanGlAccountingVList.totalElements;
      this.pageSize = rmanGlAccountingVList.size;
      this.loading = false;
      this.disableExport = false;
    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });

  }

  exportAccountsExcel() {
	this.exportCols = [
	        'accountClass', 'orderNumber', 'sourceLineNumber', 'dealLineNumber',
	        'lineStatus', 'glDate', 'periodName', 'drCr', 'account',
	        'accountDescription', 'accountName', 'currencyCode', 'functionalCurrency',
	        'amountTc', 'amountFc'
	    ];
    let searchParam = {};
    if (this.pDealLineId != null) {
      searchParam = { 'dealArrangementId': this.arrId, 'orderNumber': this.rmanGlAccountingVSearch.orderNumber, 'sourceLineNumber': this.rmanGlAccountingVSearch.sourceLineNumber, 'dealLineId': this.rmanGlAccountingVSearch.dealLineId };
    } else {
      searchParam = { 'dealArrangementId': this.arrId, 'orderNumber': this.rmanGlAccountingVSearch.orderNumber, 'sourceLineNumber': this.rmanGlAccountingVSearch.sourceLineNumber };
    }
    let serviceUrl = this.rmanGlAccountingVService.getServiceUrl(this.paginationOptions, searchParam, this.exportCols, 1);
    window.location.href = serviceUrl;

  }


  getAllRmanRevDetailsV() {
    if(!this.swicthClickRecognizer){
      this.loading = true;
      this.rmanDealArrangementsService.getDealArrangementBasis(this.arrId).then((data) => {
        this.revenueContractBasis = data;
        if(this.revenueContractBasis === 'SO' || this.revenueContractBasis === 'PO'){
          this.isRevType = true;
          this.revType = 'SO';
        }
      });
    }
    this.totalAllocAmountTotal = 0;
    this.deliveredAllocRspTotal = 0;
    this.postBillingProvisionTotal = 0;
    this.postBillingDeferralsTotal = 0;
    this.postBillingAllocationAmountTotal = 0;
    this.begBalanceTotal = 0;
    this.currentPeriodTotal = 0;
    this.endingBalanceTotal = 0;
    this.totalAllocAmountFcTotal = 0;
    this.deliveredAllocRspFcTotal = 0;
    this.pbDeferralsFcTotal = 0;
    this.totalBookedAmount = 0;
    this.totalShippedAmount = 0;
    this.totalBilledAmount = 0;
    this.totalUnamortizedAmount = 0;
    this.totalBilledAmountFc = 0;
    this.totalPbAllocAmountTc = 0;
    this.totalRevBegBalTc = 0;
    this.totalRevCurrBalTc = 0;
    this.totalRevEndBalTc = 0;
    this.totalUnamortizedAmountTc = 0;
    this.totalRevenueAdjustment = 0;
    this.loading = true;
    this.rmanRevDetailsV['dealArrangementId'] = this.arrId;
    this.rmanRevDetailsVService.getAllRmanRevDetailsV(this.paginationOptions, this.arrId, this.so, this.soLine, this.revType, this.exportCols).then((rmanRevDetailsVList: any) => {
      this.datasource = rmanRevDetailsVList.content;
      this.rmanRevDetailsVList = rmanRevDetailsVList.content;
      if (this.rmanRevDetailsVList.length > 0) {
        this.selectedRmanRevDetailsV = this.rmanRevDetailsVList[0];
        this.sku = this.selectedRmanRevDetailsV.productName;
        this.lineNumber = this.selectedRmanRevDetailsV.arrgLineNumber;

      }

      this.totalRevElements = rmanRevDetailsVList.totalElements;
      this.pageSize = rmanRevDetailsVList.size;
      this.getAllRevenuesTotal();
      this.displaySearchDialog = false;
      this.loading = false;
      this.disableExport = false;

    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });

  }


  getRmanRevDetailsV(event: any) {

    if(!this.swicthClickRecognizer){
      this.loading = true;
      this.rmanDealArrangementsService.getDealArrangementBasis(this.arrId).then((data) => {
        this.revenueContractBasis = data;
        if(this.revenueContractBasis === 'SO' || this.revenueContractBasis === 'PO'){
          this.isRevType = true;
          this.revType = 'SO';
        }
        let first: number = event.first;
        let rows: number = event.rows;
        let pageNumber: number = first / rows;
        this.loading = true;
        this.rmanRevDetailsV['dealArrangementId'] = this.arrId;
        this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
        this.rmanRevDetailsVService.getAllRmanRevDetailsV(this.paginationOptions, this.arrId, this.so, this.soLine, this.revType, this.exportCols).then((rmanRevDetailsVList: any) => {
          this.datasource = rmanRevDetailsVList.content;
          this.rmanRevDetailsVList = rmanRevDetailsVList.content;
          if (this.rmanRevDetailsVList.length > 0) {
            this.selectedRmanRevDetailsV = this.rmanRevDetailsVList[0];
            this.sku = this.selectedRmanRevDetailsV.productName;
            this.getAllRevenuesTotal();
          }
          this.totalRevElements = rmanRevDetailsVList.totalElements;
          this.pageSize = rmanRevDetailsVList.size;
          this.onTCFCSwitch(1);
          this.loading = false;
    
        }).catch((err: any) => {
          this.notificationService.showError('Error occured while getting data');
          this.loading = false;
        });
      });
    }


  }


  getAllRevenuesTotal() {
    this.rmanRevDetailsVService.getAllRevenueTotals(this.arrId, this.so, this.soLine, this.revType).then((revenueTotalResponse: any) => {
      this.totalAllocAmountTotal = revenueTotalResponse['totalAllocation'];
      this.deliveredAllocRspTotal = revenueTotalResponse['deliveredAllocationRsp'];
      this.postBillingProvisionTotal = revenueTotalResponse['postBillingContingency'];
      this.postBillingDeferralsTotal = revenueTotalResponse['postBillingDeffered'];
      this.postBillingAllocationAmountTotal = revenueTotalResponse['postBillingAllocation'];
      this.begBalanceTotal = revenueTotalResponse['revenueBeginBalance'];
      this.currentPeriodTotal = revenueTotalResponse['revenueCurrentMonthBalance'];
      this.endingBalanceTotal = revenueTotalResponse['revenueEndBalance'];
      this.totalAllocAmountFcTotal = revenueTotalResponse['totalAllocationFc'];
      this.deliveredAllocRspFcTotal = revenueTotalResponse['deliveredAllocFcRsp'];
      this.pbDeferralsFcTotal = revenueTotalResponse['pbDeferralFc'];
      this.totalBookedAmount = revenueTotalResponse['totalBookedAmount'];
      this.totalShippedAmount = revenueTotalResponse['totalShippedAmount'];
      this.totalBilledAmount = revenueTotalResponse['totalBilledAmount'];
      this.totalUnamortizedAmount = revenueTotalResponse['totalUnamortizedAmount'];
      this.totalBilledAmountFc = revenueTotalResponse['totalBilledAmountFc'];
      this.totalPbAllocAmountTc = revenueTotalResponse['totalPostBollingAllocAmtTc'];
      this.totalRevBegBalTc = revenueTotalResponse['totalRevBegBalTc'];
      this.totalRevCurrBalTc = revenueTotalResponse['totalRevCurrBalTc'];
      this.totalRevEndBalTc = revenueTotalResponse['totalRevEndBalTc'];
      this.totalUnamortizedAmountTc = revenueTotalResponse['totalUnamortizedAmountTc'];
      this.totalRevenueAdjustment = revenueTotalResponse['totalRevenueAdjustment'];
      this.totalArrgLineAmountFc = revenueTotalResponse['totalArrgLineAmountFc'];
      this.totalAttributedNetPriceFc = revenueTotalResponse['totalAttributedNetPriceFc'];
      this.totalDeferredRevenueFc = revenueTotalResponse['totalDeferredRevenueFc'];
      this.totalArrgLineCostFc = revenueTotalResponse['totalArrgLineCostFc'];
      this.totalBookedAmountFc = revenueTotalResponse['totalBookedAmountFc'];
      this.totalShippedAmountFc = revenueTotalResponse['totalShippedAmountFc'];
      this.totalDeliveredAttributedNetPriceFc = revenueTotalResponse['totalDeliveredAttributedNetPriceFc'];
      this.totalPostBillingContAmountFc = revenueTotalResponse['totalPostBillingContAmountFc'];
      this.totalCogsBegBalCogsFc = revenueTotalResponse['totalCogsBegBalCogsFc'];
      this.totalCurrentMonthCogsFc = revenueTotalResponse['totalCurrentMonthCogsFc'];
      this.totalEndingBalanceFc = revenueTotalResponse['totalEndingBalanceFc'];
      this.columns.forEach(col => {
      if (col.footer === 'totals') {
        col.footer = 'empty';
      }
    });

    // Find and set 'totals' before the first actual total value column
    for (let i = 0; i < this.columns.length; i++) {
      const col = this.columns[i];
      if (col.footer !== 'empty') {
        if (i > 0) {
          this.columns[i - 1].footer = 'totals';
        }
        break;
      }
    }

    }).catch((err: any) => {
    });
  }

  showDialogToAdd() {

    this.newRmanRevDetailsV = true;
    this.rmanRevDetailsV = new RmanRevDetailsVImpl();
    this.displayDialog = true;

  }


  save() {

    if (this.newRmanRevDetailsV) {
      this.loading = true;
      this.rmanRevDetailsVService.saveRmanRevDetailsV(this.rmanRevDetailsV).then((response: any) => {
        this.notificationService.showSuccess('Saved successfully');
        this.getAllRmanRevDetailsV();
      }).catch((err: any) => {
        this.notificationService.showError('Error occured while saving data');
        this.loading = false;
      });
    }
    else {
      this.loading = true;
      this.rmanRevDetailsVService.updateRmanRevDetailsV(this.rmanRevDetailsV).then((response: any) => {
        this.notificationService.showSuccess('Updated successfully');
        this.getAllRmanRevDetailsV();
      }).catch((err: any) => {
        this.notificationService.showError('Error occured while updating data');
        this.loading = false;
      });
    }

    this.rmanRevDetailsV = new RmanRevDetailsVImpl();

    this.displayDialog = false;

  }


  delete(rmanRevDetailsV: any) {
    this.rmanRevDetailsV = rmanRevDetailsV;
    this.displayDialog = false;
    if (window.confirm('Are you sure you want to delete this record?')) {
      this.rmanRevDetailsVList.splice(this.findSelectedRmanRevDetailsVIndex(), 1);
      this.loading = true;
      this.rmanRevDetailsVService.deleteRmanRevDetailsV(this.rmanRevDetailsV).then((response: any) => {
        this.loading = false;
        this.rmanRevDetailsV = new RmanRevDetailsVImpl();
        this.getAllRmanRevDetailsV();
      });
    }

  }

  editRow(rmanRevDetailsV: any) {
    this.newRmanRevDetailsV = false;
    this.rmanRevDetailsV = this.cloneRmanRevDetailsV(rmanRevDetailsV);
    this.displayDialog = true;
  }


  findSelectedRmanRevDetailsVIndex(): number {
    return this.rmanRevDetailsVList.indexOf(this.selectedRmanRevDetailsV);
  }

  onRowSelect(event: any) {
    this.selectedRmanRevDetailsV = event.data;
    this.pDealLineId = this.selectedRmanRevDetailsV.dealLineId;
    this.sku = this.selectedRmanRevDetailsV.productName;
    this.lineNumber = this.selectedRmanRevDetailsV.dealLineNumber;
    this.sourceLineId = this.selectedRmanRevDetailsV.sourceLineId;
  }
  onRowUnSelect(event: any) {
    this.pDealLineId = null;
    this.sourceLineId = null;
  }

  cloneRmanRevDetailsV(c: RmanRevDetailsV): RmanRevDetailsV {
    let rmanRevDetailsV: any
      = new RmanRevDetailsVImpl();
    for (let prop in c) {
      rmanRevDetailsV[prop] = c[prop];
    }
    return rmanRevDetailsV;
  }

  hideColumnMenu: boolean = true;

  toggleColumnMenu() {
    if (this.hideColumnMenu) {
      this.hideColumnMenu = false;
    } else {
      this.hideColumnMenu = true;
    }
  }

  showFilter: boolean = false;

  toggleFilterBox() {
    if (this.showFilter) {
      this.showFilter = false;
    } else {
      this.showFilter = true;
    }
  }

  showDialogToSearch() {

    this.rmanRevDetailsVSearch = new RmanRevDetailsVImpl();

    if (this.isSerached == 0) {
      this.rmanRevDetailsVSearch = new RmanRevDetailsVImpl();
    }
    this.displaySearchDialog = true;

  }

  search() {

    this.isSerached = 1;
    this.rmanRevDetailsV = this.rmanRevDetailsVSearch;
    this.displaySearchDialog = false;
    this.getAllRmanRevDetailsV();
  }

  onBeforeToggle(evt: any) {
    this.collapsed = evt.collapsed;
  }

  getRmanUsage(event: any) {
    let first: number = event.first;
    let rows: number = event.rows;
    let pageNumber: number = first / rows;
    this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
    this.loading = true;
    this.rmanRevDetailsVService.getAllUsages(this.paginationOptions, this.uDealLineId).then((rmanUsagesVList: any) => {
      this.rmanUsagesVList = rmanUsagesVList.content;
      this.totalUsagesElements = rmanUsagesVList.totalElements;
      this.pageSize = rmanUsagesVList.size;

      if (this.rmanUsagesVList.length > 0) {
        this.showUsageDialog = true;
      } else {
        this.notificationService.showWarning('Usages are not created for this deal line.');
      }
      this.loading = false;
    }).catch((err: any) => {
      this.notificationService.showError('Error occured while getting data for Usages');
      this.loading = false;
    });


  }
  showUsages(dealLineId) {
    this.uDealLineId = dealLineId;
    this.rmanUsagesVList = [];
    this.showUsageDialog = true;
  }

  fieldType(fieldValue: any) {
    return typeof fieldValue;
  }
  isCurrencyField(field: string): boolean {
    const fixedFields = [
      'Unit Net Price', 'Net Price', 'Allocation Amount',
      'Line Amount', 'Delivered Line Amount', 'Delivered Alloc Amount',
      'To be delivered - Line amount', 'To be delivered - Allocation amount'
    ];

    const monthPattern = /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{2}$/;

    return fixedFields.includes(field) || monthPattern.test(field);
  }

  isNumber(value: any): boolean {
    return !isNaN(parseFloat(value)) && isFinite(value);
  }


}


class RmanRevDetailsVImpl {
  constructor(public dealLineId?: any, public deliveredAllocRsp?: any, public totalGaapRevenueCurrentMon?: any, public dealArrangementName?: any, public postBillingContAmount?: any, public cumGaapEndingBalance?: any, public arrgLineAmount?: any, public totalGaapRevenueEndingBal?: any, public cumGaapBegBalance?: any, public bookedAmount?: any, public preBillingContAmount?: any, public dealLineNumber?: any, public productGroup?: any, public cogsBegiBalCogs?: any, public productName?: any, public dealArrangementId?: any, public accountingRuleName?: any, public shippedAmount?: any, public billedAmount?: any, public productType?: any, public netGappCurrMonth?: any, public element?: any, public postBillingAllocationAmount?: any, public currentMonthCogs?: any, public gaapUnamortized?: any, public totalGaapRevneueBegBal?: any, public arrgLineCost?: any, public endingBalance?: any, public totalAllocation?: any, public duration?: any, public postBillingDeferrals?: any, public attributedNetPrice?: any,
    public deliveredAttributedNetPrice?: any, public pobGroup?: any, public arrgLineNumber?: any, public amortMethodRevenue?: any, sourceLineId?: any) { }
}

interface ILabels {
  [index: string]: string;
}
