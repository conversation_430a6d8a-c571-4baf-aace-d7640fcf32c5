import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require:any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type':  'application/json',
  })
};




@Injectable()
export class AyaraPobMappingsInterfaceService {

    constructor(private http: HttpClient) {}
    
    getServiceUrl(paginationOptions:any,ayaraPobMappingsInterfaceSearchObject:any,  exportFlag: any, exportCols: any) {
        
         let serviceUrl = appSettings.apiUrl + '/pobMappingsInterfaceExport?';

    	 if (exportFlag == 0) {
      	 	serviceUrl = appSettings.apiUrl + '/pobMappingsInterfaceSearch?';
    	 }

        let searchString='';

        
        
        if (ayaraPobMappingsInterfaceSearchObject.processId!=undefined && ayaraPobMappingsInterfaceSearchObject.processId!="") {
            searchString=searchString+'processId:'+ayaraPobMappingsInterfaceSearchObject.processId+',';
        }

        searchString=searchString+'processFlag:E';


        if (searchString == '') {
            serviceUrl=serviceUrl+'search=%25';
        }
        else {
            serviceUrl=serviceUrl+'search='+searchString;
        }
        
        if (paginationOptions.pageNumber!=undefined && paginationOptions.pageNumber!="" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber!=0) {
           serviceUrl=serviceUrl+'&page='+paginationOptions.pageNumber+'&size='+paginationOptions.pageSize;
        }
        
        if (exportCols != undefined && exportCols != "") {
      		serviceUrl = serviceUrl + '&exportCols=' + exportCols;
    	}
   		
   		return serviceUrl;
        
    }

    getAllAyaraPobMappingsInterface(paginationOptions:any,ayaraPobMappingsInterfaceSearchObject:any, exportCols: any): Promise<any[]> {
        let serviceUrl = this.getServiceUrl(paginationOptions, ayaraPobMappingsInterfaceSearchObject, 0, exportCols);
        return this.http.get(serviceUrl).toPromise().then((data:any) => {
            return data;
        });
    }
    deleteSelectedLines(exceptionLines: any[]){

        let delUrl = appSettings.apiUrl + '/bulkDelPOBExceptions?pobIds='+exceptionLines;
        return this.http.delete(delUrl,{responseType : "text"}).toPromise().then((data:any) => {
          return data;
        });

    }


	
}
