import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { Table } from 'primeng/table';
import { NotificationService } from '../shared/notifications.service';
import { UploadService } from '../shared/upload.service';
import { AyaraPobMappingsInterface } from './ayaraPobMappingsInterface';
import { AyaraPobMappingsInterfaceService } from './ayaraPobMappingsInterfaceservice';
import { CommonSharedService } from '../shared/common.service';

declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Component({
    templateUrl: './ayaraPobMappingsInterface.component.html',
    selector: 'ayaraPobMappingsInterface-data',
    providers: [AyaraPobMappingsInterfaceService, ConfirmationService]
})

export class AyaraPobMappingsInterfaceComponent {

    displayDialog: boolean;

    displaySearchDialog: boolean;
    
    uploadLoading: boolean = false;

    ayaraPobMappingsInterface: AyaraPobMappingsInterface = new AyaraPobMappingsInterfaceImpl();

    ayaraPobMappingsInterfaceSearch: AyaraPobMappingsInterface = new AyaraPobMappingsInterfaceImpl();

    isSerached: number = 0;

    selectedAyaraPobMappingsInterface: AyaraPobMappingsInterface;

    newAyaraPobMappingsInterface: boolean;
	
	displayPobMappingsInterfaceDialog: boolean = false;
	
    ayaraPobMappingsInterfaceList: AyaraPobMappingsInterface[];

    cols: any[];
    //columns: ILabels;

    columnOptions: any[];

    paginationOptions = {};

    pages: {};


    datasource: any[];
    pageSize: number;
    totalElements: number;
    collapsed: boolean = true;
    
    customerForm: FormGroup;
    noData = appSettings.noData;
    loading: boolean;
    ayaraPobMappingsInterfacetatus: any[];
    regions:any[];
    customerTypes:any[];
    isSelectAllChecked = true;
    globalCols: any[];
    clonedCols: any[];
    userId: number;
    showPaginator: boolean = true;
    startIndex: number;
    showAddColumns = true;
    columns: any[];
    
    exportCols: string[] = [];
  	disableExport: boolean = true;
    selectedLines: any[] = [];
	  exceptionsList: any[] = [];



    constructor(
        private ayaraPobMappingsInterfaceService: AyaraPobMappingsInterfaceService,
        private confirmationService: ConfirmationService,
        private formBuilder: FormBuilder,
        private notificationService:NotificationService,
        public _uploadService:UploadService,  private commonSharedService: CommonSharedService
    ) {

        // generate list code
        this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };


    }

    ngOnInit() {
      this.selectedLines = [];
        this.globalCols = [
            { field: 'skuType', header: 'Mapping Type', showField: true, drag: false, display: "table-cell",type:'text'},
            { field: 'standaloneSubSku', header: 'Product Name', showField: true, drag: true, display: "table-cell",type:'text' },
            { field: 'parentProduct', header: 'Parent product', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'pobGrouping', header: 'POB Grouping', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'splitBasis', header: 'Split Basis', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'split', header: 'Split', showField: true, drag: true, display: "table-cell",type:'number'},
			{ field: 'status', header: 'Status', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'startDate', header: 'Start Date', showField: true, drag: true, display: "table-cell",type:'date'},
			{ field: 'endDate', header: 'End Date', showField: true, drag: true, display: "table-cell",type:'date'},
            { field: 'revenueTemplateName', header: 'Revenue Template', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'attribute2', header: 'Revenue Policy', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'attribute3', header: 'Subscription Fee Link', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'attribute4', header: 'Revenue Policy Line', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'attribute5', header: 'Date Dependent Flag', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'attribute6', header: 'Acceptance Language', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'processId', header: 'Process Id', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'exceptionMessage', header: 'Exception Message', showField: true, drag: true, display: "table-cell",type:'text'}
        ];

        this.columns = [];
        this.getTableColumns("ayaraPobMappingsInterface", "AyaraPobMappingsInterface");
    }

    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
          if (response && response != null && response.userId) {
            this.columns = [];
            let colsList = response.tableColumns.split(",");
            if (colsList.length > 0) {
              colsList.forEach((item, index) => {
                if (item) {
                  this.startIndex = this.globalCols.findIndex(col => col.field == item);
                  this.onDrop(index);
                }
              });
            }
            this.globalCols.forEach(col => {
              if (response.tableColumns.indexOf(col.field) !== -1) {
                this.columns.push(col);
              } else {
                col.showField = false;
              }
            });
            if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
            this.showPaginator = this.columns.length !== 0;
            this.userId = response.userId;
          } else {
            this.columns = this.globalCols;
          }
        }).catch(() => {
          this.notificationService.showError('Error occured while getting table columns data');
          this.loading = false;
        });
      }

      saveColumns() {
        let selectedCols = "";
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
          if (col.showField) {
            selectedCols += col.field;
            if (index < colLength) {
              selectedCols += ",";
            }
          }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns("ayaraPobMappingsInterface", "AyaraPobMappingsInterface", selectedCols, this.userId).then((response) => {
          this.columns = this.globalCols.filter(item => item.showField);
          this.userId = response["userId"];
          this.showPaginator = this.columns.length !== 0;
          this.loading = false;
        }).catch(() => {
          this.notificationService.showError('Error occured while getting data');
          this.loading = false;
        });
      }

      onDragStart(index: number) {
        this.startIndex = index;
      }

      onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
        //console.log(this.globalCols);
      }

      selectColumns(col: any) {
        let cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
          this.isSelectAllChecked = false;
        } else {
          this.isSelectAllChecked = true;
        }
      }

      onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
          if (this.isSelectAllChecked) {
            col.showField = true;
          } else {
            if (col.drag) {
              col.showField = false;
            }
          }
        });
      }

      onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
      }

      closeConfigureColumns(event: any) {
      this.showAddColumns = true;
      this.globalCols = this.clonedCols;
      let configCol = this.globalCols.filter(item => !item.showField);
      this.isSelectAllChecked = !(configCol.length > 0);
      }



    getAllAyaraPobMappingsInterface() {
        this.loading = true;
        this.ayaraPobMappingsInterfaceService.getAllAyaraPobMappingsInterface(this.paginationOptions, this.ayaraPobMappingsInterface, this.exportCols).then((ayaraPobMappingsInterfaceList: any) => {
            this.loading = false;
            this.datasource = ayaraPobMappingsInterfaceList.content;
            this.ayaraPobMappingsInterfaceList = ayaraPobMappingsInterfaceList.content;
            this.totalElements = ayaraPobMappingsInterfaceList.totalElements;
            this.pageSize = ayaraPobMappingsInterfaceList.size;
            this.displaySearchDialog = false;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    getAyaraPobMappingsInterface(event: any) {
        this.loading = true;
        let first: number = event.first;
        let rows: number = event.rows;
        let pageNumber: number = first / rows;
        this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
        this.ayaraPobMappingsInterfaceService.getAllAyaraPobMappingsInterface(this.paginationOptions, this.ayaraPobMappingsInterface, this.exportCols).then((ayaraPobMappingsInterfaceList: any) => {
            this.loading = false;
            this.datasource = ayaraPobMappingsInterfaceList.content;
            this.ayaraPobMappingsInterfaceList = ayaraPobMappingsInterfaceList.content;
            this.totalElements = ayaraPobMappingsInterfaceList.totalElements;
            this.pageSize = ayaraPobMappingsInterfaceList.size;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });

    }


   onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }


    reset(dt: Table) {
        this.paginationOptions = {};
        this.ayaraPobMappingsInterface = new AyaraPobMappingsInterfaceImpl();
        this.ayaraPobMappingsInterfaceSearch = new AyaraPobMappingsInterfaceImpl();
        this.selectedLines = [];
        dt.reset();
    }
  deleteSelected(dt: Table) {

        this.exceptionsList = [];

        for (let line of this.selectedLines) {
            this.exceptionsList.push(line.pobId);
        }
        if (this.exceptionsList.length > 0) {

        this.confirmationService.confirm({
            message: 'Are you sure you want to delete?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
              this.loading = true;
              this.ayaraPobMappingsInterfaceService.deleteSelectedLines(this.exceptionsList).then((response: any) => {
                this.notificationService.showSuccess('Selected records are deleted Successfully  ');
            }).then(() => {
                this.reset(dt);
            }).catch((err: any) => {
                this.loading = false;
                this.notificationService.showError('Getting error While delete records');
            });
            },
            reject: () => {
              this.notificationService.showWarning('You have rejected');
            }
          });

        } else {
            this.notificationService.showInfo('Select at least one record to delete');
        }
    }


	exportExcel() {
	    this.exportCols = [];
	    for (let index = 0; index < this.columns.length; index++) {
	      if (this.columns[index].showField) {
	        this.exportCols.push(this.columns[index].field);
	      }
	    }
		
	    let serviceUrl = this.ayaraPobMappingsInterfaceService.getServiceUrl(this.paginationOptions, this.ayaraPobMappingsInterface, 1, this.exportCols);
	    window.location.href = serviceUrl;
    }
    
    findSelectedAyaraPobMappingsInterfaceIndex(): number {
        return this.ayaraPobMappingsInterfaceList.indexOf(this.selectedAyaraPobMappingsInterface);
    }

    onRowSelect(event: any) {

    }

    
    hideColumnMenu: boolean = true;

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    showFilter: boolean = false;

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {
        if (this.isSerached == 0) {
            this.ayaraPobMappingsInterfaceSearch = new AyaraPobMappingsInterfaceImpl();
        }
        this.ayaraPobMappingsInterfaceSearch = new AyaraPobMappingsInterfaceImpl();
        this.displaySearchDialog = true;

    }
    
	search() {

        this.isSerached = 1;
        this.ayaraPobMappingsInterface = this.ayaraPobMappingsInterfaceSearch;
        this.paginationOptions={};
        this.getAllAyaraPobMappingsInterface();
    }
    
    
    showUploadLoader(){
    	this.uploadLoading = true;
    }
	
}


export class AyaraPobMappingsInterfaceImpl implements AyaraPobMappingsInterface {
    constructor(
	public pobId?: any,
	public originalPobMapId?: any,
    public skuType?: any,
	public standaloneSubSku?: any,
    public pobGrouping?: any,
    public parentProduct?: any,
    public attribute1?: any,
	public attribute2?: any,
	public attribute3?: any,
    public attribute4?: any,
    public attribute5?: any,
    public startDate?: any,
	public endDate?: any,
    public splitBasis?: any,
	public split?: any,
    public status?: any,
    public revenueTemplateName?: any,
    public attribute6?: any,
	public attribute7?: any,
	public attribute8?: any,
    public attribute9?: any,
    public attribute10?: any,
    public attribute11?: any,
	public attribute12?: any,
	public attribute13?: any,
    public attribute14?: any,
    public attribute15?: any,
    public sourceId?: any,    
	public processId?: any,
	public processFlag?: any,
    public exceptionMessage?: any
	) { }
}

interface ILabels {
    [index: string]: string;
}
