import {Component, OnInit , Input} from '@angular/core';
import {Column} from '../../shared/models/column.interface';
import {OpportunityService} from '../data-access/opportunity.service';
//import {Allocation, Forecast, OpportunityStageDetails, OpportunityStagePayload} from '../models/opportunity';
import {  Opportunity, OpportunityStage, Allocation, Forecast } from '../models/opportunity';
import {map, tap , switchMap} from 'rxjs/operators';
import {TableData} from '../../shared/models/tableData';
import { Observable } from 'rxjs';
import { ActivatedRoute, Route, Router } from '@angular/router';

@Component({
    selector: 'app-opportunity-details',
    templateUrl: './opportunity-details.component.html',
    styleUrls: ['./opportunity-details.component.scss']
})
export class OpportunityDetailsComponent implements OnInit {
    opportunityDetails: any = {
            customerName:'',
            opportunityNumber: '',
            opportunityName: ''
    };
    headerDetailsLoaded = false;

    tableColumns2: Column[] = [

        // {
        //     header: 'Opportunity Stage',
        //     field: 'opportunityStage',
        //     alignment: 'center',
        //     type: 'text',
        //     showField: true
        // },

        {
            header: 'Stage Name',
            field: 'opportunityStage',
            alignment: 'center',
            type: 'name',
            showField: true
        },
        {
            header: 'Stage Date',
            field: 'stageDate',
            alignment: 'center',
            type: 'text',
            showField: true
        },
        // {
        //     header: 'Expected Close Date',
        //     field: 'closeDate',
        //     alignment: 'center',
        //     type: 'text',
        //     showField: true
        // },
        // {
        //     header: 'Opportunity Type',
        //     field: 'opportunityType',
        //     alignment: 'center',
        //     type: 'text',
        //     showField: true
        // },
        // {
        //     header: 'Win (%)',
        //     field: 'winPercent',
        //     alignment: 'center',
        //     type: 'text',
        //     showField: true
        // },
        {
            header: 'Opportunity Amount',
            field: 'opportunityAmount',
            alignment: 'center',
            type: 'amount',
            showField: true
        }
    ];

    

    allocationColumns2: Column[] = [
       
    //{ header: 'Opportunity Stage Line ID', field: 'opportunityStageLineId', alignment: 'center', type: 'text', showField: true },
    //    { header: 'Opportunity Stage ID', field: 'opportunityStageId', alignment: 'center', type: 'text', showField: true },
        { header: 'Opportunity Number', field: 'opportunityNumber', alignment: 'center', type: 'name', showField: true },
        { header: 'Opportunity Name', field: 'opportunityName', alignment: 'center', type: 'name', showField: true },
        { header: 'Opportunity Type', field: 'opportunityType', alignment: 'center', type: 'name', showField: true },
        { header: 'Opportunity Stage', field: 'opportunityStage', alignment: 'center', type: 'name', showField: true },
        { header: 'Stage Date', field: 'stageDate', alignment: 'center', type: 'text', showField: true },
        // { header: ' Expected Close Date', field: 'closeDate', alignment: 'center', type: 'text', showField: true },
        // { header: 'Win Percent', field: 'winPercent', alignment: 'center', type: 'text', showField: true },
        { header: 'Opportunity Amount', field: 'opportunityAmount', alignment: 'center', type: 'amount', showField: true },
        { header: 'Customer Number', field: 'customerNumber', alignment: 'center', type: 'text', showField: true },
        { header: 'Region', field: 'region', alignment: 'center', type: 'text', showField: true },
        { header: 'Currency', field: 'currency', alignment: 'center', type: 'text', showField: true },
        { header: 'Latest Version', field: 'latestVersion', alignment: 'center', type: 'number', showField: true },
        { header: 'Opportunity Line Number', field: 'oppotunityLineNumber', alignment: 'center', type: 'name', showField: true },
        { header: 'Product Name', field: 'productName', alignment: 'center', type: 'name', showField: true },
        { header: 'Unit List Price', field: 'unitListPrice', alignment: 'center', type: 'amount', showField: true },
        { header: 'Unit Net Price', field: 'unieNetPrice', alignment: 'center', type: 'amount', showField: true },
        { header: 'Bundle Flag', field: 'bundleFlag', alignment: 'center', type: 'text', showField: true },
        { header: 'Parent Line Number', field: 'parentLineNumber', alignment: 'center', type: 'text', showField: true },
        { header: 'Allocable Amount', field: 'allocableAmount', alignment: 'center', type: 'amount', showField: true },
        { header: 'Quantity', field: 'quantity', alignment: 'center', type: 'number', showField: true },
        { header: 'Extended List Amount', field: 'extendedListAmount', alignment: 'center', type: 'amount', showField: true },
        { header: 'Extended Net Price', field: 'extendedNetPrice', alignment: 'center', type: 'amount', showField: true },
        { header: 'Discount Percent', field: 'discountPercent', alignment: 'center', type: 'percent', showField: true },
        { header: 'Child List Price', field: 'childListPrice', alignment: 'center', type: 'amount', showField: true },
        { header: 'Child Sell Price', field: 'childSellPrice', alignment: 'center', type: 'amount', showField: true },
        { header: 'SSP Per Unit', field: 'sspPerUnit', alignment: 'center', type: 'amount', showField: true },
        { header: 'SSP Low', field: 'sspLow', alignment: 'center', type: 'amount', showField: true },
        { header: 'SSP Max', field: 'sspMax', alignment: 'center', type: 'amount', showField: true },
        { header: 'SSP Amount', field: 'sspAmount', alignment: 'center', type: 'amount', showField: true },
        { header: 'Allocation Amount', field: 'allocationAmount', alignment: 'center', type: 'amount', showField: true },
        { header: 'CV In/Out Amount', field: 'cvInOutAmount', alignment: 'center', type: 'amount', showField: true },
        { header: 'Unit Cost', field: 'unitCost', alignment: 'center', type: 'amount', showField: true },
        { header: 'Line Cost', field: 'lineCost', alignment: 'center', type: 'amount', showField: true },
        { header: 'Gross Margin Percent', field: 'grossMarginPercent', alignment: 'center', type: 'percent', showField: true },
        { header: 'CV In/Out Percent', field: 'cvInOutPercent', alignment: 'center', type: 'percent', showField: true },
        { header: 'Allocation Margin Percent', field: 'allocationMarginPercent', alignment: 'center', type: 'percent', showField: true },
        { header: 'Accounting Standard', field: 'accountingStandard', alignment: 'center', type: 'text', showField: true },
        { header: 'Product Group', field: 'productGroup', alignment: 'center', type: 'name', showField: true },
        { header: 'Product Type', field: 'productType', alignment: 'center', type: 'name', showField: true },
        { header: 'Product Line', field: 'productLine', alignment: 'center', type: 'text', showField: true },
        { header: 'Product Family', field: 'productFamily', alignment: 'center', type: 'name', showField: true },
        { header: 'Revenue Guidance', field: 'revenueGuidance', alignment: 'center', type: 'symbol', showField: true },
        { header: 'Margin Guidance', field: 'marginGuidance', alignment: 'center', type: 'symbol', showField: true },
        { header: 'Discount Guidance', field: 'discountGuidance', alignment: 'center', type: 'symbol', showField: true },
        { header: 'Deal Guidance', field: 'dealGuidance', alignment: 'center', type: 'symbol', showField: true },
        { header: 'Comments', field: 'comments', alignment: 'center', type: 'text', showField: true },
        { header: 'Product Number', field: 'productNumber', alignment: 'center', type: 'text', showField: true },
        { header: 'Source Product ID', field: 'sourceProductId', alignment: 'center', type: 'name', showField: true }
        
    ];

     forecastColumns: Column[] = [];
    isFirstMaximized = false;
    isSecondMaximized = false;
  
  ///////////////////////01-12-2024---------------code working forecssts
    stages$: Observable<TableData<OpportunityStage[]>> = this.route.params.pipe(
        switchMap(params => this.opportunityService.getStages(params['opportunityNumber'])),
        tap(stages => {
           
          if (stages?.length > 0) {
            this.getStageDetails(stages[0]);
            this.allocations = this.getTableConfig(stages[0].allocations);
            this.generateForecastColumns(stages[0].forecast);
            this.forecasts = this.getTableConfig(stages[0].forecast);
          }
        }),
        map(stages => this.getTableConfig(stages))
      );
    
 
    allocations: TableData<Allocation[]>;
    forecasts: TableData<Forecast[]>;
    

   
     
    constructor(private opportunityService: OpportunityService , private route : ActivatedRoute , private router: Router) {
        const navigation = this.router.getCurrentNavigation();
        if (navigation?.extras.state) {
            this.opportunityDetails = navigation.extras.state;
        }

    }

    ngOnInit() {

    this.route.params.pipe(
        switchMap(params => {
            const opportunityNumber = params['opportunityNumber'];
            
            // First try to get from service
            const selectedOpportunity = this.opportunityService.getSelectedOpportunity();
            
            if (selectedOpportunity && selectedOpportunity.opportunityNumber === opportunityNumber) {
                // If we have matching opportunity in service, use it
                this.opportunityDetails = {
                    customerName: selectedOpportunity.customerName,
                    opportunityNumber: selectedOpportunity.opportunityNumber,
                    opportunityName: selectedOpportunity.opportunityName
                };
                return this.opportunityService.getStages(opportunityNumber);
            } else {
                // If not in service, fetch from API
                const state = sessionStorage.getItem('opportunityManagerState');
                const pageInfo = state ? JSON.parse(state) : { page: 0, size: 10 };
                
                return this.opportunityService.getOpportunities(pageInfo.page, pageInfo.size, pageInfo.searchTerm).pipe(
                    tap(response => {
                        const found = response.content.find(opp => opp.opportunityNumber === opportunityNumber);
                        if (found) {
                            this.opportunityDetails = {
                                customerName: found.customerName,
                                opportunityNumber: found.opportunityNumber,
                                opportunityName: found.opportunityName
                            };
                            // Store in service for future use
                            this.opportunityService.setSelectedOpportunity(found);
                        }
                    }),
                    switchMap(() => this.opportunityService.getStages(opportunityNumber))
                );
            }
        })
    ).subscribe(stages => {
        if (stages?.length > 0) {
            this.getStageDetails(stages[0]);
            this.allocations = this.getTableConfig(stages[0].allocations);
            this.generateForecastColumns(stages[0].forecast);
            this.forecasts = this.getTableConfig(stages[0].forecast);
        }
    });
}
    

    

    
   generateForecastColumns(forecastData: Forecast[]) {
        if (forecastData?.length > 0) {
            const firstRecord = forecastData[0];
            const columns: Column[] = Object.entries(firstRecord)
                .map(([key, value]) => ({
                    header: key,
                    field: key,
                    alignment: 'center',
                    type: typeof value === 'number' ? 'number' : 'text',
                    showField: true,
                    // Optional: Custom formatter to display null/undefined
                    //formatter: (value: any) => value ?? '-'
                }));
    
            this.forecastColumns = columns;
        }
    }


  
   

    

    getTableConfig<T>(data: T): TableData<T> {
        const dataLength = Array.isArray(data) ? data.length : 1;
        const pageSize = 10;
        return {
            content: data,
        pageable: {
            sort: {
                sorted: true,
                unsorted: false,
                empty: false
            },
            offset: 0,
            pageNumber: 0,
            pageSize: pageSize,
            unpaged: false,
            paged: true
        },
        totalElements: dataLength,
        totalPages: Math.ceil(dataLength / pageSize),
        last: false,
        size: pageSize,
        number: 0,
        sort: {
            sorted: true,
            unsorted: false,
            empty: false
        },
        first: true,
        numberOfElements: dataLength,
        empty: dataLength === 0
        };
    }


    getStageDetails(stage: OpportunityStage) {
        this.opportunityService.getStageDetails(stage.opportunityNumber, stage.opportunityStageId)
            .subscribe({
                next: (data) => {
                    this.allocations = this.getTableConfig(data.allocations);
                    this.generateForecastColumns(data.forecast);
                    this.forecasts = this.getTableConfig(data.forecast);
                },
                error: (error) => {
                    console.error('Error fetching stage details:', error);
                }
            });
    }
}
