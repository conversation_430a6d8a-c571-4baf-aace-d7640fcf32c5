import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {Column} from '../../../shared/models/column.interface';
import {ChartItemData} from '../../models/chart-data';
import {TotalItem} from '../../models/total-data';

@Component({
    selector: 'app-dashboard-filter-modal',
    templateUrl: './dashboard-filter-modal.component.html',
    styleUrls: ['./dashboard-filter-modal.component.scss']
})
export class DashboardFilterModalComponent implements OnInit {

    @Input() isVisible = false;
    @Input() data!: Column[] | { header: string, showField: boolean }[] | TotalItem[];

    @Output() save = new EventEmitter();
    @Output() close = new EventEmitter();

    dataCopy: Column[] | ChartItemData[] | TotalItem[];

    ngOnInit(): void {
        this.dataCopy = JSON.parse(JSON.stringify(this.data));
    }


    onReset() {
            this.data.forEach((item, index) => item.showField = this.dataCopy[index].showField);
    }

    onClose() {
        this.close.emit();
    }

    onSave() {
        this.save.emit();
    }

    change() {
    }
}
