// @import "../../../../assets/sass/variables";

// .chart {
//   &__container {
//     width: 400px;
//     border: 1px solid $DDTableBorderColor;
//     border-radius: 8px;
//   }

//   &__header {
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     padding: 15px;
//     border-bottom: 1px solid $DDTableBorderColor;

//     &-title {
//       color: $DDTableHeaderTextColor;
//       font-size: 12px;
//       font-weight: 700;
//     }
//   }

//   &__body {
//     height: 260px;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     padding: 10px;
//   }
// }

// :host::ng-deep {
//   p-dropdown {
//     width: 75px;
//     padding: 0 5px 0 8px;

//     .p-dropdown-trigger {
//       margin: 0;
//     }

//     .p-dropdown-trigger-icon {
//       background: none;
//       color: $flowerBlueColor;
//       font-size: 12px;
//     }

//     .p-dropdown-label {
//       font-size: 10px;
//     }
//   }
// }

// @media(max-width: 1484px) {
//   .chart {
//     &__container {
//       width: 370px;
//     }
//   }
// }

// @media(max-width: 1394px) {
//   .chart {
//     &__container {
//       width: 500px;
//     }
//   }
// }

// @media(max-width: 1287px) {
//   .chart {
//     &__container {
//       width: 450px;
//     }
//   }
// }

// @media(max-width: 1170px) {
//   .chart {
//     &__container {
//       width: 400px;
//     }
//   }
// }

// @media(max-width: 1070px) {
//   .chart {
//     &__container {
//       width: 500px;
//     }
//   }
// }

// @media(min-width: 2100px) {
//   .chart {
//     &__container {
//       width: 450px;
//     }
//   }
// }

// @media(min-width: 1785px) and (max-width: 1899px) {
//   .chart {
//     &__container {
//       width: 500px;
//     }
//   }
// }

// @media(min-width: 1635px) and (max-width: 1785px) {
//   .chart {
//     &__container {
//       width: 450px;
//     }
//   }
// }
@import "../../../../assets/sass/variables";

.chart {
  &__container {
    width: 400px;
    border: 1px solid $DDTableBorderColor;
    border-radius: 8px;

    &:has(.pricing-filters){
      width: 800px;
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid $DDTableBorderColor;

    &-title {
      color: $DDTableHeaderTextColor;
      font-size: 12px;
      font-weight: 700;
    }
    .pricing-filters{
      width: 100%;
      
      .filter-item{
        display: flex;
        gap: 15px;
        flex-wrap: wrap;


        p-autocomplete,
        p-dropdown {
          flex: 1;
          min-width: 180px;
          
          ::ng-deep .p-autocomplete,
          ::ng-deep .p-dropdown {
            width: 100%;
      }
        }}
    }
  }

  &__body {
    height: 260px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
  }
}

:host::ng-deep {
  p-dropdown {
    width: 75px;
    padding: 0 5px 0 8px;

    .p-dropdown-trigger {
      margin: 0;
    }

    .p-dropdown-trigger-icon {
      background: none;
      color: $flowerBlueColor;
      font-size: 12px;
    }

    .p-dropdown-label {
      font-size: 10px;
    }
  }
}

@media(max-width: 1484px) {
  .chart {
    &__container {
      width: 370px;
    }
  }
}

@media(max-width: 1394px) {
  .chart {
    &__container {
      width: 500px;
    }
  }
}

@media(max-width: 1287px) {
  .chart {
    &__container {
      width: 450px;
    }
  }
}

@media(max-width: 1170px) {
  .chart {
    &__container {
      width: 400px;
    }
  }
}

@media(max-width: 1070px) {
  .chart {
    &__container {
      width: 500px;
    }
  }
}

@media(min-width: 2100px) {
  .chart {
    &__container {
      width: 450px;
    }
  }
}

@media(min-width: 1785px) and (max-width: 1899px) {
  .chart {
    &__container {
      width: 500px;
    }
  }
}

@media(min-width: 1635px) and (max-width: 1785px) {
  .chart {
    &__container {
      width: 450px;
    }
  }
}






