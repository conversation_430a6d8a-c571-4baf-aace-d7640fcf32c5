import {Component, EventEmitter, Input, Output} from '@angular/core';
import {DASHBOARD_COLUMNS, DASHBOARD_DATA, LINE_CIRCLE_COLUMNS} from './columns';
import {LazyLoadEvent} from 'primeng/api';
import {FilterParams, TableFilterOptions} from '../models/filter-params';
import {TableData} from '../../shared/models/tableData';
import {Column} from '../../shared/models/column.interface';
import {Router} from '@angular/router';
import { OverlayPanel } from 'primeng/overlaypanel';
import {OpportunityService} from '../data-access/opportunity.service';
import { Observable } from 'rxjs';
import { OpportunityStage,  Forecast} from '../models/opportunity';
//mport { OpportunityService } from '../data-access/opportunity.service';
import { OpportunityDetailsComponent } from '../forecast-details/opportunity-details.component';
import {map, switchMap,tap} from 'rxjs/operators';
import { ContractDetails, InvoiceDetails, OrderDetails , QuoteDetails} from '../models/line-circle';
import { AnalyticalDashboardService } from '../data-access/analytical-dashboard.service';
import { DatePipe } from '@angular/common';


@Component({
    selector: 'app-dashboard-table',
    templateUrl: './dashboard-table.component.html',
    styleUrls: ['./dashboard-table.component.scss']
   
    
})
export class DashboardTableComponent {
    @Input() tableData: TableData<any>;
    @Input() tableColumns: Column[];
  //  @Input() tableColumns2: Column[];
    @Input() columns: Column[] = [];
    @Input() isLoading = false;
    @Input() selectedParameter = {name: 'Default', columns: DASHBOARD_COLUMNS};
    @Input() filterOptions: TableFilterOptions;

    @Output() loadData = new EventEmitter();

    hideTimeout: any;
    contractDetails: ContractDetails;
    currentRowData: any; 
    displayLineCircleDialog: boolean = false;
    // selectedLineCircleData: any[] = null;
    selectedLineCircleData: any[] = [];
    selectedLineCircleType: string = '';
    selectedViewType: 'summary'| 'line';
    //selectedOpportunitystage$ : Observable<OpportunityStage>;
    data = DASHBOARD_DATA;
   // stages$: Observable<OpportunityStage[]>;
    lineCircleColumns=LINE_CIRCLE_COLUMNS;
    displaySummaryDialog: boolean = false;
    displayLineDialog: boolean = false;
    selectedFilters: FilterParams = {};
    selectedQuoteName :string;
    generatedColumns: any[] = [];
    selectedQuoteNumber : string;
    contarctDetails: ContractDetails;
    invoiceDetails: InvoiceDetails;
    orderDetails: OrderDetails;
    quoteDetails: QuoteDetails;
    stages$: Observable<TableData<OpportunityStage[]>>;
  forecast$: Observable<TableData<Forecast[]>>;
  forecasts: TableData<Forecast[]>;
  quoteLines: any[] = [];
  quoteColumns: any[] = [];
    selectedHeaderInfo: { label: string, value: string }[] = [];
    headerData: any;
    tableColumns2: Column[] = [
      {
        header: 'Stage Name',
        field: 'opportunityStage',
        alignment: 'center',
        type: 'name',
        showField: true
      },
      {
        header: 'Stage Date',
        field: 'stageDate',
        alignment: 'center',
        type: 'text',
        showField: true
      },
      // {
      //   header: ' Expected Close Date',
      //   field: 'closeDate',
      //   alignment: 'center',
      //   type: 'text',
      //   showField: true
      // },
      // {
      //   header: 'Win (%)',
      //   field: 'winPercent',
      //   alignment: 'center',
      //   type: 'text',
      //   showField: true
      // },
      {
        header: 'Opportunity Amount',
        field: 'opportunityAmount',
        alignment: 'center',
        // type: 'text',
        type:'amount',
        showField: true
      }
    ];
  
    forecastColumns: Column[] = [
      { field: 'lineNumber', header: 'Line No.', alignment: 'center', type: 'text', showField: true },
      { field: 'amount', header: 'Amount', alignment: 'center', type: 'text', showField: true },
      { field: 'date', header: 'Date', alignment: 'center', type: 'text', showField: true },
      { field: 'status', header: 'Status', alignment: 'center', type: 'text', showField: true },
      { field: 'stage_date', header: 'Start Date', alignment: 'center', type: 'text', showField: true },
      { field: 'close_date', header: 'End Date', alignment: 'center', type: 'text', showField: true },
      { field: 'past', header: 'Past', alignment: 'center', type: 'text', showField: true },
      { field: 'months', header: '12 Months', alignment: 'center', type: 'text', showField: true },
      { field: 'future', header: 'Future', alignment: 'center', type: 'text', showField: true }
    ];
    quoteLineColumns: Column[] = [
      { field: 'LINE_NO', header: 'Line No.' },
      { field: 'PRODUCT_NAME', header: 'Product' },
      { field: 'LINE_AMOUNT', header: 'Line Amount' },
      { field: 'SERVICE_START_DATE', header: 'Start Date' },
      { field: 'SERVICE_END_DATE', header: 'End Date' },
      { field: 'EXPECTED_START_DATE', header: 'Expected Start Date' },
      { field: 'EXPECTED_END_DATE', header: 'Expected End Date' },
      //Expected Start DateExpected End Date
      { field: 'Past', header: 'Past' },
      { field: 'Future', header: 'Future' },
     // { field: 'allocationAmt', header: 'Allocation Amt' },
      { field: 'Jan-25', header: 'Jan-25' },
      {field: 'Feb-25', header: 'Feb-25'},
      {field: 'Mar-25', header: 'Mar-25'},
      {field: 'Apr-25', header: 'Apr-25'},
      {field: 'May-25', header: 'May-25'},
      {field: 'Jun-25', header: 'Jun-25'},
      {field: 'Jul-25', header: 'Jul-25'},
      {field: 'Aug-25', header: 'Aug-25'},
      {field: 'Sep-25', header: 'Sep-25'},
      {field: 'Oct-25', header: 'Oct-25'},
      {field: 'Nov-25', header: 'Nov-25'},
      {field: 'Dec-25', header: 'Dec-25'},
    ];
    constructor(private router: Router , private opportunityService: OpportunityService, private dashboardService: AnalyticalDashboardService, private datePipe: DatePipe) {
    }
    
  
  

    onLoadData(event?: LazyLoadEvent) {
        this.loadData.emit({event});
    }
  

    onFilter(field: string, value: string) {
        this.selectedFilters = {
            ...this.selectedFilters,
            [field]: value ? value : ''
        };
        this.loadData.emit({event: null, selectedFilters: this.selectedFilters});
    }

    redirectToTab(number: string) {
        this.router.navigate(['/rmanArrangementsAllV', 'rmanArrangementsOverView', number, 'Y'], {queryParams: {fromDashboard: 'true'}});
    }
   
    
   
   /* onSummaryClick(event: Event) {
        event.preventDefault();
        alert('Summary clicked!');
    }

    onLineClick(event: Event) {
        event.preventDefault();
        alert('Line clicked!');
    }*/
    
   /* openSummaryDialog(data: any) {
        this.summaryData = {
          opportunityName: data.opportunityName,
          quoteNumber: data.quoteNumber,
          opportunityAmount: data.opportunityAmount,
          contracts: data.contracts,
          opportunityDate: data.opportunityDate,
        };
        this.showSummaryDialog = true;
      }
      onCloseSummary(){
        this.showSummaryDialog = false;
      }
    */
   
    
  
    
   /* openSummaryDialog(data: any): void {
        // Your logic to handle summary data
        this.showSummaryDialog = true;
    }

    openLineDialog(data: any): void {
        // Your logic to handle line data
        this.showLineDialog = true;
    }

    onCloseSummary(): void {
        this.showSummaryDialog = false;
    }

    onCloseLine(): void {
        this.showLineDialog = false;
    }*/

       /* onSummaryClick() {
            this.displaySummaryDialog = true;
            console.log('Hello summary');

       
}
        onLineClick() {
    this.displayLineDialog = true;
    console.log('Hello line');


}*/


onSummaryClick(rowData: any, field: string) {
    this.selectedLineCircleType = field;
    this.selectedLineCircleData =[];
   // this.selectedLineCircleData = this.lineCircleColumns[field].sampleData.summary;
    this.selectedViewType = 'summary';
    this.displayLineCircleDialog = true;
    //this.selectedQuoteName = rowData.quoteName;
    //this.selectedQuoteNumber = rowData.quoteNumber;
    this.setSelectedHeaderInfo(rowData, field);
    if (rowData.opportunityName) {
      this.stages$ = this.opportunityService.getStages(rowData.opptyNum).pipe(
        map(stages => this.getTableConfig(stages))
      );
    }
    if (field === 'contracts' && rowData.contracts?.number) {
     
      // Using the contract number from the dashboard response
      this.dashboardService.getContractDetails(rowData.contracts.number)
        .subscribe({
        next: (response: ContractDetails) => {
          //this.contractDetails = response;
          // Transform header data for summary view
          if (response && response.header) {
          this.selectedLineCircleData = [{
            contractNumber: response.header.dealArrangementId,
            customerName: response.header.customerName,
            region: response.header.region,
            salesRep: response.header.salesRep,
            bookings: response.header.bookings,
            del: response.header.delivered,
            billed: response.header.billed,
            revrecog: response.header.revenueRecognized,
            revenueGuidance: response.header.revenueGuidance,
            marginGuidance: response.header.marginGuidance,
            discountGuidance: response.header.discountGuidance,
            dealGuidance: response.header.dealGuidance,
            revenueContractValue: response.header.revenueContractValue,
          }];
        }
      }
      })
  }
  if (field === 'invoice') {
    this.dashboardService.getInvoiceDetails(rowData.quoteNumber)
      .subscribe({
        next: (response: InvoiceDetails) => {
          this.invoiceDetails = response;
          
          // Transform header data for summary view
          this.selectedLineCircleData = [{
            invoiceValue: response.header.invoiceValue,
            currency: response.header.currency,
            billToLocation: response.header.billToLocation,
            pendingBills: response.header.pendingBills,
            numberOfInvoices: response.header.numberOfInvoices,
            customerName: response.header.customerName,
          }];
        }
      });
    }
    if (field === 'quote') {
      this.dashboardService.getQuoteDetails(rowData.quoteNumber)
        .subscribe({
          next: (response: QuoteDetails) => {
            this.quoteDetails = response;
            
            if (response.header) {
              this.selectedLineCircleData = [{
                quoteName: response.header.quoteName,
                quoteNumber: response.header.quoteNumber,
                quoteValue: response.header.quoteValue,
                quoteDate: this.datePipe.transform(response.header.quoteDate, 'MM/dd/yyyy') || '',
                customerName: response.header.customerName,
                region: response.header.region,
                currency: response.header.currency,
                contractNumber: response.header.contractNumber,
                salesRep: response.header.salesRep,
                winPercent: response.header.winPercent,
                dealGuidance: response.header.dealGuidance,
                revenueGuidance: response.header.revenueGuidance,
                discountGuidance: response.header.discountGuidance,
                marginGuidance: response.header.marginGuidance
              }];
              
              console.log('Updated selectedLineCircleData:', this.selectedLineCircleData);
            }
          }
        });
    }
    if (field === 'order' && rowData.quoteNumber) {
      this.dashboardService.getOrderDetails(rowData.quoteNumber)
        .subscribe({
          next: (response: OrderDetails) => {
            this.orderDetails = response;
            // Transform header data for summary view
            this.selectedLineCircleData = [{
            orderNumber: response.header.orderNumber,
            orderName: response.header.orderName,
           // orderDate: response.header.orderedDate,
           orderedDate: this.datePipe.transform(response.header.orderedDate, 'MM/dd/yyyy') || '',
            orderAmount: response.header.orderAmount,
            currency: response.header.currency,
            contractNumber: response.header.contractNumber,
            customerName: response.header.customerName,
            
            revenueBeg: response.header.revenueBeg,
            revenueCurrent: response.header.revenueCurrent,
            revenueEnd: response.header.revenueEnd,
            delAmount: response.header.delAmount,
            


              // rcNumber removed as it's not available in the header
            }];
          }
        });
      }
  
  }

  onLineClick(rowData: any, field: string) {
    this.selectedLineCircleType = field;
   // this.selectedLineCircleData = this.lineCircleColumns[field].sampleData.line;
    this.selectedViewType = 'line';
    this.displayLineCircleDialog = true;
    //this.selectedQuoteName = rowData.quoteName;
    //this.selectedQuoteNumber = rowData.quoteNumber;
    this.setSelectedHeaderInfo(rowData, field);

    if (field === 'contracts' && rowData.contracts?.number) {
      this.dashboardService.getContractDetails(rowData.contracts.number)
        .subscribe({
          next: (response: ContractDetails) => {
            this.contractDetails = response;
            // Transform line data
            this.selectedLineCircleData = response.lines.map(line => ({
              lineNo: line.lineNo,
              parentLineNumber: line.parentLineNumber,
              lineAmount: line.lineAmount,
              productName: line.productName,
              allocationAmount: line.allocationAmount,
              allocationPercent: line.allocationPercent,
              netPriceDiscount: line.netPriceDiscount,
              discountGuidance: line.discountGuidance,
              grossMarginPercent: line.grossMarginPercent,
              revenueRecognition: line.revenueRecognition,
              billed: line.billed,
              delivered: line.delivered,
              marginGuidance: line.marginGuidance,
              revenueGuidance: line.revenueGuidance,
              dealGuidance: line.dealGuidance
            }));
          }
        });
      }

      // if (field === 'invoice' && rowData.quoteNumber) {
      //   this.dashboardService.getInvoiceDetails(rowData.quoteNumber)
      //     .subscribe({
      //       next: (response: InvoiceDetails) => {
      //         this.invoiceDetails = response;
      //         // Transform line data
      //         this.selectedLineCircleData = response.lines.map(line => ({
      //           invoiceLineNumber: line.invoiceLineNumber,
      //           invoiceDate: this.datePipe.transform(line.invoiceDate, 'MM/dd/yyyy'),
      //           invoiceValue: line.invoiceValue,
      //           productName: line.productName
      //         }));
      //       }
      //     });
    
      
      // }
      if (field === 'invoice' && rowData.quoteNumber) {
        this.dashboardService.getInvoiceDetails(rowData.quoteNumber)
          .subscribe({
            next: (response: InvoiceDetails) => {
              this.invoiceDetails = response;
              if (!response?.lines) {
                this.selectedLineCircleData = [];
                return;
              }
              this.selectedLineCircleData = response.lines
                .filter(line => line !== null)
                .map(line => ({
                  invoiceNumber: line?.invoiceNumber || '',
                  invoiceLineNumber: line?.invoiceLineNumber || '',
                  invoiceDate: line?.invoiceDate ? this.datePipe.transform(line.invoiceDate, 'MM/dd/yyyy') : '',
                  invoiceValue: line?.invoiceValue || 0, 
                  productName: line?.productName || ''
                }));
            },
            error: () => {
              this.selectedLineCircleData = [];
            }
          });
      }
  
      if(field === 'order' && rowData.quoteNumber) {
        this.dashboardService.getOrderDetails(rowData.quoteNumber)
          .subscribe({
            next: (response: OrderDetails) => {
              this.orderDetails = response;
              // Transform line data
              this.selectedLineCircleData = response.lines.map(line => ({
                orderlineNo: line.orderlineNo,
                parentLineNumber: line.parentLineNumber,
                dealLineNumber: line.dealLineNumber,
                //orderDate: this.datePipe.transform(line.orderDate, 'dd-MM-yyyy'),
                delAmount: line.delAmount,
                product: line.product,
                revenueBeg: line.revenueBeg,
                bookingAmount: line.bookingAmount,
                allocationAmount: line.allocationAmount,
                revenueCurrent: line.revenueCurrent,
                unAmortizedAmt: line.unAmortizedAmt,
              }));
            }
          });
      }
      
  //   if (field === 'quote' && rowData.quoteNumber) {
  //     this.selectedLineCircleType = field;
  //     this.selectedViewType = 'line';
  //     this.displayLineCircleDialog = true;
  //     this.setSelectedHeaderInfo(rowData, field);
  //     this.isLoading = true;

  //     this.dashboardService.getQuoteDetails(rowData.quoteNumber).subscribe({
  //       next: (response: QuoteDetails) => {
  //         if (response?.LineForecastDetails?.length > 0) {
  //           // Generate columns from all available keys in the first line
  //           const firstLine = response.LineForecastDetails[0];
  //           this.generatedColumns = this.generateDynamicColumns(firstLine);
            
  //           // Process the line data
  //           this.selectedLineCircleData = this.processLineData(response.LineForecastDetails);
  //         } else {
  //           this.generatedColumns = [];
  //           this.selectedLineCircleData = [];
  //         }
  //         this.isLoading = false;
  //       },
  //       error: (error) => {
  //         console.error('Error fetching quote details:', error);
  //         this.isLoading = false;
  //         this.generatedColumns = [];
  //         this.selectedLineCircleData = [];
  //       }
  //     });
  //   }
  // }

  // private generateDynamicColumns(firstLine: { [key: string]: any }): any[] {
  //   return Object.keys(firstLine).map(key => ({
  //     field: key,
  //     header: this.formatColumnHeader(key),
  //     alignment: 'center',
  //     type: this.determineColumnType(firstLine[key]),
  //     showField: true,
  //     width: this.determineColumnWidth(key, firstLine[key])
  //   }));
  // }

  // private processLineData(lines: Array<{ [key: string]: any }>): any[] {
  //   return lines.map(line => {
  //     const processedLine: { [key: string]: any } = {};
      
  //     Object.entries(line).forEach(([key, value]) => {
  //       // Format the value based on its type and field name
  //       processedLine[key] = this.formatFieldValue(key, value);
  //     });
      
  //     return processedLine;
  //   });
  // }

  // private formatFieldValue(key: string, value: any): any {
  //   if (value === null || value === undefined) return '';

  //   // Handle numeric fields that should be formatted as currency
  //   if (key.includes('AMOUNT') && typeof value === 'number') {
  //     return new Intl.NumberFormat('en-US', {
  //       style: 'currency',
  //       currency: 'USD',
  //       minimumFractionDigits: 2
  //     }).format(value);
  //   }

  //   // Handle date fields
  //   if (key.includes('DATE') && value) {
  //     try {
  //       return new Date(value).toLocaleDateString();
  //     } catch {
  //       return value;
  //     }
  //   }

  //   // Handle numeric fields that should be formatted as numbers
  //   if (typeof value === 'number') {
  //     return new Intl.NumberFormat('en-US', {
  //       minimumFractionDigits: 2,
  //       maximumFractionDigits: 2
  //     }).format(value);
  //   }

  //   return value;
  // }

  // private determineColumnType(value: any): string {
  //   if (value === null || value === undefined) return 'text';
  //   if (typeof value === 'number') return 'number';
  //   if (value instanceof Date || !isNaN(Date.parse(value))) return 'date';
  //   return 'text';
  // }

  // private determineColumnWidth(key: string, value: any): string {
  //   // Set column widths based on content type
  //   if (key.includes('NAME') || key.includes('PRODUCT')) return '200px';
  //   if (key.includes('DATE')) return '150px';
  //   if (key.includes('AMOUNT')) return '150px';
  //   if (typeof value === 'number') return '120px';
  //   return '150px';
  // }
  // Update the quote handling in onLineClick method
  if (field === 'quote' && rowData.quoteNumber) {
    this.dashboardService.getQuoteDetails(rowData.quoteNumber).subscribe({
      next: (response: QuoteDetails) => {
      if (response?.LineForecastDetails?.length > 0) {
        // Generate columns
        const firstLine = response.LineForecastDetails[0];
        const columns: Column[] = Object.keys(firstLine)
        .filter(key => firstLine[key] !== undefined)
        .map(key => {
          let alignment: string;
          let columnStyle: { [key: string]: string } = {};

          // Determine alignment based on field type
          if (key === 'LINE_NO') {
            alignment = 'center';
            columnStyle = { 'text-align': 'center' };
          } else if (key.includes('PRODUCT') || key.includes('NAME')) {
            alignment = 'left';
            columnStyle = { 'text-align': 'left' };
          } else if (
            key.includes('AMOUNT') || 
            key === 'Past' || 
            key === 'Future' ||
            /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{2}$/.test(key)
          ) {
            alignment = 'right';
            columnStyle = { 'text-align': 'right' };
          } else if (
            key.includes('DATE') ||
            key === 'SERVICE_START_DATE' ||
            key === 'SERVICE_END_DATE' ||
            key === 'EXPECTED_START_DATE' ||
            key === 'EXPECTED_END_DATE'
          ) {
            alignment = 'center';
            columnStyle = { 'text-align': 'center' };
          } else {
            alignment = 'left';
            columnStyle = { 'text-align': 'left' };
          }

          return {
            header: this.formatColumnHeader(key),
            field: key,
            alignment: alignment,
            type: this.getColumnType(key, firstLine[key]),
            showField: true,
            style: columnStyle
          };
        });
    
        this.generatedColumns = columns;
        
        // Process and format the data
        this.selectedLineCircleData = response.LineForecastDetails.map(line => {
        const formattedLine = { ...line };
        
        // Format date fields
        const dateFields = ['SERVICE_START_DATE', 'SERVICE_END_DATE', 'EXPECTED_START_DATE', 'EXPECTED_END_DATE'];
        dateFields.forEach(dateField => {
          if (formattedLine[dateField]) {
          formattedLine[dateField] = this.datePipe.transform(formattedLine[dateField], 'MM/dd/yyyy') || '';
          }
        });
    
        return formattedLine;
        });
      }
      this.isLoading = false;
      },
      error: (error) => {
      console.error('Error fetching quote details:', error);
      this.isLoading = false;
      }
    })
  }
}
  // Helper method to format currency
  private formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  }
 
    

    
  setSelectedHeaderInfo(rowData: any, field: string) {
    switch (field) {
        case 'opportunity':
            this.selectedHeaderInfo = [
                { label: 'Opportunity Name', value: rowData.opportunityName || '' },
                //{ label: 'Opportunity Number', value: rowData.opportunityNumber || '' }
            ];
            if (rowData.opptyNum) {
              this.isLoading = true;
              this.opportunityService
                .getStages(rowData.opptyNum)
                .pipe(
                  map(stages => stages[0]?.opportunityStageId),
                  switchMap(stageId => this.opportunityService.getForecasting(rowData.opptyNum, stageId)),
                  map(forecast => {
                    this.generateForecastColumns(forecast);
                    return {
                      content: forecast,
                      pageable: {
                        pageNumber: 0,
                        pageSize: 10,
                        sort: {
                          sorted: false,
                          unsorted: true,
                          empty: true
                        },
                        offset: 0,
                        unpaged: false,
                        paged: true
                      },
                      totalElements: forecast.length,
                      totalPages: Math.ceil(forecast.length / 10),
                      last: true,
                      size: 10,
                      number: 0,
                      sort: {
                        sorted: false,
                        unsorted: true,
                        empty: true
                      },
                      first: true,
                      numberOfElements: forecast.length,
                      empty: forecast.length === 0
                    };
                  })
                )
                .subscribe({
                  next: forecastData => {
                    this.forecasts = forecastData;
                    this.isLoading = false;
                  },
                  error: err => {
                    console.error('Error fetching forecast:', err);
                    this.isLoading = false;
                    this.forecasts = null;
                  }
                });
            }
            break;
        case 'quote':
            this.selectedHeaderInfo = [
                { label: 'Quote Name', value: rowData.quoteName || '' },
                { label: 'Quote Number', value: rowData.quoteNumber || '' },
               // { label: 'Quote Date', value: rowData.quoteDate || '' }
            ];
            break;
        case 'order':
            this.selectedHeaderInfo = [
              //  { label: 'Order Number', value: rowData.order?.number || '' },
               // { label: 'Order Date', value: rowData.order?.date || '' }
            ];
            break;
        /*case 'invoice':
            this.selectedHeaderInfo = [
                { label: 'Invoice Value', value: rowData.invoice?.value || '' }
            ];
            break;*/
        case 'contracts':
            this.selectedHeaderInfo = [
                { label: 'RC Number', value: rowData.contracts?.number || '' }
            ];
            break;
        default:
            this.selectedHeaderInfo = [];
    }
}

  // getLineCircleColumns() {
  //   if (this.selectedLineCircleType === 'quote' && this.selectedViewType === 'line') {
  //     return this.lineCircleColumns[this.selectedLineCircleType]?.line || [];
  // }
  //   return this.lineCircleColumns[this.selectedLineCircleType][this.selectedViewType];
  // }
  getLineCircleColumns(): Column[] {
   
    return this.lineCircleColumns[this.selectedLineCircleType]?.[this.selectedViewType] || [];
}


onMouseLeave(event: MouseEvent, overlayPanel: OverlayPanel) {
    setTimeout(() => {
        overlayPanel.hide();
    }, 1000); // 1 seconds delay
}
  

// isDetailsLoading = false;




ngOnInit() {


   /* this.stages$ = this.opportunityService.getStages().pipe(
        map(stages => stages.map(stage => ({
          opportunityDate: stage.opportunityDate,
          opportunityNumber: stage.opportunityNumber,
          win: stage.win,
          opportunityAmount: stage.opportunityAmount,
          stageName: stage.stageName
        })))
      );*/
    // this.opportunityService.getStages().subscribe(console.log);
}
isDecimal(value: any): boolean {
  // Check if it's a number and has decimals
  return !isNaN(value) && value % 1 !== 0;
}


getTableConfig<T>(data: T): TableData<T> {
    return {
        content: data,
        pageable: {
            sort: {
                sorted: true,
                unsorted: false,
                empty: false
            },
            offset: 0,
            pageNumber: 1,
            pageSize: 10,
            unpaged: false,
            paged: true
        },
        totalElements: 2,
        totalPages: 1,
        last: false,
        size: 5,
        number: 1,
        sort: {
            sorted: true,
            unsorted: false,
            empty: false
        },
        first: true,
        numberOfElements: 2,
        empty: false
    };
}
private generateForecastColumns(forecastData: Forecast[]) {
  if (forecastData && forecastData.length > 0) {
    const firstRecord = forecastData[0];
    const columns: Column[] = Object.keys(firstRecord)
      .filter(key => firstRecord[key] !== undefined)
      .map(key => ({
        header: key,
        field: key,
        //alignment: 'center',
       alignment : this.getColumnAlignment(key, firstRecord[key]),
       // type: typeof firstRecord[key] === 'number' ? 'number' : 'text',
       type: this.getColumnType(key, firstRecord[key]),
        showField: true
      }));
    this.forecastColumns = columns;
  }
}

// generateQuoteColumns(quoteLines: QuoteLine[]): Column[] {
//   if (quoteLines && quoteLines.length > 0) {
//     const firstRecord = quoteLines[0];
//     const columns: Column[] = Object.keys(firstRecord)
//       .filter(key => firstRecord[key] !== undefined)
//       .map(key => ({
//         header: key,
//         field: key,
//         alignment: 'center',
//         type: typeof firstRecord[key] === 'number' ? 'number' : 'text',
//         showField: true
//       }));
    
//     this.quoteColumns = columns;
//     return columns;
//   }
  
//   return [];
// }
generateQuoteColumns(lines: any[]): any[] {
  if (!lines || lines.length === 0) {
      // Return an empty array if no data is present
      return [];
  }

  // Generate columns dynamically from the keys of the first object in lines
  return Object.keys(lines[0]).map(key => ({
      field: key, // Field name
      header: this.formatColumnHeader(key), // Formatted column header
      alignment: 'center', // Default alignment
      type: typeof lines[0][key] === 'number' ? 'number' : 'text', // Determine column type
      showField: true, // Visibility of the column
      width: '150px' // Default column width
  }));
}

  // Extract keys from the first line to generate columns
  

// Helper method to format cell values
formatCellValue(value: any, column: Column): string {
  if (value === null || value === undefined) return '';

  switch (column.type) {
      case 'number':
          return typeof value === 'number' 
              ? value.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })
              : value;
      
      case 'date':
          return value instanceof Date
              ? value.toLocaleDateString()
              : new Date(value).toLocaleDateString();
      
      default:
          return String(value);
  }
}

formatColumnHeader(key: string): string {
  if (!key) {
    return '';
  }
  // Replace underscores with spaces and convert to lowercase first
  return key
    .toLowerCase() 
    .replace(/_/g, ' ')
    .replace(/\b\w/g, char => char.toUpperCase());
}

private getColumnType(key: string, value: any): string {
  
  if (key.includes('DATE')){
     return 'date';
  }

  if (key === 'Quantity' || key === 'QUANTITY') {
    return 'quantity';
  }


  // if (key === 'LINE_AMOUNT' || 
  //   key === 'Past' || 
  //   key === 'Allocation Amount' ||
  //   key === 'Future' || 
  //   key.includes('AMOUNT') ||
  //   /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{2}$/.test(key)) {
  //   return 'amount';
  // } 

  if (
    key === 'LINE_AMOUNT' || 
    key === 'Past' || 
    key === 'Future' || 
    key.includes('AMOUNT') ||
    key.includes('Amount') ||
    key.includes('Allocation') ||
    /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{2}$/.test(key)
  ) {
    return 'amount';
  }

  if (typeof value === 'number') {
    return 'number';
  }
  



  return typeof value === 'number' ? 'number' : 'text';
}


// getColorClass(value: number): string {
//   switch(value) {
//     case 1:
//       return 'green';
//     case 2:
//       return 'yellow';
//     case 3:
//       return 'red';
//     default:
//       return '';
//   }
// }

getGuidanceColor(value: any): string {
  if (!value || value === null || value === 'null' || value === undefined) {
    return '';
  }

  const cleanValue = typeof value === 'string' ? value.replace(/"/g, '') : value;
  
  switch(cleanValue?.toUpperCase()) {
    
    case 'RED': return 'red-dot';
    case 'GREEN': return 'green-dot'; 
    case 'YELLOW': return 'yellow-dot';
    default: return '';
    
  }
} 
private getColumnAlignment(key: string, value: any): string {
  // Date fields
  if (key.includes('DATE') || key.includes('Date')) {
    return 'center';
  }

  // Amount fields
  if (key.includes('AMOUNT') || 
      key.includes('Price') || 
      key.includes('Allocation Amount') ||
      key === 'Past' || 
      key === 'Future' ||
      /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{2}$/.test(key)) {
    return 'right';
  }

  // Number fields
  if (typeof value === 'number') {
    return 'right';  
  }
  if (key === 'Quantity' || key === 'QUANTITY') {
    return 'center';
    

  }
  
  // Amount fields
  if (
    key.includes('AMOUNT') || 
    key.includes('Price') || 
    key.includes('price') ||
    key === 'Past' || 
    key === 'Future'
  ) {
    return 'right';
  }
    
  
  
  // Fixed number fields
  if (
    key === 'Win%' || 
    key === 'Customer Number' || 
    key === 'Parent Line Number' ||
    key === 'Quantity' 
   
  ) {
    return 'center';
  }


  
  // Fixed text fields
  if (
    key === 'Region' ||
    key === 'Currency' ||
    key === 'Bundle Flag'
  ) {
    return 'center';
  }
  
  // Default left alignment
    return 'left';
}





 }
  


