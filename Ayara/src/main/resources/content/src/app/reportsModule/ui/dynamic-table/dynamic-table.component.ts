import {Component, EventEmitter, Input, OnInit, Output , ViewChild} from '@angular/core';
import {TableData} from '../../../shared/models/tableData';
import {Column} from '../../../shared/models/column.interface';
import {TableFilterOptions} from '../../models/filter-params';
import { Table} from 'primeng/table';
import { DatePipe } from '@angular/common';
import { LazyLoadEvent } from 'primeng/api';

@Component({
    selector: 'app-dynamic-table',
    templateUrl: './dynamic-table.component.html',
    styleUrls: ['./dynamic-table.component.scss']
})
export class DynamicTableComponent implements OnInit {
    @Input() header = '';
    @ViewChild ('dt') table: Table;
    data: any[] = [];
    searchData: any[] = [];
    @Input() defaultSelectedIndex: number = 0;
    selectedRow: any = null;
    @Input() customerName: string;
@Input() opportunityNumber: string;
@Input() opportunityName: string;
    @Input() tableData: TableData<any>;
    @Input() columns: Column[] = [];
    @Input() isLoading = false;
    @Input() filterOptions: TableFilterOptions;
    @Input() withSearch = false; 
    @Input() withRefresh  = false;  
    @Input() withTabs = false;
    @Input() tabsCount = 2;
    @Input() backLink: string;
    @Input() selectionMode = '';
    @Input() showToolbar: boolean = true; 
    @Input() totalRecords: number = 0;

    
    @Output() search = new EventEmitter<string>();

    @Output() toggleMaximize = new EventEmitter<boolean>();
    @Output() rowSelect = new EventEmitter<any>();
    @Output() refresh = new EventEmitter();
    @Output() loadData = new EventEmitter<LazyLoadEvent>();
    selectedColumns: Column[] = [];

    
    ngOnInit() {
        this.selectedColumns = [...this.columns];
    }


    onColumnsChange(updatedColumns: Column[]) {
        this.selectedColumns = [...updatedColumns];
    }
    ngAfterViewInit() {
        // Select default row after view is initialized
        this.selectRowByIndex(this.defaultSelectedIndex);
    }

    selectRowByIndex(index: number) {
        if (this.tableData?.content?.length > index) {
            setTimeout(() => {
                const rowToSelect = this.tableData.content[index];
                this.selectedRow = rowToSelect;
                
                // Properly set the selected row using PrimeNG's selection mechanism
                if (this.selectionMode === 'single') {
                    // This will trigger the onRowSelect event
                    this.table.selection = rowToSelect;
                    // Manually emit the selection event
                    this.onRowSelect({ data: rowToSelect });
                }
            });
        }
    }

    onRowSelect(event: any) {
        console.log(event);
        this.selectedRow = event.data;
        this.rowSelect.emit(event.data);
    }
    onRefresh() {
        this.isLoading = true;
        this.refresh.emit();
      }

    //   onSearch(filteredData: any[]) {
    //     this.tableData.content = filteredData;
    // }
    onLazyLoad(event:LazyLoadEvent) {
        // Emit the lazy load event with pagination info
        this.loadData.emit(event);
    }
    onSearch(term: string) {
        console.log('Search Term:', term);
        this.search.emit(term);
    }


    // exportExcel() {
    //     import("xlsx").then(xlsx => {
    //         const worksheet = xlsx.utils.json_to_sheet(this.products);
    //         const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
    //         const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
    //         this.saveAsExcelFile(excelBuffer, "products");
    //     });
    // }
    //
    // saveAsExcelFile(buffer: any, fileName: string): void {
    //     import("file-saver").then(FileSaver => {
    //         let EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    //         let EXCEL_EXTENSION = '.xlsx';
    //         const data: Blob = new Blob([buffer], {
    //             type: EXCEL_TYPE
    //         });
    //         FileSaver.saveAs(data, fileName + '_export_' + new Date().getTime() + EXCEL_EXTENSION);
    //     });
    // }

}
