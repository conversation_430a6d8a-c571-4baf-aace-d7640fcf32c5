<p-dialog
        styleClass="dd-table-dialog"
        [(visible)]="isVisible"
        (onHide)="onClose()">
    <ng-template pTemplate="header">
        <h2 class="p-dialog-title">
            Filter
            <!--            <div class="dialog-subtitle light-text md text-align-left">Add or delete filter and attributes</div>-->
        </h2>
    </ng-template>
    <div class="container filter-container">
        <div class="d-flex justify-content-between align-items-center">
            <div class="dd-bold-text">Attributes</div>
            <!--            <button class="dd-primary-btn filter-btn add">-->
            <!--                <i class="fa fa-plus"></i>-->
            <!--                Add New-->
            <!--            </button>-->
            <!--            <button class="dd-primary-btn filter-btn remove">-->
            <!--                <i class="fa fa-minus"></i>-->
            <!--                Remove-->
            <!--            </button>-->
        </div>
        <div class="filter-items d-flex flex-column">
            <p-listbox [options]="data">
                <ng-template
                        pTemplate="item"
                        let-item let-index="index">
                    <div class="filter-item d-flex align-items-center mt-2">
                        <input
                                type="checkbox"
                                [(ngModel)]="item.showField"
                                [checked]="item.showField"
                                (change)="change()"
                                [disabled]="item.display && index === 0"
                                class="filter-checkbox">
                        <span class="light-text ml-1">{{item.header | chartTitle | titlecase}}</span>
                    </div>
                </ng-template>
            </p-listbox>
            <!--            <div class="filter-item d-flex align-items-center mt-2" *ngFor="let c of globalCols">-->
            <!--                <input type="checkbox" [(ngModel)]="c.showField" [checked]="c.showField" class="filter-checkbox" (change)="onShowColField($event)">-->
            <!--                <span class="light-text ml-1">{{c.header}}</span>-->
            <!--            </div>-->
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="row">
            <div class="col">
                <button
                        class="dd-primary-link"
                        (click)="onReset()">Reset to default
                </button>
            </div>
            <div class="col-sm d-flex justify-content-between">
                <button
                        class="dd-secondary-btn"
                        (click)="onClose()">Cancel
                </button>
                <button
                        class="dd-primary-btn"
                        (click)="onSave()">Save
                </button>
            </div>
        </div>
    </ng-template>
</p-dialog>
