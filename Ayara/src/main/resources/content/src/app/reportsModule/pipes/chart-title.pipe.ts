import {Pipe, PipeTransform} from '@angular/core';

@Pipe({
    name: 'chartTitle'
})
export class ChartTitlePipe implements PipeTransform {

    transform(value: string): string {
        const str = value.charAt(0).toUpperCase() + value.slice(1).replace(/([A-Z])/g, ' $1').trim();
        if (str.includes('Total')) {
            return str.replace('Total', '');
        } else {
            return str.replace('By', 'by').replace('S K U', 'SKU');
        }
    }

}
