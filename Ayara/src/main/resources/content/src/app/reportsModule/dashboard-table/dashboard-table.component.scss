// @import "../../../assets/sass/variables";

// :host ::ng-deep {

//   .p-datatable-scrollable-body {
//     &::-webkit-scrollbar-track {
//       background: $DDTableBorderColor;
//     }

//     &::-webkit-scrollbar-thumb {
//       background: $flowerBlueColor;
//     }
//   }

//   .p-datatable-wrapper {
//     padding-bottom: 20px;

//     .p-inputtext {
//       font-size: 12px;
//     }

//     .p-column-filter-row {
//       height: 20px;
//     }

//   }
// }

// :host ::ng-deep p-dropdown {
//   width: 90px;
//   padding: 0;
//   margin: auto;
//   background: none;
//   border: none;
//   line-height: 20px;

//   &.parameter {
//     width: 100%;

//     .p-dropdown-label {
//       font-weight: 700;

//       &:hover {
//         color: $flowerBlueColor;

//       }
//     }
//   }

//   .p-dropdown-panel {
//     box-shadow: $boxShadow !important;
//     border-radius: 10px;

//     .p-dropdown-item {
//       &:hover {
//         background: $DDTableBgColor;
//         color: $DDTableHeaderTextColor
//       }

//       &.p-highlight {
//         background: $flowerBlueColor;
//         color: #fff;
//       }
//     }

//     .p-dropdown-items-wrapper {
//       &::-webkit-scrollbar-track {
//         background: $DDTableBorderColor;
//       }

//       &::-webkit-scrollbar-thumb {
//         background: $flowerBlueColor;
//       }
//     }

//     .p-dropdown-items {
//       padding: 10px;

//       .p-dropdown-item {
//         border-bottom: 1px solid $DDTableBorderColor;
//       }
//     }
//   }

//   &.bordered {
//     background: $DDTableBgColor;
//     padding: 0 5px;
//     border: 1px solid $DDTableBorderColor;
//     border-radius: 20px;
//   }

//   .p-dropdown-trigger-icon {
//     background: $flowerBlueColor;
//   }

//   .p-dropdown-clear-icon {
//     right: -20px;
//   }
// }


// :host ::ng-deep p-paginator {
//   .p-paginator {
//     background: transparent;
//     justify-content: flex-end;
//     margin: 0;

//     .p-paginator-current {
//       color: $DDTableTextLightColor;
//       font-size: 10px;
//     }

//     .p-link {
//       width: 18px;
//       height: 18px !important;
//       background: $flowerBlueColor;
//       color: #fff;
//       font-size: 12px;
//       opacity: 1;
//       border-radius: 50%;

//       &.p-disabled {
//         background: $DDTableBorderColor;
//       }
//     }

//     .p-paginator-page {
//       background: none;

//       &.p-highlight {
//         background: $flowerBlueColor !important;
//       }

//       &:hover {
//         background: $flowerBlueColor !important;
//       }
//     }

//     .p-paginator-pages {
//       margin: 0;
//     }
//   }
// }

// .dashboard__table {
//   overflow: visible;
//   color: $DDTableTextColor;
//   font-size: 14px;
//   line-height: 1.5;

//  /* ::ng-deep .p-datatable {
//     border-collapse: separate;
//     border-spacing: 0;
//     //border: 1px solid $DDTableBorderColor;
//     border-radius: 8px 8px 0 0;
//     overflow: hidden;
//    // border: 2px solid $DDTableBorderColor;
    
//   }
//   ::ng-deep .p-datatable-tbody > tr > td:first-child,
//   ::ng-deep .p-datatable-thead > tr > th:first-child {
//   position: sticky;
//   left: 0;
// }*/

//   table {
//     border: 1px solid $DDTableBorderColor;
//     border-radius: 25px;
    
//   }
  

//   td, th {
//     border: 2px solid $DDTableBorderColor;
//     background: transparent;
//     text-align: center;
    
//   }

//   td {
//     font-size: 12px;
//     height: 87px;
//   }

//   td, th {
//     position: relative;
//     padding: 0 14px;
//   }

//    th:first-child,td:first-child {
//     position: sticky;
//     left: 0;
//     top: 0;
//     z-index: 100;
//     padding: 0 10px;
//    text-align: left;
//    font-weight: 700;
//   }


//   .triangle {
//     border-right: none;
//     border-left: none;
//     padding-right: 0;
//     padding-left: 0;

//     div {
//       position: relative;

//       &:after {
//         content: " ";
//         display: block;
//         width: 2px;
//         height: 25px;
//         background: #D0D2FF;
//         position: absolute;
//         top: -65%;
//         left: 95%;
//         z-index: 1;
//         transform: skew(25deg);
//       }

//       &:before {
//         content: " ";
//         display: block;
//         width: 2px;
//         height: 25px;
//         background: #D0D2FF;
//         position: absolute;
//         top: 54%;
//         left: 95%;
//         z-index: 1;
//         transform: skew(-25deg);
//       }
//     }

//     .triangle:last-of-type {
//       div {
//         background: #000;
//       }
//     }
//   }

//   td:first-child {
//     background: #fff;
//   }

//   th {
//     color: $DDTableHeaderTextColor;
//     background: $DDTableBgColor;
//   }

//   .header-filter {
//     display: flex;
//     flex-direction: column;
//     align-items: center;

//     span {
//       font-size: 12px;
//     }
//   }

//   .table-icon {
//     width: 22px;
//   }

//   .count-field {
//     padding: 5px 10px;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     background: $flowerBlueColor;
//     border-radius: 20px;
//     color: #fff;
//     text-overflow: ellipsis;
//   }

//   .status-field {
//     min-width: 80px;
//     margin: 0 auto;
//     border-radius: 20px;
//     border: 1px solid;
//     text-align: center;
//   }

//   .praposal {
//     border-color: #7D7D7D;
//     color: #7D7D7D;
//     background: #00000029;
//   }

//   .completed {
//     border-color: $lightGreenColor;
//     color: $lightGreenColor;
//     background: $mintColor;
//   }

//   .bestcase {
//     border-color: $flowerBlueColor;
//     color: $flowerBlueColor;
//     background: $DDTableBorderColor;
//   }

//   .ordered {
//     border-color: $DDTableHeaderTextColor;
//     color: $DDTableHeaderTextColor;
//     background: #10052D21;
//   }

//   .color-number {
//     font-weight: 700;

//     &.GREEN {
//       color: $lightGreenColor;
//     }

//     &.RED {
//       color: $redColor;
//     }

//     &.YELLOW {
//       color: $orangeColor;
//     }
//   }
// }

// .light-text {
//   color: $DDTableTextLightColor;
//   font-size: 10px;

//   &.sm {
//     font-size: 10px;
//   }

//   &.md {
//     font-size: 12px;
//   }

//   &.lg {
//     font-size: 14px;
//   }
// }

// .blue-text {
//   color: $flowerBlueColor;
//   font-size: 10px;


//   &.sm {
//     font-size: 10px;
//   }

//   &.md {
//     font-size: 12px;
//   }

//   &.lg {
//     font-size: 14px;
//   }
// }

// .separator {
//   margin: 0 5px;
//   height: 11px;
//   background: $DDTableTextLightColor;
//   width: 1px;
// }

// .checkbox {
//   display: flex;
//   align-items: center;
//   width: max-content;
//   height: 30px;
//   border-radius: 20px;
//   padding: 5px 10px 5px 5px;
//   background: $mintColor;
//   border: 1px solid $lightGreenColor;
//   color: $lightGreenColor;
//   margin: 10px auto;

//   &-date {
//     height: 24px;
//   }

//   img {
//     margin-right: 10px;
//   }

//   span {
//     margin-left: 3px;
//   }
// }

// .line {
//   position: absolute;
//   height: 2px;
//   width: 100%;
//   background: $lightGreenColor;

//   &.red {
//     background: $redColor;
//   }

//   &-col {
//     padding: 0 !important;
//     border: none !important;
//     border-bottom: 2px solid #D0D2FF !important;
//   }

//   &-container {
//     overflow: hidden;
//     display: flex;
//     flex-direction: column;
//     padding: 5px 0 5px 5px;
//     height: 85px;
//     justify-content: flex-end;

//     &.first {
//       padding-left: 15px;
//     }

//     &.last {
//       padding-right: 15px;
//     }
//   }

//   &-body {
//     position: relative;
//     display: flex;
//     align-items: center;
//   }

//   &-leakage {
//     text-align: left;
//     align-self: flex-start;
//     height: 20px;
//   }

//   &-date {
//     align-self: flex-start;
//   }

//   &-amount {
//     align-self: flex-start;
//     font-weight: bold;
//   }

//   &-circle {
//     position: relative;
//     flex: 0 0 auto;
//     width: 9px;
//     height: 9px;
//     left: -5px;
//     background: $lightGreenColor;
//     border-radius: 50%;
//     z-index: 10;
    
//     &.red {
//       background: $redColor;

//       &:after {
//         position: absolute;
//         content: "";
//         width: 3px;
//         height: 3px;
//         left: 3px;
//         top: 3px;
//         background: #fff;
//         border-radius: 50%;
//       }
      
//     }
//   }
// }


// :host::ng-deep .dd-table-dialog {
//   max-height: 570px;
//   min-width: 300px;
//   width: 100%;
//  // border-radius: 8px 8px 8px 8px;

//   &.line{
//     max-height: 570px;
//     min-width: 300px;
//     width: 100%;
//   }

//   &.export {
//     max-width: 650px;
//   }

//   &.calendar {
//     width: auto;
//   }

//   .p-dialog-header {
//     color: $flowerBlueColor;
//     background: none;
//     border-bottom: 2px solid $DDTableBorderColor;
//   }

//   .p-dialog-title {
//     font-size: 14px;
//     font-weight: 700;
//   }

//   .dialog-subtitle {
//     margin-top: 10px;
//     font-weight: 400;
//   }

//   .p-dialog-header-close {
//     background: $flowerBlueColor;
//     width: 18px;
//     height: 18px !important;
//     border-radius: 50%;

//     &-icon {
//       color: #fff;
//       font-size: 10px;
//     }
//   }

//   .p-dialog-footer {
//     padding: 20px;
//     box-shadow: none;
//     background: none;
//     border-top: 2px solid $DDTableBorderColor;
//   }
// }

// .dd-primary-btn {
//   margin: 0 10px;
//   cursor: pointer;
//   width: 80px;
//   background: $flowerBlueColor;
//   border-radius: 20px;
//   color: #fff;
//   font-size: 12px;

//   &:hover {
//     background: $DDTableHeaderTextColor;
//   }


//   &.filter-btn {
//     display: flex;
//     justify-content: space-evenly;
//     align-items: center;
//     width: 75px;
//     height: 22px !important;
//     font-size: 10px;
//   }

//   &.add {
//     &:hover {
//       background: none;
//       border: 1px solid $flowerBlueColor;
//       color: $flowerBlueColor;
//     }
//   }

//   &.remove {
//     background: $grayBgColor;

//     &:hover {
//       background: $redColor;
//     }
//   }
// }

// .dd-secondary-btn {
//   margin: 0;
//   cursor: pointer;
//   background: none;
//   color: $DDTableTextColor;
//   font-weight: 700;
// }

// .dd-primary-link {
//   margin: 0;
//   cursor: pointer;
//   background: none;
//   color: $flowerBlueColor;
//   font-weight: 700;
// }

// .dd-input {
//   border-radius: 10px;
//   padding: 2px;
//   background: $DDTableBgColor;
//   border: 1px solid $DDTableBorderColor;

//   &-pages {
//     text-align: center;
//     margin: 0 5px;
//     width: 35px;
//     height: 20px;

//     &::-webkit-outer-spin-button,
//     &::-webkit-inner-spin-button {
//       -webkit-appearance: none;
//       margin: 0;
//     }
//   }
// }

// .sparks-icon {
//   position: absolute;
//   top: 4px;
//   right: 10px;
// }
//  .custom-tooltip {
//   background-color: #2C2C54;
//   box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
//   color: white;
//   position: absolute;
//   display: none;
//   padding: 0.50em 1.0rem;
//   max-width: 12.5rem;
//   pointer-events: none;
//   text-decoration:underline;
//   border-radius: 50px;
//   transform: translateY(-7px);
// }

// ::ng-deep .p-tooltip .p-tooltip-text {
//   white-space:nowrap;
//   text-align: center;
// }
// ::ng-deep .p-overlaypanel {
//   z-index: 1000 !important; /* Override the z-index */
//   top: -30px !important; /* Adjust the top position */
//   left: 0px !important; /* Adjust the left position */
//   background-color: #2C2C54;
//   box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
//   border-radius: 50px;
//   transform: translateY(-7px);
//   position: absolute;
//   padding: 0.50em 1.0rem;
//   max-width: 12.5rem;
//   text-decoration: underline;
// }
// ::ng-deep .p-overlaypanel .tooltip-options {
//   display: flex;
//   align-items: center;
//   gap: 0.1rem; 
//   }

// ::ng-deep .p-overlaypanel .tooltip-options a {
//   color: #FFFFFF; /* White text color */
//    text-decoration: underline !important; 
// }
// ::ng-deep .p-overlaypanel .tooltip-options .separate {
//   color: #fff; /* White color for the separator */
// }
// ::ng-deep .checkbox-overlaypanel {
//   z-index: 1000 !important; 
//   left: 14px !important; 
//   border-radius: 50px;
//   position: absolute;
//   bottom: 15px;
 
// }
// .header-content{
 
//   padding: 15px 25px;
//   font-family: sans-serif;
// }

// .subheader{
//   //font-size: 12px;
//   color: #585858;;
//   font: normal normal bold 16px/12px Poppins;
//   padding-bottom: 0px;
//   margin-bottom: 1px;
//   letter-spacing: 0px;
 
//   .subheader-label {
//     font: normal normal normal 15px/10px 'Poppins';
//     color: #585858;
//     margin-right: 5px;
//   }

//   .subheader-value {
//     color: black; // Assuming you have this color variable defined
//     margin-right: 5px;
//     font: normal normal bold 14px/10px 'Poppins';
//   }
// }

//  .header-content{
//   //font-size: 16px;
//   //font-weight: bold;
//   padding: 5px;
//   display: flex;
//   flex-direction: column;
//   padding-left: 20px;
//   gap: 1px;
// }
//  .view-type {
//   //font-weight: bold;
//   font: normal normal bolder 16px/21px Poppins;
//   letter-spacing: 0px;
//   color: #5F5AF7;
//   padding-bottom: 4px;
//   //opacity: 1;

// }
// .p-table{
//   width: 100%;
// }
// .summary-columns {
//   display: flex;
//   justify-content: space-between;
//   padding: 10px;
//   flex-direction: row;
//   flex-wrap: nowrap;
//   align-items: baseline;
// }
// .header-text {
//       flex: 0 0 auto;
//       color: #7D7D7D;
//       width: 135px;
//       display: inline-block;
//       text-align: right;
//       padding-right: 5px;
//       padding-left: 0px;
//       font: normal normal normal 14px/21px 'Poppins';
//       //background-color: #3366cc;
//   //padding: 2px 5px;
//   //border-radius: 3px;
//  // font-weight: bold;
//   //margin-right: 5px;
// }
// .colon{
//   flex: 0 0 auto;
//   padding: 0 10px;
//   color: #7D7D7D;
//   margin-left: 1px;
// }


// .data-text {
//   color: #5F5AF7;
//   flex: 1;
//  // background-color: #f0f0f0;
//   padding: 0px;
//   padding-left: 5px;
//   //border-radius: 3px;
//   display: inline-block;
//   font-size: 12px;
//   width: 130px;
//   font: normal normal normal 14px/21px Poppins;
// }
// .left-column,
// .right-column {
//   display: flex;
//   flex-direction: column;
//   width: 48%;
// }


//   .column {
//     width: 47%;
//     display: inline-flex;
//     flex-wrap: nowrap;
//     justify-content: space-evenly;
//     align-items: center;

//   }

//   .row {
//     //display: block-ruby;
//     //justify-content: space-between;
//     //margin-bottom: 10px;
//     //align-items: baseline;
//     display: grid;
//     gap: 10px;
//     grid-template-columns: auto auto auto auto;
//     //flex-direction: row;
//     //flex-wrap: nowrap;
//    // align-content: center;
//     //justify-content: space-evenly;
//     align-items: center;
//   }
//    /* .label {
//       font-size: 14px;
//       color: #585858;
//       font-weight: 500;
  
//     }

//     .value {
//       font-size: 14px;
//       color: #0066cc;
//       font-weight: 600;
//       width: 55%;
//       text-align: right;
//     }*/

//     .line__table {
//       overflow: visible;
//       color: $DDTableTextColor;
//       font-size: 14px;
//       line-height: 1;
//       //padding: 30px;

//       .table-wrapper {
//         border-left: 1px solid $DDTableBorderColor;
//         border-right: 1px solid $DDTableBorderColor;
//         border-top: none;
//         border-bottom: none;
//         overflow: hidden;
//       }
    
//       ::ng-deep .p-datatable {
//         border-collapse: separate;
//         border-spacing: 0;
//         border: 1px solid $DDTableBorderColor;
//         border-radius: 8px 8px 0 0;
//         overflow: hidden;
//       }
//      ::ng-deep .p-datatable-wrapper{
//         overflow-x: auto;
//         overflow-y: hidden;
//         border: none;
        
//       }
//       ::ng-deep .p-datatable-tbody >tr > td {
//         border-bottom: none;
//       }
    
    
       
     
    
//       table {
//         border: 1px solid $DDTableBorderColor;
//        // border-radius: 8px 8px 0px 0px;


//       }
     

    
//       td, th {
//         border: 1px solid $DDTableBorderColor;
//         background: transparent;
//         text-align: center;
//       }
     
    
//       td {
//         font-size: 12px;
//         height: 50px;
//       }
    
//       td, th {
//         position: relative;
//         padding: 5px 14px;
//       }

//      th 
//      {
//             color: $DDTableHeaderTextColor;
//             background: $DDTableBgColor;
//           }
          
      
//         }
      
//       :host::ng-deep p-dialog .p-dialog {
//         max-width: 1000px;
//         /*max-height: 570px;
//         min-width: 300px;
//         width: 100%; 
//         min-width: 500px;
//         max-width: 1000px;
//         background: #fff;
//         min-height: 150px;
//         box-shadow: 0 10px 20px rgb(0 0 0 / 19%), 0 6px 6px rgb(0 0 0 / 23%);
//         border-radius: 3px;*/
//       }
//       :host::ng-deep .p-dialog {
//         .dd-table-dialog {
//           .line-view-content {
//             padding: 30px;
//           }
//         }
//       }

//       /*:host::ng-deep .p-dialog {
//         .p-dialog-content {
//           padding: 30px; // Add 30px padding to the dialog content
//         }
//       }*/
//   //  }
 
//    /* .dd-table-dialog .header-content{
//       display: flex;
//       flex-direction: column;
//       gap: 8px;
//       padding: 15px;
//     }
//     .dd-table-dialog .header-content .view-type{
//       font-size: 1.5em;
//       font-weight: bold;
//       margin-bottom: 8px ;
//     }*/

//     /*.line__table ::ng-deep .p-datatable {
//       border-collapse: separate;
//       border-spacing: 0;
//       border: 2px solid $DDTableBorderColor;
//       border-radius: 10px 10px 0 0;
//       overflow: hidden;
//     }
    
//     .line__table ::ng-deep .p-datatable .p-datatable-thead > tr > th {
//       background: $DDTableBgColor;
//       color: $DDTableHeaderTextColor;
//       border: 2px solid $DDTableBorderColor;
//       border-width: thick;
//       padding: 5px 14px;
//       font-size: 14px;
//     }*/
    
//    /* .line__table ::ng-deep .p-datatable .p-datatable-thead > tr:first-child > th:first-child {
//       border-top-left-radius: 8px;
//     }
    
//     .line__table ::ng-deep .p-datatable .p-datatable-thead > tr:first-child > th:last-child {
//       border-top-right-radius: 8px;
//     }*/
    
//     .line__table ::ng-deep  .p-datatable-tbody > tr > td {
//       //border: 1px solid $DDTableBorderColor;
//       border: 1px solid $DDTableBorderColor;
//       border-bottom: none;
//      // border-width: 0 1px 1px 0;
//       padding: 5px 14px;
//       font-size: 12px;
//       height: 50px;
//       background: transparent;
//     }
   
    
    
    

   
    
//    :host::ng-deep .p-dialog {
//       max-width: 1000px;
//     }
//     :host ::ng-deep {
//       .dd-table-dialog {
//         .p-dialog-content {
//           padding: 0; // Reset padding to 0
    
//           .line-view-content {
//             padding: 30px; // Apply 30px padding only to line view
//           }
    
//         /*  .summary-columns {
//             padding: 15px; // Keep original padding for summary view
//           }*/
//         }
//       }
//     }

@import "../../../assets/sass/variables";

:host ::ng-deep {

  .p-datatable-scrollable-body {
    &::-webkit-scrollbar-track {
      background: $DDTableBorderColor;
    }

    &::-webkit-scrollbar-thumb {
      background: $flowerBlueColor;
    }
  }

  .p-datatable-wrapper {
    padding-bottom: 20px;

    .p-inputtext {
      font-size: 12px;
    }

    .p-column-filter-row {
      height: 20px;
    }

  }
}

:host ::ng-deep p-dropdown {
  width: 90px;
  padding: 0;
  margin: auto;
  background: none;
  border: none;
  line-height: 20px;

  &.parameter {
    width: 100%;

    .p-dropdown-label {
      font-weight: 700;

      &:hover {
        color: $flowerBlueColor;

      }
    }
  }

  .p-dropdown-panel {
    box-shadow: $boxShadow !important;
    border-radius: 10px;

    .p-dropdown-item {
      &:hover {
        background: $DDTableBgColor;
        color: $DDTableHeaderTextColor
      }

      &.p-highlight {
        background: $flowerBlueColor;
        color: #fff;
      }
    }

    .p-dropdown-items-wrapper {
      &::-webkit-scrollbar-track {
        background: $DDTableBorderColor;
      }

      &::-webkit-scrollbar-thumb {
        background: $flowerBlueColor;
      }
    }

    .p-dropdown-items {
      padding: 10px;

      .p-dropdown-item {
        border-bottom: 1px solid $DDTableBorderColor;
      }
    }
  }

  &.bordered {
    background: $DDTableBgColor;
    padding: 0 5px;
    border: 1px solid $DDTableBorderColor;
    border-radius: 20px;
  }

  .p-dropdown-trigger-icon {
    background: $flowerBlueColor;
  }

  .p-dropdown-clear-icon {
    right: -20px;
  }
}


:host ::ng-deep p-paginator {
  .p-paginator {
    background: transparent;
    justify-content: flex-end;
    margin: 0;

    .p-paginator-current {
      color: $DDTableTextLightColor;
      font-size: 10px;
    }

    .p-link {
      width: 18px;
      height: 18px !important;
      background: $flowerBlueColor;
      color: #fff;
      font-size: 12px;
      opacity: 1;
      border-radius: 50%;

      &.p-disabled {
        background: $DDTableBorderColor;
      }
    }

    .p-paginator-page {
      background: none;

      &.p-highlight {
        background: $flowerBlueColor !important;
      }

      &:hover {
        background: $flowerBlueColor !important;
      }
    }

    .p-paginator-pages {
      margin: 0;
    }
  }
}

.dashboard__table {
  overflow: visible;
  color: $DDTableTextColor;
  font-size: 14px;
  line-height: 1.5;

 /* ::ng-deep .p-datatable {
    border-collapse: separate;
    border-spacing: 0;
    //border: 1px solid $DDTableBorderColor;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
   // border: 2px solid $DDTableBorderColor;
    
  }
  ::ng-deep .p-datatable-tbody > tr > td:first-child,
  ::ng-deep .p-datatable-thead > tr > th:first-child {
  position: sticky;
  left: 0;
}*/

  table {
    border: 1px solid $DDTableBorderColor;
    border-radius: 25px;
    
  }
  

  td, th {
    border: 2px solid $DDTableBorderColor;
    background: transparent;
    text-align: center;
    
  }

  td {
    font-size: 12px;
    height: 87px;
  }

  td, th {
    position: relative;
    padding: 0 14px;
  }

   th:first-child,td:first-child {
    position: sticky;
    left: 0;
    top: 0;
    z-index: 100;
    padding: 0 10px;
   text-align: left;
   font-weight: 700;
  }


  .triangle {
    border-right: none;
    border-left: none;
    padding-right: 0;
    padding-left: 0;

    div {
      position: relative;

      &:after {
        content: " ";
        display: block;
        width: 2px;
        height: 25px;
        background: #D0D2FF;
        position: absolute;
        top: -65%;
        left: 95%;
        z-index: 1;
        transform: skew(25deg);
      }

      &:before {
        content: " ";
        display: block;
        width: 2px;
        height: 25px;
        background: #D0D2FF;
        position: absolute;
        top: 54%;
        left: 95%;
        z-index: 1;
        transform: skew(-25deg);
      }
    }

    .triangle:last-of-type {
      div {
        background: #000;
      }
    }
  }

  td:first-child {
    background: #fff;
  }

  th {
    color: $DDTableHeaderTextColor;
    background: $DDTableBgColor;
  }

  .header-filter {
    display: flex;
    flex-direction: column;
    align-items: center;

    span {
      font-size: 12px;
    }
  }

  .table-icon {
    width: 22px;
  }

  .count-field {
    padding: 5px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: $flowerBlueColor;
    border-radius: 20px;
    color: #fff;
    text-overflow: ellipsis;
  }

  .status-field {
    min-width: 80px;
    margin: 0 auto;
    border-radius: 20px;
    border: 1px solid;
    text-align: center;
  }

  .praposal {
    border-color: #7D7D7D;
    color: #7D7D7D;
    background: #00000029;
  }

  .completed {
    border-color: $lightGreenColor;
    color: $lightGreenColor;
    background: $mintColor;
  }

  .bestcase {
    border-color: $flowerBlueColor;
    color: $flowerBlueColor;
    background: $DDTableBorderColor;
  }

  .ordered {
    border-color: $DDTableHeaderTextColor;
    color: $DDTableHeaderTextColor;
    background: #10052D21;
  }

  .color-number {
    font-weight: 700;

    &.GREEN {
      color: $lightGreenColor;
    }

    &.RED {
      color: $redColor;
    }

    &.YELLOW {
      color: $orangeColor;
    }
  }
}

.light-text {
  color: $DDTableTextLightColor;
  font-size: 10px;

  &.sm {
    font-size: 10px;
  }

  &.md {
    font-size: 12px;
  }

  &.lg {
    font-size: 14px;
  }
}

.blue-text {
  color: $flowerBlueColor;
  font-size: 10px;


  &.sm {
    font-size: 10px;
  }

  &.md {
    font-size: 12px;
  }

  &.lg {
    font-size: 14px;
  }
}

.separator {
  margin: 0 5px;
  height: 11px;
  background: $DDTableTextLightColor;
  width: 1px;
}

.checkbox {
  display: flex;
  align-items: center;
  width: max-content;
  height: 30px;
  border-radius: 20px;
  padding: 5px 10px 5px 5px;
  background: $mintColor;
  border: 1px solid $lightGreenColor;
  color: $lightGreenColor;
  margin: 10px auto;

  &-date {
    height: 24px;
  }

  img {
    margin-right: 10px;
  }

  span {
    margin-left: 3px;
  }
}

.line {
  position: absolute;
  height: 2px;
  width: 100%;
  background: $lightGreenColor;

  &.red {
    background: $redColor;
  }

  &-col {
    padding: 0 !important;
    border: none !important;
    border-bottom: 2px solid #D0D2FF !important;
  }

  &-container {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 5px 0 5px 5px;
    height: 85px;
    justify-content: flex-end;

    &.first {
      padding-left: 15px;
    }

    &.last {
      padding-right: 15px;
    }
  }

  &-body {
    position: relative;
    display: flex;
    align-items: center;
  }

  &-leakage {
    text-align: left;
    align-self: flex-start;
    height: 20px;
  }

  &-date {
    align-self: flex-start;
  }

  &-amount {
    align-self: flex-start;
    font-weight: bold;
  }

  &-circle {
    position: relative;
    flex: 0 0 auto;
    width: 9px;
    height: 9px;
    left: -5px;
    background: $lightGreenColor;
    border-radius: 50%;
    z-index: 10;
    
    &.red {
      background: $redColor;

      &:after {
        position: absolute;
        content: "";
        width: 3px;
        height: 3px;
        left: 3px;
        top: 3px;
        background: #fff;
        border-radius: 50%;
      }
      
    }
  }
}


:host::ng-deep .dd-table-dialog {
  max-height: 570px;
  min-width: 300px;
  width: 100%;
 // border-radius: 8px 8px 8px 8px;

  &.line{
    max-height: 570px;
    min-width: 300px;
    width: 100%;
  }

  &.export {
    max-width: 650px;
  }

  &.calendar {
    width: auto;
  }

  .p-dialog-header {
    color: $flowerBlueColor;
    background: none;
    border-bottom: 2px solid $DDTableBorderColor;
  }

  .p-dialog-title {
    font-size: 14px;
    font-weight: 700;
  }

  .dialog-subtitle {
    margin-top: 10px;
    font-weight: 400;
  }

  .p-dialog-header-close {
    background: $flowerBlueColor;
    width: 18px;
    height: 18px !important;
    border-radius: 50%;

    &-icon {
      color: #fff;
      font-size: 10px;
    }
  }

  .p-dialog-footer {
    padding: 20px;
    box-shadow: none;
    background: none;
    border-top: 2px solid $DDTableBorderColor;
  }
}

.dd-primary-btn {
  margin: 0 10px;
  cursor: pointer;
  width: 80px;
  background: $flowerBlueColor;
  border-radius: 20px;
  color: #fff;
  font-size: 12px;

  &:hover {
    background: $DDTableHeaderTextColor;
  }


  &.filter-btn {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 75px;
    height: 22px !important;
    font-size: 10px;
  }

  &.add {
    &:hover {
      background: none;
      border: 1px solid $flowerBlueColor;
      color: $flowerBlueColor;
    }
  }

  &.remove {
    background: $grayBgColor;

    &:hover {
      background: $redColor;
    }
  }
}

.dd-secondary-btn {
  margin: 0;
  cursor: pointer;
  background: none;
  color: $DDTableTextColor;
  font-weight: 700;
}

.dd-primary-link {
  margin: 0;
  cursor: pointer;
  background: none;
  color: $flowerBlueColor;
  font-weight: 700;
}

.dd-input {
  border-radius: 10px;
  padding: 2px;
  background: $DDTableBgColor;
  border: 1px solid $DDTableBorderColor;

  &-pages {
    text-align: center;
    margin: 0 5px;
    width: 35px;
    height: 20px;

    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
}

.sparks-icon {
  position: absolute;
  top: 4px;
  right: 10px;
}
 .custom-tooltip {
  background-color: #2C2C54;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  color: white;
  position: absolute;
  display: none;
  padding: 0.50em 1.0rem;
  max-width: 12.5rem;
  pointer-events: none;
  text-decoration:underline;
  border-radius: 50px;
  transform: translateY(-7px);
}

::ng-deep .p-tooltip .p-tooltip-text {
  white-space:nowrap;
  text-align: center;
}
::ng-deep .p-overlaypanel {
  z-index: 1000 !important; /* Override the z-index */
  top: -30px !important; /* Adjust the top position */
  left: 0px !important; /* Adjust the left position */
  background-color: #2C2C54;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border-radius: 50px;
  transform: translateY(-7px);
  position: absolute;
  padding: 0.50em 1.0rem;
  max-width: 12.5rem;
  text-decoration: underline;
}
::ng-deep .p-overlaypanel .tooltip-options {
  display: flex;
  align-items: center;
  gap: 0.1rem; 
  }

::ng-deep .p-overlaypanel .tooltip-options a {
  color: #FFFFFF; /* White text color */
   text-decoration: underline !important; 
}
::ng-deep .p-overlaypanel .tooltip-options .separate {
  color: #fff; /* White color for the separator */
}
::ng-deep .checkbox-overlaypanel {
  z-index: 1000 !important; 
  left: 14px !important; 
  border-radius: 50px;
  position: absolute;
  bottom: 15px;
 
}
.header-content{
 
  padding: 15px 25px;
  font-family: sans-serif;
}

.subheader{
  //font-size: 12px;
  color: #585858;;
  font: normal normal bold 16px/12px Poppins;
  padding-bottom: 0px;
  margin-bottom: 1px;
  letter-spacing: 0px;
 
  .subheader-label {
    font: normal normal normal 15px/10px 'Poppins';
    color: #585858;
    margin-right: 5px;
  }

  .subheader-value {
    color: black; // Assuming you have this color variable defined
    margin-right: 5px;
    font: normal normal bold 14px/10px 'Poppins';
  }
}

 .header-content{
  //font-size: 16px;
  //font-weight: bold;
  padding: 5px;
  display: flex;
  flex-direction: column;
  padding-left: 20px;
  gap: 1px;
}
 .view-type {
  //font-weight: bold;
  font: normal normal bolder 16px/21px Poppins;
  letter-spacing: 0px;
  color: #5F5AF7;
  padding-bottom: 4px;
  //opacity: 1;

}
.p-table{
  width: 100%;
}
.summary-columns {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: baseline;
}
.header-text {
      flex: 0 0 auto;
      color: #7D7D7D;
      width: 135px;
      display: inline-block;
      text-align: right;
      padding-right: 5px;
      padding-left: 0px;
      font: normal normal normal 14px/21px 'Poppins';
      //background-color: #3366cc;
  //padding: 2px 5px;
  //border-radius: 3px;
 // font-weight: bold;
  //margin-right: 5px;
}
.colon{
  flex: 0 0 auto;
  padding: 0 10px;
  color: #7D7D7D;
  margin-left: 1px;
}


.data-text {
  color: #5F5AF7;
  flex: 1;
 // background-color: #f0f0f0;
  padding: 0px;
  padding-left: 5px;
  //border-radius: 3px;
  display: inline-block;
  font-size: 12px;
  width: 130px;
  font: normal normal normal 14px/21px Poppins;
}
.left-column,
.right-column {
  display: flex;
  flex-direction: column;
  width: 48%;
}


  .column {
    width: 47%;
    display: inline-flex;
    flex-wrap: nowrap;
    justify-content: space-evenly;
    align-items: center;

  }

  .row {
    //display: block-ruby;
    //justify-content: space-between;
    //margin-bottom: 10px;
    //align-items: baseline;
    display: grid;
    gap: 10px;
    grid-template-columns: auto auto auto auto;
    //flex-direction: row;
    //flex-wrap: nowrap;
   // align-content: center;
    //justify-content: space-evenly;
    align-items: center;
  }
   /* .label {
      font-size: 14px;
      color: #585858;
      font-weight: 500;
  
    }

    .value {
      font-size: 14px;
      color: #0066cc;
      font-weight: 600;
      width: 55%;
      text-align: right;
    }*/

    .line__table {
      overflow: visible;
      color: $DDTableTextColor;
      font-size: 14px;
      line-height: 1;
      //padding: 30px;

      .table-wrapper {
        border-left: 1px solid $DDTableBorderColor;
        border-right: 1px solid $DDTableBorderColor;
        border-top: none;
        border-bottom: none;
        overflow: hidden;
      }
    
      ::ng-deep .p-datatable {
        border-collapse: separate;
        border-spacing: 0;
        border: 1px solid $DDTableBorderColor;
        border-radius: 8px 8px 0 0;
        overflow: hidden;
      }
     ::ng-deep .p-datatable-wrapper{
        overflow-x: auto;
        overflow-y: hidden;
        border: none;
        
      }
      ::ng-deep .p-datatable-tbody >tr > td {
        border-bottom: none;
      }
    
    
       
     
    
      table {
        border: 1px solid $DDTableBorderColor;
       // border-radius: 8px 8px 0px 0px;


      }
     

    
      td, th {
        border: 1px solid $DDTableBorderColor;
        background: transparent;
        text-align: center;
      }
     
    
      td {
        font-size: 12px;
        height: 50px;
      }
    
      td, th {
        position: relative;
        padding: 5px 14px;
      }

     th 
     {
            color: $DDTableHeaderTextColor;
            background: $DDTableBgColor;
          }
          
      
        }
      
      :host::ng-deep p-dialog .p-dialog {
        max-width: 1000px;
        /*max-height: 570px;
        min-width: 300px;
        width: 100%; 
        min-width: 500px;
        max-width: 1000px;
        background: #fff;
        min-height: 150px;
        box-shadow: 0 10px 20px rgb(0 0 0 / 19%), 0 6px 6px rgb(0 0 0 / 23%);
        border-radius: 3px;*/
      }
      :host::ng-deep .p-dialog {
        .dd-table-dialog {
          .line-view-content {
            padding: 30px;
          }
        }
      }

      /*:host::ng-deep .p-dialog {
        .p-dialog-content {
          padding: 30px; // Add 30px padding to the dialog content
        }
      }*/
  //  }
 
   /* .dd-table-dialog .header-content{
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 15px;
    }
    .dd-table-dialog .header-content .view-type{
      font-size: 1.5em;
      font-weight: bold;
      margin-bottom: 8px ;
    }*/

    /*.line__table ::ng-deep .p-datatable {
      border-collapse: separate;
      border-spacing: 0;
      border: 2px solid $DDTableBorderColor;
      border-radius: 10px 10px 0 0;
      overflow: hidden;
    }
    
    .line__table ::ng-deep .p-datatable .p-datatable-thead > tr > th {
      background: $DDTableBgColor;
      color: $DDTableHeaderTextColor;
      border: 2px solid $DDTableBorderColor;
      border-width: thick;
      padding: 5px 14px;
      font-size: 14px;
    }*/
    
   /* .line__table ::ng-deep .p-datatable .p-datatable-thead > tr:first-child > th:first-child {
      border-top-left-radius: 8px;
    }
    
    .line__table ::ng-deep .p-datatable .p-datatable-thead > tr:first-child > th:last-child {
      border-top-right-radius: 8px;
    }*/
    
    .line__table ::ng-deep  .p-datatable-tbody > tr > td {
      //border: 1px solid $DDTableBorderColor;
      border: 1px solid $DDTableBorderColor;
      border-bottom: none;
     // border-width: 0 1px 1px 0;
      padding: 5px 14px;
      font-size: 12px;
      height: 50px;
      background: transparent;
    }
   
    
    
    

   
    
   :host::ng-deep .p-dialog {
      max-width: 1000px;
    }
:host ::ng-deep {
  .p-paginator {
    .p-paginator-pages {
      .p-paginator-element {
        min-width: 0px !important;
      }
    }
  }

}


// .guidance-dot {
//   display: inline-flex;
//   align-items: center;
// }

.red-dot { color: red; }
.green-dot { color: green; }
.yellow-dot { color: #ffbf00; }


 
    