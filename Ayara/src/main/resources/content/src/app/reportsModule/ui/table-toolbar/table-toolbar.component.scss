@import "../../../../assets/sass/variables";

::ng-deep .toolbar-header {
  font-size: 18px;
  font-weight: 700;
  color: $DDTableHeaderTextColor;

  a {
    color: $flowerBlueColor;
  }
}

h3.toolbar-header {
  margin: 0 10px 0 0;
}

.toolbar-container {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;;
  margin-bottom: 30px;
}

.search__container {
  width: 200px;

  em {
    color: $flowerBlueColor;
  }
}

:host::ng-deep {

  p-toolbar {

    .p-toolbar {
      background: #fff;
      padding: 15px 30px;
    }
  }

  p-button {
    .p-button {
      background: none;
      gap: 5px;
      color: $DDTableHeaderTextColor;

      &:hover {
        color: $flowerBlueColor;
      }
      &-icon {
        color: $flowerBlueColor;
        font-weight: 700;
      }
    }
  }
}
