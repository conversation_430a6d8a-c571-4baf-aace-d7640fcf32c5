import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
//import {Opportunity, StageAllocationForecast, OpportunityStageDetails, OpportunityStagePayload} from '../models/opportunity';
import {map , catchError} from 'rxjs/operators';
import {  Opportunity, OpportunityStagePayload, OpportunityStage, Allocation, Forecast, PageResponse } from '../models/opportunity';
import { Observable , of } from 'rxjs';
import {DatePipe} from '@angular/common';
import * as appSettings from '../../appsettings';
import { forkJoin } from 'rxjs';
import { BehaviorSubject } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class OpportunityService {
  
  selectedOpportunitySubject = new BehaviorSubject<Opportunity| null >(null);
  selectedOpportunity$ = this.selectedOpportunitySubject.asObservable();

  setSelectedOpportunity(opportunity: Opportunity) {
    console.log('Service - Setting data:', opportunity); 
      this.selectedOpportunitySubject.next(opportunity);  this.selectedOpportunitySubject.next(opportunity);
      sessionStorage.setItem('selectedOpportunity', JSON.stringify(opportunity));
  }
  
  getSelectedOpportunity(): Opportunity | null {
    // Try to get from BehaviorSubject first
    let opportunity = this.selectedOpportunitySubject.getValue();
    
    // If not in BehaviorSubject, try sessionStorage
    if (!opportunity) {
        const stored = sessionStorage.getItem('selectedOpportunity');
        if (stored) {
            opportunity = JSON.parse(stored);
            this.selectedOpportunitySubject.next(opportunity);
        }
    }
    
    return opportunity;
}
  
    constructor(private http: HttpClient, private datePipe: DatePipe) {
    }


   
    getOpportunities(page: number = 0, size: number = 10, searchTerm?: string): Observable<PageResponse<Opportunity>> {
     let  params = new HttpParams()
          .set('page', page.toString())
          .set('size', size.toString());
         
         if (searchTerm && searchTerm.trim() !== '') {
            params = params.set('searchTerm', searchTerm.trim());
        }

        console.log('API params:', params.toString()); // Debug log

      return this.http.get<PageResponse<Opportunity>>(
          `${appSettings.apiUrl}/opportunities`,
          { params }
      );
  }


  


    // getOpportunities(search: string = ''): Observable<Opportunity[]> {
    //   const params = search ? { searchTerm: search } : {};
      
    //   return this.http.get<Opportunity[]>(`${appSettings.apiUrl}/opportunities`, { params }).pipe(
    //     map(opportunities => opportunities.map(opp => ({
    //       ...opp,
    //       opportunityDate: this.datePipe.transform(opp.opportunityDate, 'MM/dd/yyyy')
    //     })))
    //   );
    // }

    
  
  getStages(opportunityNumber: string): Observable<OpportunityStage[]> {
    return this.http
        .get<OpportunityStagePayload[]>(`${appSettings.apiUrl}/opportunityNumber/${opportunityNumber}`)
        .pipe(
            map((data) => {
                if (!data.length) return [];
                
                return data[0].stages.map(stage => ({
                  opportunityStageId: stage.opportunityStageId || 0,
                  opportunityDate: this.datePipe.transform(data[0].opportunityDate, 'MM/dd/yyyy') || '',
                  opportunityNumber: data[0].opportunityNumber || '',
                  customerName: '', 
                  opportunityType: stage.opportunityType || '',
                  opportunityStage: stage.opportunityStage || '',
                  winPercent: stage.winPercent || '',
                  id: data[0].id || '',
                  stageName: stage.stageName || '',
                  stageDate: this.datePipe.transform(stage.stageDate, 'MM/dd/yyyy') || '',
                  closeDate: this.datePipe.transform(stage.closeDate, 'MM/dd/yyyy') || '',
                  winPercentage: stage.winPercent || '',
                  opportunityAmount: stage.opportunityAmount || 0,
                  allocations: [],
                  forecast: []
              }));
          }),
          catchError(() => of([]))
      );
    }
            
           




  getStageDetails(opportunityNumber: string, stageId: number): Observable<{allocations: Allocation[], forecast: Forecast[]}> {
    return forkJoin({
      // Make both API calls in parallel
      allocations: this.getAllocations(opportunityNumber, stageId),
      forecast: this.getForecasting(opportunityNumber, stageId)
    }).pipe(
      catchError(error => {
       
        // Return empty arrays if there's an error
        return of({ allocations: [], forecast: [] });
      })
    );
  }
  

  
  getAllocations(opportunityNumber: string, stageId: number): Observable<Allocation[]> {
    return this.http
      .get<[{ allocations: Allocation[] }]>(
        `${appSettings.apiUrl}/opportunityNumber/${opportunityNumber}/stage/${stageId}/allocations`
      )
      .pipe(
        map(response => {
          const allocations = response[0].allocations || [];
          return allocations.map(allocation => ({
            ...allocation,
            stageDate: this.datePipe.transform(allocation.stageDate, ' MM/dd/yyyy'),
            closeDate: this.datePipe.transform(allocation.closeDate, ' MM/dd/yyyy')
          }));
        }),
        catchError(() => of([]))
      );
 
  }

  getForecasting(opportunityNumber: string, stageId: number): Observable<Forecast[]> {
    return this.http
      .get<[{ forecast: Forecast[] }]>(
        `${appSettings.apiUrl}/opportunityNumber/${opportunityNumber}/stage/${stageId}/forecasting`
      )
      .pipe(
        map(response => response[0].forecast || []),
        catchError(() => of([]))
      );
  }











  
  }

    
    
    

        /*getStages(opportunityNumber: string) {
          return this.http.get<OpportunityStagePayload[]>(`${appSettings.apiUrl}/opportunityNumber/${opportunityNumber}`).pipe(
              map((data) => data.map((opp): OpportunityStage => {
                  // Log the data to see what's coming from API
                  console.log('API Response:', opp);
                  
                  return {
                      opportunityDate: this.datePipe.transform(opp.opportunityDate, 'dd-MM-yyyy'),
                      opportunityNumber: opp.opportunityNumber,
                      win: opp.win,
                      opportunityAmount: opp.opportunityAmount,
                      id: opp.id,
                      stages: opp.stages  // Keep all stages instead of spreading just the first one
                  }
              }))
          );
      }*/
        /*getStages(): Observable<OpportunityStage[]> {
            return this.http.get<OpportunityStagePayload[]>('https://67079045a0e04071d22adde3.mockapi.io/stages').pipe(
              map((data) => data.map((opp): OpportunityStage => ({
                id: opp.id,
                opportunityDate: this.datePipe.transform(opp.opportunityDate, 'dd-MM-yyyy'),
                opportunityNumber: opp.opportunityNumber,
                win: opp.win,
                opportunityAmount: opp.opportunityAmount,
                stageName: opp.stages[0].stageName,
                stageDate: opp.stages[0].stageDate,
                winPercentage: opp.stages[0].winPercentage,
                allocations: opp.stages[0].allocations,
                forecasting: opp.stages[0].forecasting
              })))
            );
          }
        
          getStagesForOpportunity(opportunityNumber: string): Observable<OpportunityStage[]> {
            return this.getStages().pipe(
              map(stages => stages.filter(stage => stage.opportunityNumber === opportunityNumber))
            );
          }*/


