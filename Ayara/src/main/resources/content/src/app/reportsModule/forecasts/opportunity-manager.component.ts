import {Component, Input, OnInit, ChangeDetectorRef} from '@angular/core';
import {Column} from '../../shared/models/column.interface';
import { OpportunityService } from '../data-access/opportunity.service';
import { finalize, map, tap} from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';
//import { from } from 'rxjs';
import  {Router} from '@angular/router';
import { Opportunity } from '../models/opportunity';
import { BehaviorSubject, Observable } from 'rxjs';
import { PageResponse } from '../models/opportunity';
import { LazyLoadEvent } from 'primeng/api';

@Component({
    selector: 'app-opportunity-manager',
    templateUrl: './opportunity-manager.component.html',
    styleUrls: ['./opportunity-manager.component.css']
})
export class OpportunityManagerComponent implements OnInit {

    selectedOpportunity: any = null;
   // opportunities: PageResponse<Opportunity>;
   opportunitiesSubject = new BehaviorSubject<PageResponse<Opportunity>>({
    content: [],
    pageable: {
        sort: { sorted: false, unsorted: true, empty: true },
        pageNumber: 0,
        pageSize: 10,
        offset: 0,
        unpaged: false,
        paged: true
    },
    totalElements: 0,
    totalPages: 0,
    last: true,
    size: 10,
    number: 0,
    sort: { sorted: false, unsorted: true, empty: true },
    first: true,
    numberOfElements: 0,
    empty: true
});
opportunities$ = this.opportunitiesSubject.asObservable()
    
    tableColumns: Column[] = [
        {
            header: 'Opportunity Number',
            field: 'opportunityNumber',
            alignment: 'center',
            type: 'link',
            linkField:'opportunityNumber',
            showField: true,
        },
        {
            header: 'Opportunity Name',
            field: 'opportunityName',
            alignment: 'center',
            type: 'name',
            showField: true
        },
        {
            header: 'Opportunity Date',
            field: 'opportunityDate',
            alignment: 'center',
            type: 'date',
            showField: true
        },
        {
            header: 'Customer Name',
            field: 'customerName',
            alignment: 'center',
            type: 'name',
            showField: true
        },
        /*{
            header: 'Stages',
            field: 'opportunityNumber',
            alignment: 'center',
            type: 'link',
            linkField:'opportunityNumber',
            showField: true,
        },*/
        // {
        //     header: 'Win (%)',
        //     field: 'winPercent',
        //     alignment: 'center',
        //     type: 'number',
        //     showField: true
        // },
        {
            header: 'Opportunity Amount',
            field: 'opportunityAmount',
            alignment: 'center',
            //type: 'text',
            showField: true,
            type:'amount',
        },
    ];
    searchTerm: string = '';
    isLoading = false;
    //selectedOpportunity :  string;
   

    constructor(private opportunityService: OpportunityService , private route :ActivatedRoute , private router : Router,  private cdr: ChangeDetectorRef) {
    }

    ngOnInit(): void {
       this.loadOpportunities();
    }
   
    

onRowSelect(opportunity: Opportunity) {
    if(opportunity) {
        // Store the full opportunity object including all details
        const opportunityDetails = {
            ...opportunity,
            // Add any additional fields if needed
            pageInfo: {
                currentPage: this.opportunitiesSubject.getValue().pageable.pageNumber,
                pageSize: this.opportunitiesSubject.getValue().pageable.pageSize
            }
        };
        
        // Store in service
        this.opportunityService.setSelectedOpportunity(opportunityDetails);
        
        // Navigate with complete state
        this.router.navigate(['opportunity', opportunity.opportunityNumber], {
            state: opportunityDetails,
            queryParams: {
                page: this.opportunitiesSubject.getValue().pageable.pageNumber,
                size: this.opportunitiesSubject.getValue().pageable.pageSize,
                search: this.searchTerm
            }
        });
    }
}

  
  onTableRefresh() {
    this.searchTerm = '';
    this.loadOpportunities();
  }
//   loadOpportunities(event?: LazyLoadEvent) {
//     this.isLoading = true;
//     const page = event ? Math.floor(event.first / event.rows) : 0;
//         const size = event?.rows || 10;

//         this.opportunityService.getOpportunities(page, size, this.searchTerm)
//             .pipe(finalize(() => {
//                 this.isLoading = false;
//                 this.cdr.detectChanges();
//             }))
//             .subscribe({
//                 next: (response) => {
//                     this.opportunitiesSubject.next(response);
//                 },
//                 error: (error) => {
//                     console.error('Error loading opportunities:', error);
//                     // Keep the previous state on error
//                     this.opportunitiesSubject.next(this.opportunitiesSubject.getValue());
//                 }
//             });
//     }


loadOpportunities(event?: LazyLoadEvent) {
    this.isLoading = true;
    const page = event ? Math.floor(event.first / event.rows) : 0;
    const size = event?.rows || 10;

    this.opportunityService.getOpportunities(page, size, this.searchTerm)
        .pipe(
            tap(response => {
                // Store current page state
                const currentState = {
                    page,
                    size,
                    searchTerm: this.searchTerm
                };
                sessionStorage.setItem('opportunityManagerState', JSON.stringify(currentState));
            }),
            finalize(() => {
                this.isLoading = false;
                this.cdr.detectChanges();
            })
        )
        .subscribe({
            next: (response) => {
                this.opportunitiesSubject.next(response);
            },
            error: (error) => {
                console.error('Error loading opportunities:', error);
                this.opportunitiesSubject.next(this.opportunitiesSubject.getValue());
            }
        });
}
    onSearch(searchTerm: string) {
        console.log('searchTerm:', searchTerm);
        this.searchTerm = searchTerm;
        this.loadOpportunities(); // Reset to first page when searching
    }
}

     
  
    
