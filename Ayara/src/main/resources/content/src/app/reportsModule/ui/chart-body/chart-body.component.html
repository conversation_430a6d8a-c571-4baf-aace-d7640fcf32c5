<ng-container *ngIf="chartData && chartView?.datasets">
<ng-container [ngSwitch]="chartData.type">

    <ng-container *ngSwitchCase="'bar'">
        <canvas
                baseChart
                [datasets]="chartView.datasets"
                [labels]="chartView.labels"
                [options]="chartOptions"
                [chartType]="'bar'">
        </canvas>
    </ng-container>
    
    <ng-container *ngSwitchCase="'stacked-bar'">
        <canvas
                baseChart
                [chartType]="'bar'"
                [datasets]="chartView.datasets"
                [labels]="chartView.labels"
                [options]="chartOptions">
        </canvas>
    </ng-container>

    <ng-container *ngSwitchCase="'stacked-bar-percent'">
        <canvas
                baseChart
                [chartType]="'bar'"
                [datasets]="chartView.datasets"
                [labels]="chartView.labels"
                [options]="chartOptions">
        </canvas>
    </ng-container>

    <ng-container *ngSwitchCase="'pie'">
        <canvas
                baseChart
                [chartType]="'doughnut'"
                [datasets]="chartView.datasets"
                [labels]="chartView.labels"
                [options]="chartOptions">
        </canvas>
    </ng-container>

    <ng-container *ngSwitchCase="'pricing'">
        <canvas
                baseChart
                [datasets]="chartView.datasets"
                [labels]="chartView.labels"
                [options]="chartOptions"
                [chartType]="'bar'">
        </canvas>
    </ng-container>

</ng-container>
</ng-container>


