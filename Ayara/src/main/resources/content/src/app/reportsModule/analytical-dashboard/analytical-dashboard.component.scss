@import "../../../assets/sass/variables";

.card-wrapper {
  background: #fff;
}

.container-fluid {
  background: #fff;
}


.control-panel {
  box-shadow: 0 3px 6px #00000029;
  background: #fff;
  padding: 0 30px;

  &__container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 15px 0;
  }

  &__parameter {
    display: flex;
    align-items: center;

    span {
      margin-right: 5px;
      flex: 0 0 auto;
    }
  }

  &__controls {
    display: flex;
    flex-wrap: wrap;

    .controls-item {
      cursor: pointer;
      display: flex;
      align-items: center;
      margin: 0 10px;

      &:hover {
        span {
          color: $flowerBlueColor;
        }
      }

      span {
        color: $DDTableTextColor;
        font-size: 11px;
        margin-left: 5px;
        font-weight: 700;
      }
    }
  }
}

:host ::ng-deep p-dropdown {
  width: 90px;
  padding: 0;
  margin: auto;
  background: none;
  border: none;
  line-height: 20px;

  &.parameter {
    width: 100%;

    .p-dropdown-label {
      font-weight: 700;

      &:hover {
        color: $flowerBlueColor;

      }
    }
  }

  .p-dropdown-panel {
    border-radius: 10px;

    &.p-component {
      box-shadow: $boxShadow !important;

    }
    .p-dropdown-item {
      &:hover {
        background: $DDTableBgColor;
        color: $DDTableHeaderTextColor
      }

      &.p-highlight {
        background: $flowerBlueColor;
        color: #fff;
      }
    }

    .p-dropdown-items-wrapper {
      &::-webkit-scrollbar-track {
        background: $DDTableBorderColor;
      }

      &::-webkit-scrollbar-thumb {
        background: $flowerBlueColor;
      }
    }

    .p-dropdown-items {
      padding: 10px;

      .p-dropdown-item {
        border-bottom: 1px solid $DDTableBorderColor;
      }
    }
  }

  &.bordered {
    background: $DDTableBgColor;
    padding: 0 8px;
    border: 1px solid $DDTableBorderColor;
    border-radius: 20px;
  }

  .p-dropdown-trigger-icon {
    background: $flowerBlueColor;
  }

  .p-dropdown-clear-icon {
    right: -20px;
  }
}


.dashboard-container {
  padding: 30px;
}


.light-text {
  color: $DDTableTextLightColor;

  &.sm {
    font-size: 10px;
  }

  &.md {
    font-size: 12px;
  }

  &.lg {
    font-size: 14px;
  }
}

.blue-text {
  color: $flowerBlueColor;


  &.sm {
    font-size: 10px;
  }

  &.md {
    font-size: 12px;
  }

  &.lg {
    font-size: 14px;
  }
}


.separator {
  margin: 0 5px;
  height: 11px;
  background: $DDTableTextLightColor;
  width: 1px;
}

.panel-separator {
  width: 1px;
  background: $DDTableBorderColor;
  margin: 0 20px;
}

:host::ng-deep .dd-table-dialog {
  max-height: 570px;
  min-width: 300px;
  width: 100%;

  &.export {
    max-width: 650px;
  }

  &.calendar {
    width: auto;
  }

  .p-dialog-header {
    color: $flowerBlueColor;
    background: none;
    border-bottom: 2px solid $DDTableBorderColor;
  }

  .p-dialog-title {
    font-size: 14px;
    font-weight: 700;
  }

  .dialog-subtitle {
    margin-top: 10px;
    font-weight: 400;
  }

  .p-dialog-header-close {
    background: $flowerBlueColor;
    width: 18px;
    height: 18px !important;
    border-radius: 50%;

    &-icon {
      color: #fff;
      font-size: 10px;
    }
  }

  .p-dialog-footer {
    padding: 20px;
    box-shadow: none;
    background: none;
    border-top: 2px solid $DDTableBorderColor;
  }
}

.dd-primary-btn {
  margin: 0 10px;
  cursor: pointer;
  width: 80px;
  background: $flowerBlueColor;
  border-radius: 20px;
  color: #fff;
  font-size: 12px;

  &:hover {
    background: $DDTableHeaderTextColor;
  }


  &.filter-btn {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 75px;
    height: 22px !important;
    font-size: 10px;
  }

  &.add {
    &:hover {
      background: none;
      border: 1px solid $flowerBlueColor;
      color: $flowerBlueColor;
    }
  }

  &.remove {
    background: $grayBgColor;

    &:hover {
      background: $redColor;
    }
  }
}

.dd-secondary-btn {
  margin: 0;
  cursor: pointer;
  background: none;
  color: $DDTableTextColor;
  font-weight: 700;
}

.dd-primary-link {
  margin: 0;
  cursor: pointer;
  background: none;
  color: $flowerBlueColor;
  font-weight: 700;
}

.dd-input {
  border-radius: 10px;
  padding: 2px;
  background: $DDTableBgColor;
  border: 1px solid $DDTableBorderColor;

  &-pages {
    text-align: center;
    margin: 0 5px;
    width: 35px;
    height: 20px;

    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
}

.filter-container {
  margin: 0;
}

.filter-items {
  max-height: 300px;
  overflow: auto;
  margin: 20px 0;

  &::-webkit-scrollbar-track {
    background: $DDTableBorderColor;
  }

  &::-webkit-scrollbar-thumb {
    background: $flowerBlueColor;
  }
}

.filter-item {
  input {
    width: auto;
  }
}

.filter-checkbox {
  accent-color: $flowerBlueColor;
}

:host ::ng-deep p-calendar {
  .p-highlight {
    background: $flowerBlueColor;
    color: #fff;
  }

  .p-datepicker-calendar {
    thead tr {
      background: $DDTableBgColor;
    }

    th span {
      color: $DDTableHeaderTextColor !important;
    }

    td {
      text-align: center;
    }
  }

  .p-datepicker-title {
    button {
      color: $DDTableHeaderTextColor !important;
    }
  }

  .p-datepicker-next, .p-datepicker-prev {
    span {
      color: $flowerBlueColor !important;
    }
  }

  .p-datepicker-today {
    span {
      font-weight: normal;
      border-color: $flowerBlueColor !important;
      color: $flowerBlueColor !important;

      &.p-highlight {
        background: $flowerBlueColor;
        color: $DDTableHeaderTextColor !important;
        font-weight: bold;
        border: none;
        border-radius: 50%;
      }
    }
  }
}
