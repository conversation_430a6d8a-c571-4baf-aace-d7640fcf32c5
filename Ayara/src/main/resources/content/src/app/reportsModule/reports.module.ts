import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {pdfComponent} from '../reports/analyticalReports/report/PdfComponent';
import {ReportsService} from '../reports/reportsservice';
import {RmanLookupsVService} from '../rmanLookupsV/rmanLookupsVservice';
import {NewSharedModule} from '../shared/shared.module';
import {ChartsModule} from 'ng2-charts';
import {GraphSharedModule} from '../shared/graphShared.module';
import {DashboardTableComponent} from './dashboard-table/dashboard-table.component';
import {TableModule} from 'primeng/table';
import {DialogModule} from 'primeng/dialog';
import {CalendarModule} from 'primeng/calendar';
import {ListboxModule} from 'primeng/listbox';
import {RmanLookupTypesService} from '../rmanLookupTypes/rmanLookupTypesservice';
import {AnalyticalDashboardComponent} from './analytical-dashboard/analytical-dashboard.component';
import {DashboardChartsComponent} from './dashboard-charts/dashboard-charts.component';
import {TotalItemComponent} from './ui/total-item/total-item.component';
import {DashboardFilterModalComponent} from './ui/dashboard-filter-modal/dashboard-filter-modal.component';
import {ChartItemComponent} from './ui/chart-item/chart-item.component';
import {ChartTitlePipe} from './pipes/chart-title.pipe';
import {ChartBodyComponent} from './ui/chart-body/chart-body.component';
import {AngularSplitModule} from 'angular-split';
import {OpportunityManagerComponent} from './forecasts/opportunity-manager.component';
import {DynamicTableComponent} from './ui/dynamic-table/dynamic-table.component';
import {OpportunityDetailsComponent} from './forecast-details/opportunity-details.component';
import {TableToolbarComponent} from './ui/table-toolbar/table-toolbar.component';
import {ToolbarModule} from 'primeng/toolbar';
import {OverlayPanelModule} from 'primeng/overlaypanel';

const routes: Routes = [
    {
        path: 'operational',
        loadChildren: () => import('../reports/operationalReports/operationalReports.module').then(m => m.OperationalReportsModule)
    },
    {
        path: 'reconciliation',
        loadChildren: () => import('../reports/reconciliationReports/reconciliationReports.module').then(m => m.ReconciliationReportsModule)
    },
    {
        path: 'analytical',
        loadChildren: () => import('../reports/analyticalReports/dashboard/dashboard.module').then(m => m.DashboardModule)
    },
    {path: 'pdfDownload', component: pdfComponent},
    {path: 'analytical-dashboard', component: AnalyticalDashboardComponent},
    {
        path: 'opportunity-manager', component: OpportunityManagerComponent
    },
    { 
        path: 'opportunity-details', component: OpportunityDetailsComponent 
    
    },
    {
        path: 'opportunity-manager/:opportunityNumber', component: OpportunityDetailsComponent
    },
    {path: '', redirectTo: 'rmanArrangementsAllV', pathMatch: 'full'}
];

@NgModule({
    imports: [NewSharedModule, GraphSharedModule, ChartsModule, RouterModule.forChild(routes), TableModule, DialogModule, CalendarModule, ListboxModule, ChartsModule, AngularSplitModule, ToolbarModule, OverlayPanelModule],
    declarations: [DashboardTableComponent, AnalyticalDashboardComponent, DashboardChartsComponent, TotalItemComponent, DashboardFilterModalComponent, ChartItemComponent, ChartTitlePipe,
        ChartBodyComponent, OpportunityManagerComponent, DynamicTableComponent, OpportunityDetailsComponent, TableToolbarComponent],
    providers: [RmanLookupsVService, ReportsService, RmanLookupTypesService]

})

export class reportsModule {
}
