@import "../../../../assets/sass/variables";

.table-container {
  padding: 0 30px;
  background: #fff;
  overflow: auto;
}

.table-dashboard {
  background: #fff;
}

.dashboard__table {
  overflow: auto;
  color: $DDTableTextColor;
  font-size: 14px;
  line-height: 1.5;

  tr {
    background: #fff;
    cursor: default;
  }

  table {
    border: 1px solid $DDTableBorderColor;
  }

  td, th {
    border: 2px solid $DDTableBorderColor;
    background: transparent;
    text-align: center;
  }

  td {
    font-size: 12px;
    height: 50px;
  }

  td, th {
    position: relative;
    padding: 10px 14px;
  }

  //th:first-child, td:first-child {
  //  position: sticky;
  //  left: 0;
  //  top: 0;
  //  z-index: 100;
  //  padding: 0 10px;
  //  text-align: left;
  //  font-weight: 700;
  //}


  .triangle {
    border-right: none;
    border-left: none;
    padding-right: 0;
    padding-left: 0;

    div {
      position: relative;

      &:after {
        content: " ";
        display: block;
        width: 2px;
        height: 25px;
        background: #D0D2FF;
        position: absolute;
        top: -65%;
        left: 95%;
        z-index: 1;
        transform: skew(25deg);
      }

      &:before {
        content: " ";
        display: block;
        width: 2px;
        height: 25px;
        background: #D0D2FF;
        position: absolute;
        top: 54%;
        left: 95%;
        z-index: 1;
        transform: skew(-25deg);
      }
    }

    .triangle:last-of-type {
      div {
        background: #000;
      }
    }
  }

  //td:first-child {
  //  background: #fff;
  //}

  th {
    color: $DDTableHeaderTextColor;
    background: $DDTableBgColor;
  }

  .header-filter {
    display: flex;
    flex-direction: column;
    align-items: center;

    span {
      font-size: 12px;
    }
  }

  .table-icon {
    width: 22px;
  }

  .count-field {
    padding: 5px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: $flowerBlueColor;
    border-radius: 20px;
    color: #fff;
    text-overflow: ellipsis;
  }

  .status-field {
    min-width: 80px;
    margin: 0 auto;
    border-radius: 20px;
    border: 1px solid;
    text-align: center;
  }

  .praposal {
    border-color: #7D7D7D;
    color: #7D7D7D;
    background: #00000029;
  }

  .completed {
    border-color: $lightGreenColor;
    color: $lightGreenColor;
    background: $mintColor;
  }

  .bestcase {
    border-color: $flowerBlueColor;
    color: $flowerBlueColor;
    background: $DDTableBorderColor;
  }

  .ordered {
    border-color: $DDTableHeaderTextColor;
    color: $DDTableHeaderTextColor;
    background: #10052D21;
  }

  .color-number {
    font-weight: 700;

    &.GREEN {
      color: $lightGreenColor;
    }

    &.RED {
      color: $redColor;
    }

    &.YELLOW {
      color: $orangeColor;
    }
  }
}

:host ::ng-deep p-paginator {
  .p-paginator {
    background: transparent;
    justify-content: flex-end;
    margin: 0;

    .p-paginator-current {
      color: $DDTableTextLightColor;
      font-size: 10px;
    }
   

    .p-link {
      width: 18px;
      height: 18px !important;
      background: $flowerBlueColor;
      color: #fff;
      font-size: 12px;
      opacity: 1;
      border-radius: 50%;

      &.p-disabled {
        background: $DDTableBorderColor;
      }
    }

    .p-paginator-page {
      background: none;

      &.p-highlight {
        background: $flowerBlueColor !important;
      }

      &:hover {
        background: $flowerBlueColor !important;
      }
    }

    .p-paginator-pages {
      margin: 0;
    }
  }

}
a {
  text-decoration: underline !important;
}
:host ::ng-deep {
  .p-paginator {
    .p-paginator-pages {
      .p-paginator-element {
        min-width: 0px !important;
      }
    }
  }
}


.red-dot { color: red; }
.green-dot { color: green; }
.yellow-dot { color: #ffbf00; }