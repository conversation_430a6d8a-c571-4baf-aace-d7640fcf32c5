 
export interface ContractDetails {
    header: {
      dealArrangementId: string;
      rcNumber: string;
      contractAmount: number;
      contractDate: string;
      customerName: string;
      region: string;
      salesRep: string;
      bookings: number;
      delivered: number;
      billed: number;
      revenueRecognized: number;
      revenueGuidance: string;
      marginGuidance: string;
      discountGuidance: string;
      dealGuidance: string;
      revenueContractValue: number;
    };
    lines: Array<{
      lineNo: string;
      productName: string;
      lineAmount: number;
      allocationAmount: number;
      netPriceDiscount: number;
      grossMarginPercent: number;
      allocationPercent: number;
      revenueGuidance: String;
      discountGuidance: String;
      marginGuidance:String;
      revenueRecognition: number;
      billed: number;
      delivered: number;
      dealGuidance: String;
      parentLineNumber: string;
      
    }>;
  }
 
  
export interface InvoiceDetails {
  header: {

    invoiceValue: number;

    currency: string;

    invoiceDate: number;

    billToLocation: string;

    pendingBills: number;

    numberOfInvoices: number;

    customerName: string;

  };

  lines: Array<{
    invoiceNumber: string;
    invoiceLineNumber: string;
    invoiceDate: number;
    invoiceValue: number;
    productName: string;
  }>;
}
  export interface QuoteDetails {
    header: {
      quoteName: string;
      quoteNumber: string;
      quoteValue: number;
      quoteDate: string;
      customerName: string;
      region: string;
      currency: string;
      contractNumber: string;
      salesRep: string;
      winPercent: number;
      //guidance: string;
      dealGuidance: number;
      revenueGuidance: number;
      marginGuidance: number;
      discountGuidance: number;
    };
    
    LineForecastDetails: Array<{ [key: string]: any }>;
  }
  
 
  
  export interface OrderDetails {
    header: {
      orderNumber: string;
      quoteNumber: string;  
      orderName: string;
      orderedDate: number;  
      orderAmount: number;
      currency: string;
      contractNumber: number;  // Changed to number based on response
    //  shipToLocation: string | null;  // Added null possibility
      revenueBeg: string;  // Changed to string based on response
      revenueCurrent: string;  // Changed to string based on response
      revenueEnd: string;  // Changed to string based on response
      delAmount: number;
      customerName: string;
    };
    lines: Array<{
      orderlineNo: string;
      quoteNumber: string;  // Added this field
      product: string;
      delAmount: number;
      bookingAmount: number;
      allocationAmount: number;
      revenueBeg: string;  // Changed to string based on response
      revenueCurrent: string;  // Changed to string based on response
      unAmortizedAmt: string;  // Changed to string based on response
      parentLineNumber: number;  // Added this field
      dealLineNumber: string;  // Added this field
    }>;
  }

   
// export interface OrderDetails {
//   header: {
//     orderNumber: string;
//     orderName: string;
//     orderedDate: string;
//     orderAmount: number;
//     currency: string;
//     contractNumber: string;
//     shipToLocation: string;
//     revenueBeg: number;
//     revenueCurrent: number;
//     delAmount: number;
//     revenueEnd: number;
//     customerName: string;
//   };
//   lines: Array<{
//     lineNo: string;
//     product: string;
//     delAmount: number;
//     bookingAmount: number;
//     allocationAmount: number;
//     // startDate: string;
//     // endDate: string;
//     revenueBeg: number;
//     revenueCurrent: number;
//     unAmortizedAmt: number;
//   }>;
// }
// export interface OrderHeader {
//   header: {
//     orderNumber: string;
//     orderName: string;
//     orderedDate: string;
//     orderAmount: number;
//     currency: string;
//     contractNumber: string;
//     shipToLocation: string;
//     revenueBeg: number;
//     revenueCurrent: number;
//     delAmount: number;
//     revenueEnd: number;
//     customerName: string;
//   };
// }

// export interface OrderLine {
//   lineNo: string;
//   product: string;
//   delAmount: number;
//   bookingAmount: number;
//   allocationAmount: number;
//   revenueBeg: number;
//   revenueCurrent: number;
//   unAmortizedAmt: number;
// }

// export interface OrderDetails {
//   header: OrderHeader;
//   lines: OrderLine[];
// }
