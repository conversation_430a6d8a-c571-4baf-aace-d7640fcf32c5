import {Component, OnInit} from '@angular/core';
import {TotalItem} from '../models/total-data';
import {BarData, ChartItemData, PeriodParameter, PieData, StackedBarData, StackedBarGMPMCMPData,PricingAnalytics} from '../models/chart-data';
import {Observable, of} from 'rxjs';
import {AnalyticalDashboardService} from '../data-access/analytical-dashboard.service';
import {FilterConfigService} from '../data-access/filter-config.service';
import {catchError, map, tap} from 'rxjs/operators';
import { data } from 'jquery';
//import { CHART_DATA } from './chart_data';

@Component({
    selector: 'app-dashboard-charts',
    templateUrl: './dashboard-charts.component.html',
    styleUrls: ['./dashboard-charts.component.scss']
})
export class DashboardChartsComponent implements OnInit {

    totalData$: Observable<TotalItem[]>;

    bookingsValueBySKU$: Observable<ChartItemData>;
    revenueByRegion$: Observable<ChartItemData>;
    revenueByAccount$: Observable<ChartItemData>;
    revenueBySalesRep$: Observable<ChartItemData>;
    revenueByQuarter$: Observable<ChartItemData>;
    invoiceByAccount$: Observable<ChartItemData>;
    dealsByDiscountGuidance$: Observable<ChartItemData>;
    dealsByRevenueGuidance$: Observable<ChartItemData>;
    dealsByMarginGuidance$: Observable<ChartItemData>;
    bookingValue$: Observable<ChartItemData>;
    bookingVsDelValue$: Observable<ChartItemData>;
    invoiceValue$: Observable<ChartItemData>;
    revenueVsInvoiceValue$: Observable<ChartItemData>;
    bookingsVsInvoiceValue$: Observable<ChartItemData>;
    bookingsInvoiceVarianceByAmount$: Observable<ChartItemData>;
    bookingsInvoiceVarianceByPercentage$: Observable<ChartItemData>;
  //  stackedRevenueByProduct$: Observable<ChartItemData>;
    bookingVsDelByAccount$: Observable<ChartItemData>;
    bookingVsDelByItemType$: Observable<ChartItemData>;
    bookingVsDelBySKU$: Observable<ChartItemData>;

    grossMarginPercentByAccount$: Observable<ChartItemData>;
    grossMarginPercentByItemType$: Observable<ChartItemData>;
    pacingMarginPercentByAccount$: Observable<ChartItemData>;
    pacingMarginPercentByItemType$: Observable<ChartItemData>;
    cmPercentVarAsSoldByAccount$: Observable<ChartItemData>;
    cmPercentVarAsSoldByItemType$: Observable<ChartItemData>;

    grossVsPacingMarginPercentByAccount$: Observable<ChartItemData>;
    grossVsPacingMarginPercentByItemType$: Observable<ChartItemData>;
    pricingAnalytics$: Observable<ChartItemData>;
    chartsConfig$: Observable<{ [key: string]: { header: string, showField: boolean } }>;

    constructor(private dashboardService: AnalyticalDashboardService, 
        private filterConfigService: FilterConfigService,) {
    }


    ngOnInit(): void {
        this.getTotalData();
        this.getChartsData();
        this.chartsConfig$ = this.filterConfigService.chartsConfig$.pipe(
            map(config => config.reduce((acc, curr) => {
                acc[curr.header] = curr;
                return acc;
            }, {}))
        );
    }

    getTotalData() {
        this.totalData$ = this.dashboardService.getTotalData().pipe(
            map(data => Object.entries(data[0]).map(([name, value]: [string, number]): TotalItem => ({
                header: name,
                value: value,
                showField: true,
            }))),
            map(data => {
                const sortOrder = ['totalOpportunityAmt', 'totalQuoteValue', 'dealsCount', 'totalMarginPercent', 'totalMarginValue'];
                return data.sort((a, b) => sortOrder.indexOf(a.header) - sortOrder.indexOf(b.header));
            }),
            tap(data => this.filterConfigService.addTotalsToConfig(data))
        );
    }

    getChartsData() {
        this.getChartByParams('bookingsValueBySKU');
        this.getChartByParams('revenueByRegion');
        this.getChartByParams('revenueByAccount');
        this.getChartByParams('revenueBySalesRep');
        this.getChartByParams('revenueByQuarter');
        this.getChartByParams('invoiceByAccount');
        this.getChartByParams('dealsByDiscountGuidance');
        this.getChartByParams('dealsByRevenueGuidance');
        this.getChartByParams('dealsByMarginGuidance');
        this.getChartByParams('bookingValue');
        this.getChartByParams('bookingVsDelValue');
        this.getChartByParams('invoiceValue');
        this.getChartByParams('revenueVsInvoiceValue');
        this.getChartByParams('bookingsVsInvoiceValue');
        this.getChartByParams('bookingsInvoiceVarianceByAmount');
        this.getChartByParams('bookingsInvoiceVarianceByPercentage');
        this.getChartByParams('bookingVsDelByAccount');
        this.getChartByParams('bookingVsDelByItemType');
        this.getChartByParams('bookingVsDelBySKU');
        this.getChartByParams('grossMarginPercentByAccount');
        this.getChartByParams('grossMarginPercentByItemType');
        this.getChartByParams('pacingMarginPercentByAccount');
        this.getChartByParams('pacingMarginPercentByItemType');
        this.getChartByParams('cmPercentVarAsSoldByAccount');
        this.getChartByParams('cmPercentVarAsSoldByItemType');
        this.getChartByParams('grossVsPacingMarginPercentByAccount');
        this.getChartByParams('grossVsPacingMarginPercentByItemType');
        this.getChartByParams('pricingAnalytics');
     //   this.getChartByParams('stackedRevenueByProduct');

  }

    updateChart(params: { name: string; period: PeriodParameter }) {
        this.getChartByParams(params.name, params.period);
    }
    // In dashboard-charts.component.ts

updatePricingChart(event: any) {
    if (!event?.filters) {
        return;
    }

    const { productName, region, segment, term } = event.filters;
    
    // Log the values to debug
    console.log('Updating pricing chart with:', { productName, region, segment, term });

    this.pricingAnalytics$ = this.dashboardService.getPriceAnalytics(
        productName || '',
        region || '',
        segment || '',
        typeof term === 'object' ? term.value || '' : term || ''
    ).pipe(
        tap(data => console.log('API Response:', data)),
        map(data => this.convertToChartItem({
            'pricingAnalytics': data
        })),
        catchError(error => {
            console.error('Error fetching pricing analytics:', error);
            return of(null);
        })
    );
}
    // updatePricingChart(event: any) {
    //     console.log('pricing',event)
    //     this.pricingAnalytics$ = this.dashboardService.getPriceAnalytics(
    //         event.filters.productName,
    //         event.filters.region,
    //         event.filters.segment,
    //         event.filters.term
    //     ).pipe(
    //         tap(data => console.log('pricingApi response',data)),
    //         map(data => this.convertToChartItem({
    //             'pricingAnalytics': data
    //         })),
    //         tap(chartItem => console.log('Converted Chart Item:', chartItem))
    //     );
    // }
	
  /*  convertToStackedChartItem(data: StackedBarData[], header: string): ChartItemData {
    return {
        header: name,
        type: 'stacked-bar',  // Custom type for stacked bar charts
        showField: true,
        data: data,
        displayAsPercentage: false
    };
}*/

    convertToChartItem(chart: { [key: string]: any }): ChartItemData {
        const [name,value] =Object.entries(chart)[0];
        console.log(name);
        if(name === 'bookingVsDelByAccount' ||  name === 'bookingVsDelByItemType'|| name === 'bookingVsDelBySKU'){
		
            return{
                header: name,
                showField: true,
                data:  value as StackedBarData[],
                type: 'stacked-bar',
			    displayAsPercentage: false

            };
        }
        else if(name === 'pricingAnalytics'){
            return {
                header: name,
                showField: true,
                data: value as PricingAnalytics[],
                type: 'pricing',
                displayAsPercentage: false
        };
    }
        
        
        else if(name === 'grossMarginPercentByAccount' ||  name === 'grossMarginPercentByItemType' ||
                  name === 'pacingMarginPercentByAccount' ||  name === 'pacingMarginPercentByItemType' ||
                  name === 'cmPercentVarAsSoldByAccount' ||  name === 'cmPercentVarAsSoldByItemType' ||
                  name === 'grossVsPacingMarginPercentByAccount' ||  name === 'grossVsPacingMarginPercentByItemType'
                ){
            return {
                header: name,
                showField: true,
                data:  value as StackedBarGMPMCMPData[],
                type: 'stacked-bar-percent',
			    displayAsPercentage: true

            };
        } else{
        const chartItem = Object.entries(chart).filter(entry => !!entry[1].length)
            .map(([name, value]) => {
              
                const chartData = ('guidance' in value[0]) ? value : this.groupBarDataBy(value as BarData[],
                    (item) => item.sku || item.region || item.account||item.totalType || item.salesRep || item.periodValue );

                    
                let chartType: 'bar' | 'pie' | 'stacked-bar' = ('guidance' in value[0]) ? 'pie' : 'bar';
                if (name === 'bookingVsDelByAccount' || name === 'bookingVsDelByItemType' || name === 'bookingVsDelBySKU') {
                    chartType = 'stacked-bar';
                }

                let displayPercent:boolean =false;
                displayPercent=(name==='bookingsInvoiceVarianceByPercentage' ? true : false);
               
                return ({
                    header: name,
                    showField: true,
                    data: chartData,
                    type: chartType,
                    displayAsPercentage: displayPercent
                });
            });
        
        return chartItem[0];

    }
}


    getChartByParams(chartName: string, period?: string) {
        const periodParam = period ? period : 'PTD';
        switch (chartName) {
            case 'bookingsValueBySKU':
                this.bookingsValueBySKU$ = this.dashboardService.getBookingsValueBySKU(periodParam).pipe(
                    map(data => this.convertToChartItem({[chartName]: data}))
                );
                break;
            case 'revenueByRegion':
                this.revenueByRegion$ = this.dashboardService.getRevenueByRegion(periodParam).pipe(
                    map(data => this.convertToChartItem({[chartName]: data}))
                );
                break;
            case 'revenueByAccount':
                this.revenueByAccount$ = this.dashboardService.getRevenueByAccount(periodParam).pipe(
                    map(data => this.convertToChartItem({[chartName]: data}))
                );
                break;
            case 'revenueBySalesRep':
                this.revenueBySalesRep$ = this.dashboardService.getRevenueBySalesRep(periodParam).pipe(
                    map(data => this.convertToChartItem({[chartName]: data}))
                );
                break;
            case 'revenueByQuarter':
                this.revenueByQuarter$ = this.dashboardService.getRevenueByQuarter(periodParam).pipe(
                    map(data => this.convertToChartItem({[chartName]: data}))
                );
                break;
            case 'invoiceByAccount':
                this.invoiceByAccount$ = this.dashboardService.getInvoiceByAccount(periodParam).pipe(
                    map(data => this.convertToChartItem({[chartName]: data}))
                );
                break;
            case 'dealsByDiscountGuidance':
                this.dealsByDiscountGuidance$ = this.dashboardService.getDealsByDiscountGuidance().pipe(
                    map(data => this.convertToChartItem({[chartName]: data}))
                );
                break;
            case 'dealsByRevenueGuidance':
                this.dealsByRevenueGuidance$ = this.dashboardService.getDealsByRevenueGuidance().pipe(
                    map(data => this.convertToChartItem({[chartName]: data}))
                );
                break;
            case 'dealsByMarginGuidance':
                this.dealsByMarginGuidance$ = this.dashboardService.getDealsByMarginGuidance().pipe(
                    map(data => this.convertToChartItem({[chartName]: data}))
                );
                break;
          
                case 'bookingValue':
                this.bookingValue$=this.dashboardService.getBookingValue(periodParam).pipe(
                    map(data => this.convertToChartItem({[chartName]:data}))
                );
                break;
                
                case 'bookingVsDelValue':
                    this.bookingVsDelValue$=this.dashboardService.getBookingVsDelValue(periodParam).pipe(
                        map(data=>this.convertToChartItem({[chartName]:data}))
                    );
                break;
                case 'invoiceValue':
                    this.invoiceValue$=this.dashboardService.getInvoiceValue(periodParam).pipe(
                        map(data=>this.convertToChartItem({[chartName]:data}))
                    );
                break;
                case 'revenueVsInvoiceValue':
                    this.revenueVsInvoiceValue$=this.dashboardService.getRevenueVsInvoiceValue(periodParam).pipe(
                        map(data=>this.convertToChartItem({[chartName]:data}))
                    );
                break;
                    case 'bookingsVsInvoiceValue':
                        this.bookingsVsInvoiceValue$=this.dashboardService.getBookingsVsInvoiceValue(periodParam).pipe(
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );
                 break;
                    case 'bookingsInvoiceVarianceByAmount':
                            this.bookingsInvoiceVarianceByAmount$=this.dashboardService.getBookingsInvoiceVarianceByAmount(periodParam).pipe(
                                map(data=>this.convertToChartItem({[chartName]:data}))
                            );
                    break;
                        case 'bookingsInvoiceVarianceByPercentage':
                                this.bookingsInvoiceVarianceByPercentage$=this.dashboardService.getBookingsInvoiceVarianceByPercentage(periodParam).pipe(
                                    map(data=>this.convertToChartItem({[chartName]:data}))
                                );
                    break;

                    case 'bookingVsDelByAccount':
                        this.bookingVsDelByAccount$=this.dashboardService.getBookingVsDelByAccount(periodParam).pipe(
                            tap(data => console.log(`${chartName} data:`, data)),
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );

                    break;

                    case 'bookingVsDelByItemType':
                        this.bookingVsDelByItemType$=this.dashboardService.getBookingVsDelByItemType(periodParam).pipe(
                            tap(data => console.log(`${chartName} data:`, data)),
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );
                    break;
                    case 'bookingVsDelBySKU':
                        this.bookingVsDelBySKU$=this.dashboardService.getBookingVsDelBySKU(periodParam).pipe(
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );
                    break;

                    case 'grossMarginPercentByAccount':
                        this.grossMarginPercentByAccount$=this.dashboardService.getGrossMarginPercentByAccount(periodParam).pipe(
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );
                    break;

                    case 'grossMarginPercentByItemType':
                        this.grossMarginPercentByItemType$=this.dashboardService.getGrossMarginPercentByItemType(periodParam).pipe(
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );
                    break;

                    case 'pacingMarginPercentByAccount':
                        this.pacingMarginPercentByAccount$=this.dashboardService.getPacingMarginPercentByAccount(periodParam).pipe(
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );
                    break;

                    case 'pacingMarginPercentByItemType':
                        this.pacingMarginPercentByItemType$=this.dashboardService.getPacingMarginPercentByItemType(periodParam).pipe(
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );
                    break;

                    case 'cmPercentVarAsSoldByAccount':
                        this.cmPercentVarAsSoldByAccount$=this.dashboardService.getCMPercentVarAsSoldByAccount(periodParam).pipe(
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );
                    break;

                    case 'cmPercentVarAsSoldByItemType':
                        this.cmPercentVarAsSoldByItemType$=this.dashboardService.getCMPercentVarAsSoldByItemType(periodParam).pipe(
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );
                    break;

                    case 'grossVsPacingMarginPercentByAccount':
                        this.grossVsPacingMarginPercentByAccount$=this.dashboardService.getGrossVsPacingMarginPercentByAccount(periodParam).pipe(
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );
                    break;

                    case 'grossVsPacingMarginPercentByItemType':
                        this.grossVsPacingMarginPercentByItemType$=this.dashboardService.getGrossVsPacingMarginPercentByItemType(periodParam).pipe(
                            map(data=>this.convertToChartItem({[chartName]:data}))
                        );
                    break;

                    case 'pricingAnalytics':
                this.pricingAnalytics$ = this.dashboardService.getPriceAnalytics('', '', '', '').pipe(
                    map(data => this.convertToChartItem({
                        [chartName]: data
                    }))
                );
                break;

                        /*case 'stackedRevenueByProduct':
                            this.stackedRevenueByProduct$=this.dashboardService.getStackedRevenueByProduct(periodParam).pipe(
                                map(data => this.convertToChartItem({[chartName]:data}))
                            )
                        break;*/















                  /*case 'bookingValue':
                this.bookingValue$=this.dashboardService.getBookingValue(periodParam).pipe(
                    map(data=>this.convertToChartItem({[chartName]:data}))
                );
                break;*/

            
                
                
        }
    }
    

    groupBarDataBy(data: BarData[], criterion: (item: BarData) => string) {
        return data.reduce((result, item) => {
            const groupKey = criterion(item);
            result[groupKey] = result[groupKey] || [];
            result[groupKey].push(item);
            return result;
        }, {});
    }
    
}
