export interface FilterParams {
    accountType?: string;
    businessType?: string;
    region?: string;
    status?: string;
    channel?: string;
}

export interface TableFilterOptions {
    channel: FilterOption[];
    businessType: FilterOption[];
    accountType: FilterOption[];
    region: FilterOption[];
    status: FilterOption[];
}

interface FilterOption {
    label: string;
    value: string;
}
