.charts-container {
  display: flex;
}

.totals-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  position: sticky;
  top: 20px;
}

.charts-section {
  display: flex;
  flex-wrap: wrap;
  margin-left: 20px;
  gap: 15px;
}

@media(max-width: 1484px) {
  .charts-section {
    justify-content: space-between;
  }
}

@media(max-width: 1070px) {
  .charts-section {
    justify-content: center;
  }
}
