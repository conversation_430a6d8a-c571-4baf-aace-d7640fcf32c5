import {ChartData, ChartType} from 'chart.js';

export interface ChartItemData {
    header: string;
    type:  'bar' | 'pie' | 'stacked-bar' | 'stacked-bar-percent'| 'pricing' ;
    showField: boolean;
    data: BarDataGroup | PieData[] | StackedBarData[] | StackedBarGMPMCMPData[] |  PricingAnalytics[] |ChartData;
    displayAsPercentage?: boolean;
    //isStacked?: boolean;
}
export interface BarDataGroup {
    [key: string]: BarData[];
}
export enum PricingTerm {
    LESS_THAN_12 = '<12 Months',
    BETWEEN_12_24 = '12-24 Months',
    GREATER_THAN_24 = '24< Months'
}
// export enum QuantityRange {
//     RANGE_1_100 = '1-100',
//     RANGE_101_250 = '101-250',
//     RANGE_251_500 = '251-500',
//     RANGE_501_10000 = '500-10000'
// }


export interface BarData {
    value: number;
    periodValue: string;
    account?: string;
    salesRep?: string;
    region?: string;
    sku?: string;
    totalType?: string;
}
export interface StackedBarData{
    account?: string;
    periodType: string;
    periodValue: string;
    bookedAmount: number;
    deliveredAmount: number;
    itemType?: string;
    productName?: string;
    
}
export interface StackedBarGMPMCMPData{
    categoryValue: string;
    periodValue: string;
    grossMarginPercent?:number;
    pacingMarginPercent?:number;
    cmPercentVarAsSold?: number;
}
export interface PricingFilters{
    productName: string;
    region: string;
    segment: string;
    term: string;
}
export interface PricingAnalytics{
    productName: string;
    region: string;
    term: string;
    quantityRange: string;
    segment: string;
    minPrice: number;
    avgPrice: number;
    maxPrice: number;
}

export interface PieData {
    guidance: 'RED' | 'GREEN' | 'YELLOW';
    totalCount: number;
}

export type PeriodParameter = 'PTD' | 'QTD' | 'YTD';

