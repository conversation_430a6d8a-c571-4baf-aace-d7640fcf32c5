<p-table class="dashboard__table" id="dashboard-table"
         #dt
         [value]="tableData ? tableData.content : []"
         [loading]="isLoading"
         reorderableColumns="true"
         lazy="true"
         (onLazyLoad)="onLoadData($event)"
         [scrollable]="true"
         [paginator]="true"
         [rows]="tableData ? tableData.size : 0"
         [showCurrentPageReport]="true"
         currentPageReportTemplate="{first} - {last} of {totalRecords}"
         [totalRecords]="tableData ? tableData.totalElements : 0"
         [columns]="columns"
         [globalFilterFields]="['accountType', 'businessType', 'region', 'status']"
         exportFilename="Dashboard_T</p-table>able_Data.csv">
    <ng-template pTemplate="colgroup" let-columns>
        <colgroup>
            <ng-container *ngFor="let col of columns">
                <ng-container *ngIf="col.showField">
                    <col [style]="{width: col.width || '150px', height: '100px'}">
                </ng-container>
            </ng-container>
        </colgroup>
    </ng-template>
    <ng-template pTemplate="header" let-columns>

        <tr (click)="$event.preventDefault()">
            <ng-container *ngFor="let col of columns" pReorderableColumn>
                <ng-container *ngIf="col.showField">
                    <th [class]="{'triangle': col.type === 'line'}" style="text-align: center">
                        <div [class]="{'header-filter': col.filter}">

                            <span [class]="{'light-text': col.filter}">{{col.header}}</span>
                            <ng-container *ngIf="selectedParameter.name === 'Default' && col.field === 'win'">
                                <span class="sparks-icon">
                                    <img width="30" src="../../../assets/icons/sparks-icon.svg" alt="sparks">
                                </span>
                            </ng-container>

                            <div *ngIf="col.subHeader" class="d-flex justify-content-center align-items-center">
                                <span class="light-text">{{col.subHeader}}</span>
                                <ng-container *ngIf="col.subHeader2">
                                    <div class="separator"></div>
                                    <span class="blue-text">{{col.subHeader2}}</span>
                                </ng-container>
                            </div>

                            <ng-container *ngIf="col.filter">
                                <p-dropdown class="dashboard-dropdown" placeholder="Select All"
                                            appendTo="body"
                                            [showClear]="true"
                                            [options]="filterOptions[col.field]"
                                            (onChange)="onFilter(col.field, $event.value)">
                                    <ng-template pTemplate="item" let-option>
                                        <span>{{option.label}}</span>
                                    </ng-template>
                                </p-dropdown>
                            </ng-container>
                        </div>
                    </th>
                </ng-container>
            </ng-container>
        </tr>

    </ng-template>

    <ng-template pTemplate="body" let-rowData let-columns='columns'>
        <tr [pSelectableRow]="rowData">

            <ng-container *ngFor="let col of columns;">

                <ng-container *ngIf="col.showField">

                    <td *ngIf="col.type === 'quote'">
                          <div>
                            <!--span [style.font-weight]="700">Quote Name:</span-->
                            <span>Quote Name:</span>
                            <span [style.font-weight]="600">{{rowData[col.field]}}</span>
                        </div>
                        <div>
                            <span>Quote Number:</span>
                            <span [style.font-weight]="600">{{rowData['quoteNumber']}}</span>
                        </div>
                    </td>

                    <td *ngIf="col.type === 'text'">
                        {{rowData[col.field]}}
                        <div *ngIf="col.subHeader" class="light-text">{{rowData['opportunityName']}}</div>
                    </td>

                    <td *ngIf="col.type === 'status'">
                        {{rowData[col.field]}}
                    </td>

                    <td *ngIf="col.type === 'size'">
                        <div class="d-flex flex-column align-content-end flex-wrap text-right">
                            <div>${{
                                (
                                    rowData['opportunity'].amount > rowData['order'].amount
                                        ? rowData['order'].amount
                                        : rowData['opportunity'].amount
                                ) | round
                                }}
                            </div>
                            <div class="light-text">${{rowData['opportunity'].amount | round}}</div>
                            <div class="blue-text">${{rowData['order'].amount  |round}}</div>
                        </div>
                    </td>
					
					<td *ngIf="col.type === 'tcv'">
						<div class="d-flex flex-column align-content-end flex-wrap text-right">
							<div>{{rowData['tcv'] | currency:'USD':'symbol':'1.0'}}</div>
							<div class="light-text">{{rowData['tcvRecurring'] | currency:'USD':'symbol':'1.0'}}</div>
							<div class="blue-text">{{rowData['tcvNonRecurring'] | currency:'USD':'symbol':'1.0'}}</div>
						</div>
					</td>

                    <td *ngIf="col.type === 'color'">
                        <ng-container *ngIf="rowData[col.field]">
                            <ng-container *ngIf="rowData[col.field].color; else noColor">
                                <div [class]="['color-number', rowData[col.field].color]">
                                    {{rowData[col.field].value ? rowData[col.field].value : 0}}
                                </div>
                            </ng-container>
                            <ng-template #noColor>
                                <div class="color-number">
                                    {{rowData[col.field].value ? rowData[col.field].value : 0}}
                                </div>
                            </ng-template>
                        </ng-container>

                    </td>

                    <td *ngIf="col.type === 'number'">
                        {{rowData[col.field] | number}}
                    </td>

                    <!-- <td *ngIf="col.type === 'currency'">
                        {{rowData[col.field] | round }}
                    </td> -->

                    <td *ngIf="col.type === 'currency'">
                      <span>{{rowData[col.field] !== null ? ('$' + (rowData[col.field] | round)) : ''}}</span>
                    </td>

                    

                    

                    <td *ngIf="col.type === 'percent'">
                        {{((rowData[col.field] ? rowData[col.field] : 0))}}
                    </td>
                    <td *ngIf="col.type === 'deal'">

                        <ng-container *ngIf="rowData[col.field] && (selectedParameter.name === 'Default' || selectedParameter.name === 'Opportunity'); else countField">
                            <ng-container *ngIf="rowData[col.field].number; else none">
                                <div class="checkbox"  (click)="redirectToTab(rowData[col.field].number)"
                                (mouseenter)="overlayPanel.toggle($event)"
                                (mouseleave)="onMouseLeave($event, overlayPanel)"
                                >
                                    <img class="table-icon" src="../../../assets/icons/check-circle.svg"
                                         alt="check">

                                    <span>{{rowData[col.field].number}}</span>
                                   
                                    
                                </div>
                                
                               
                                <!--div class="checkbox" (click)="redirectToTab(rowData[col.field].number)">
                                    <img class="table-icon" src="../../../assets/icons/dropdown.svg">
                                         
                                <span   (click)="redirectToTab(rowData[col.field].number)">{{rowData[col.field].number}}</span>
                                </div-->

                                <div class="checkbox-date light-text">
                                    {{rowData[col.field].date | date:'d MMM y'}}
                                </div>
                                <p-overlayPanel #overlayPanel  class="checkbox-overlaypanel">
                                    <div class="tooltip-options">
                                        <a (click)="onSummaryClick(rowData, col.field)">Summary</a>
                                        <span class="separate">|</span>
                                        <a (click)="onLineClick(rowData, col.field)">Line</a>
                                    </div>
                                </p-overlayPanel>
                               

                            </ng-container>
                           
                            <ng-template #none>
                                <img class="table-icon" src="../../../assets/icons/minus-circle.svg"
                                     alt="minus"/>
                            </ng-template>
                        </ng-container>

                        <ng-template #countField>
                            <div class="count-field">{{rowData[col.field].number ? rowData[col.field].number : 0}}</div>
                        </ng-template>
                    </td>

                    <td *ngIf="col.type == 'line'" class="line-col">
                        <div class="line-container" *ngIf="rowData[col.field].amount !== null && rowData[col.field].amount !== undefined">
                            <ng-container *ngIf="rowData[col.field].leakage !== null && rowData[col.field].leakage !== undefined">
                                <div class="line-leakage light-text">
                                    Leakage:{{rowData[col.field].leakage |  currency:'USD': 'symbol':'1.0'}}</div>
                            </ng-container>
                            <ng-container *ngIf="(rowData[col.field].leakageDays !== null && rowData[col.field].leakageDays !== undefined) && selectedParameter.name === 'Default';">
                                <div class="line-leakage light-text">
                                    Days: {{rowData[col.field].leakageDays}}</div>
                            </ng-container>
                            <ng-container *ngIf="rowData[col.field] && (selectedParameter.name !== 'Default' && selectedParameter.name !== 'Opportunity')">
                                <div class="line-leakage light-text count-field">
                                    {{rowData[col.field].count}}</div>
                            </ng-container>

                            <div *ngIf="rowData[col.field].pending"
                                 class="line-leakage light-text">
                                Pending: {{rowData[col.field].pending |  currency:'USD': 'symbol':'1.0'}}
                            </div>
                            <div class="line-body" >
                                <div class="line-circle"
                                     [class]="{red: rowData[col.field].status === 'RED'}"
                                     (mouseenter)="overlayPanel.toggle($event)"
                                     
                                     (mouseleave)="onMouseLeave($event, overlayPanel)"
                                     >
                                    </div>
                                    <p-overlayPanel #overlayPanel >
                                        <div class="tooltip-options">
                                            <a (click)=" onSummaryClick(rowData, col.field)">Summary</a>
                                            <span class="separate">|</span>
                                            <a (click)="onLineClick(rowData, col.field)">Line</a>
                                        </div>
                                    </p-overlayPanel>
                            
                                <div class="line" *ngIf="!!rowData[col.field].status"
                                     [class]="{red: rowData[col.field].status === 'RED'}"></div>

                                     <!--ng-template #tooltipTemplate>
                                        <div>
                                            <a href="#" (click)="onSummaryClick($event)">Summary</a>
                                            &nbsp;|&nbsp;
                                            <a href="#" (click)="onLineClick($event)">Line</a>
                                        </div>
                                    </ng-template-->
                                     
                            
                            </div>
                           
                           
                            <div class="line-date light-text">{{rowData[col.field].date | date:'d MMM y'}}</div>
                            <div class="line-amount blue-text">{{rowData[col.field].amount | currency:'USD': 'symbol':'1.0'}}</div>
                        </div>

                    </td>
                </ng-container>
            </ng-container>
            <!--p-dialog header="Summary Details" [(visible)]="displaySummaryDialog" [modal]="true" [closable]="true">
               
                <p>Here are the details for the summary...</p>
            </p-dialog>
            <p-dialog header="Line " [(visible)]="displayLineDialog" [modal]="true" [closable]="true">
                
                <p>Here are the details for the Line..</p>
            </p-dialog-->
           
        </tr>
       
       </ng-template>

    <ng-template pTemplate="emptymessage" let-columns>
        <tr>
            <td colspan="5">No records found.</td>
        </tr>
    </ng-template>
    </p-table>

  
    

    
      <p-dialog [(visible)]="displayLineCircleDialog" 
      [resizable]="true"
      showEffect="fade"
     styleClass="dd-table-dialog"
     [style]="{'width': selectedViewType === 'summary' ? '50vw' : '100%'}"
      [modal]="true" >
       
        <ng-template pTemplate="header">
          <div class="header-content">
            <div class="view-type">
              {{ selectedViewType === 'summary' ? 'Summary' : 'Line' }} View
            </div>
            <div class="subheader">
              <ng-container *ngFor="let info of selectedHeaderInfo; let last = last; let odd = odd">
                <span class="subheader-label" [class.odd]="odd">{{info.label}}:</span>
                <span class="subheader-value" [class.odd]="odd">{{info.value}}</span>{{!last ? ' | ' : ''}}
              </ng-container>
            </div>
          </div>
        </ng-template>

 
        
        <ng-template pTemplate="content">
          <div [ngClass]="{'summary-columns': selectedViewType === 'summary', 'line-view-content': selectedViewType === 'line'}">

          
          <!-- <div class="summary-columns" *ngIf="selectedViewType === 'summary' && selectedLineCircleData?.length">
            <div class="left-column">
              <ng-container *ngFor="let col of getLineCircleColumns(); let i = index">
                <div *ngIf="i < getLineCircleColumns().length / 2" class="row">
                  <span class="header-text">{{col.header}}</span>
                  <span class="colon">:</span>
                  <ng-container *ngIf="col.type == 'symbol' && selectedLineCircleData[0][col.field] !== null">
                    <span class="data-text">
                      <i class="fa fa-circle" [ngClass]="{
                        'red-dot': selectedLineCircleData[0][col.field] === 'RED',
                        'green-dot': selectedLineCircleData[0][col.field] === 'GREEN',
                        'yellow-dot': selectedLineCircleData[0][col.field] === 'YELLOW'
                      }"></i>
                    </span>
                  </ng-container>
                  <ng-container *ngIf="col.type !== 'symbol'">
                    <span class="data-text">{{selectedLineCircleData?.[0]?.[col.field]}}</span>
                  </ng-container>
                  
                </div>
              </ng-container>
            </div>
          
            <div class="right-column">
              <ng-container *ngFor="let col of getLineCircleColumns(); let i = index">
                <div *ngIf="i >= getLineCircleColumns().length / 2" class="row">
                  <span class="header-text">{{col.header}}</span>
                  <span class="colon">:</span>
                  <ng-container *ngIf="col.type == 'symbol' && selectedLineCircleData[0][col.field] !== null">
                    <span class="data-text">
                      <i class="fa fa-circle" [ngClass]="{
                        'red-dot': selectedLineCircleData[0][col.field] === 'RED',
                        'green-dot': selectedLineCircleData[0][col.field] === 'GREEN',
                        'yellow-dot': selectedLineCircleData[0][col.field] === 'YELLOW'
                      }"></i>
                    </span>
                  </ng-container>
                  <ng-container *ngIf="col.type !== 'symbol'">
                    <span class="data-text">{{selectedLineCircleData?.[0]?.[col.field]}}</span>
                  </ng-container>


                  <ng-container *ngIf="col.type == 'number'">
                    <span class="data-text">{{selectedLineCircleData[0][col.field] | currency:'USD': 'symbol':'1.0'}}</span>
                  </ng-container>
         
                  
                  
                </div>
              </ng-container>
            </div>
          </div>
          -->

          <div class="summary-columns" *ngIf="selectedViewType === 'summary' && selectedLineCircleData && selectedLineCircleData.length > 0">
            <div class="left-column">
              <ng-container *ngFor="let col of getLineCircleColumns(); let i = index">
                <div *ngIf="i < getLineCircleColumns().length / 2" class="row">
                  <span class="header-text">{{col.header}}</span>
                  <span class="colon">:</span>
                  <ng-container *ngIf="col.type === 'symbol'  ">
                    <span  class="data-text"  >
                      <ng-container *ngIf="selectedLineCircleData[0][col.field] !== null && selectedLineCircleData[0][col.field] !== undefined">
                      <i class="fa fa-circle" [ngClass]="{
                        'red-dot': selectedLineCircleData[0][col.field] === 'RED',
                        'green-dot': selectedLineCircleData[0][col.field] === 'GREEN',
                        'yellow-dot': selectedLineCircleData[0][col.field] === 'YELLOW'
                      }"></i>
                      </ng-container>
                    </span>
                  </ng-container>



                  
                  <ng-container *ngIf="col.type === 'number' ">
                    <span class="data-text">{{selectedLineCircleData[0][col.field] | currency:'USD': round}}</span>
                  </ng-container>
                  <!-- <ng-container *ngIf="col.type !== 'symbol' && col.type !== 'number' && selectedLineCircleData[0][col.field] !== null">
                    <span class="data-text">{{selectedLineCircleData[0][col.field]}}</span>
                  </ng-container> -->
                  <!-- <ng-container *ngIf=" col.type !== 'number' ">
                    <span class="data-text">{{selectedLineCircleData[0][col.field]}}</span>
                  </ng-container> -->
                  <ng-container *ngIf=" col.type !== 'symbol' && col.type !== 'number' && col.type !== 'num'">
                    <span class="data-text">{{selectedLineCircleData[0][col.field]}}</span>
                  </ng-container>

                  <ng-container *ngIf="col.type === 'num' ">
                    <span class="data-text">{{selectedLineCircleData[0][col.field] | round}}</span>
                  </ng-container>
                
                </div>
              </ng-container>
            
            </div>
           
            <div class="right-column">
              <ng-container *ngFor="let col of getLineCircleColumns(); let i = index">
                <div *ngIf="i >= getLineCircleColumns().length / 2" class="row">
                  <span class="header-text">{{col.header}}</span>
                  <span class="colon">:</span>
                  <ng-container *ngIf="col.type === 'symbol'">
                    <span class="data-text"  >
                      <ng-container *ngIf="selectedLineCircleData[0][col.field] !== null && selectedLineCircleData[0][col.field] !== undefined">
                      <i class="fa fa-circle" [ngClass]="{
                        'red-dot': selectedLineCircleData[0][col.field] === 'RED',
                        'green-dot': selectedLineCircleData[0][col.field] === 'GREEN',
                        'yellow-dot': selectedLineCircleData[0][col.field] === 'YELLOW'
                      }"></i>
                     
                       </ng-container>
                    </span>
                  </ng-container>
                  <ng-container *ngIf="col.type === 'number'">
                    <span class="data-text">{{selectedLineCircleData[0][col.field] | currency:'USD': round}}</span>
                  </ng-container>
                  <!-- <ng-container *ngIf="col.type !== 'symbol' && col.type !== 'number' && selectedLineCircleData[0][col.field] !== null">
                    <span class="data-text">{{selectedLineCircleData[0][col.field]}}</span>
                  </ng-container> -->
                  <ng-container *ngIf=" col.type !== 'symbol' && col.type !== 'number' && col.type !== 'digits'">
                    <span class="data-text">{{selectedLineCircleData[0][col.field]}}</span>
                    
                  </ng-container>

                  <ng-container *ngIf="col.type === 'digits' ">
                    <span class="data-text">{{selectedLineCircleData[0][col.field] | round}}</span>
                  </ng-container>

                </div>
              </ng-container>
            </div>
            </div>

      
          <div  *ngIf="selectedViewType === 'line' && selectedLineCircleType !== 'opportunity' " >
            <p-table class="line__table "
            [value]="selectedLineCircleData || []"
            [columns]="getLineCircleColumns()" 
             [scrollable]="true" 
             [paginator]="true"
             [rows]="tableData ? tableData.size : 0"
             [totalRecords]="tableData ? tableData.totalElements : 0"
              [showCurrentPageReport]="true"
             currentPageReportTemplate="{first} - {last} of {totalRecords}"
       
             >
             <ng-template pTemplate="colgroup" let-columns>
              <colgroup>
                  <ng-container *ngFor="let col of columns">
                          <col [style]="{width: col.width || '150px' , height: '100px'}">
                      </ng-container>
                 
              </colgroup>
          </ng-template>
             
          <ng-template pTemplate="header" let-columns>
                <tr>
                  <th *ngFor="let col of columns">{{col.header}}</th>
                </tr>
              </ng-template>

              <ng-template pTemplate="body" let-rowData let-columns="columns">
                <tr>
              <td *ngFor="let col of columns">
                  <!-- <td *ngFor="let col of columns">
                    {{rowData[col.field]| number:'1.0-2'}}
                  </td> -->

                  <!-- <ng-container *ngIf="col.type !== 'symbol' && col.type !== 'number'">
                    {{rowData[col.field]}}
                    {{rowData[col.field] | number:'1.0-2'}}
                  </ng-container> -->

                  <!-- <ng-container *ngIf="col.type === 'symbol' ">
                    <span >
                      <i class="fa fa-circle" [ngClass]="{
                        'red-dot': selectedLineCircleData[0][col.field] === 'RED',
                        'green-dot': selectedLineCircleData[0][col.field] === 'GREEN',
                        'yellow-dot': selectedLineCircleData[0][col.field] === 'YELLOW'
                      }"></i>
                    </span>
                  </ng-container>

                    <ng-container *ngIf="col.type === 'number' ">
                      {{rowData[col.field] | currency:'USD':'symbol':'1.0-2'}}
                    </ng-container>


                    <ng-container *ngIf="col.type !== 'number' && col.type !== 'symbol'">
                      {{rowData[col.field]}}
                    </ng-container> -->
                <ng-container *ngIf="col.type === 'symbol' && rowData[col.field]">
                  <span>
                    <i class="fa fa-circle" [ngClass]="{
                      'red-dot': rowData[col.field] === 'RED',
                      'green-dot': rowData[col.field] === 'GREEN', 
                      'yellow-dot': rowData[col.field] === 'YELLOW'
                    }"></i>
                  </span>
                </ng-container>
              
                

                    <ng-container *ngIf="col.type === 'number'">
                      <div class="d-flex flex-column align-content-end flex-wrap text-right">
                        <!-- {{rowData[col.field] !== null ? (rowData[col.field] | currency:'USD':  round) : '$0' }} -->
                        {{rowData[col.field] | currency:'USD': round}}
                      </div>
                    </ng-container>

              
                    <!-- Text type - for all other fields -->
                    <ng-container *ngIf="col.type !== 'symbol' && col.type !== 'number' && col.type !== 'percent' && col.type !== 'alphanum' && col.type !== 'date' && col.type !== 'text'">
                      {{rowData[col.field]}}
                    </ng-container>
                    <!--    type for percent-->
                        <ng-container *ngIf="col.type === 'percent'">
                          {{rowData[col.field] ? (rowData[col.field] | round) + '%' : ''}}
                        </ng-container>

                        <ng-container *ngIf="col.type === 'alphanum'">
                          <div class="d-flex flex-column align-content-start flex-wrap text-left">
                            {{rowData[col.field]}}
                          </div>
                        </ng-container>

                        <ng-container *ngIf="col.type === 'date'">
                          {{rowData[col.field]  |  date: 'MM/dd/yyyy'}}
                        </ng-container>

                        <ng-container *ngIf="col.type === 'text'">
                          <div class="d-flex flex-column align-content-end flex-wrap text-left">
                            <!-- {{rowData[col.field] !== null ? (rowData[col.field] | currency:'USD':  round) : '$0' }} -->
                            {{rowData[col.field]}}
                          </div>
                        </ng-container>
              </td>
                </tr>
                

              </ng-template>
             
             
            <ng-template
            pTemplate="emptymessage"
            let-columns
    >
        <tr>
            <td colspan="5">No records found.</td>
        </tr>
    </ng-template>
            </p-table>
          </div>
           </div>
         
        </ng-template>
        <div *ngIf="selectedViewType === 'summary' && selectedLineCircleType === 'opportunity'" style="margin-top: 20px;">
          <ng-container *ngIf="(stages$ | async) as stages">
          <app-dynamic-table
         
          [tableData]="stages"
          [columns]="tableColumns2"
          [isLoading]="isLoading"
          [showToolbar]="false"
        ></app-dynamic-table>
      </ng-container>
        </div>
        <!-- <div  *ngIf="selectedViewType === 'line' && selectedLineCircleType === 'opportunity'" style="margin-top: 20px;">
          <app-dynamic-table
          [tableData]="forecasts"
          [columns]="forecastColumns"
          [isLoading]="isLoading"
          [showToolbar]="false"
          >
        </app-dynamic-table>
        </div> -->
        <div  *ngIf="selectedViewType === 'line' && selectedLineCircleType === 'opportunity'" style="margin-top: 10px;">
        <p-table class="line__table"
        [value]="forecasts?.content" 
        [columns]="forecastColumns"
        [loading]="isLoading"
        [scrollable]="true"
        [paginator]="true"
        [selection]="selectedRow"
        [rows]="tableData ? tableData.size : 0"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="{first} - {last} of {totalRecords}"
        [totalRecords]="tableData ? tableData.totalElements : 0"
        [columns]="columns"
        [showPageLinks]="true"
        [showJumpToPageDropdown]="false"
       
        [selectionMode]="selectionMode"
        >
        <ng-template
        pTemplate="colgroup"
        let-columns
>
    <colgroup>
        <ng-container *ngFor="let col of columns">
            <ng-container *ngIf="col.showField">
                <col style="min-width: 200px">
            </ng-container>
        </ng-container>
    </colgroup>
</ng-template>
        
        <!-- <ng-template pTemplate="header">
          <tr>
            <th *ngFor="let col of forecastColumns" [ngStyle]="{'text-align': col.alignment}">
              {{col.header}}
            </th>
          </tr>
        </ng-template> -->
    
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th *ngFor="let col of columns">{{col.header}}</th>
          </tr>
        </ng-template>


        <ng-template pTemplate="body" let-rowData>
          <tr>
            <td *ngFor="let col of forecastColumns" [ngStyle]="{'text-align': col.alignment}">
              <ng-container [ngSwitch]="col.type">
                <!-- Amount fields -->
                <ng-container *ngSwitchCase="'amount'">
                  {{rowData[col.field] | currency:'USD':'symbol':'1.2-2'}}
                </ng-container>
                
                <!-- Date fields -->
                <ng-container *ngSwitchCase="'date'">
                  {{rowData[col.field] | date:'MM/dd/yyyy'}}
                </ng-container>

                <!-- Quantity fields -->
            <ng-container *ngSwitchCase="'quantity'">
              {{rowData[col.field]}}
            </ng-container>
            

             <!-- Regular number fields -->
             <ng-container *ngSwitchCase="'number'">
              {{rowData[col.field] | currency:'USD':'symbol':'1.2-2'}}
            </ng-container>
                
                <!-- Default text fields -->
                <ng-container *ngSwitchDefault>
                  {{rowData[col.field]}}
                </ng-container>
              </ng-container>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage">
          <tr>
            <td [attr.colspan]="forecastColumns.length" class="text-center">
              No records found
            </td>
          </tr>
        </ng-template>
      </p-table>

    </div>
        
       
        <!-- Update the quote line view section in your dialog template -->
<div *ngIf="selectedViewType === 'line' && selectedLineCircleType === 'quote'">
  <p-table class="line__table"
    [value]="selectedLineCircleData"
    [columns]="generatedColumns"
    [loading]="isLoading"
    [scrollable]="true"
    
    >
    <ng-template pTemplate="colgroup" let-columns>
      <colgroup>
          <ng-container *ngFor="let col of generatedColumns">
                  <col [style]="{width: col.width || '150px' , height: '100px'}">
              </ng-container>
         
      </colgroup>
  </ng-template>
    
    <!-- <ng-template pTemplate="header">
      <tr>
        <th *ngFor="let col of generatedColumns" 
            [style.width]="col.width"
            [pSortableColumn]="col.field">
          {{col.header}}
         
        </th>
      </tr>
    </ng-template> -->
    <ng-template pTemplate="header" let-columns>
      <tr>
        <th *ngFor="let col of generatedColumns">{{col.header}}</th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-rowData>
      <tr>
        <td *ngFor="let col of generatedColumns" [ngStyle]="{
          'text-align': col.field === 'LINE_NO' ? 'center' :
                 col.field === 'PRODUCT_NAME' ? 'left' :
                 col.type === 'amount' ? 'right' : 'center'
        }">
          <ng-container [ngSwitch]="col.type">
              <ng-container *ngSwitchCase="'amount'">
              <div class="text-right">
          <span *ngIf="rowData[col.field] !== null && rowData[col.field] !== undefined">
            ${{rowData[col.field] | round}}
          </span>
              </div>
            </ng-container>
            
            <!-- Date fields -->
            <ng-container *ngSwitchCase="'date'">
              {{rowData[col.field] | date:'MM/dd/yyyy'}}
            </ng-container>
            
            <!-- Text fields -->
            <ng-container *ngSwitchDefault>
              {{rowData[col.field]}}
            </ng-container>
          </ng-container>
        </td>
            </tr>
          </ng-template>

    <!-- <ng-template pTemplate="emptymessage">
      <tr>
        <td [attr.colspan]="generatedColumns.length" class="text-center">
          No records found
        </td>
      </tr>
    </ng-template> -->
  </p-table>
</div>
      </p-dialog>