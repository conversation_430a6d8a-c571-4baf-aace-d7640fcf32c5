<div class="toolbar-container">
    <p-toolbar>
        <div class="p-toolbar-group-left">
            <ng-container *ngIf="backLink">
                <h3 class="toolbar-header"><a routerLink="../"> {{ backLink }}</a></h3>
                <i class="pi pi-arrow-right mr-2"></i>
            </ng-container>
            <h3 class="toolbar-header">{{ header }}</h3>
        </div>

        <div class="p-toolbar-group-right">
            <div
                    class="search__container"
                    *ngIf="withSearch"
                    [style.margin-left]="withTabs ? 100 * tabsCount + 20 + 'px': 0"
            >
                <input
                        pInputText
                        class="textbox"
                        name="searchKey"
                        (keyup.enter)="onSearch()"  
                        id="searchKey"
                        [(ngModel)]="searchTerm"
                        placeholder="Search here..."
                />
                <a class="globalSearchBtn">
                    <em
                            class="fa fa-search"
                            aria-hidden="true"
                            (click)="onSearch()"
                    ></em>
                </a>
                <a
                        *ngIf="isSearched"
                        class="globalSearchBtnx"
                        (click)="onResetGlobalSearch()"
                >
                    <em
                            class="fa fa-times"
                            aria-hidden="true"
                            (click)="onResetGlobalSearch()"
                    ></em>
                </a>
            </div>
            <p-button
                    label="Configure Columns"
                    icon="pi pi-cog"
                    iconPos="left"
                    (click)="showFilter = true"
            ></p-button>
            <p-button
                    label="Export"
                    icon="pi pi-sort-alt"
                    iconPos="left"
                    (click)="onExport()"
            ></p-button>

            <div *ngIf="withRefresh">
                <p-button
                    label="Refresh"
                    icon="pi pi-refresh"
                    iconPos="left"
                    (click)="onRefresh()"
                ></p-button>
            </div>

            <p-overlayPanel #op>
                <p-listbox
                        [options]="columns"
                        [(ngModel)]="selectedColumns"
                        (ngModelChange)="onColumnsChange($event)"
                        multiple="true"
                        optionLabel="header"
                        [style]="{'width':'200px'}"
                        [checkbox]="true"
                >
                </p-listbox>
            </p-overlayPanel>
        </div>
    </p-toolbar>
    <ng-content select="[projected-details]"></ng-content>
</div>

<app-dashboard-filter-modal
        [data]="columns"
        [isVisible]="showFilter"
        (close)="showFilter = false"
>
</app-dashboard-filter-modal>

