<div class="charts-container">
    <div class="totals-section">
        <div class="totals-container">
            <ng-container *ngIf="(totalData$ | async) as totalItems; else spinner">
                <ng-container *ngFor="let total of totalItems">
                    <ng-container *ngIf="total.showField">
                        <app-total-item [total]="total"></app-total-item>
                    </ng-container>
                </ng-container>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
        </div>
    </div>
    <div class="charts-section">
        <ng-container *ngIf="(chartsConfig$ | async) as config">
            <app-chart-item
                    *ngIf="config['bookingsValueBySKU'].showField"
                    [header]="'As Sold by Short Scope'"
                    (periodChange)="updateChart($event)">
                <ng-container *ngIf="(bookingsValueBySKU$ | async) as chartItem; else spinner">
                    <app-chart-body [chartData]="chartItem">
                    </app-chart-body>
                </ng-container>
                <ng-template #spinner>
                    <p-progressSpinner></p-progressSpinner>
                </ng-template>
            </app-chart-item>

            <app-chart-item
                    *ngIf="config['revenueByRegion'].showField"
                    [header]="'revenueByRegion'"
                    (periodChange)="updateChart($event)">
                <ng-container *ngIf="(revenueByRegion$ | async) as chartItem; else spinner">
                    <app-chart-body [chartData]="chartItem">
                    </app-chart-body>
                </ng-container>
                <ng-template #spinner>
                    <p-progressSpinner></p-progressSpinner>
                </ng-template>
            </app-chart-item>

            <app-chart-item
                    *ngIf="config['revenueByAccount'].showField"
                    [header]="'revenueByAccount'"
                    (periodChange)="updateChart($event)">
                <ng-container *ngIf="(revenueByAccount$ | async) as chartItem; else spinner">
                    <app-chart-body [chartData]="chartItem">
                    </app-chart-body>
                </ng-container>
                <ng-template #spinner>
                    <p-progressSpinner></p-progressSpinner>
                </ng-template>
            </app-chart-item>

            <app-chart-item
                    *ngIf="config['revenueBySalesRep'].showField"
                    [header]="'revenueBySalesRep'"
                    (periodChange)="updateChart($event)">
                <ng-container *ngIf="(revenueBySalesRep$ | async) as chartItem; else spinner">
                    <app-chart-body [chartData]="chartItem">
                    </app-chart-body>
                </ng-container>
                <ng-template #spinner>
                    <p-progressSpinner></p-progressSpinner>
                </ng-template>
            </app-chart-item>

            <app-chart-item
                    *ngIf="config['revenueByQuarter'].showField"
                    [header]="'revenueByQuarter'"
                    (periodChange)="updateChart($event)">
                <ng-container *ngIf="(revenueByQuarter$ | async) as chartItem; else spinner">
                    <app-chart-body [chartData]="chartItem">
                    </app-chart-body>
                </ng-container>
                <ng-template #spinner>
                    <p-progressSpinner></p-progressSpinner>
                </ng-template>
            </app-chart-item>

            <app-chart-item
                    *ngIf="config['invoiceByAccount'].showField"
                    [header]="'invoiceByAccount'"
                    (periodChange)="updateChart($event)">
                <ng-container *ngIf="(invoiceByAccount$ | async) as chartItem; else spinner">
                    <app-chart-body [chartData]="chartItem">
                    </app-chart-body>
                </ng-container>
                <ng-template #spinner>
                    <p-progressSpinner></p-progressSpinner>
                </ng-template>
            </app-chart-item>
            
            


        


            <app-chart-item
                    *ngIf="config['dealsByDiscountGuidance'].showField"
                    [header]="'dealsByDiscountGuidance'"
                    (periodChange)="updateChart($event)">
                <ng-container *ngIf="(dealsByDiscountGuidance$ | async) as chartItem; else spinner">
                    <app-chart-body [chartData]="chartItem">
                    </app-chart-body>
                </ng-container>
                <ng-template #spinner>
                    <p-progressSpinner></p-progressSpinner>
                </ng-template>
            </app-chart-item>

            <app-chart-item
                    *ngIf="config['dealsByRevenueGuidance'].showField"
                    [header]="'dealsByRevenueGuidance'"
                    (periodChange)="updateChart($event)">
                <ng-container *ngIf="(dealsByRevenueGuidance$ | async) as chartItem; else spinner">
                    <app-chart-body [chartData]="chartItem">
                    </app-chart-body>
                </ng-container>
                <ng-template #spinner>
                    <p-progressSpinner></p-progressSpinner>
                </ng-template>
            </app-chart-item>

            <app-chart-item
                    *ngIf="config['dealsByMarginGuidance'].showField"
                    [header]="'dealsByMarginGuidance'"
                    (periodChange)="updateChart($event)">
                <ng-container *ngIf="(dealsByMarginGuidance$ | async) as chartItem; else spinner">
                    <app-chart-body [chartData]="chartItem">
                    </app-chart-body>
                </ng-container>
                <ng-template #spinner>
                    <p-progressSpinner></p-progressSpinner>
                </ng-template>
            </app-chart-item>


                    <!-- Booking Value Chart -->
                    <app-chart-item
                        *ngIf="config['bookingValue'].showField"
                        [header]="'bookingValue'"
                        (periodChange)="updateChart($event)">
                    <ng-container *ngIf="(bookingValue$ | async) as chartItem; else spinner">
                        <app-chart-body [chartData]="chartItem">
                        </app-chart-body>
                    </ng-container>
                    <ng-template #spinner>
                        <p-progressSpinner></p-progressSpinner>
                    </ng-template>
                    </app-chart-item>

        <!-- Booking vs Delivery Value Chart -->
        <app-chart-item
        *ngIf="config['bookingVsDelValue'].showField"
        [header]="'As Sold vs As Delivered'"
        (periodChange)="updateChart($event)">
        <ng-container *ngIf="(bookingVsDelValue$ | async) as chartItem; else spinner">
            <app-chart-body [chartData]="chartItem">
            </app-chart-body>
        </ng-container>
        <ng-template #spinner>
            <p-progressSpinner></p-progressSpinner>
        </ng-template>
        </app-chart-item>

        <!-- Invoice Value Chart -->
        <app-chart-item
        *ngIf="config['invoiceValue'].showField"
        [header]="'invoiceValue'"
        (periodChange)="updateChart($event)">
        <ng-container *ngIf="(invoiceValue$ | async) as chartItem; else spinner">
            <app-chart-body [chartData]="chartItem">
            </app-chart-body>
        </ng-container>
        <ng-template #spinner>
            <p-progressSpinner></p-progressSpinner>
        </ng-template>
        </app-chart-item>

        <!-- Revenue vs Invoice Value Chart -->
        <app-chart-item
        *ngIf="config['revenueVsInvoiceValue'].showField"
        [header]="'revenueVsInvoiceValue'"
        (periodChange)="updateChart($event)">
        <ng-container *ngIf="(revenueVsInvoiceValue$ | async) as chartItem; else spinner">
            <app-chart-body [chartData]="chartItem">
            </app-chart-body>
        </ng-container>
        <ng-template #spinner>
            <p-progressSpinner></p-progressSpinner>
        </ng-template>
        </app-chart-item>

        <!-- Bookings vs Invoice Value Chart -->
        <app-chart-item
        *ngIf="config['bookingsVsInvoiceValue'].showField"
        [header]="' As Sold vs Invoiced'"
        (periodChange)="updateChart($event)">
        <ng-container *ngIf="(bookingsVsInvoiceValue$ | async) as chartItem; else spinner">
            <app-chart-body [chartData]="chartItem">
            </app-chart-body>
        </ng-container>
        <ng-template #spinner>
            <p-progressSpinner></p-progressSpinner>
        </ng-template>
        </app-chart-item>

        <!-- Bookings - Invoice Variance by Amount Chart -->
        <app-chart-item
        *ngIf="config['bookingsInvoiceVarianceByAmount'].showField"
        [header]="'Booking to Invoice Variance by Amount'"
        (periodChange)="updateChart($event)">
        <ng-container *ngIf="(bookingsInvoiceVarianceByAmount$ | async) as chartItem; else spinner">
            <app-chart-body [chartData]="chartItem">
            </app-chart-body>
        </ng-container>
        <ng-template #spinner>
            <p-progressSpinner></p-progressSpinner>
        </ng-template>
        </app-chart-item>

<!-- Bookings - Invoice Variance by Percentage Chart -->
            <app-chart-item
            *ngIf="config['bookingsInvoiceVarianceByPercentage'].showField"
            [header]="'Booking to Invoice Variance %'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(bookingsInvoiceVarianceByPercentage$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>
            
            <app-chart-item
            *ngIf="config['bookingVsDelByAccount'].showField"
            [header]="'As Sold vs As Delivered by Account'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(bookingVsDelByAccount$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>

            <app-chart-item
            *ngIf="config['bookingVsDelByItemType'].showField"
            [header]="'As Sold vs As Delivered by Scope'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(bookingVsDelByItemType$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>

            <app-chart-item
            *ngIf="config['bookingVsDelBySKU'].showField"
            [header]="'As Sold vs As Delivered by Short Scope'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(bookingVsDelBySKU$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>

            <app-chart-item
            *ngIf="config['grossMarginPercentByAccount'].showField"
            [header]="'Gross Margin % by Account'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(grossMarginPercentByAccount$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>

            <app-chart-item
            *ngIf="config['grossMarginPercentByItemType'].showField"
            [header]="'Gross Margin % by Scope'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(grossMarginPercentByItemType$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>

            <app-chart-item
            *ngIf="config['pacingMarginPercentByAccount'].showField"
            [header]="'Pacing Margin % by Account'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(pacingMarginPercentByAccount$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>

            <app-chart-item
            *ngIf="config['pacingMarginPercentByItemType'].showField"
            [header]="'Pacing Margin % by Scope'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(pacingMarginPercentByItemType$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>

            <app-chart-item
            *ngIf="config['cmPercentVarAsSoldByAccount'].showField"
            [header]="'CM % Variance to As Sold by Account'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(cmPercentVarAsSoldByAccount$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>

            <app-chart-item
            *ngIf="config['cmPercentVarAsSoldByItemType'].showField"
            [header]="'CM % Variance to As Sold by Scope'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(cmPercentVarAsSoldByItemType$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>

            <app-chart-item
            *ngIf="config['grossVsPacingMarginPercentByAccount'].showField"
            [header]="'Gross Vs Pacing Margin %  by Account'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(grossVsPacingMarginPercentByAccount$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>

            <app-chart-item
            *ngIf="config['grossVsPacingMarginPercentByItemType'].showField"
            [header]="'Gross Vs Pacing Margin % by Scope'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(grossVsPacingMarginPercentByItemType$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item>

            <app-chart-item
            *ngIf="config['pricingAnalytics'].showField"
            [header]="'Pricing Analytics'"
            [chartData]="pricingAnalytics$ | async"
            (pricingFilterChange)="updatePricingChart($event)">
            <ng-container *ngIf="(pricingAnalytics$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem"
                (defaultDataChange)="setDefaultFilters($event)">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
        </app-chart-item>

           <!--app-chart-item
            *ngIf="config['stackedRevenueByProduct'].showField"
            [header]="'stackedRevenueByProduct'"
            (periodChange)="updateChart($event)">
            <ng-container *ngIf="(stackedRevenueByProduct$ | async) as chartItem; else spinner">
                <app-chart-body [chartData]="chartItem">
                </app-chart-body>
            </ng-container>
            <ng-template #spinner>
                <p-progressSpinner></p-progressSpinner>
            </ng-template>
            </app-chart-item-->

            
            
        </ng-container>
        
    </div>
</div>

