export const DASHBOARD_COLUMNS = [
	{
		field: 'quoteName',
		header: 'Quote',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'quote',
		width: '200px'
	},
	{
		field: 'opportunity',
		header: 'Opportunity',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'quote',
		header: 'Quote',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'order',
		header: 'Order',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'invoice',
		header: 'Invoice',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	/*  {
		  field: 'recognised Revenue',
		  header: 'Recognised Revenue',
		  showField: true,
		  drag: true,
		  alignment: 'center',
		  display: 'table-cell',
		  type: 'line',
	  },*/

	{
		field: 'revenueEnding',
		header: 'Revenue Ending',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},

	{
		field: 'deals',
		header: 'Deals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'contracts',
		header: 'Contracts',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'size',
		header: 'Size',
		subHeader: 'Deal',
		subHeader2: 'Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'size',
	},
	{
		field: 'owner',
		header: 'Owner',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
	},
	{
		field: 'guidance',
		header: 'Guidance',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'discount',
		header: 'Discount%',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'revTransfers',
		header: 'Rev Transfers %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'margin',
		header: 'Margin %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'accountType',
		header: 'Account Type',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [{ label: 'Top', value: 'Top' }, { label: 'Strategic', value: 'Strategic' }]
	},
	{
		field: 'businessType',
		header: 'Business Type',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [
			{ label: 'New Business', value: 'New Business' },
			{ label: 'Renewals', value: 'Renewals' },
			{ label: 'Upsell/CrossSell', value: 'Upsell/CrossSell' },
			{ label: 'Chrun', value: 'Chrun' }
		]
	},
	{
		field: 'region',
		header: 'Region',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [
			{ label: 'America', value: 'America' },
			{ label: 'Germany', value: 'Germany' }
		]
	},
	{
		field: 'status',
		header: 'Status',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'status',
		options: [
			{ label: 'Invoice', value: 'Invoice' },
			{ label: 'Best Case', value: 'BestCase' },
			{ label: 'Complete', value: 'Complete' },
			{ label: 'Ordered', value: 'Ordered' },
		]
	},
	{
		field: 'channel',
		header: 'Channel',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [
			{ label: 'Direct', value: 'Direct' },
			{ label: 'Re-Seller', value: 'Re-Seller' },
		]
	},
	{
		field: 'win',
		header: 'Win %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'percent',
	},
	{
		field: 'forecast',
		header: 'Forecast',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'actuals',
		header: 'Actuals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenuePast',
		header: 'Past Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueCurrent',
		header: 'Current Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueFuture',
		header: 'Future Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcv',
		header: 'TCV',
		subHeader: 'Recurring',
		subHeader2: 'Non-Recurring',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'tcv',
		width: '180px'
	},
	{
		field: 'acv',
		header: 'Annualised ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear1',
		header: 'ACV Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear2',
		header: 'ACV Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear3',
		header: 'ACV Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear4',
		header: 'ACV Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear5',
		header: 'ACV Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear1',
		header: 'ARR Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear2',
		header: 'ARR Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear3',
		header: 'ARR Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear4',
		header: 'ARR Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear5',
		header: 'ARR Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedAcv',
		header: 'Allocated ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'mrr',
		header: 'MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedMrr',
		header: 'Allocated MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'rpo',
		header: 'RPO',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'atr',
		header: 'ATR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcl',
		header: 'Total Contract Length',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'grr',
		header: 'GRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'nrr',
		header: 'NRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	}
];

export const OPPORTUNITY_COLUMNS = [
	{
		field: 'account',
		header: 'Account',
		subHeader: 'Opportunity',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		width: '200px'
	},
	{
		field: 'opportunity',
		header: 'Opportunity',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'quote',
		header: 'Quote',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'order',
		header: 'Order',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'invoice',
		header: 'Invoice',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'revenueEnding',
		header: 'Revenue Ending',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'deals',
		header: 'Deals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'contracts',
		header: 'Contracts',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'size',
		header: 'Size',
		subHeader: 'Deal',
		subHeader2: 'Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'size',
	},
	{
		field: 'owner',
		header: 'Owner',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
	},
	{
		field: 'guidance',
		header: 'Guidance',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'discount',
		header: 'Discount%',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'revTransfers',
		header: 'Rev Transfers %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'margin',
		header: 'Margin %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'accountType',
		header: 'Account Type',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [{ label: 'Top', value: 'Top' }, { label: 'Strategic', value: 'Strategic' }]
	},
	{
		field: 'businessType',
		header: 'Business Type',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [
			{ label: 'New Business', value: 'New Business' },
			{ label: 'Renewals', value: 'Renewals' },
			{ label: 'Upsell/CrossSell', value: 'Upsell/CrossSell' },
			{ label: 'Chrun', value: 'Chrun' }
		]
	},
	{
		field: 'region',
		header: 'Region',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [
			{ label: 'America', value: 'America' },
			{ label: 'Germany', value: 'Germany' }
		]
	},
	{
		field: 'status',
		header: 'Status',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'status',
		options: [
			{ label: 'Invoice', value: 'Invoice' },
			{ label: 'Best Case', value: 'BestCase' },
			{ label: 'Complete', value: 'Complete' },
			{ label: 'Ordered', value: 'Ordered' },
		]
	},
	{
		field: 'channel',
		header: 'Channel',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [
			{ label: 'Direct', value: 'Direct' },
			{ label: 'Re-Seller', value: 'Re-Seller' },
		]
	},
	{
		field: 'win',
		header: 'Win %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'percent',
	},
	{
		field: 'forecast',
		header: 'Forecast',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'actuals',
		header: 'Actuals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenuePast',
		header: 'Past Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueCurrent',
		header: 'Current Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueFuture',
		header: 'Future Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcv',
		header: 'TCV',
		subHeader: 'Recurring',
		subHeader2: 'Non-Recurring',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'tcv',
		width: '180px'
	},
	{
		field: 'acv',
		header: 'Annualised ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear1',
		header: 'ACV Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear2',
		header: 'ACV Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear3',
		header: 'ACV Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear4',
		header: 'ACV Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear5',
		header: 'ACV Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear1',
		header: 'ARR Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear2',
		header: 'ARR Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear3',
		header: 'ARR Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear4',
		header: 'ARR Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear5',
		header: 'ARR Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedAcv',
		header: 'Allocated ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'mrr',
		header: 'MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedMrr',
		header: 'Allocated MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'rpo',
		header: 'RPO',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'atr',
		header: 'ATR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcl',
		header: 'Total Contract Length',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'grr',
		header: 'GRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'nrr',
		header: 'NRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	}
];

export const ACCOUNT_COLUMNS = [
	{
		field: 'account',
		header: 'Account',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		width: '200px'
	},
	{
		field: 'opportunity',
		header: 'Opportunity',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'quote',
		header: 'Quote',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'order',
		header: 'Order',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'invoice',
		header: 'Invoice',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'revenueEnding',
		header: 'Revenue Ending',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'deals',
		header: 'Deals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'contracts',
		header: 'Contracts',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'size',
		header: 'Size',
		subHeader: 'Deal',
		subHeader2: 'Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'size',
	},
	{
		field: 'owner',
		header: 'Owner',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
	},
	{
		field: 'guidance',
		header: 'Guidance',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'discount',
		header: 'Discount%',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'revTransfers',
		header: 'Rev Transfers %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'margin',
		header: 'Margin %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'accountType',
		header: 'Account Type',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [{ label: 'Top', value: 'Top' }, { label: 'Strategic', value: 'Strategic' }]
	},
	{
		field: 'forecast',
		header: 'Forecast',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'actuals',
		header: 'Actuals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenuePast',
		header: 'Past Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueCurrent',
		header: 'Current Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueFuture',
		header: 'Future Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcv',
		header: 'TCV',
		subHeader: 'Recurring',
		subHeader2: 'Non-Recurring',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'tcv',
		width: '180px'
	},
	{
		field: 'acv',
		header: 'Annualised ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear1',
		header: 'ACV Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear2',
		header: 'ACV Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear3',
		header: 'ACV Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear4',
		header: 'ACV Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear5',
		header: 'ACV Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear1',
		header: 'ARR Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear2',
		header: 'ARR Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear3',
		header: 'ARR Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear4',
		header: 'ARR Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear5',
		header: 'ARR Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedAcv',
		header: 'Allocated ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'mrr',
		header: 'MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedMrr',
		header: 'Allocated MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'rpo',
		header: 'RPO',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'atr',
		header: 'ATR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcl',
		header: 'Total Contract Length',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'grr',
		header: 'GRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'nrr',
		header: 'NRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	}
];

export const PRODUCT_COLUMNS = [
	{
		field: 'product',
		header: 'Product',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		width: '200px'
	},
	{
		field: 'opportunity',
		header: 'Opportunity',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'quote',
		header: 'Quote',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'order',
		header: 'Order',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'invoice',
		header: 'Invoice',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'revenueEnding',
		header: 'Revenue Ending',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'deals',
		header: 'Deals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'contracts',
		header: 'Contracts',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'size',
		header: 'Size',
		subHeader: 'Deal',
		subHeader2: 'Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'size',
	},
	{
		field: 'guidance',
		header: 'Guidance',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'discount',
		header: 'Discount%',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'revTransfers',
		header: 'Rev Transfers %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'margin',
		header: 'Margin %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'forecast',
		header: 'Forecast',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'actuals',
		header: 'Actuals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenuePast',
		header: 'Past Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueCurrent',
		header: 'Current Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueFuture',
		header: 'Future Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcv',
		header: 'TCV',
		subHeader: 'Recurring',
		subHeader2: 'Non-Recurring',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'tcv',
		width: '180px'
	},
	{
		field: 'acv',
		header: 'Annualised ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear1',
		header: 'ACV Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear2',
		header: 'ACV Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear3',
		header: 'ACV Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear4',
		header: 'ACV Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear5',
		header: 'ACV Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear1',
		header: 'ARR Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear2',
		header: 'ARR Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear3',
		header: 'ARR Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear4',
		header: 'ARR Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear5',
		header: 'ARR Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedAcv',
		header: 'Allocated ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'mrr',
		header: 'MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedMrr',
		header: 'Allocated MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'rpo',
		header: 'RPO',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'atr',
		header: 'ATR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcl',
		header: 'Total Contract Length',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'grr',
		header: 'GRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'nrr',
		header: 'NRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	}
];

export const SALES_COLUMNS = [
	{
		field: 'account',
		header: 'Account',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		width: '200px'
	},
	{
		field: 'opportunity',
		header: 'Opportunity',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'quote',
		header: 'Quote',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'order',
		header: 'Order',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'invoice',
		header: 'Invoice',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'deals',
		header: 'Deals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'contracts',
		header: 'Contracts',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'size',
		header: 'Size',
		subHeader: 'Deal',
		subHeader2: 'Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'size',
	},
	{
		field: 'owner',
		header: 'Owner',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
	},
	{
		field: 'guidance',
		header: 'Guidance',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'discount',
		header: 'Discount%',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'revTransfers',
		header: 'Rev Transfers %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'forecast',
		header: 'Forecast',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'actuals',
		header: 'Actuals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenuePast',
		header: 'Past Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueCurrent',
		header: 'Current Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueFuture',
		header: 'Future Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcv',
		header: 'TCV',
		subHeader: 'Recurring',
		subHeader2: 'Non-Recurring',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'tcv',
		width: '180px'
	},
	{
		field: 'acv',
		header: 'Annualised ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear1',
		header: 'ACV Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear2',
		header: 'ACV Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear3',
		header: 'ACV Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear4',
		header: 'ACV Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear5',
		header: 'ACV Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear1',
		header: 'ARR Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear2',
		header: 'ARR Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear3',
		header: 'ARR Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear4',
		header: 'ARR Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear5',
		header: 'ARR Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedAcv',
		header: 'Allocated ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'mrr',
		header: 'MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedMrr',
		header: 'Allocated MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'rpo',
		header: 'RPO',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'atr',
		header: 'ATR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcl',
		header: 'Total Contract Length',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'grr',
		header: 'GRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'nrr',
		header: 'NRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	}
];

export const REGION_COLUMNS = [
	{
		field: 'region',
		header: 'Region',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
	},
	{
		field: 'opportunity',
		header: 'Opportunity',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'quote',
		header: 'Quote',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'order',
		header: 'Order',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'invoice',
		header: 'Invoice',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'revenueEnding',
		header: 'Revenue Ending',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'deals',
		header: 'Deals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'contracts',
		header: 'Contracts',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'size',
		header: 'Size',
		subHeader: 'Deal',
		subHeader2: 'Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'size',
	},
	{
		field: 'guidance',
		header: 'Guidance',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'discount',
		header: 'Discount%',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'revTransfers',
		header: 'Rev Transfers %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'margin',
		header: 'Margin %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'region',
		header: 'Region',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [
			{ label: 'America', value: 'America' },
			{ label: 'Germany', value: 'Germany' }
		]
	},
	{
		field: 'forecast',
		header: 'Forecast',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'actuals',
		header: 'Actuals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenuePast',
		header: 'Past Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueCurrent',
		header: 'Current Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueFuture',
		header: 'Future Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcv',
		header: 'TCV',
		subHeader: 'Recurring',
		subHeader2: 'Non-Recurring',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'tcv',
		width: '180px'
	},
	{
		field: 'acv',
		header: 'Annualised ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear1',
		header: 'ACV Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear2',
		header: 'ACV Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear3',
		header: 'ACV Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear4',
		header: 'ACV Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear5',
		header: 'ACV Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear1',
		header: 'ARR Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear2',
		header: 'ARR Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear3',
		header: 'ARR Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear4',
		header: 'ARR Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear5',
		header: 'ARR Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedAcv',
		header: 'Allocated ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'mrr',
		header: 'MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedMrr',
		header: 'Allocated MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'rpo',
		header: 'RPO',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'atr',
		header: 'ATR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcl',
		header: 'Total Contract Length',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'grr',
		header: 'GRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'nrr',
		header: 'NRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	}
];

export const CHANNEL_COLUMNS = [
	{
		field: 'channel',
		header: 'Channel',
		filter: false,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [
			{ label: 'Direct', value: 'Direct' },
			{ label: 'Re-Seller', value: 'Re-Seller' },
		]
	},
	{
		field: 'opportunity',
		header: 'Opportunity',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'quote',
		header: 'Quote',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'order',
		header: 'Order',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'invoice',
		header: 'Invoice',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'deals',
		header: 'Deals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'contracts',
		header: 'Contracts',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'size',
		header: 'Size',
		subHeader: 'Deal',
		subHeader2: 'Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'size',
	},
	{
		field: 'owner',
		header: 'Owner',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
	},
	{
		field: 'guidance',
		header: 'Guidance',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'discount',
		header: 'Discount%',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'revTransfers',
		header: 'Rev Transfers %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'margin',
		header: 'Margin %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'channel',
		header: 'Channel',
		filter: true,
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		options: [
			{ label: 'Direct', value: 'Direct' },
			{ label: 'Re-Seller', value: 'Re-Seller' },
		]
	},
	{
		field: 'forecast',
		header: 'Forecast',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'actuals',
		header: 'Actuals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenuePast',
		header: 'Past Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueCurrent',
		header: 'Current Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueFuture',
		header: 'Future Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcv',
		header: 'TCV',
		subHeader: 'Recurring',
		subHeader2: 'Non-Recurring',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'tcv',
		width: '180px'
	},
	{
		field: 'acv',
		header: 'Annualised ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear1',
		header: 'ACV Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear2',
		header: 'ACV Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear3',
		header: 'ACV Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear4',
		header: 'ACV Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear5',
		header: 'ACV Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear1',
		header: 'ARR Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear2',
		header: 'ARR Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear3',
		header: 'ARR Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear4',
		header: 'ARR Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear5',
		header: 'ARR Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedAcv',
		header: 'Allocated ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'mrr',
		header: 'MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedMrr',
		header: 'Allocated MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'rpo',
		header: 'RPO',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'atr',
		header: 'ATR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcl',
		header: 'Total Contract Length',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'grr',
		header: 'GRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'nrr',
		header: 'NRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	}
];

export const PRODUCT_FAMILY_COLUMNS = [
	{
		field: 'productFamily',
		header: 'Product Family',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'text',
		width: '200px'
	},
	{
		field: 'opportunity',
		header: 'Opportunity',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'quote',
		header: 'Quote',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'order',
		header: 'Order',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'invoice',
		header: 'Invoice',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'line',
	},
	{
		field: 'revenueEnding',
		header: 'Revenue Ending',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'deals',
		header: 'Deals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'contracts',
		header: 'Contracts',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'deal',
	},
	{
		field: 'size',
		header: 'Size',
		subHeader: 'Deal',
		subHeader2: 'Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'size',
	},
	{
		field: 'guidance',
		header: 'Guidance',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'discount',
		header: 'Discount%',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'revTransfers',
		header: 'Rev Transfers %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'margin',
		header: 'Margin %',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'color',
	},
	{
		field: 'forecast',
		header: 'Forecast',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'actuals',
		header: 'Actuals',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenuePast',
		header: 'Past Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueCurrent',
		header: 'Current Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'revenueFuture',
		header: 'Future Revenue',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcv',
		header: 'TCV',
		subHeader: 'Recurring',
		subHeader2: 'Non-Recurring',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'tcv',
		width: '180px'
	},
	{
		field: 'acv',
		header: 'Annualised ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear1',
		header: 'ACV Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear2',
		header: 'ACV Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear3',
		header: 'ACV Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear4',
		header: 'ACV Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'acvYear5',
		header: 'ACV Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear1',
		header: 'ARR Year 1',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear2',
		header: 'ARR Year 2',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear3',
		header: 'ARR Year 3',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear4',
		header: 'ARR Year 4',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'arrYear5',
		header: 'ARR Year 5',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedAcv',
		header: 'Allocated ACV',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'mrr',
		header: 'MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'allocatedMrr',
		header: 'Allocated MRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'rpo',
		header: 'RPO',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'atr',
		header: 'ATR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'currency',
	},
	{
		field: 'tcl',
		header: 'Total Contract Length',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'grr',
		header: 'GRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	},
	{
		field: 'nrr',
		header: 'NRR',
		showField: true,
		drag: true,
		alignment: 'center',
		display: 'table-cell',
		type: 'number',
	}
];

export const DASHBOARD_DATA = [
	{
		account: 'ABC Labs Inc.',
		opportunity: {
			status: 'GREEN',
			date: '2 Jan 2023',
			amount: 200000
		},
		quote: {
			status: 'RED',
			date: '7 Jan 2023',
			amount: 200000,
			leakage: 0
		},
		order: {
			status: 'RED',
			date: '15 Jan 2023',
			amount: 150000,
			leakage: 50000
		},
		invoice: {
			status: 'GREEN',
			date: '1 Mar 2023',
			amount: 150000,
			pending: 100000
		},
		deals: {
			number: 'DN001',
			date: '7 Jan 2023'
		},
		contracts: {
			number: 'RC001',
			date: '15 Feb 2023'
		},
		size: 150000,
		owner: 'James Sayer',
		guidance: 3,
		discount: 25,
		revTransfers: 15,
		margin: 55,
		accountType: 'Top',
		businessType: 'New Business',
		region: 'America',
		status: 'Best Case',
		channel: 'Re-Seller',
		win: 'Won',
		forecast: 200000,
		actuals: 150000,
		revenueCurrent: 25000,
		revenuePast: 250000,
		revenueFuture: 100000
	},
	{
		account: 'ABC Labs Inc.',
		opportunity: {
			status: 'GREEN',
			date: '2 Jan 2023',
			amount: 200000
		},
		quote: {
			status: 'RED',
			date: '7 Jan 2023',
			amount: 200000,
			leakage: 0
		},
		order: {
			status: 'RED',
			date: '15 Jan 2023',
			amount: 150000,
			leakage: 50000
		},
		invoice: {
			status: 'GREEN',
			date: '1 Mar 2023',
			amount: 150000,
			pending: 0
		},
		deals: {
			number: 'DN001',
			date: '7 Jan 2023'
		},
		contracts: null,
		size: 150000,
		owner: 'James Sayer',
		guidance: 3,
		discount: 25,
		revTransfers: 15,
		margin: 55,
		accountType: 'Strategic',
		businessType: 'Renewals',
		region: 'Germany',
		status: 'Complete',
		channel: 'Direct',
		win: 'Won',
		forecast: 200000,
		actuals: 150000,
		revenueCurrent: 25000,
		revenuePast: 250000,
		revenueFuture: 100000
	}
];

/*export const LINE_CIRCLE_COLUMNS = {
	opportunity: {
	summary: [
	  { field: 'opportunityName', header: 'Opportunity Name' },
	  { field: 'opportunityNumber', header: 'Opportunity Number' },
	  { field: 'opportunityValue', header: 'Opportunity Value' },
	  { field: 'status', header: 'Status' },
	  { field: 'winPercentage', header: 'Win %' },
	  { field: 'quoteNumber', header: 'Quote Number' },
	  { field: 'customerName', header: 'Customer Name' },
	  { field: 'stageWise', header: 'Stage Wise' },
	],
	line: [
	  { field: 'lineNo', header: 'Line No.' },
	  { field: 'product', header: 'Product' },
	  { field: 'lineAmt', header: 'Line Amt' },
	  { field: 'bookingsAmt', header: 'Bookings Amt' },
	  { field: 'allocationAmt', header: 'Allocation Amt' },
	  { field: 'startDate', header: 'Start Date' },
	  { field: 'endDate', header: 'End Date' },
	  { field: 'revenueBeg', header: 'Revenue Beg' },
	  { field: 'revenueCurrent', header: 'Revenue Current' },
	  { field: 'unAmortizedAmt', header: 'Un-Amortized Amt' },
	],
	quote: {
		summary: [
		  { field: 'quoteName', header: 'Quote Name' },
		  { field: 'quoteNumber', header: 'Quote Number' },
		  { field: 'quoteValue', header: 'Quote Value' },
		  { field: 'currency', header: 'Currency' },
		  { field: 'region', header: 'Region' },
		],
		line: [
		  { field: 'quoteName', header: 'Quote Name' },
		  { field: 'quoteNumber', header: 'Quote Number' },
		  { field: 'quoteValue', header: 'Quote Value' },
		  { field: 'currency', header: 'Currency' },
		  { field: 'region', header: 'Region' },
		  { field: 'customerName', header: 'Customer Name' },
		  { field: 'contractNumber', header: 'Contract Number' },
		  { field: 'salesRep', header: 'Sales Rep' },
		  { field: 'winPercentage', header: 'Win %' },
		],
	  },
	  order: {
		summary: [
		  { field: 'orderNumber', header: 'Order Number' },
		  { field: 'orderAmount', header: 'Order Amount' },
		  { field: 'orderDate', header: 'Order Date' },
		  { field: 'currency', header: 'Currency' },
		],
		line: [
		  { field: 'orderDate', header: 'Order Date' },
		  { field: 'orderNumber', header: 'Order Number' },
		  { field: 'orderAmount', header: 'Order Amount' },
		  { field: 'currency', header: 'Currency' },
		  { field: 'contractNumber', header: 'Contract Number' },
		  { field: 'leakage', header: 'Leakage' },
		  { field: 'customerName', header: 'Customer Name' },
		],
	  },
	  invoice: {
		summary: [
		  { field: 'invoiceNumber', header: 'Invoice Number' },
		  { field: 'invoiceValue', header: 'Invoice Value' },
		  { field: 'currency', header: 'Currency' },
		  { field: 'invoiceDate', header: 'Invoice Date' },
		],
		line: [
		  { field: 'billtolocation', header: 'Bill to Location ' },
		  { field: 'currency', header: 'Currency' },
		  { field: 'invoiceValue', header: 'Invoice Value' },
		  { field: 'customerName', header: 'Customer Name' },
		  { field: 'pendingBillings', header: 'Pending Billings' },
		  { field: 'noofinvoices', header: 'Number of invoices' },
		],
	  },
	  contract: {
		summary: [
		  { field: 'contractName', header: 'Contract Name' },
		  { field: 'contractNumber', header: 'Contract Number' },
		  { field: 'contractAmount', header: 'Contract Amount' },
		  { field: 'contractDate', header: 'Contract Date' },
		],
		line: [
		  { field: 'contractName', header: 'Contract Name' },
		  { field: 'contractNumber', header: 'Contract Number' },
		  { field: 'contractAmount', header: 'Contract Amount' },
		  { field: 'customerName', header: 'Customer Name' },
		  { field: 'contractDate', header: 'Contract Date' },
		  { field: 'contractStatus', header: 'Contract Status' },
		],
	},
};*/
export const LINE_CIRCLE_COLUMNS = {
	opportunity: {
		summary: [
			// { field: 'opportunityName', header: 'Opportunity Name' },
			// { field: 'opportunityNumber', header: 'Opportunity Number' },
			// { field: 'opportunityValue', header: 'Opportunity Value' },
			// { field: 'status', header: 'Status' },
			// { field: 'winPercentage', header: 'Win (%)' },
			// { field: 'quoteNumber', header: 'Quote Number' },
			// { field: 'customerName', header: 'Customer Name' },
			// { field: 'stageWise', header: 'Stage Wise' },
		],
		line: [
			// { field: 'lineNo', header: 'Line No.' },
			// { field: 'amt', header: 'Amount' },
			// { field: 'date', header: 'Date' },
			// { field: 'status', header: 'Status' },
			// { field: 'startDate', header: 'Start Date' },
			// { field: 'endDate', header: 'End Date' },
			// { field: 'past', header: 'Past' },
			// { field: 'months', header: '12 Months' },
			// { field: 'future', header: 'Future' },
			// //{ field: 'unAmortizedAmt', header: 'Un-Amortized Amt' },
		],
		sampleData: {
			summary: {
				opportunityName: 'Software Upgrade',
				opportunityNumber: 'OPP-2023-001',
				opportunityValue: 500000,
				status: 'In Progress',
				winPercentage: 75,
				quoteNumber: 'Q-2023-001',
				customerName: 'Big Corp Inc.',
				stageWise: 'Negotiation',
			},
			line: [
				{
					lineNo: 1,
					//product: 'Software License A',
					lineAmt: 300000,
					status: 'negative',
					allocationAmt: 150000,
					startDate: '2023-10-01',
					endDate: '2024-09-30',
					past: 0,
					months: 2000,
					future: 225000,
				},
			],
		},
	},

	quote: {
		summary: [
			// { field: 'quoteName', header: 'Quote Name' },
			// { field: 'quoteNumber', header: 'Quote Number' },
			{ field: 'quoteValue', header: 'Quote Value', type: 'number' },
			{ field: 'quoteDate', header: 'Quote Date' },
			{ field: 'customerName', header: 'Customer Name' },
			{ field: 'region', header: 'Region' },
			{ field: 'currency', header: 'Currency' },
			{ field: 'contractNumber', header: 'Contract Number (RC)' },
			{ field: 'salesRep', header: 'Sales Rep' },
			{ field: 'winPercent', header: 'Win (%)' },
			{ field: 'dealGuidance', header: 'Deal Guidance', type: 'symbol' },
			{ field: 'revenueGuidance', header: 'Revenue Guidance', type: 'symbol' },
			{ field: 'marginGuidance', header: 'Margin Guidance', type: 'symbol' },
			{ field: 'discountGuidance', header: 'Discount Guidance', type: 'symbol' },

			// { field: 'summary guidance', header: 'Summary Guidances' },
		],
		line: [
			// { field: 'lineNo', header: 'Line No.' },
			// { field: 'product', header: 'Product' },
			// { field: 'lineAmt', header: 'Line Amt' },
			// { field: 'startDate', header: 'Start Date' },
			// { field: 'endDate', header: 'End Date' },
			// { field: 'past', header: 'Past' },
			// { field: 'future', header: 'Future' },
			// { field: 'allocationAmt', header: 'Allocation Amt' },
			// { field: 'months', header: '12 Months' },
			// { field: 'future', header: 'Future' },
		],
		sampleData: {
			summary: {
				quoteName: 'Software Bundle',
				quoteNumber: 'Q-2023-002',
				quoteValue: 250000,
				quoteDate: 2 - 12 - 2023,
				customerName: 'Big Corp Inc.',
				region: 'North America',
				currency: 'USD',
				contractNumber: 'RC-2023-002',
				salesRep: 'Jane Doe',
				winPercentage: 80,
				guidance: 'High Probability',
			},
			line: [
				{
					lineNo: 1,
					product: 'Software License B',
					lineAmt: 200000,
					startDate: '2023-11-01',
					endDate: '2024-10-31',
					past: '30 Days',
					future: '365 Days',
					allocationAmt: 200000,
				},
			],
		},
	},

	order: {
		summary: [
			{ field: 'orderNumber', header: 'Order Number' },
			{ field: 'orderName', header: 'Order Name' },
			{ field: 'orderedDate', header: 'Order Date' },
			{ field: 'orderAmount', header: 'Order Value', type: 'number' },
			{ field: 'currency', header: 'Currency' },
			{ field: 'contractNumber', header: 'Contract Number (RC)' },
			// { field: 'shipToLocation', header: 'Ship to Location' },
			{ field: 'revenueBeg', header: ' Begin Revenue ', type: 'number' },
			{ field: 'revenueCurrent', header: 'Revenue Current', type: 'number' },
			//{ field: 'revenueBeg', header: 'Revenue Beg' ,type: 'number'},
			{ field: 'revenueEnd', header: 'Revenue End', type: 'number' },
			{ field: 'delAmount', header: 'Delivery Amount', type: 'number' },
			{ field: 'customerName', header: 'Customer Name' },
		],
		line: [
			{ field: 'orderlineNo', header: ' Order Line No', type: 'alphanum' },
			{ field: 'parentLineNumber', header: 'Parent Line Number' },
			{ field: 'dealLineNumber', header: 'Deal Line Number' },
			{ field: 'product', header: 'Product Name', type: 'alphanum' },
			{ field: 'delAmount', header: 'Delivered Amount', type: 'number' },
			{ field: 'bookingAmount', header: 'Booked Amount', type: 'number' },
			{ field: 'allocationAmount', header: 'Allocation Amount', type: 'number' },
			// { field: 'delAmount', header: 'Del. Amount' },
			// { field: 'startDate', header: 'Start Date' },
			// { field: 'endDate', header: 'End Date' },
			{ field: 'revenueBeg', header: 'Revenue Begining Balance ', type: 'number' },
			{ field: 'revenueCurrent', header: 'Revenue Current Balance', type: 'number' },
			{ field: 'unAmortizedAmt', header: 'Un-Amortized Amount', type: 'number' },

		],
		sampleData: {
			summary: {
				orderNumber: 'ORD-2023-001',
				orderDate: '2023-10-15',
				orderAmount: 150000,
				currency: 'USD',
				contractNumber: 'RC-2023-003',
				shipToLocation: 'Big Corp HQ',
				revenueBeg: 10000,
				revenueCurrent: 50000,
				delAmount: 140000,
				customerName: 'Big Corp Inc.',
			},
			line: [
				{
					lineNo: 1,
					product: 'Software Implementation',
					delAmt: 150000,
					bookingsAmt: 150000,
					allocationAmt: 75000,
					startDate: '2023-10-20',
					endDate: '2024-03-20',
					revenueBeg: 5000,
					revenueCurrent: 25000,
					unAmortizedAmt: 100000,
				},
				{
					lineNo: 2,
					product: 'Hardware Setup',
					delAmt: 50000,
					bookingsAmt: 50000,
					allocationAmt: 25000,
					startDate: '2023-11-01',
					endDate: '2023-12-31',
					revenueBeg: 2000,
					revenueCurrent: 10000,
					unAmortizedAmt: 30000,
				},
				{
					lineNo: 2,
					product: 'Hardware Setup',
					delAmt: 50000,
					bookingsAmt: 50000,
					allocationAmt: 25000,
					startDate: '2023-11-01',
					endDate: '2023-12-31',
					revenueBeg: 2000,
					revenueCurrent: 10000,
					unAmortizedAmt: 30000,
				},
				{
					lineNo: 2,
					product: 'Hardware Setup',
					delAmt: 50000,
					bookingsAmt: 50000,
					allocationAmt: 25000,
					startDate: '2023-11-01',
					endDate: '2023-12-31',
					revenueBeg: 2000,
					revenueCurrent: 10000,
					unAmortizedAmt: 30000,
				},
				{
					lineNo: 2,
					product: 'Hardware Setup',
					delAmt: 50000,
					bookingsAmt: 50000,
					allocationAmt: 25000,
					startDate: '2023-11-01',
					endDate: '2023-12-31',
					revenueBeg: 2000,
					revenueCurrent: 10000,
					unAmortizedAmt: 30000,
				},
				{
					lineNo: 2,
					product: 'Hardware Setup',
					delAmt: 50000,
					bookingsAmt: 50000,
					allocationAmt: 25000,
					startDate: '2023-11-01',
					endDate: '2023-12-31',
					revenueBeg: 2000,
					revenueCurrent: 10000,
					unAmortizedAmt: 30000,
				},
				{
					lineNo: 2,
					product: 'Hardware Setup',
					delAmt: 50000,
					bookingsAmt: 50000,
					allocationAmt: 25000,
					startDate: '2023-11-01',
					endDate: '2023-12-31',
					revenueBeg: 2000,
					revenueCurrent: 10000,
					unAmortizedAmt: 30000,
				},

				{
					lineNo: 2,
					product: 'Hardware Setup',
					delAmt: 50000,
					bookingsAmt: 50000,
					allocationAmt: 25000,
					startDate: '2023-11-01',
					endDate: '2023-12-31',
					revenueBeg: 2000,
					revenueCurrent: 10000,
					unAmortizedAmt: 30000,
				},
				{
					lineNo: 2,
					product: 'Hardware Setup',
					delAmt: 50000,
					bookingsAmt: 50000,
					allocationAmt: 25000,
					startDate: '2023-11-01',
					endDate: '2023-12-31',
					revenueBeg: 2000,
					revenueCurrent: 10000,
					unAmortizedAmt: 30000,
				}
			],
		},
	},

	invoice: {
		summary: [
			// { field: 'invoiceNumber', header: 'Invoice Number' },
			{ field: 'invoiceValue', header: 'Invoice Value', type: 'number' },
			{ field: 'currency', header: 'Currency' },
			{ field: 'billToLocation', header: 'Bill to Location' },
			{ field: 'pendingBills', header: 'Pending Billings', type: 'digits' },
			{ field: 'numberOfInvoices', header: 'Number of Invoices' },
			{ field: 'customerName', header: 'Customer Name' },

		],
		line: [
			{ field: 'invoiceNumber', header: 'Invoice Number', type: 'alphanum' },
			{ field: 'invoiceLineNumber', header: 'Invoice Line Number', type: 'alphanum' },
			{ field: 'invoiceDate', header: 'InvoiceDate', type: 'date' },
			{ field: 'invoiceValue', header: 'Invoice Value', type: 'number' },
			{ field: 'productName', header: 'Product Name', type: 'alphanum' },
		],

	},

	contracts: {
		summary: [
			{ field: 'bookings', header: 'Bookings', type: 'number' },
			{ field: 'del', header: 'Delivered', type: 'number' },
			{ field: 'billed', header: 'Billed', type: 'number' },
			{ field: 'revrecog', header: 'Rev Recognition', type: 'number' },
			//   { field: 'contractNumber', header: 'Contract Number' },
			{ field: 'customerName', header: 'Customer Name' },
			{ field: 'region', header: 'Region' },
			{ field: 'salesRep', header: 'Sales Rep' },
			{ field: 'revenueGuidance', header: 'Revenue Guidance', type: 'symbol' },
			{ field: 'marginGuidance', header: 'Margin Guidance', type: 'symbol' },
			{ field: 'discountGuidance', header: 'Discount Guidance', type: 'symbol' },
			{ field: 'dealGuidance', header: 'Deal Guidance', type: 'symbol' },
			{ field: 'revenueContractValue', header: 'Contract Value', type: 'number' },
		],
		line: [
			{ field: 'lineNo', header: 'Line No.' },
			{ field: 'parentLineNumber', header: 'Parent Line Number' },
			{ field: 'productName', header: 'Product Name', type: 'alphanum' },
			{ field: 'lineAmount', header: 'Line Amt', type: 'number' },
			{ field: 'allocationAmount', header: 'Allocation Amt', type: 'number' },
			{ field: 'netPriceDiscount', header: 'Discount %', type: 'percent' },
			{ field: 'grossMarginPercent', header: 'Gross Margin %', type: 'percent' },
			{ field: 'allocationPercent', header: 'Allocation %', type: 'percent' },
			{ field: 'discountGuidance', header: 'Discount Guidance', type: 'symbol' },
			{ field: 'revenueGuidance', header: 'Revenue Guidance', type: 'symbol' },
			{ field: 'marginGuidance', header: 'Margin Guidance', type: 'symbol' },
			{ field: 'dealGuidance', header: 'Deal Guidance', type: 'symbol' },
			{ field: 'billed', header: 'Billed Amount', type: 'number' },
			{ field: 'delivered', header: 'Delivered Amount', type: 'number' },
			{ field: 'revenueRecognition', header: 'Revenue Recog Amount', type: 'number' }
		],

		sampleData: {
			summary: {
				bookings: 500000,
				del: 500000,
				billed: 500000,
				revrecog: 500000,
				contractNumber: '123456',
				customerName: 'Big Corp Inc.',
				region: 'North America',
				salesRep: 'John Doe',
				revenueGuidance: 'RED',
				marginGuidance: 'RED',
				discountGuidance: 'RED',
				dealGuidance: 'RED'
			},
			line: [
				{
					lineNo: 1,
					lineAmount: 500000,
					allocationAmount: 250000,
					netPriceDiscount: 10,
					grossMarginPercentage: 40,
					allocationPercent: 10,
					discountGuidance: 'RED',
					revenueGuidance: 'RED',
					marginGuidance: 'RED',
					dealGuidance: 'RED',
					billed: 200000,
					delivered: 1000,
					revenueRecognition: 150000
				},
			],
		},
	},
};