<ng-container *ngIf="(stages$ | async) as stages">
    <app-dynamic-table
            header="Stages"
            [tableData]="stages"
            [columns]="tableColumns2"
            [isLoading]="isLoading"
            [defaultSelectedIndex]="0"
           
           
            backLink="Opportunity"
            (toggleMaximize)="isFirstMaximized = $event"
            (rowSelect)="getStageDetails($event)"
            selectionMode="single"
            [customerName]="opportunityDetails?.customerName"
            [opportunityNumber]="opportunityDetails?.opportunityNumber"
            [opportunityName]="opportunityDetails?.opportunityName"
    >  
        <div
                projected-details
                class="header-details"  
        >
            <span class="toolbar-header" > Customer Name: {{ opportunityDetails.customerName }}</span>
            <span class="toolbar-header"> Opportunity Number: {{ opportunityDetails.opportunityNumber }}</span>
            <span class="toolbar-header"> Opportunity Name: {{ opportunityDetails.opportunityName }}</span>
        </div>
    </app-dynamic-table>
</ng-container>
<ng-container *ngIf="!isFirstMaximized">
    <p-tabView styleClass="new">
        <p-tabPanel header="Allocations">
            <app-dynamic-table
                    [tableData]="allocations"   
                    [columns]="allocationColumns2"
                    [isLoading]="isLoading"
                  
                    withTabs="true"
                    (toggleMaximize)="isSecondMaximized = $event"
            ></app-dynamic-table>
        </p-tabPanel>
        <p-tabPanel header="Forecasting">
            <app-dynamic-table
                    [tableData]="forecasts"
                    [columns]="forecastColumns"
                    [isLoading]="isLoading"
                   
                    withTabs="true"
                    (toggleMaximize)="isSecondMaximized = $event"
            ></app-dynamic-table>
        </p-tabPanel>
    </p-tabView>
</ng-container>