import {Component, Input, OnInit, Output, EventEmitter} from '@angular/core';
import {ChartData, ChartOptions} from 'chart.js';
import {BarDataGroup, ChartItemData, PieData, StackedBarData, StackedBarGMPMCMPData, PricingAnalytics} from '../../models/chart-data';
import { data } from 'jquery';

@Component({
    selector: 'app-chart-body',
    templateUrl: './chart-body.component.html',
    styles: [`
        :host {
            height: 100%;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    `]
})
export class ChartBodyComponent implements OnInit {
    @Input() chartData: ChartItemData;

    chartView: ChartData = {
        labels: [],
        datasets: []
    };

    chartOptions: ChartOptions;
    stackTypes: any[] = ['Book', 'Del.'];
    marginStackTypes: any[] = ['GM', 'PM'];
    groupMarginStack: boolean = false;
    dataLabels: string[] = [];

    @Output() defaultDataChange = new EventEmitter<any>();

    bookedDatasets: any[];
    dataSets: any[];

    barPalette = {
        bookingsValueBySKU: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        revenueByRegion: ['rgb(219,53,133)', 'rgb(0,141,213)', 'rgb(223,187,177)', 'rgb(245,100,118)', 'rgb(228,63,111)', 'rgb(89,13,34)'],
        revenueByAccount: ['rgb(94,252,141)', 'rgb(142,249,243)', 'rgb(147,190,223)', 'rgb(131,119,209)', 'rgb(109,90,114)', 'rgb(72,57,167)'],
        revenueBySalesRep: ['rgb(251,97,7)', 'rgb(243,222,44)', 'rgb(124,181,24)', 'rgb(92,128,1)', 'rgb(251,176,45)', 'rgb(200,128,4)'],
        revenueByQuarter: ['rgb(166,130,255)', 'rgb(113,90,255)', 'rgb(88,135,255)', 'rgb(85,193,255)', 'rgb(16,46,74)', 'rgb(22,63,100)'],
        invoiceByAccount: ['rgb(172,228,170)', 'rgb(225,205,181)', 'rgb(131,33,97)', 'rgb(218,65,103)', 'rgb(240,239,244)', 'rgb(90,83,121)'],
        bookingValue: ['rgb(172,228,170)', 'rgb(225,205,181)', 'rgb(131,33,97)', 'rgb(218,65,103)', 'rgb(240,239,244)', 'rgb(90,83,121)'],
        bookingVsDelValue: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        invoiceValue: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        revenueVsInvoiceValue: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        bookingsVsInvoiceValue: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        bookingsInvoiceVarianceByAmount: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        bookingsInvoiceVarianceByPercentage: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        bookingVsDelByAccount: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        bookingVsDelByItemType: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        bookingVsDelBySKU: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        grossMarginPercentByAccount: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        grossMarginPercentByItemType: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        pacingMarginPercentByAccount: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        pacingMarginPercentByItemType: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        cmPercentVarAsSoldByAccount: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        cmPercentVarAsSoldByItemType: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        grossVsPacingMarginPercentByAccount: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        grossVsPacingMarginPercentByItemType: ['rgb(95,90,247)', 'rgb(90,209,247)', 'rgb(160,85,226)', 'rgb(74,170,255)', 'rgb(117,197,56)', 'rgb(247,210,90)'],
        pricingAnalytics: ['rgb(234, 66, 92)', 'rgb(243,180,49)', 'rgb(1,144,49)'],
    };

    ngOnInit(): void {
        if (this.chartData) {
            this.buildChart();
            this.setChartOptions();
        }
    }
    setChartOptions() {
                let symbol ='$';
                if(this.chartData.displayAsPercentage){
                    symbol='%';
                }
                if (this.chartData.type === 'bar' || this.chartData.type === 'stacked-bar' || this.chartData.type === 'stacked-bar-percent'|| this.chartData.type === 'pricing') {
                    this.chartOptions = {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            xAxes: [{
                                //stacked: true,
                                gridLines: {
                                    drawOnChartArea: false,
                                },
                                offset: true,
                                ticks: {
                                    fontStyle: 'bold'
                                }
                            }],
                            yAxes: [{
                               // stacked: true,
                                ticks: {
                                    callback: (value: number): string | number | null | undefined => {
                                        const map = [
                                            {suffix: 'T', threshold: 1e12},
                                            {suffix: 'Bn', threshold: 1e9},
                                            {suffix: 'M', threshold: 1e6},
                                            {suffix: 'K', threshold: 1e3},
                                            {suffix: '', threshold: 1},
                                        ];
        
                                        const found = map.find((x) => Math.abs(value) >= x.threshold);
                                        if (found) {
                                            const formatted = (value / found.threshold).toFixed(0) + found.suffix;
                                        if(this.chartData.displayAsPercentage){
                                                symbol='%';
                                            return  formatted +  symbol;
                                        }
                                        return symbol + formatted;
                                    }
                                    if(this.chartData.displayAsPercentage){
                                        symbol='%';
                                    return  value+  symbol;
                                }
        
                                        
        
                                        return  symbol + value;
                                    }
                                }
                            }]
                        },
                        legend: {
                            display: true,
                            position: 'bottom',
                            labels: {
                                fontColor: '#7D7D7D',
                                fontSize: 9,
                                boxWidth: 7,
                                usePointStyle: true,
                            }
                        },
                        tooltips: {
                            callbacks: {
                                title(): string | string[] {
                                    return '';
                                },
                                label(tooltipItem, data): string | string[] {
                                    const label = data.datasets[tooltipItem.datasetIndex].label;
                                    
                                    
                                   // return label + ': $' + tooltipItem.value;
                                   return (symbol==='%') ? label + ': ' + tooltipItem.value+symbol :  label + ': ' +symbol+ tooltipItem.value;
                                    
                                }
                            }
                        }
                    };
                } else {
                    console.log("this.chartData.type=>"+this.chartData.type);
                    this.chartOptions = {
                        responsive: true,
                        legend: {
                            display: true,
                            position: 'right',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 7
                            }
                        },
                        cutoutPercentage: 90,
        
                    };
                }
        
            }

            formatNumber(value: number): string {
                const map = [
                    {suffix: 'T', threshold: 1e12},
                    {suffix: 'Bn', threshold: 1e9},
                    {suffix: 'M', threshold: 1e6},
                    {suffix: 'K', threshold: 1e3},
                    {suffix: '', threshold: 1},
                ];
            
                const found = map.find((x) => Math.abs(value) >= x.threshold);
                if (found) {
                    return (value / found.threshold).toFixed(2) + found.suffix;
                }
                return value.toString();
            }

    buildChart() {
        // Initial check for chartData
        if (!this.chartData || !this.chartData.data) {
            console.warn('Chart data is missing or invalid');
            this.chartView = { labels: [], datasets: [] };
            return;
        }

        try {
            switch (this.chartData.type) {
                case 'bar':
                    this.buildBarChart();
                    break;
                case 'pie':
                    this.buildPieChart();
                    break;
                case 'stacked-bar':
                    this.buildStackedBarChart();
                    break;
                case 'stacked-bar-percent':
                    this.buildStackedBarPercentChart();
                    break;
                case 'pricing':
                    this.buildPricingChart();
                    break;
                default:
                    console.warn(`Unsupported chart type: ${this.chartData.type}`);
                    this.chartView = { labels: [], datasets: [] };
            }
        } catch (error) {
            console.error('Error building chart:', error);
            this.chartView = { labels: [], datasets: [] };
        }
    }

     buildBarChart() {
        if (!this.chartData.data) {
            throw new Error('Bar chart data is missing');
        }

        try {
            const barEntries = Object.entries(this.chartData.data as BarDataGroup);
            const labels = Array.from(new Set([].concat(...barEntries.map(([, data]) => 
                data.map(bar => bar.periodValue)))));
            const chartHeader = this.chartData.header;

            this.chartView = {
                labels,
                datasets: barEntries.map(([label, dataset], index) => ({
                    data: dataset.map(bar => ({x: bar.periodValue, y: bar.value})),
                    label: label,
                    backgroundColor: this.getBarColor(chartHeader, index),
                    hoverBackgroundColor: this.getBarColor(chartHeader, index),
                    barPercentage: .5,
                    categoryPercentage: 1,
                    maxBarThickness: 8
                }))
            };
        } catch (error) {
            console.error('Error building bar chart:', error);
            throw error;
        }
    }

    buildPieChart() {
        if (!Array.isArray(this.chartData.data)) {
            throw new Error('Pie chart data is not an array');
        }

        try {
            const pieData = this.chartData.data as PieData[];
            const labels = pieData.map(item => item.guidance);
            const values = pieData.map(item => item.totalCount);

            this.chartView = {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: labels.map(label => this.getPieColor(label)),
                    borderWidth: 0
                }]
            };
        } catch (error) {
            console.error('Error building pie chart:', error);
            throw error;
        }
    }

    buildStackedBarChart() {
        if (!Array.isArray(this.chartData.data)) {
            console.warn('Stacked bar data is not an array');
            this.chartView = { labels: [], datasets: [] };
            return;
        }

        try {
            const stackedData = this.chartData.data as StackedBarData[];
            
            if (!stackedData || stackedData.length === 0) {
                console.warn('Stacked data is empty');
                this.chartView = { labels: [], datasets: [] };
                return;
            }

            const uniquePeriods = Array.from(new Set(stackedData.map(item => item.periodValue)));
            const uniqueAccounts = Array.from(new Set(stackedData.map(item => Object.values(item)[0])));
            this.dataLabels = [];

            uniquePeriods.forEach(item => {
                this.stackTypes.forEach(x => {
                    this.dataLabels.push(`${x} ${item}`);
                });
            });

            const datasets = uniqueAccounts.map((account, index) => ({
                label: account,
                data: this.dataLabels.map(period => {
                    const booked = stackedData.find(
                        item => 
                            `Book ${item.periodValue}` === period && 
                            Object.values(item)[0] === account
                    );
                    const delivered = stackedData.find(
                        item => 
                            `Del. ${item.periodValue}` === period && 
                            Object.values(item)[0] === account
                    );

                    return booked?.bookedAmount || delivered?.deliveredAmount || 0;
                }),
                backgroundColor: this.getBarColor(this.chartData.header, index),
                borderColor: '#White'
            }));

            this.chartView = {
                labels: this.dataLabels,
                datasets: datasets
            };
        } catch (error) {
            console.error('Error building stacked bar chart:', error);
            this.chartView = { labels: [], datasets: [] };
        }
    }

    buildStackedBarPercentChart() {
        if (!Array.isArray(this.chartData.data)) {
            console.warn('Stacked bar percent data is not an array');
            this.chartView = { labels: [], datasets: [] };
            return;
        }

        try {
            const stackedData = this.chartData.data as StackedBarGMPMCMPData[];
            const uniquePeriods = Array.from(new Set(stackedData.map(item => item.periodValue)));
            const uniqueAccounts = Array.from(new Set(stackedData.map(item => Object.values(item)[0])));
           const dataLabels : string[] = []; 

            // Check if it's a group margin stack
            const firstItem = stackedData[0];
            const isGroupMarginStack = firstItem && 
                Object.keys(firstItem)[2] === 'grossMarginPercent' && 
                Object.keys(firstItem)[3] === 'pacingMarginPercent';

            if (isGroupMarginStack) {
                uniquePeriods.forEach(item => {
                    ['GM', 'PM'].forEach(x => {
                        dataLabels.push(`${x} ${item}`);
                    });
                });

                const datasets = uniqueAccounts.map((account, index) => ({
                    label: account,
                    data: dataLabels.map(period => {
                        const [type, itemPeriod] = period.split(' ');
                        const item = stackedData.find(
                            d => d.periodValue === itemPeriod && 
                            Object.values(d)[0] === account
                        );
                        return type === 'GM' ? 
                            item?.grossMarginPercent || 0 : 
                            item?.pacingMarginPercent || 0;
                    }),
                    backgroundColor: this.getBarColor(this.chartData.header, index),
                    borderColor: '#White'
                }));

                this.chartView = {
                    labels: dataLabels,
                    datasets: datasets
                };
            } else {
                uniquePeriods.forEach(item => {
                    dataLabels.push(item);
                });

                const datasets = uniqueAccounts.map((account, index) => ({
                    label: account,
                    data: dataLabels.map(period => {
                        const item = stackedData.find(
                            d => d.periodValue === period && 
                            Object.values(d)[0] === account
                        );
                        return item?.grossMarginPercent || 
                               item?.pacingMarginPercent || 
                               (item?.cmPercentVarAsSold ?? 0);
                    }),
                    backgroundColor: this.getBarColor(this.chartData.header, index),
                    borderColor: '#White'
                }));
    
                this.chartView = {
                    labels: dataLabels,
                    datasets: datasets
                };
            }
        } catch (error) {
            console.error('Error building stacked bar percent chart:', error);
            this.chartView = { labels: [], datasets: [] };
        }
    }
    buildPricingChart() {
        if (!Array.isArray(this.chartData.data)) {
            console.warn('Pricing data is not an array');
            this.chartView = { labels: [], datasets: [] };
            return;
        }
    
        try {
            const pricingData = this.chartData.data as PricingAnalytics[];
    
            // Emit default data if available
            if (pricingData.length > 0) {
                const defaultItem = pricingData[0];
                this.defaultDataChange.emit({
                    productName: defaultItem.productName,
                    region: defaultItem.region,
                    segment: defaultItem.segment,
                    term: defaultItem.term
                });
            }
    
            // Define fixed quantity ranges
            const labels = ['1-100', '101-250', '251-500', '500-10000'];
    
            this.chartView = {
                labels: labels,
                datasets: [
                    {
                        label: 'Min Price',
                        data: labels.map(range => {
                            const item = pricingData.find(d => d.quantityRange === range);
                            return item?.minPrice || 0;
                        }),
                        backgroundColor: this.getBarColor('pricingAnalytics', 0),
                        hoverBackgroundColor: this.getBarColor('pricingAnalytics', 0),
                        barPercentage: 0.9,
                        categoryPercentage: 0.8,
                        maxBarThickness: 60,
                         stack: 'Stack 0'
                    },
                    {
                        label: 'Average Price',
                        data: labels.map(range => {
                            const item = pricingData.find(d => d.quantityRange === range);
                            return item?.avgPrice || 0;
                        }),
                        backgroundColor: this.getBarColor('pricingAnalytics', 1),
                        hoverBackgroundColor: this.getBarColor('pricingAnalytics', 1),
                        barPercentage: 0.9,
                        categoryPercentage: 0.8,
                        maxBarThickness: 60,
                        stack: 'Stack 0'
                    },
                    {
                        label: 'Max Price',
                        data: labels.map(range => {
                            const item = pricingData.find(d => d.quantityRange === range);
                            return item?.maxPrice || 0;
                        }),
                        backgroundColor: this.getBarColor('pricingAnalytics', 2),
                        hoverBackgroundColor: this.getBarColor('pricingAnalytics', 2),
                        barPercentage: 0.9,
                        categoryPercentage: 0.8,
                        maxBarThickness: 60,
                        stack: 'Stack 0'
                    }
                ]
            };
        } catch (error) {
            console.error('Error building pricing chart:', error);
            this.chartView = { labels: [], datasets: [] };
        }
    }

    getPieColor(color: 'RED' | 'YELLOW' | 'GREEN') {
                switch (color) {
                    case 'YELLOW': {
                        return '#FF7D31';
                    }
                    case 'RED': {
                        return '#E22D2D';
                    }
                    case 'GREEN': {
                        return '#75C538';
                    }
                }
            }
        
            getBarColor(name: string, index: number, opacity = 1) {
                const rgb = this.barPalette[name][index];
                return rgb ? `${rgb.slice(0, -1)}, ${opacity})` : `${'rgb(95,90,247)'.slice(9, -1)}, ${opacity}))`;
            
            }
        }