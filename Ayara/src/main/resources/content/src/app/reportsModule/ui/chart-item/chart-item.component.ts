import {Component, EventEmitter, Input, OnInit, Output, OnChanges, SimpleChanges} from '@angular/core';
import {PeriodParameter} from '../../models/chart-data';
import {FilterConfigService} from '../../data-access/filter-config.service';
 import { AnalyticalDashboardService } from '../../data-access/analytical-dashboard.service';
interface PeriodOption {
    label: string;
    value: PeriodParameter;
}

@Component({
    selector: 'app-chart-item',
    templateUrl: './chart-item.component.html',
    styleUrls: ['./chart-item.component.scss']
})
export class ChartItemComponent implements OnInit {
    @Input() header: string;
    @Input() chartData: any;
    @Output() pricingFilterChange = new EventEmitter<any>();
    @Output() periodChange = new EventEmitter<{ name: string, period: PeriodParameter }>();

    periodOptions: PeriodOption[] = [{label: 'Month', value: 'PTD'}, {label: 'Quarter', value: 'QTD'}, {label: 'Year', value: 'YTD'}];
    selectedOption: PeriodOption = {label: 'Month', value: 'PTD'};
    isBar = true;
    isPricing = false;
    productSuggestions: string[] = [];
    regionSuggestions: string[] = [];
    segmentSuggestions: string[] = [];
    termOptions: any[] = [];

    filters = {
        productName: '',
        region: '',
        segment: '',
        term: ''
    };
    valueResults: string[] = [];
    selectedProduct: any = {};
    selectedRegion: any = {};
    selectedSegment: any = {};

    constructor( private dashboardService: AnalyticalDashboardService) {
    }

    ngOnInit(): void {
        this.isBar = !this.header.includes('deals');
        console.log('header',this.header);
        this.isPricing = this.header === 'Pricing Analytics';
        console.log('isproving',this.isPricing);
        if (this.isPricing){
            this.loadTerms();
            this.setInitialValues();
            
        } 
    }
    ngOnChanges(changes: SimpleChanges) {
        if (changes.chartData && this.isPricing && this.chartData?.data) {
            this.setInitialValues();
    
    }
}
    setInitialValues() {
        if (this.chartData?.data && Array.isArray(this.chartData.data) && this.chartData.data.length > 0) {
            const defaultData = this.chartData.data[0];
            
            // Set filters
            this.filters = {
                productName: defaultData.productName || '',
                region: defaultData.region || '',
                segment: defaultData.segment || '',
                term: defaultData.term || ''
            };

            // Set suggestions
            this.productSuggestions = [defaultData.productName];
            this.regionSuggestions = [defaultData.region];
            this.segmentSuggestions = [defaultData.segment];
            //this.termOptions= [defaultData.term];

            if (this.termOptions.length === 0) {
                this.loadTerms();
            } else {
                // If terms are already loaded, format the term properly
                const foundTerm = this.termOptions.find(t => t.value === defaultData.term);
                if (foundTerm) {
                    this.filters.term = foundTerm; // Set the whole term object
                }
            }
        
            
            console.log('Set default values:', this.filters);
        }
    }
    setDefaultFilters(data: any) {
        if (data) {
            this.filters = {
                productName: data.productName || '',
                region: data.region || '',
                segment: data.segment || '',
                term: data.term || ''
            };
            if (data.term && this.termOptions.length > 0) {
                const termOption = this.termOptions.find(t => t.value === data.term);
                if (termOption) {
                    this.filters.term = termOption;  // Set the whole option object
                }
            }
            
            // Also update suggestions
            this.productSuggestions = [data.productName];
            this.regionSuggestions = [data.region];
            this.segmentSuggestions = [data.segment];
            
        }
    }

    
    loadTerms() {
        this.dashboardService.getTerms().subscribe(
            terms => {
                this.termOptions = terms.map(term => ({
                    label: term,
                    value: term
                }));
                if (this.filters.term) {
                    const defaultTerm = this.termOptions.find(t => t.value === this.filters.term);
                    if (defaultTerm) {
                        this.filters.term = defaultTerm;
                    }
                }
        
            },
            error => {
                console.error('Error loading terms:', error);
                this.termOptions = []; // Reset on error
            }
        );
    }

    onPeriodChange() {
        this.periodChange.emit({name: this.header, period: this.selectedOption.value});
    }
    

    
    searchValues(parameterName: string, event: any) {
        if (event.query.length >= 3) {
            this.dashboardService.getParameterValues(parameterName, event.query)
                .subscribe(results => {
                    switch(parameterName) {
                        case 'PRODUCT_NAME':
                            this.productSuggestions = results;
                            break;
                        case 'REGION':
                            this.regionSuggestions = results;
                            break;
                        case 'SEGMENT':
                            this.segmentSuggestions = results;
                            break;
                    }
                });
        }
    }
    // onTermChange(event: any) {
    //     // Set the term value directly, not the whole object
    //     this.filters.term = event.value;
    //     this.onFilterChange();
    // }




    // onFilterChange() {
    //     if (this.filters.productName && this.filters.region && 
    //         this.filters.segment && this.filters.term) {
    //         this.dashboardService.getPriceAnalytics(this.filters)
    //             .subscribe(data => {
    //                 this.pricingFilterChange.emit({
    //                     name: this.header,
    //                     data: data
    //                 });
    //             });
    //     }
    // }
    onFilterChange() {
        if (this.filters.productName && 
            this.filters.region && 
            this.filters.segment && 
            this.filters.term) {
            this.pricingFilterChange.emit({
                // filters: {
                //     // productName: this.filters.productName,
                //     // region: this.filters.region,
                //     // segment: this.filters.segment,
                //     // term: this.filters.term
                    
                // }
                filters: this.filters
            });
        }
    }


}
