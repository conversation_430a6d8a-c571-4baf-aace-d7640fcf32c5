<div class="card-wrapper">
    <div class="control-panel">
        <div class="control-panel__container">
            <div class="control-panel__controls">
                <div class="control-panel__parameter" *ngIf="isTableView">

                    <span>Roll-Up Parameter</span>
                    <p-dropdown
                            class="dashboard-dropdown parameter"
                            [options]="rollUpParameters"
                            [(ngModel)]="selectedParameter"
                            (onChange)="onChangeParameter($event.value)"
                            optionLabel="name"
                    ></p-dropdown>
                </div>
				<div class="search__container" *ngIf="isTableView">
                    <!--<form (ngSubmit)="search()">-->
                        <input pinputtext class="textbox" name="searchKey" id="searchKey"
                               [(ngModel)]="searchTerm" 
                               placeholder="Search here..." value=""/>
                        <a class="globalSearchBtn">
                            <em class="fa fa-search" aria-hidden="true" (click)="onSearch()"></em>
                        </a>
						<a *ngIf="isSearched" class="globalSearchBtnx" (click)="onResetGlobalSearch()">
                            <em class="fa fa-times" aria-hidden="true"></em>
                        </a>
                    <!--</form>-->
                </div>
                <ng-container *ngIf="!isTableView">
                    <div class="control-panel__controls">
                        <div
                                class="controls-item"
                                (click)="openTilesFilter()">
                            <img
                                    src="../../../assets/icons/plus-circle.svg"
                                    alt="Tiles">
                            <span>Select Tiles</span>
                        </div>
                        <div
                                class="controls-item"
                                (click)="openChartsFilter()">
                            <img
                                    src="../../../assets/icons/plus-circle.svg"
                                    alt="Charts">
                            <span>Select Charts</span>
                        </div>
                    </div>
                </ng-container>
            </div>

            <div class="control-panel__controls">
                <div
                        class="controls-item"
                        *ngIf="isTableView"
                        (click)="redirectToReport()">
                    <img
                            src="../../../assets/icons/research.svg"
                            alt="Forecasting view">
                    <span>Forecasting view</span>
                </div>
                <div
                        class="controls-item"
                        (click)="toggleView()">
                    <img
                            src="../../../assets/icons/material-flip.svg"
                            alt="Flip">
                    <span>Flip</span>
                </div>
                <ng-container *ngIf="isTableView">
                    <div
                            class="controls-item"
                            (click)="openCalendar()">
                        <img
                                src="../../../assets/icons/date-range.svg"
                                alt="Date">
                        <span>Date</span>
                    </div>
                    <div
                            class="controls-item"
                            (click)="openExport()">
                        <img
                                src="../../../assets/icons/material-import-export.svg"
                                alt="Export">
                        <span>Export</span>
                    </div>
                    <div
                            class="controls-item"
                            (click)="openFilter()">
                        <img
                                src="../../../assets/icons/feather-filter.svg"
                                alt="Filter">
                        <span>Filter</span>
                    </div>
                </ng-container>

                <div
                        class="controls-item"
                        (click)="openSave()">
                    <img
                            src="../../../assets/icons/feather-save.svg"
                            alt="Save">
                    <span>Save</span>
                </div>
                <div
                        class="controls-item"
                        (click)="openSelect()">
                    <img
                            src="../../../assets/icons/material-select-all.svg"
                            alt="Select">
                    <span>Select</span>
                </div>
            </div>
        </div>
    </div>
    <div class="dashboard-container">

        <ng-container *ngIf="isTableView">
            <app-dashboard-table
                    [tableData]="tableData"
                    [columns]="tableColumns"
                    [filterOptions]="filterOptions"
                    (loadData)="loadTableData($event)"
                    [isLoading]="isLoading"
                    [selectedParameter]="selectedParameter"
            ></app-dashboard-table>
        </ng-container>

        <ng-container *ngIf="!isTableView">
            <app-dashboard-charts></app-dashboard-charts>
            <ng-container *ngIf="(chartsConfig$ | async) as charts">
                <app-dashboard-filter-modal
                        [data]="charts"
                        [isVisible]="showChartsFiler"
                        (save)="saveFilteredCols()"
                        (close)="closeFilter()">
                </app-dashboard-filter-modal>
            </ng-container>

            <ng-container *ngIf="(totalsConfig$ | async) as totals">
                <app-dashboard-filter-modal
                        [data]="totals"
                        [isVisible]="showTilesFilter"
                        (save)="saveFilteredCols()"
                        (close)="closeFilter()">
                </app-dashboard-filter-modal>
            </ng-container>
        </ng-container>
    </div>

</div>

<p-dialog
        header="Export"
        [(visible)]="showExport"
        styleClass="dd-table-dialog export"
        [contentStyle]="{overflow: 'visible'}">
    <div class="d-flex align-items-center justify-content-between">
        <div class="col-auto">
            <div class="d-flex align-items-center">
                <span class="light-text">Page Range</span>
                <input
                        class="dd-input dd-input-pages"
                        type="number"
                        min="1"
                        [max]="data.length"
                        [(ngModel)]="exportPagesFrom">
                <span>to</span>
                <input
                        class="dd-input dd-input-pages"
                        type="number"
                        min="1"
                        [max]="data.length"
                        [(ngModel)]="exportPagesTo">
            </div>
        </div>
        <div class="col-sm-4">
            <div class="d-flex align-items-center justify-content-between">
                <span class="light-text">Export Type</span>
                <p-dropdown
                        class="dashboard-dropdown bordered w-100"
                        [options]="exportOptions"
                        optionLabel="name"
                        [(ngModel)]="selectedExportType">
                </p-dropdown>
            </div>
        </div>
        <button
                class="dd-primary-btn"
                (click)="onExport()">Export
        </button>
    </div>
</p-dialog>

<app-dashboard-filter-modal
        [data]="tableColumns"
        [isVisible]="showFilter"
        (save)="saveFilteredCols()"
        (close)="closeFilter()">
</app-dashboard-filter-modal>

<p-dialog
        header="Save"
        [(visible)]="showSave"
        styleClass="dd-table-dialog"
        [contentStyle]="{overflow: 'visible'}">
    <div class="container">
        <div class="row align-items-center justify-content-between">
            <div class="col-7">
                <div class="row justify-content-between align-items-center">
                    <span class="light-text">Choose name</span>
                    <input
                            class="w-75 dd-input"
                            type="text"
                            [(ngModel)]="chosenConfigName"
                            required>
                </div>
            </div>
            <button
                    class="dd-primary-btn"
                    (click)="onSave()">Save
            </button>
        </div>
    </div>
</p-dialog>
<p-dialog
        header="Select"
        [(visible)]="showSelect"
        styleClass="dd-table-dialog"
        [contentStyle]="{overflow: 'visible'}">
    <div class="container">
        <div class="row align-items-center justify-content-between">
            <div class="col-7">
                <div class="row">
                    <ng-container *ngIf="filterConfigList$ | async; let configs">
                        <p-dropdown
                                class="dashboard-dropdown bordered"
                                [options]="configs"
                                optionLabel="name"
                                placeholder="Select Filter Config"
                                [(ngModel)]="selectedConfig">
                        </p-dropdown>
                    </ng-container>
                </div>
            </div>
            <button
                    class="dd-primary-btn"
                    (click)="onSelect()">Apply
            </button>
        </div>
    </div>
</p-dialog>
<p-dialog
        header="Select Date Range"
        [(visible)]="showCalendar"
        styleClass="dd-table-dialog calendar"
        [contentStyle]="{overflow: 'visible'}">
    <div class="container">
        <p-calendar selectionMode="range"></p-calendar>
    </div>
</p-dialog>
