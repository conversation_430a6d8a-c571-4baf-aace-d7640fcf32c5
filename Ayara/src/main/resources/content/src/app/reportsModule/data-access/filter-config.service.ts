import {Injectable} from '@angular/core';
import {BehaviorSubject, Subject} from 'rxjs';
import {TotalItem} from '../models/total-data';

@Injectable({
    providedIn: 'root'
})
export class FilterConfigService {

    private _chartsConfigSubject$ = new BehaviorSubject<{ header: string, showField: boolean }[]>([
        {header: 'bookingsValueBySKU', showField: true},
        {header: 'revenueByRegion', showField: true},
        {header: 'revenueByAccount', showField: true},
        {header: 'revenueBySalesRep', showField: true},
        {header: 'revenueByQuarter', showField: true},
        {header: 'invoiceByAccount', showField: true},
        {header: 'dealsByDiscountGuidance', showField: true},
        {header: 'dealsByRevenueGuidance', showField: true},
        {header: 'dealsByMarginGuidance', showField: true},
        {header:  'bookingValue',showField:true},
        {header: 'bookingVsDelValue',showField:true},
        {header: 'invoiceValue',showField:true},
        {header: 'revenueVsInvoiceValue',showField:true},
        {header: 'bookingsVsInvoiceValue',showField:true},
        {header: 'bookingsInvoiceVarianceByAmount',showField:true},
        {header: 'bookingsInvoiceVarianceByPercentage',showField:true},
        {header:  'bookingVsDelByAccount',showField:true},
        {header:   'bookingVsDelByItemType',showField:true},
        {header:   'bookingVsDelBySKU',showField:true},
        {header:   'grossMarginPercentByAccount',showField:true},
        {header:   'grossMarginPercentByItemType',showField:true},
        {header:   'pacingMarginPercentByAccount',showField:true},
        {header:   'pacingMarginPercentByItemType',showField:true},
        {header:   'cmPercentVarAsSoldByAccount',showField:true},
        {header:   'cmPercentVarAsSoldByItemType',showField:true},
        {header:   'grossVsPacingMarginPercentByAccount',showField:true},
        {header:   'grossVsPacingMarginPercentByItemType',showField:true},
        {header:  'pricingAnalytics', showField:true},
      //  {header:  'stackedRevenueByProduct', showField:true},

    ]);

    private _totalsConfigSubject$ = new Subject<TotalItem[]>();

    chartsConfig$ = this._chartsConfigSubject$.asObservable();
    totalsConfig$ = this._totalsConfigSubject$.asObservable();


    addTotalsToConfig(totals: TotalItem[]) {
        this._totalsConfigSubject$.next(totals);
    }
}
