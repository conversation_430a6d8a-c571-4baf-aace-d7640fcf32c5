export interface Opportunity {
    
     //revenueContract: string;
    opportunityNumber: string;
    opportunityName: string;
    opportunityDate: string;
    customerName: string;
    stages: string;
    winPercent: string;
    opportunityAmount: number;
}

export interface OpportunityStagePayload {
    opportunityDate: number;
    opportunityName: string;
    opportunityNumber: string;
    winPercent: string;
    opportunityAmount: number;
    id: string;
    opportunityStageId: number;
    opportunityStage: string;
    stageName: string;
    stageDate: number;
    closeDate: number;
    stages: OpportunityStage[];
}

export interface OpportunityStage {
    opportunityStageId: number;
    opportunityDate: string;
    opportunityNumber: string;
    customerName: string;
    opportunityType: string;
    winPercent: string;
    opportunityStage: string;
    id: string;
    stageName: string;
    stageDate: string;
    closeDate: string;
    winPercentage: string;
    opportunityAmount: number;
    allocations: Allocation[];
    forecast: Forecast[];
}

export interface Allocation {
    opportunityStageLineId: number;
    opportunityStageId: number;
    opportunityNumber: string;
    opportunityName: string;
    opportunityType: string;
    opportunityStage: string;
    stageDate: string;
    closeDate: string;
    winPercent: string;
    opportunityAmount: number;
    customerNumber: string;
    region: string;
    currency: string;
    latestVersion: number;
    oppotunityLineNumber: string;
    productName: string;
    unitListPrice: number;
    unieNetPrice: number;
    bundleFlag: string;
    parentLineNumber: number;
    allocableAmount: number;
    quantity: number;
    extendedListAmount: number;
    extendedNetPrice: number;
    discountPercent: number;
    childListPrice: number;
    childSellPrice: number;
    sspPerUnit: number;
    sspLow: number;
    sspMax: number;
    sspAmount: number;
    allocationAmount: number;
    cvInOutAmount: number;
    unitCost: number;
    lineCost: number;
    grossMarginPercent: number;
    cvInOutPercent: number;
    allocationMarginPercent: number;
    accountingStandard: string;
    productGroup: string;
    productType: string;
    productLine: string;
    productFamily: string;
    revenueGuidance: string;
    marginGuidance: string;
    discountGuidance: string ;
    dealGuidance: string;
    comments: string;
    productNumber: string;
    sourceProductId: string;
}

export interface Forecast {
    
}


export interface PageResponse<T> {
    content: T[];
    pageable: {
      pageNumber: number;
      pageSize: number;
      sort: {
        sorted: boolean;
        unsorted: boolean;
        empty: boolean;
      };
      offset: number;
      unpaged: boolean;
      paged: boolean;
    };
    totalElements: number;
    totalPages: number;
    last: boolean;
    size: number;
    number: number;
    sort: {
      sorted: boolean;
      unsorted: boolean;
      empty: boolean;
    };
    first: boolean;
    numberOfElements: number;
    empty: boolean;
  }

