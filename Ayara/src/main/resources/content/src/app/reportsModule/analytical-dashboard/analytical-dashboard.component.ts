import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {
    ACCOUNT_COLUMNS,
    DASHBOARD_COLUMNS,
    DASHBOARD_DATA,
    OPPORTUNITY_COLUMNS,
    PRODUCT_COLUMNS,
    PRODUCT_FAMILY_COLUMNS,
    REGION_COLUMNS
} from '../dashboard-table/columns';
import {FilterParams} from '../models/filter-params';
import {Observable, Subscription} from 'rxjs';
import {FilterConfig} from '../models/filter-config';
import {Table} from 'primeng/table';
import {AnalyticalDashboardService} from '../data-access/analytical-dashboard.service';
import {LazyLoadEvent} from 'primeng/api';
import {TableData} from '../../shared/models/tableData';
import {Column} from '../../shared/models/column.interface';
import {TotalItem} from '../models/total-data';
import {RmanLookupsVService} from '../../rmanLookupsV/rmanLookupsVservice';
import {FilterConfigService} from '../data-access/filter-config.service';
import {Router} from '@angular/router';

//import { RmanArrgForecastingDetailReportService } from './rmanArrgForecastingDetailReportservice';

@Component({
    selector: 'app-analytical-dashboard1',
    templateUrl: './analytical-dashboard.component.html',
    styleUrls: ['./analytical-dashboard.component.scss']
})
export class AnalyticalDashboardComponent implements OnInit, OnDestroy {
    isTableView = true;
	//searchData: Data[] = [];
    searchTerm: string = '';
    page: number = 0;
    size: number = 10;
    sort: string = 'id,asc';
	filterParams: FilterParams = {};
    data = DASHBOARD_DATA;
	results: TableData<any>;
    tableData: TableData<any>;
         
    isLoading = false;
	isSearched: boolean = false;
    tableSize = 5;
    totalRecords = 0;
    tableColumns: Column[] = [];
    tableDataSubscription: Subscription;
    
	// Assume these filter options are available in your component
    /* accountType: string = '';
    businessType: string = '';
    region: string = '';
    status: string = '';
    channel: string = '';
	//contractNum: number = '';
	quoteName: string = ''; */

    rollUpParameters = [
        {name: 'Default', columns: DASHBOARD_COLUMNS},
        {name: 'Account', columns: ACCOUNT_COLUMNS},
        {name: 'Opportunity', columns: OPPORTUNITY_COLUMNS},
        {name: 'Region', columns: REGION_COLUMNS},
        // {name: 'Channel', columns: CHANNEL_COLUMNS},
        {name: 'Product', columns: PRODUCT_COLUMNS},
        {name: 'Product Family', columns: PRODUCT_FAMILY_COLUMNS},
        // {name: 'Sales rep', columns: SALES_COLUMNS}
    ];
    selectedParameter = {name: 'Default', columns: DASHBOARD_COLUMNS};
    selectedExportType = {name: 'XLS', code: 'xls'};

    filterOptions = {
        channel: [{label: '', value: ''}],
        businessType: [{label: '', value: ''}],
        accountType: [{label: '', value: ''}],
        region: [{label: '', value: ''}],
        status: [{label: '', value: ''}]
		};

    showExport = false;
    showFilter = false;
    showTilesFilter = false;
    showChartsFiler = false;
    showSave = false;
    showSelect = false;
    showCalendar = false;

    exportOptions = [
        {name: 'PDF', code: 'pdf'},
        {name: 'CSV', code: 'csv'},
        {name: 'XLS', code: 'xls'}
    ];
    exportPagesFrom = 1;
    exportPagesTo: number;
    exportColumns = this.tableColumns.map(col => ({title: col.header, dataKey: col.field}));

    chosenConfigName = '';
    filterConfigList$: Observable<FilterConfig[]>;
    selectedConfig: FilterConfig;

    @ViewChild('dt') dt: Table;
    totalsConfig$: Observable<TotalItem[]>;
    chartsConfig$: Observable<any>;
    //router: any;

    constructor(private dashboardService: AnalyticalDashboardService, private lookupService: RmanLookupsVService, private filterConfigService: FilterConfigService,private router: Router) {
    }

    ngOnInit(): void {
        this.tableColumns = [...this.selectedParameter.columns];
        this.filterConfigList$ = this.dashboardService.filterConfigList$;
        this.totalsConfig$ = this.filterConfigService.totalsConfig$;
        this.chartsConfig$ = this.filterConfigService.chartsConfig$;
        this.getTableFilterOptions();
		this.searchData();
    }

    toggleView() {
        this.isTableView = !this.isTableView;
    }

    loadTableData(params?: {
        event?: LazyLoadEvent, selectedFilters?: FilterParams
    }) {
        this.isLoading = true;
        const parameter = this.selectedParameter.name.toLowerCase() === 'default' ? 'quote' : this.selectedParameter.name.toLowerCase();
		
		this.filterParams = params?.selectedFilters || this.filterParams || {};

        const page = params?.event ? params?.event.first / params?.event.rows : 0;

        //console.log('Persisting filterParams:', this.filterParams); // Debugging log

        this.tableDataSubscription = this.dashboardService.getTableData(parameter, page, this.tableSize, this.filterParams).subscribe(
            (data: TableData<any>) => {
                this.isLoading = false;
                this.tableData = data;
		    }
        );
    }


    onChangeParameter(value: { name: string, columns: any[] }) {
        this.selectedParameter = value;
        this.loadTableData();
        this.tableColumns = [...value.columns];
    }

    async getTableFilterOptions() {

        const filters = await Promise.all([
                this.lookupService.getAllRmanLookupsV(null, {'lookupTypeName': 'DB_CHANNEL'}),
                this.lookupService.getAllRmanLookupsV(null, {'lookupTypeName': 'DB_BUSINESS_TYPE'}),
                this.lookupService.getAllRmanLookupsV(null, {'lookupTypeName': 'DB_REGION'}),
                this.lookupService.getAllRmanLookupsV(null, {'lookupTypeName': 'DB_ACCOUNT_TYPE'}),
                this.lookupService.getAllRmanLookupsV(null, {'lookupTypeName': 'DB_STATUS'})
			]
        );

        const [channel, businessType, region, accountType, status] = filters.map((items: any) => items.content.map(option => ({
            label: option.lookupCode,
            value: option.lookupCode
        })));

        this.filterOptions = {
            channel,
            businessType,
            accountType,
            region,
            status
        };
    }

    openFilter() {
        this.showFilter = true;
    }

    exportPdf() {
        // import('jspdf').then(jsp => {
        //     import('jspdf-autotable').then((x) => {
        //         const doc = new jsp.default('p', 'px', 'a4');
        //         (doc as any).autoTable(this.exportColumns, this.data);
        //         doc.save('products.pdf');
        //     });
        // });
    }

    exportExcel() {
        // import('xlsx').then(xlsx => {
        //     const worksheet = xlsx.utils.json_to_sheet(this.data);
        //     const workbook = {Sheets: {'data': worksheet}, SheetNames: ['data']};
        //     const excelBuffer: any = xlsx.write(workbook, {bookType: 'xlsx', type: 'array'});
        //     this.saveAsExcelFile(excelBuffer, 'dashboard');
        // });
    }

    saveAsExcelFile(buffer: any, fileName: string): void {
        // const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
        // const EXCEL_EXTENSION = '.xlsx';
        // const data: Blob = new Blob([buffer], {
        //     type: EXCEL_TYPE
        // });
        // FileSaver.saveAs(data, fileName + '_export_' + new Date().getTime() + EXCEL_EXTENSION);
    }

    closeFilter() {
        this.showFilter = false;
        this.showTilesFilter = false;
        this.showChartsFiler = false;
    }

    openExport() {
        this.showExport = true;
    }

    onExport() {
        const type = this.selectedExportType.code;
        if (type === 'xls') {
            this.exportExcel();
        } else if (type === 'pdf') {
        } else {
            this.dt.exportCSV();
        }
    }


    saveFilteredCols() {
        this.closeFilter();
    }

    openSave() {
        this.showSave = true;
    }

    onSave() {
        const name = this.chosenConfigName;
        const filterConfig = {
            name,
            columns: [...this.tableColumns]
        };
        this.dashboardService.saveFilterConfig(filterConfig);
        this.chosenConfigName = '';
        this.showSave = false;
    }

    openSelect() {
        this.showSelect = true;
    }

    onSelect() {
        this.tableColumns = this.selectedConfig.columns;
        this.showSelect = false;
    }

    openCalendar() {
        this.showCalendar = true;
    }

    openTilesFilter() {
        this.showTilesFilter = true;
    }

    openChartsFilter() {
        this.showChartsFiler = true;
    }
    redirectToReport() {
        this.router.navigate(['/reports','operational','rmanArrgForecastingDetailReport']);
    }

    ngOnDestroy(): void {
        this.tableDataSubscription.unsubscribe();
    }
	
	
  searchData() {
  this.filterParams = this.filterParams || {};
  this.searchTerm = this.searchTerm || ''; 
 
  const hasFilters = Object.entries(this.filterParams).some(([key, value]) => value !== null && value !== undefined && value !== '');


  if (this.searchTerm.trim() !== '' || hasFilters) {
    this.isLoading = true;
        const parameter = this.selectedParameter.name.toLowerCase() === 'default' ? 'quote' : this.selectedParameter.name.toLowerCase();
    this.dashboardService.searchData(parameter, this.searchTerm.trim(), this.page, this.size, this.filterParams)
      .subscribe((data: TableData<any>) => {
                this.isLoading = false;
                this.tableData = data;
				this.isSearched = true;
           }
      , error => {
        console.error('Error fetching data', error);
      }
	  );
  }
  }

  onSearch(): void {
    this.page = 0; // Reset to first page on new search
    this.searchData();
	this.isSearched = true;
  }
  
  onResetGlobalSearch(): void {
        this.searchTerm = '';
		this.loadTableData();
		this.isSearched = false;
		
		this.filterParams = {}; // Reset filters
        this.page = 0; // Reset to first page
    }
}
