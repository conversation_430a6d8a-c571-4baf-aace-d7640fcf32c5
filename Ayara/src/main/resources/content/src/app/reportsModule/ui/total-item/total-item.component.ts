import {Component, Input} from '@angular/core';
import {TotalItem} from '../../models/total-data';

@Component({
  selector: 'app-total-item',
  templateUrl: './total-item.component.html',
  styleUrls: ['./total-item.component.scss']
})
export class TotalItemComponent {
  @Input() total: TotalItem;

  totalIconsMap = {
    totalQuoteValue: '../../../../assets/icons/awesome-file-invoice-dollar.svg',
    totalOpportunityAmt: '../../../../assets/icons/awesome-search-dollar.svg',
    totalMarginValue: '../../../../assets/icons/metro-dollar2.svg',
    totalMarginPercent: '../../../../assets/icons/awesome-percentage.svg',
    dealsCount: '../../../../assets/icons/awesome-handshake.svg',
  };

}
