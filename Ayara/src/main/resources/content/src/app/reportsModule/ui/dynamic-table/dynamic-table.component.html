<div class='table-dashboard'>
    <app-table-toolbar
            *ngIf="showToolbar"
            [header]="header"
            [withTabs]="withTabs"
            [tabsCount]="tabsCount"
            [withSearch]="withSearch"
            [withRefresh]="withRefresh"
            [columns]="columns"
            [backLink]="backLink"
            [selectedColumns]="selectedColumns"
            (columnsChange)="onColumnsChange($event)"
            (exportCsv)="dt.exportCSV()"
            (refresh)="onRefresh()"
            (search)="onSearch($event)"
    >
    </app-table-toolbar>
    <!-- <ng-content
            select="[projected-details]"
            ngProjectAs="details"
    ></ng-content> -->
    <ng-content
    select="[projected-details]"
    
></ng-content>
    <div class="table-container">
        <p-table
                class="dashboard__table"
                id="dashboard-table"
                #dt
                [value]="tableData ? tableData.content : []"
                [loading]="isLoading"
                [scrollable]="true"
                [paginator]="true"
                [lazy]="true"
                (onLazyLoad)="onLazyLoad($event)"
                [selection]="selectedRow"
                [rows]="tableData ? tableData.size : 0"
                [showCurrentPageReport]="true"
                currentPageReportTemplate="{first} - {last} of {totalRecords}"
                [totalRecords]="tableData ?.totalElements"
                [columns]="columns"
                [showPageLinks]="true"
                [showJumpToPageDropdown]="false"
                (onRowSelect)="onRowSelect($event)"
                [selectionMode]="selectionMode"
        >
            <ng-template
                    pTemplate="colgroup"
                    let-columns
            >
                <colgroup>
                    <ng-container *ngFor="let col of columns">
                        <ng-container *ngIf="col.showField">
                            <col style="min-width: 200px">
                        </ng-container>
                    </ng-container>
                </colgroup>
            </ng-template>
            <ng-template
                    pTemplate="header"
                    let-columns
            >

                <tr (click)="$event.preventDefault()">
                    <ng-container *ngFor="let col of columns">
                        <ng-container *ngIf="col.showField">
                            <th
                                    style="min-width: 200px"
                                    [class]="{'triangle': col.type === 'line'}"
                                    [style.text-align]="col.alignment"
                            >
                                {{ col.header }}
                            </th>
                        </ng-container>
                    </ng-container>
                </tr>

            </ng-template>

            <ng-template
                    pTemplate="body"
                    let-rowData
                    let-columns='columns'
            >
                <tr [pSelectableRow]="rowData" [style.cursor]="selectionMode ? 'pointer' : 'default'">

                    <ng-container *ngFor="let col of columns;">

                        <ng-container *ngIf="col.showField">

                            <td *ngIf="col.type === 'text'">
                                {{ rowData[col.field]  }}
                            </td>
                           
                            <td *ngIf="col.type === 'date'">
                                {{ rowData[col.field] | date: 'MM/dd/yyyy' }}
                            </td>

                           
                            <td *ngIf="col.type === 'link'">
                                <a [routerLink]="rowData[col.field]">{{ rowData[col.field] }}</a>
                            </td>

                            <td *ngIf="col.type === 'event'">
                                <a [routerLink]="rowData[col.field]">{{ rowData[col.field] }}</a>
                            </td>

                            <td *ngIf="col.type === 'status'">
                                {{ rowData[col.field] }}
                            </td>

                            <td *ngIf="col.type === 'number'">
                                {{ rowData[col.field] | number }}
                            </td>

                            <td *ngIf="col.type === 'currency'">
                                {{rowData[col.field] | currency:'USD': 'symbol':'1.0'}}
                            </td>

                            <td *ngIf="col.type === 'name'" style="text-align: left;">
                                {{rowData[col.field]}}
                            </td>

                            <td *ngIf="col.type === 'amount'" style="text-align: right;">
                                 ${{rowData[col.field] | round}}
                            </td>

                            <td *ngIf="col.type === 'percent'">
                                {{rowData[col.field] ? (rowData[col.field] | round) + '%' : ''}}
                            </td>
                            <!-- <td *ngIf="col.type === 'symbol'">
								<span>
				                    <i class="fa fa-circle" [ngClass]="{
				                      'red-dot': rowData[col.field] === 'RED',
				                      'green-dot': rowData[col.field] === 'GREEN', 
				                      'yellow-dot': rowData[col.field] === 'YELLOW'
				                    }"></i>
				                 </span>
                            </td> -->

                               <td *ngIf="col.type === 'symbol'">
                                <span *ngIf="rowData[col.field]">
                                    <i class="fa fa-circle" [ngClass]="{
                                        'red-dot': rowData[col.field] === 'RED',
                                        'green-dot': rowData[col.field] === 'GREEN', 
                                        'yellow-dot': rowData[col.field] === 'YELLOW'
                                    }"></i>
                                </span>
                            </td>


                        </ng-container>
                    </ng-container>
                </tr>
            </ng-template>

            <ng-template
                    pTemplate="emptymessage"
                    let-columns
            >
                <tr>
                    <td colspan="5">No records found.</td>
                </tr>
            </ng-template>
        </p-table>

    </div>
</div>

