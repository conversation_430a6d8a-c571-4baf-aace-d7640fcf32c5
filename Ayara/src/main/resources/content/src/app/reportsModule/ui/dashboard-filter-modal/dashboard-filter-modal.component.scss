@import "../../../../assets/sass/variables";

:host::ng-deep .dd-table-dialog {
  max-height: 570px;
  min-width: 300px;
  width: 100%;

  &.export {
    max-width: 650px;
  }

  &.calendar {
    width: auto;
  }

  .p-dialog-header {
    color: $flowerBlueColor;
    background: none;
    border-bottom: 2px solid $DDTableBorderColor;
  }

  .p-dialog-title {
    font-size: 14px;
    font-weight: 700;
  }

  .dialog-subtitle {
    margin-top: 10px;
    font-weight: 400;
  }

  .p-dialog-header-close {
    background: $flowerBlueColor;
    width: 18px;
    height: 18px !important;
    border-radius: 50%;

    &-icon {
      color: #fff;
      font-size: 10px;
    }
  }

  .p-dialog-footer {
    padding: 20px;
    box-shadow: none;
    background: none;
    border-top: 2px solid $DDTableBorderColor;
  }
}

.dd-primary-btn {
  margin: 0 10px;
  cursor: pointer;
  width: 80px;
  background: $flowerBlueColor;
  border-radius: 20px;
  color: #fff;
  font-size: 12px;

  &:hover {
    background: $DDTableHeaderTextColor;
  }


  &.filter-btn {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 75px;
    height: 22px !important;
    font-size: 10px;
  }

  &.add {
    &:hover {
      background: none;
      border: 1px solid $flowerBlueColor;
      color: $flowerBlueColor;
    }
  }

  &.remove {
    background: $grayBgColor;

    &:hover {
      background: $redColor;
    }
  }
}

.dd-secondary-btn {
  margin: 0;
  cursor: pointer;
  background: none;
  color: $DDTableTextColor;
  font-weight: 700;
}

.dd-primary-link {
  margin: 0;
  cursor: pointer;
  background: none;
  color: $flowerBlueColor;
  font-weight: 700;
}

.dd-input {
  border-radius: 10px;
  padding: 2px;
  background: $DDTableBgColor;
  border: 1px solid $DDTableBorderColor;

  &-pages {
    text-align: center;
    margin: 0 5px;
    width: 35px;
    height: 20px;

    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
}

.filter-container {
  margin: 0;
}

.filter-items {
  max-height: 300px;
  overflow: auto;
  margin: 20px 0;

  &::-webkit-scrollbar-track {
    background: $DDTableBorderColor;
  }

  &::-webkit-scrollbar-thumb {
    background: $flowerBlueColor;
  }
}

.filter-item {
  input {
    width: auto;
  }
}

.filter-checkbox {
  accent-color: $flowerBlueColor;
}

.light-text {
  color: $DDTableTextLightColor;

  &.sm {
    font-size: 10px;
  }

  &.md {
    font-size: 12px;
  }

  &.lg {
    font-size: 14px;
  }
}

.dd-bold-text {
  font-weight: 700;
  color: $DDTableHeaderTextColor;
}
