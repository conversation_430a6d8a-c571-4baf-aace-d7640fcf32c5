import {Injectable} from '@angular/core';
import {BehaviorSubject} from 'rxjs';
import {FilterConfig} from '../models/filter-config';
import {HttpClient, HttpParams} from '@angular/common/http';
import * as appSettings from '../../appsettings';
import {FilterParams} from '../models/filter-params';
import {TableData} from '../../shared/models/tableData';
import {TotalData} from '../models/total-data';
import {BarData, PeriodParameter, PieData, StackedBarData, StackedBarGMPMCMPData, PricingAnalytics} from '../models/chart-data';
import { Observable } from 'rxjs';
import { of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ContractDetails, InvoiceDetails, QuoteDetails,OrderDetails } from '../models/line-circle';

@Injectable({
    providedIn: 'root'

})
export class AnalyticalDashboardService {
    private filterConfSubject$ = new BehaviorSubject<FilterConfig[]>([]);

    filterConfigList$ = this.filterConfSubject$.asObservable();

    constructor(private http: HttpClient) {
    }

    saveFilterConfig(config: FilterConfig) {
        const prevConfigs = this.filterConfSubject$.value;
        this.filterConfSubject$.next([...prevConfigs, config]);
    }

    getTableData(parameter: string, page: number, size: number, filterParams?: FilterParams) {
        return this.http.get<TableData<any>>(`${appSettings.apiUrl}/fetchContractsDashbaord?rollupParam=${parameter}&page=${page}&size=${size}`, {
            params: {
                ...filterParams
            }
        });
		
    }
    // searchProductNames(query: string): Observable<string[]> {
    //     return this.http.get<string[]>(`${appSettings.apiUrl}/searchFilterValues?parameterName=productName&parameterValue=${query}`);
    // }

    // searchRegions(query: string): Observable<string[]> {
    //     return this.http.get<string[]>(`${appSettings.apiUrl}/searchFilterValues?parameterName=region&parameterValue=${query}`);
    // }

    // searchSegments(query: string): Observable<string[]> {
    //     return this.http.get<string[]>(`${appSettings.apiUrl}/searchFilterValues?parameterName=segment&parameterValue=${query}`);
    // }
    // getPriceAnalytics(filters: {
    //     productName: string;
    //     region: string;
    //     term: string;
    //     segment: string;
    // }): Observable<PricingAnalytics[]> {
    //     let params = new HttpParams()
    //         .set('productName', filters.productName || '')
    //         .set('region', filters.region || '')
    //         .set('term', filters.term || '')
    //         .set('segment', filters.segment || '');

    //     return this.http.get<PricingAnalytics[]>(`${appSettings.apiUrl}/charts/priceAnalytics`, { params });
    // }
    searchProductNames(query: string): Observable<string[]> {
        // Original incorrect URL
        // return this.http.get<string[]>(`${appSettings.apiUrl}/searchFilterValues?parameterName=PRODUCT_NAME&parameterValue=${query}`);
        
        // Fix: Update to match backend endpoint
        return this.http.get<string[]>(`${appSettings.apiUrl}/charts/searchFilterValues`, {
            params: {
                parameterName: 'productName',
                parameterValue: query
            }
        });
    }
    
    searchRegions(query: string): Observable<string[]> {
        return this.http.get<string[]>(`${appSettings.apiUrl}/charts/searchFilterValues`, {
            params: {
                parameterName: 'region',
                parameterValue: query
            }
        });
    }
    
    searchSegments(query: string): Observable<string[]> {
        return this.http.get<string[]>(`${appSettings.apiUrl}/charts/searchFilterValues`, {
            params: {
                parameterName: 'segment',
                parameterValue: query
            }
        });
    }
   
getTerms(): Observable<string[]> {
    return this.http.get<string[]>(`${appSettings.apiUrl}/charts/terms`).pipe(
        map(terms => Array.isArray(terms) ? terms : []),
        catchError(error => {
            console.error('Error fetching terms:', error);
            return of([]);
        })
    );
}


    
  

    getParameterValues(paramName: string, paramValue: string) {
        return this.http.get<any>(`${appSettings.apiUrl}/fetchFilterValues?parameterName=${paramName}&parameterValue=${paramValue}`);
    }
  // In analytical-dashboard.service.ts
getPriceAnalytics(productName: string, region: string, segment: string, term: string) {
    let serviceUrl = `${appSettings.apiUrl}/charts/priceAnalytics`;
    let params = new HttpParams()
        .set('productName', productName || '')
        .set('region', region || '')
        .set('segment', segment || '')
        .set('term', term || '');
    
    return this.http.get<PricingAnalytics[]>(serviceUrl, { params });
}
  
     searchData(parameter: string, searchTerm: string, page: number, size: number, filterParams: FilterParams = {}): Observable<TableData<any>> {
	
    let params = new HttpParams()
      .set('rollupParam', parameter)
      .set('searchTerm', searchTerm || '')
      .set('page', page.toString())
      .set('size', size.toString());
	  
	    // Append searchTerm only if it's not empty
  if (searchTerm.trim()) {
    params = params.set('searchTerm', searchTerm.trim());
  }
  
    // Ensure filterParams is an object
  filterParams = filterParams || {}; 
 
  const hasFilters = Object.keys(filterParams).length > 0;
  
  if (hasFilters) {
     	Object.entries(filterParams).forEach(([key, value]) => {
		  if (value !== undefined && value !== null && value !== '') {
      //  params = params.set(key, filterParams[key]);
	     // params = params.set(key, typeof value === 'number' ? value.toString() : value as string);
        params = params.set(key, String(value).trim()); // Convert everything to string
			//console.log(`Appending: ${key}=${value}`); 


		}
      });
	
    }
	 else {
        console.log('No filterParams provided'); // Log if no filter params
    }

   
    return this.http.get<TableData<any>>(`${appSettings.apiUrl}/fetchContractsDashbaord`, { params });
  }
    
    getTotalData() {
        return this.http.get<TotalData>(`${appSettings.apiUrl}/charts/dealTotals`);
    }

    getBookingsValueBySKU(period: string) {
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/bookValBySku?periodType=${period}`);
    }

    getRevenueByRegion(period: string) {
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/revenueByRegion?periodType=${period}`);
    }

    getRevenueByAccount(period: string) {
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/revenueByAccount?periodType=${period}`);
    }

    getRevenueBySalesRep(period: string) {
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/revenueBySalesrep?periodType=${period}`);
    }

    getRevenueByQuarter(period: string) {
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/revenueByPeriod?periodType=${period}`);
    }

    getInvoiceByAccount(period: string) {
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/invoiceByAccount?periodType=${period}`);
    }

    getDealsByDiscountGuidance() {
        return this.http.get<PieData[]>(`${appSettings.apiUrl}/charts/dealsByGuidance?guidanceType=D`);
    }

    getDealsByRevenueGuidance() {
        return this.http.get<PieData[]>(`${appSettings.apiUrl}/charts/dealsByGuidance?guidanceType=R`);
    }

    getDealsByMarginGuidance() {
        return this.http.get<PieData[]>(`${appSettings.apiUrl}/charts/dealsByGuidance?guidanceType=M`);
    }
    getBookingValue(period: string) {
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/bookingAmtByPeriod?periodType=${period}`);
    }
    getBookingVsDelValue(period: string){
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/bookingnDeliveredAmtByPeriod?periodType=${period}`);
    }
    getInvoiceValue(period:string){
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/invoiceAmtByPeriod?periodType=${period}`);
        
    }
    getRevenueVsInvoiceValue(period:string){
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/revenuenInvoiceAmtByPeriod?periodType=${period}`);

    }
    getBookingsVsInvoiceValue(period:string){
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/bookingnInvoiceAmtByPeriod?periodType=${period}`);

    }
    getBookingsInvoiceVarianceByAmount(period:string){
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/bookingnInvoiceVarianceAmtByPeriod?periodType=${period}`);
    }
    getBookingsInvoiceVarianceByPercentage(period:string){
        return this.http.get<BarData[]>(`${appSettings.apiUrl}/charts/bookingnInvoiceVarianceByPeriod?periodType=${period}`);
    }
    getBookingVsDelByAccount(period:string):Observable<StackedBarData[]> {
        return this.http.get<StackedBarData[]>(`${appSettings.apiUrl}/charts/bookingnDeliveredAmtByAccount?periodType=${period}`);
    }
    getBookingVsDelByItemType(period:string):Observable<StackedBarData[]>{
        return this.http.get<StackedBarData[]>(`${appSettings.apiUrl}/charts/bookingnDeliveredAmtByItemType?periodType=${period}`);
    }
    getBookingVsDelBySKU(period:string):Observable<StackedBarData[]>{
        return this.http.get<StackedBarData[]>(`${appSettings.apiUrl}/charts/bookingnDeliveredAmtByProduct?periodType=${period}`);
    }
    getGrossMarginPercentByAccount(period:string):Observable<StackedBarGMPMCMPData[]>{
        return this.http.get<StackedBarGMPMCMPData[]>(`${appSettings.apiUrl}/charts/grossMarginPercentByAccount?periodType=${period}`);
    }
    getGrossMarginPercentByItemType(period:string):Observable<StackedBarGMPMCMPData[]>{
        return this.http.get<StackedBarGMPMCMPData[]>(`${appSettings.apiUrl}/charts/grossMarginPercentByItemType?periodType=${period}`);
    }
    getPacingMarginPercentByAccount(period:string):Observable<StackedBarGMPMCMPData[]>{
        return this.http.get<StackedBarGMPMCMPData[]>(`${appSettings.apiUrl}/charts/pacingMarginPercentByAccount?periodType=${period}`);
    }
    getPacingMarginPercentByItemType(period:string):Observable<StackedBarGMPMCMPData[]>{
        return this.http.get<StackedBarGMPMCMPData[]>(`${appSettings.apiUrl}/charts/pacingMarginPercentByItemType?periodType=${period}`);
    }
    getCMPercentVarAsSoldByAccount(period:string):Observable<StackedBarGMPMCMPData[]>{
        return this.http.get<StackedBarGMPMCMPData[]>(`${appSettings.apiUrl}/charts/cmPercentVarAsSoldByAccount?periodType=${period}`);
    }
    getCMPercentVarAsSoldByItemType(period:string):Observable<StackedBarGMPMCMPData[]>{
        return this.http.get<StackedBarGMPMCMPData[]>(`${appSettings.apiUrl}/charts/cmPercentVarAsSoldByItemType?periodType=${period}`);
    }
    getGrossVsPacingMarginPercentByAccount(period:string):Observable<StackedBarGMPMCMPData[]>{
        return this.http.get<StackedBarGMPMCMPData[]>(`${appSettings.apiUrl}/charts/grossVsPacingMarginPercentByAccount?periodType=${period}`);
    }
    getGrossVsPacingMarginPercentByItemType(period:string):Observable<StackedBarGMPMCMPData[]>{
        return this.http.get<StackedBarGMPMCMPData[]>(`${appSettings.apiUrl}/charts/grossVsPacingMarginPercentByItemType?periodType=${period}`);
    }

    getContractDetails(dealArrangementId: number): Observable<ContractDetails> {
        return this.http.get<ContractDetails>(`${appSettings.apiUrl}/${dealArrangementId}`);
        
      }

    //   getInvoiceDetails(quoteNumber: string): Observable<InvoiceDetails> {
    //     return this.http.get<InvoiceDetails>(`${appSettings.apiUrl}/invoices/${quoteNumber}`);
        
    //   }
    getInvoiceDetails(quoteNumber: string): Observable<InvoiceDetails> {
        return this.http.get<InvoiceDetails>(`${appSettings.apiUrl}/invoices/${quoteNumber}`);
      }
      getQuoteDetails(quoteNumber: string): Observable<QuoteDetails> {
        const params = new HttpParams().set('quoteNumber', quoteNumber);
        return this.http.get<QuoteDetails>(`${appSettings.apiUrl}/quoteforecastDetails/`,{ params });
    }

      getOrderDetails(quoteNumber: string): Observable<OrderDetails> {
        return this.http.get<OrderDetails>(`${appSettings.apiUrl}/orders/${quoteNumber}`);
        
      }


    /*getBookingValue(periodType:PeriodParameter){
        switch (periodType){
            case 'QTD':
                return this.http.get<BarData[]>(`assets/json-data/quarter.json`);
            
            case 'PTD':
                return this.http.get<BarData[]>(`assets/json-data/period.json`);
                
            case 'YTD':
                return this.http.get<BarData[]>(`assets/json-data/year.json`);
               
            default:
                
        }*/
          /* getStackedRevenueByProduct(period: string): Observable<any> {
                // This is where you'd normally make an HTTP request
                // For this example, we'll return mock data
                return of({
                    labels: [
                        'Q1','Q2','Q3','Q4'
                      ],
                      datasets: [
                        {
                          label: 'Product A',
                          backgroundColor: '#42A5F5',
                          borderColor: '#1E88E5',
                          stack:"Booked",
                          data: [65, 59, 80, 81, 56, 55, 40, 90],
                          
                        },
                        {
                          label: 'Product B',
                          backgroundColor: '#9CCC65',
                          borderColor: '#7CB342',
                          data: [28, 48, 40, 19, 86, 27, 90, 92],
                          stack: "Booked",
                        },
                        {
                          label: 'Product A',
                          backgroundColor: '#A2A5F5',
                          borderColor: '#1E88E5',
                          data: [65, 59, 80, 81, 56, 55, 40, 10],
                          stack: "Delivered",
                        },
                        {
                          label: 'Product B',
                          backgroundColor: '#CCCC65',
                          borderColor: '#7CB342',
                          data: [28, 48, 40, 19, 86, 27, 90, 40],
                          stack: 'Delivered',
                        },
                      ],
                    });
            }*/


    
        }
   
   
    

