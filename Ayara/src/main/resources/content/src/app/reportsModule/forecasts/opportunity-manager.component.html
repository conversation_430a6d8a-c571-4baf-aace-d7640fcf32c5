<!--app-dynamic-table
        header="Opportunities Manager"
        [tableData]="tableData"
        [columns]="tableColumns"
        [isLoading]="isLoading"
></app-dynamic-table-->

<ng-container *ngIf="(opportunities$ | async) as oppData">
        <app-dynamic-table
                header="Opportunities"
                [tableData]="oppData"
                [columns]="tableColumns"
                [isLoading]="isLoading"
                [withRefresh]="true"
                [withSearch]="true"
                (rowSelect)="onRowSelect($event)"
                (refresh)="onTableRefresh()"
                (loadData)="loadOpportunities($event)"
                (search)="onSearch($event)"
              
               
                

        
        >
        
       


</app-dynamic-table>
    </ng-container>
    
