import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {Column} from '../../../shared/models/column.interface';
import {finalize} from 'rxjs/operators';
import {OpportunityService} from '../../data-access/opportunity.service';
import {Observable} from 'rxjs';

@Component({
    selector: 'app-table-toolbar',
    templateUrl: './table-toolbar.component.html',
    styleUrls: ['./table-toolbar.component.scss']
})
export class TableToolbarComponent implements OnInit {
    @Input() header = '';
    @Input() customerName: string;
@Input() opportunityNumber: string;
@Input() opportunityName: string;
    @Input() columns: Column[];
    @Input() selectedColumns: Column[];
    @Input() withSearch = false;
    @Input() withRefresh = false;
    @Input() withTabs = false;
    @Input() tabsCount = 2;
    @Input() backLink: string;
    data: any[] = []; // Add this to store original data
    searchData: any[] = [];

    @Output() toggle = new EventEmitter<boolean>();
    @Output() refresh = new EventEmitter();
    @Output() search = new EventEmitter<string>();
    @Output() exportCsv = new EventEmitter();
    @Output() columnsChange = new EventEmitter<Column[]>();

    isMaximized = false;
    showFilter = false;
    searchTerm = '';
    isSearched = false;
    isLoading = false;
    opportunities$: Observable<any>;

    constructor( private opportunityService: OpportunityService) { 
    }

    ngOnInit(): void {
        //this.loadOpportunities();
    }

    onToggle() {
        this.isMaximized = !this.isMaximized;
        this.toggle.emit(this.isMaximized);
    }

    onColumnsChange(event: any) {
        console.log(event);
        this.columnsChange.emit(this.selectedColumns);
    }

    // onSearch() {
    //     this.isSearched = true;

    // }

    // onSearch() {
    //     this.isSearched = true;
    //     //this.searchTerm = term;
    //     this.search.emit(this.searchTerm);
    //   }

    onSearch() {
        console.log('Search Term:', this.searchTerm);
        this.isSearched = true;
        this.search.emit(this.searchTerm);
    }

    
    onResetGlobalSearch() {
        this.searchTerm = '';
        this.isSearched = false;
        this.search.emit('');
    }

    onExport() {
        this.exportCsv.emit();
    }
    async onRefresh(): Promise<void> {
        try {
          this.isLoading = true;
          // Emit refresh event to parent
          this.refresh.emit();
        } finally {
          this.isLoading = false;
        }
      }
}
