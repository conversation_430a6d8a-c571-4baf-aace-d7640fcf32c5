<div class="chart__container">
    <div class="chart__header">
        <div class="chart__header-title">{{header | chartTitle}}</div>
        <ng-container *ngIf="isBar && !isPricing">
            <div class="chart__header-dropdown">
                <p-dropdown
                        class="dashboard-dropdown bordered"
                        [options]="periodOptions"
                        optionLabel="label"
                        [(ngModel)]="selectedOption"
                        (onChange)="onPeriodChange()">
                </p-dropdown>
            </div>
        </ng-container>

            <ng-container *ngIf="header.includes('Pricing Analytics')">
                <div class="pricing-filters">
                    <!-- Product Name -->
                    <div class="filter-item">
                        <p-autoComplete 
            [(ngModel)]="filters.productName"
            [defaultValue]="filters.productName"
            [suggestions]="productSuggestions"
            (completeMethod)="searchValues('PRODUCT_NAME', $event)"
            placeholder="Type Product"
            [value]="filters.productName"
            [forceSelection]="true"
            minLength="3"
            (onSelect)="onFilterChange()"
            [ngModelOptions]="{standalone: true}"
            appendTo="body">
        </p-autoComplete>

        <!-- Region -->
        <p-autoComplete 
            [(ngModel)]="filters.region"
            [suggestions]="regionSuggestions"
            (completeMethod)="searchValues('REGION', $event)"
            placeholder="Type Region"
            [minLength]="3"
            (onSelect)="onFilterChange()"
            [defaultValue]="filters.region"
            [ngModelOptions]="{standalone: true}"
            appendTo="body">
        </p-autoComplete>

        <!-- Segment -->
        <p-autoComplete 
            [(ngModel)]="filters.segment"
            [suggestions]="segmentSuggestions"
            (completeMethod)="searchValues('SEGMENT', $event)"
            placeholder="Type Segment"
            [minLength]="3"
            [defaultValue]="filters.segment"
            (onSelect)="onFilterChange()"
            [ngModelOptions]="{standalone: true}"
            appendTo="body">
        </p-autoComplete>

        <!-- Term Dropdown -->
        <p-dropdown
        [(ngModel)]="filters.term"
        [options]="termOptions"
        optionLabel="label"
       
        (onChange)="onFilterChange()"
        [placeholder]="filters.term?.label || 'Select Term'"
        appendTo="body"
        [ngModelOptions]="{standalone: true}"
        class="dashboard-dropdown bordered">
    </p-dropdown>
        </div>
    </div>
                </ng-container>
            </div>
            <div class="chart__body">
                <ng-content></ng-content>
            </div>
    </div>

