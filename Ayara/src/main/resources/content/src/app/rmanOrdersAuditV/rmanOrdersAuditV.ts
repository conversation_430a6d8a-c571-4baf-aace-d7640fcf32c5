export interface RmanOrdersAuditV {
    origRmanLineId: any;
    sourceLineId: any;
    carveInOrOut: any;
    repCurrExtendedAmt: any;
    territory: any;
    lineNum: any;
    elementType: any;
    salesrep: any;
    shippingEntityId: any;
    productOrgId: any;
    billToCountry: any;
    cancelledQty: any;
    unitSellingPrice: any;
    topModelLineId: any;
    serviceRefOrder: any;
    billToLocation: any;
    glStatus: any;
    scheduleShipDate: any;
    customerPoNum: any;
    arrangementId: any;
    region: any;
    rmanAcctRuleId: any;
    fmvRuleDefId: any;
    orderedQuantity: any;
    glUpdateDate: any;
    shippingOrgCode: any;
    taskId: any;
    lineAttribute6: any;
    lineAttribute5: any;
    headerAttribute10: any;
    lineAttribute8: any;
    headerAttribute11: any;
    lineAttribute7: any;
    headerAttribute12: any;
    soldToCustomer: any;
    lineAttribute2: any;
    headerAttribute13: any;
    customerPoLineNum: any;
    lineAttribute1: any;
    headerAttribute14: any;
    lineAttribute4: any;
    headerAttribute15: any;
    repUnitSellingPrice: any;
    lineAttribute3: any;
    allocationAmount: any;
    lineAttribute9: any;
    contingencyCode: any;
    cancelledDate: any;
    fxRate: any;
    objVersionNumber: any;
    sno: any;
    lineAttribute12: any;
    shippedQuantity: any;
    lineAttribute13: any;
    lineAttribute10: any;
    lineAttribute11: any;
    shipToLocation: any;
    dealNumber: any;
    expenditureType: any;
    billToCustomer: any;
    lineAttribute14: any;
    projectId: any;
    lineAttribute15: any;
    shipToCustomer: any;
    refOrderNumber: any;
    dealLineNumber: any;
    bookingCurrency: any;
    lineType: any;
    projectNumber: any;
    orderType: any;
    revrecAcctRule: any;
    parentLineId: any;
    roleName: any;
    bookedDate: any;
    accountingRuleName: any;
    repCurrUlp: any;
    exceptionFlag: any;
    serviceEndDate: any;
    domesticInternational: any;
    poHeaderId: any;
    taskNumber: any;
    shipToCountry: any;
    revenueCategory: any;
    orderNumModifier: any;
    eitfSop: any;
    revrecStDate: any;
    bundleFlag: any;
    fxDate: any;
    revrecAcctScope: any;
    priceList: any;
    repCurrUsp: any;
    orginalOrderNumber: any;
    exceptionMessage: any;
    sourceHeaderId: any;
    lastUpdateDate: any;
    orginalLineId: any;
    fob: any;
    unitListPrice: any;
    allocationFlag: any;
    createdBy: any;
    lastUpdatedBy: any;
    revrecDelay: any;
    sourceLineNumber: any;
    taskName: any;
    creationDate: any;
    shippableFlag: any;
    productName: any;
    lineStatus: any;
    stdWarranty: any;
    linkToLineId: any;
    orginalLineNumber: any;
    repUnitListPrice: any;
    rmanLineId: any;
    deliveredFlag: any;
    attributedListPrice: any;
    dealLineId: any;
    headerAttribute1: any;
    headerAttribute6: any;
    headerAttribute7: any;
    headerAttribute8: any;
    headerAttribute9: any;
    headerAttribute2: any;
    fmvHistoryFlag: any;
    headerAttribute3: any;
    servicePeriod: any;
    headerAttribute4: any;
    projectName: any;
    headerAttribute5: any;
    serviceRefOrdLineNum: any;
    revrecEndDate: any;
    subElementType: any;
    fulfilledQuantity: any;
    orderStatus: any;
    serviceStartDate: any;
    endCustomer: any;
    revrecHoldType: any;
    repCurrCode: any;
    actualFulfilledDate: any;
    serviceDuration: any;
    conversionRate: any;
    serviceNumber: any;
    productId: any;
    invoiceFlag: any;
    dealArrangementId: any;
    bookingEntityId: any;
    attributedSellPrice: any;
    extWarranty: any;
    revrecAccount: any;
    orderSource: any;
    orderNumber: any;
    orderedDate: any;
    parentLine: any;
    lineCost: any;
    poLineId: any;
    extendedSellingAmount: any;
}
