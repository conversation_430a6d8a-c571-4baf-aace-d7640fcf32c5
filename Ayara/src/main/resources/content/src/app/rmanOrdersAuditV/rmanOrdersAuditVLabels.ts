interface ILabels {
    [index: string]: string;
}

export class RmanOrdersAuditVLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["origRmanLineId"] = "Orig Rman Line Id";
        this.fieldLabels["sourceLineId"] = "Source Line Id";
        this.fieldLabels["carveInOrOut"] = "Carve In Or Out";
        this.fieldLabels["repCurrExtendedAmt"] = "Rep Curr Extended Amt";
        this.fieldLabels["territory"] = "Territory";
        this.fieldLabels["lineNum"] = "Line#";
        this.fieldLabels["elementType"] = "Element Type";
        this.fieldLabels["salesrep"] = "Salesrep";
        this.fieldLabels["shippingEntityId"] = "Shipping Entity Id";
        this.fieldLabels["productOrgId"] = "Product Org Id";
        this.fieldLabels["billToCountry"] = "Bill To Country";
        this.fieldLabels["cancelledQty"] = "Cancelled Qty";
        this.fieldLabels["unitSellingPrice"] = "Net Price";
        this.fieldLabels["topModelLineId"] = "Top Model Line Id";
        this.fieldLabels["serviceRefOrder"] = "Service Ref SO#";
        this.fieldLabels["billToLocation"] = "Bill To Location";
        this.fieldLabels["glStatus"] = "Gl Status";
        this.fieldLabels["scheduleShipDate"] = "Schedule Ship Date";
        this.fieldLabels["customerPoNum"] = "Customer Po#";
        this.fieldLabels["arrangementId"] = "Arrangement Id";
        this.fieldLabels["region"] = "Region";
        this.fieldLabels["rmanAcctRuleId"] = "Rman Acct Rule Id";
        this.fieldLabels["fmvRuleDefId"] = "Fmv Rule Def Id";
        this.fieldLabels["orderedQuantity"] = "Ordered Quantity";
        this.fieldLabels["glUpdateDate"] = "GL Update Date";
        this.fieldLabels["shippingOrgCode"] = "Shipping Org Code";
        this.fieldLabels["taskId"] = "Task Id";
        this.fieldLabels["lineAttribute6"] = "Additional Discount";
        this.fieldLabels["lineAttribute5"] = "Discount Type";
        this.fieldLabels["headerAttribute10"] = "Header Attribute10";
        this.fieldLabels["lineAttribute8"] = "Line Attribute8";
        this.fieldLabels["headerAttribute11"] = "Header Attribute11";
        this.fieldLabels["lineAttribute7"] = "Line Attribute7";
        this.fieldLabels["headerAttribute12"] = "Header Attribute12";
        this.fieldLabels["soldToCustomer"] = "Sold To Customer";
        this.fieldLabels["lineAttribute2"] = "Line Attribute2";
        this.fieldLabels["headerAttribute13"] = "Header Attribute13";
        this.fieldLabels["customerPoLineNum"] = "Customer Po Line Num";
        this.fieldLabels["lineAttribute1"] = "Line Attribute1";
        this.fieldLabels["headerAttribute14"] = "Header Attribute14";
        this.fieldLabels["lineAttribute4"] = "Line Attribute4";
        this.fieldLabels["headerAttribute15"] = "Header Attribute15";
        this.fieldLabels["repUnitSellingPrice"] = "Rep Unit Selling Price";
        this.fieldLabels["lineAttribute3"] = "Line Attribute3";
        this.fieldLabels["allocationAmount"] = "Allocation Amount";
        this.fieldLabels["lineAttribute9"] = "Line Attribute9";
        this.fieldLabels["contingencyCode"] = "Contingency Code";
        this.fieldLabels["cancelledDate"] = "Cancelled Date";
        this.fieldLabels["fxRate"] = "Fx Rate";
        this.fieldLabels["objVersionNumber"] = "Obj Version Number";
        this.fieldLabels["sno"] = "Sno";
        this.fieldLabels["lineAttribute12"] = "UOM";
        this.fieldLabels["shippedQuantity"] = "Shipped Quantity";
        this.fieldLabels["lineAttribute13"] = "Line Attribute13";
        this.fieldLabels["lineAttribute10"] = "Line Attribute10";
        this.fieldLabels["lineAttribute11"] = "Line Attribute11";
        this.fieldLabels["shipToLocation"] = "Ship To Location";
        this.fieldLabels["dealNumber"] = "Deal Number";
        this.fieldLabels["expenditureType"] = "Expenditure Type";
        this.fieldLabels["billToCustomer"] = "Bill To Customer";
        this.fieldLabels["lineAttribute14"] = "LineAttribute14";
        this.fieldLabels["projectId"] = "Project Id";
        this.fieldLabels["lineAttribute15"] = "Line Attribute15";
        this.fieldLabels["shipToCustomer"] = "Ship To Customer";
        this.fieldLabels["refOrderNumber"] = "Ref Order Number";
        this.fieldLabels["dealLineNumber"] = "Deal Line Number";
        this.fieldLabels["bookingCurrency"] = "Booking Currency";
        this.fieldLabels["lineType"] = "Line Type";
        this.fieldLabels["projectNumber"] = "Project Number";
        this.fieldLabels["orderType"] = "Order Type";
        this.fieldLabels["revrecAcctRule"] = "Revrec Acct Rule";
        this.fieldLabels["parentLineId"] = "Parent Line Id";
        this.fieldLabels["roleName"] = "Role Name";
        this.fieldLabels["bookedDate"] = "Booked Date";
        this.fieldLabels["accountingRuleName"] = "Accounting Rule";
        this.fieldLabels["repCurrUlp"] = "Rep Curr Ulp";
        this.fieldLabels["exceptionFlag"] = "Exception Flag";
        this.fieldLabels["serviceEndDate"] = "Contract End Date";
        this.fieldLabels["domesticInternational"] = "Domestic International";
        this.fieldLabels["poHeaderId"] = "poHeader Id";
        this.fieldLabels["taskNumber"] = "Task Number";
        this.fieldLabels["shipToCountry"] = "Ship To Country";
        this.fieldLabels["revenueCategory"] = "Revenue Category";
        this.fieldLabels["orderNumModifier"] = "Order Num Modifier";
        this.fieldLabels["eitfSop"] = "Eitf Sop";
        this.fieldLabels["revrecStDate"] = "Revrec St Date";
        this.fieldLabels["bundleFlag"] = "Bundle Flag";
        this.fieldLabels["fxDate"] = "Fx Date";
        this.fieldLabels["revrecAcctScope"] = "Revrec Acct Scope";
        this.fieldLabels["priceList"] = "Price List";
        this.fieldLabels["repCurrUsp"] = "Rep Curr Usp";
        this.fieldLabels["orginalOrderNumber"] = "Reference Order Number";
        this.fieldLabels["exceptionMessage"] = "Exception Message";
        this.fieldLabels["sourceHeaderId"] = "Source Header Id";
        this.fieldLabels["lastUpdateDate"] = "Last Update Date";
        this.fieldLabels["orginalLineId"] = "Reference Order Line Id";
        this.fieldLabels["fob"] = "Fob";
        this.fieldLabels["unitListPrice"] = "List Price";
        this.fieldLabels["allocationFlag"] = "Allocation Flag";
        this.fieldLabels["createdBy"] = "Created By";
        this.fieldLabels["lastUpdatedBy"] = "Last Updated By";
        this.fieldLabels["revrecDelay"] = "Revrec Delay";
        this.fieldLabels["sourceLineNumber"] = "SO Line#";
        this.fieldLabels["taskName"] = "Task Name";
        this.fieldLabels["creationDate"] = "Creation Date";
        this.fieldLabels["shippableFlag"] = "Shippable Flag";
        // this.fieldLabels["productName"] = "SKU Name";
        this.fieldLabels["productName"] = "Product Name";
        this.fieldLabels["lineStatus"] = "Line Status";
        this.fieldLabels["stdWarranty"] = "Std Warranty";
        this.fieldLabels["linkToLineId"] = "Link To Line Id";
        this.fieldLabels["orginalLineNumber"] = "Reference Line Number";
        this.fieldLabels["repUnitListPrice"] = "Rep Unit List Price";
        this.fieldLabels["rmanLineId"] = "Rman Line Id";
        this.fieldLabels["deliveredFlag"] = "Delivered Flag";
        this.fieldLabels["attributedListPrice"] = "Attributed List Price";
        this.fieldLabels["dealLineId"] = "Deal Line Id";
        this.fieldLabels["headerAttribute1"] = "Header Attribute1";
        this.fieldLabels["headerAttribute6"] = "Header Attribute6";
        this.fieldLabels["headerAttribute7"] = "Header Attribute7";
        this.fieldLabels["headerAttribute8"] = "Header Attribute8";
        this.fieldLabels["headerAttribute9"] = "Header Attribute9";
        this.fieldLabels["headerAttribute2"] = "Header Attribute2";
        this.fieldLabels["fmvHistoryFlag"] = "Fmv History Flag";
        this.fieldLabels["headerAttribute3"] = "Header Attribute3";
        this.fieldLabels["servicePeriod"] = "Service Period";
        this.fieldLabels["headerAttribute4"] = "Header Attribute4";
        this.fieldLabels["projectName"] = "Project Name";
        this.fieldLabels["headerAttribute5"] = "Header Attribute5";
        this.fieldLabels["serviceRefOrdLineNum"] = "Service Ref Line#";
        this.fieldLabels["revrecEndDate"] = "Revrec End Date";
        this.fieldLabels["subElementType"] = "Sub Element Type";
        this.fieldLabels["fulfilledQuantity"] = "Fulfilled Quantity";
        this.fieldLabels["orderStatus"] = "Order Status";
        this.fieldLabels["serviceStartDate"] = "Contract Start Date";
        this.fieldLabels["endCustomer"] = "End Customer";
        this.fieldLabels["revrecHoldType"] = "Revrec Hold Type";
        this.fieldLabels["repCurrCode"] = "Rep Curr Code";
        this.fieldLabels["actualFulfilledDate"] = "Delivered Date";
        this.fieldLabels["serviceDuration"] = "Service Duration";
        this.fieldLabels["conversionRate"] = "Conversion Rate";
        this.fieldLabels["serviceNumber"] = "Service Number";
        this.fieldLabels["productId"] = "Product Id";
        this.fieldLabels["invoiceFlag"] = "Invoice Flag";
        this.fieldLabels["dealArrangementId"] = "Deal Arrangement Id";
        this.fieldLabels["bookingEntityId"] = "Booking Entity Id";
        this.fieldLabels["attributedSellPrice"] = "Attributed Sell Price";
        this.fieldLabels["extWarranty"] = "Ext Warranty";
        this.fieldLabels["revrecAccount"] = "Revrec Account";
        this.fieldLabels["orderSource"] = "Order Source";
        this.fieldLabels["orderNumber"] = "SO #";
        this.fieldLabels["orderedDate"] = "Ordered Date";
        this.fieldLabels["parentLine"] = "Parent Line";
        this.fieldLabels["lineCost"] = "Line Amount";
        this.fieldLabels["poLineId"] = "Po Line Id";
        this.fieldLabels["extendedSellingAmount"] = "Line Amount";
    }

}
