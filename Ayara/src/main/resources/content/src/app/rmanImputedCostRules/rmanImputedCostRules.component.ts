import {Component, OnInit, ViewChild} from '@angular/core';
import {ConfirmationService, Message} from 'primeng/api';
import {RmanImputedCostRuleService} from './rmanImputedCostRule.service';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {RmanLookupsVService} from '../rmanLookupsV/rmanLookupsVservice';
import {NotificationService} from '../shared/notifications.service';
import {CommonSharedService} from '../shared/common.service';
import {Table} from 'primeng/table';
import {RmanRuleParameterValueComponent} from '../rmanRuleParameterValue/rmanRuleParameterValue.component';
import {RmanImputedCostRuleHeader} from './rmanImputedCostRuleHeader';
import {RmanImputedCostRuleParameterValueComponent} from '../rmanImputedCostRuleParameterValue/rmanImputedCostRuleParameterValue.component';


declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Component({
    selector: 'rmanImputedCostRules',
    templateUrl: './rmanImputedCostRules.component.html',
    providers: [RmanImputedCostRuleService, ConfirmationService]
})
export class RmanImputedCostRulesComponent implements OnInit {

    constructor(private rmanRulesHeaderService: RmanImputedCostRuleService, private rmanLookupsVService: RmanLookupsVService,
                private formBuilder: FormBuilder, private confirmationService: ConfirmationService,
                private notificationService: NotificationService, private commonSharedService: CommonSharedService) {
        // generate list code
        this.paginationOptions = {'pageNumber': 0, 'pageSize': '10000'};
        this.loading = true;
        this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, {'lookupTypeName': 'IMPUTED_COST_BASIS'})
            .then((rmanLookupsVList: any) => {
            this.loading = false;
            this.rmanCostBasisList = rmanLookupsVList.content;
            this.prepareRmanCostBasisObject();
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting "Cost Basis" data');
            this.loading = false;
        });

        this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, {'lookupTypeName': 'ENABLED_FLAG'})
            .then((rmanLookupsVList: any) => {
            this.loading = false;
            this.rmanActivatedFlagList = rmanLookupsVList.content;
            this.prepareRmanActivatedFlagObject();
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting "Activated Flag" data');
            this.loading = false;
        });

    }

    displayDialog: boolean;

    showMsg: any;

    displaySearchDialog: boolean;

    rmanRulesHeader: any = new RmanImputedCostRulesImpl();

    rmanRulesHeaderSearch: any = new RmanImputedCostRulesImpl();

    isSerached = 0;

    selectedRmanRulesHeader: RmanImputedCostRuleHeader;

    newRmanRulesHeader: boolean;

    pRuleHeaderId: number;
    pRuleCategory: string;

    rmanRulesHeaderList: any[];

    cols: any[];
    columns: any[];

    columnOptions: any[];

    msgs: Message[] = [];


    paginationOptions: any;

    pages: {};

    datasource: any[];
    pageSize: number;
    totalElements: number;

    globalCols: any[];
    clonedCols: any[];

    showAddColumns = true;
    isSelectAllChecked = true;

    userId: number;
    showPaginator = true;
    startIndex: number;


    rmanCostBasisList: any[] = [];
    rmanActivatedFlagList = [];
    rmanCostBasisV: any[];
    rmanActivatedFlagsV: any[];
    noData = appSettings.noData;
    collapsed = true;
    arrangementRulesForm: FormGroup;
    loading: boolean;

    @ViewChild(RmanImputedCostRuleParameterValueComponent) private childTable: RmanImputedCostRuleParameterValueComponent;

    formErrors = {
        'cstRuleName': '', 'startDate': '', 'endDate': '', 'cstAdjustment': '', 'enabledFlag': '', 'cstBasis': ''
    };

    validationMessages = {
        'cstRuleName': {
            'required': 'Rule Name is required (minimum length is 4 and maximum length is 65 characters)',
            'minlength': 'Rule Name must be at least 4 characters long.',
            'maxlength': 'Rule Name cannot be more than 65 characters long.',
        },
        'startDate': {
            'required': 'Rule Start Date is Required'
        },
        'category': {
            'required': 'Rule Category is required'
        },
        'cstAdjustment': {
            'required': 'Cost Adjustment % is required (Only number values are allowed [0-9])',
        'minlength': 'Cost Adjustment % must be at least 1 characters long.',
            'pattern': 'Only number values are allowed [0-9]',
            'min': 'Minimum number allowed is -100.00',
        }

    };

    hideColumnMenu = true;

    showFilter = false;

    ngOnInit(): void {
        this.globalCols = [
            {field: 'cstRuleName', header: 'Rule Name', showField: true, display: 'table-cell', type: 'text', drag: false},
            {field: 'cstAdjustment', header: 'Cost Adjustment %', showField: true, display: 'table-cell', type: 'number', drag: false},
            {field: 'cstBasis', header: 'Cost Basis Value', showField: true, display: 'table-cell', type: 'text', drag: true},
            {field: 'startDate', header: 'Start Date', showField: true, display: 'table-cell', type: 'date', drag: true},
            {field: 'endDate', header: 'End Date', showField: true, display: 'table-cell', type: 'date', drag: true},
            {field: 'enabledFlag', header: 'Activated Flag', showField: true, display: 'table-cell', type: 'text', drag: true},
        ];

        this.columns = [];
        this.getTableColumns('ImputedCostRules', 'Imputed Cost Rules');

        this.buildForm();
    }

    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
            if (response && response != null && response.userId) {
                this.columns = [];
                const colsList = response.tableColumns.split(',');
                if (colsList.length > 0) {
                    colsList.forEach((item, index) => {
                        if (item) {
                            this.startIndex = this.globalCols.findIndex(col => col.field === item);
                            this.onDrop(index);
                        }
                    });
                }
                this.globalCols.forEach(col => {
                    if (response.tableColumns.indexOf(col.field) !== -1) {
                        this.columns.push(col);
                    } else {
                        col.showField = false;
                    }
                });
                if (this.columns.length !== this.globalCols.length) {
                    this.isSelectAllChecked = false;
                }
                this.showPaginator = this.columns.length !== 0;
                this.userId = response.userId;
            } else {
                this.columns = this.globalCols;
            }
        }).catch(() => {
            this.notificationService.showError('Error occured while getting table columns data');
            this.loading = false;
        });
    }

    saveColumns() {
        let selectedCols = '';
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
            if (col.showField) {
                selectedCols += col.field;
                if (index < colLength) {
                    selectedCols += ',';
                }
            }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns('ImputedCostRules', 'Imputed Cost Rules', selectedCols, this.userId).then((response) => {
            this.columns = this.globalCols.filter(item => item.showField);
            this.userId = response['userId'];
            this.showPaginator = this.columns.length !== 0;
            this.loading = false;
        }).catch(() => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    onDragStart(index: number) {
        this.startIndex = index;
    }

    onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
        // console.log(this.globalCols);
    }

    selectColumns(col: any) {
        const cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
            this.isSelectAllChecked = false;
        } else {
            this.isSelectAllChecked = true;
        }
    }

    onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
            if (this.isSelectAllChecked) {
                col.showField = true;
            } else {
                if (col.drag) {
                    col.showField = false;
                }
            }
        });
    }

    reset(dt: Table) {
        this.paginationOptions = {};
        this.rmanRulesHeader = new RmanImputedCostRulesImpl();
        dt.reset();
    }


    onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
    }

    closeConfigureColumns(event: any) {
        this.showAddColumns = true;
        this.globalCols = this.clonedCols;
        const configCol = this.globalCols.filter(item => !item.showField);
        this.isSelectAllChecked = !(configCol.length > 0);
    }

    buildForm() {
        this.arrangementRulesForm = this.formBuilder.group({
            'cstRuleName': ['', [Validators.required, Validators.minLength(4), Validators.maxLength(65)]],
            'startDate': ['', [Validators.required]],
            'cstAdjustment': ['', [Validators.required, Validators.minLength(1), Validators.pattern(/^\-?\d+(\.\d+)?$/), Validators.min(-100.00)]],
            'cstBasis': ['', [Validators.required]],
            'enabledFlag': ['', [Validators.required]],
            'endDate': [''],
        });
        this.arrangementRulesForm.valueChanges
            .subscribe(data => this.onValueChanged(data));
        this.onValueChanged();
    }

    onValueChanged(data?: any) {
        if (!this.arrangementRulesForm) {
            return;
        }
        const form = this.arrangementRulesForm;

        for (const field in this.formErrors) {
            // clear previous error message (if any)
            this.formErrors[field] = '';
            const control = form.get(field);
            if (control && control.dirty && !control.valid) {
                const messages = this.validationMessages[field];
                for (const key in control.errors) {
                    this.formErrors[field] += messages[key] + ' ';
                }
            }
        }
    }

    transformRmanLookupsV(rmanLookupsV: any) {
        if (rmanLookupsV) {
            return rmanLookupsV.lookupDescription;
        }
    }


    getAllRmanRulesHeader() {
        this.loading = true;
        this.rmanRulesHeaderService.getAllRmanImputedCostRules(this.paginationOptions, this.rmanRulesHeader)
            .then((rmanRulesHeaderList: any) => {
            this.loading = false;
            this.datasource = rmanRulesHeaderList.content;
            this.rmanRulesHeaderList = rmanRulesHeaderList.content;
            if (this.rmanRulesHeaderList.length > 0) {
                /*Begining of Code used for default First Row Selected*/
                this.selectedRmanRulesHeader = this.rmanRulesHeaderList[0];
                this.pRuleHeaderId = this.selectedRmanRulesHeader.cstRuleId;
                this.pRuleCategory = this.selectedRmanRulesHeader.ruleCategory;
                this.childTable.parentCall(this.selectedRmanRulesHeader);
                /*End of Code used for default First Row Selected*/
            } else {
                this.childTable.parentCall('');
            }


            this.totalElements = rmanRulesHeaderList.totalElements;
            this.pageSize = rmanRulesHeaderList.size;
            this.displaySearchDialog = false;
        }).catch((err: any) => {
            console.error(err);
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }


    getRmanRulesHeader(event: any) {

        const first: number = event.first;
        const rows: number = event.rows;
        const pageNumber: number = first / rows;
        this.paginationOptions = {
            'pageNumber': pageNumber,
            'pageSize': this.pageSize,
            'sortField': event.sortField,
            'sortOrder': event.sortOrder
        };
        this.loading = true;
        this.rmanRulesHeaderService.getAllRmanImputedCostRules(this.paginationOptions, this.rmanRulesHeader).then((rmanRulesHeaderList: any) => {
            this.loading = false;
            this.datasource = rmanRulesHeaderList.content;
            this.rmanRulesHeaderList = rmanRulesHeaderList.content;
            if (this.rmanRulesHeaderList.length > 0) {
                /*Begining of Code used for default First Row Selected*/
                this.selectedRmanRulesHeader = this.rmanRulesHeaderList[0];
                this.pRuleHeaderId = this.selectedRmanRulesHeader.cstRuleId;
                this.pRuleCategory = this.selectedRmanRulesHeader.ruleCategory;
                this.childTable.parentCall(this.selectedRmanRulesHeader);
                /*End of Code used for default First Row Selected*/
            } else {
                this.childTable.parentCall('');
            }
            this.totalElements = rmanRulesHeaderList.totalElements;
            this.pageSize = rmanRulesHeaderList.size;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });

    }


    showDialogToAdd() {

        this.newRmanRulesHeader = true;
        this.rmanRulesHeader = new RmanImputedCostRulesImpl();
        this.displayDialog = true;
        this.prepareRmanCostBasisObject();
        this.buildForm();

    }

    saveOrUpdate(msg: any) {
        this.notificationService.showSuccess(msg);
        this.loading = false;
        this.getAllRmanRulesHeader();
    }

    save() {
        if (this.newRmanRulesHeader) {
            this.loading = true;
            this.rmanRulesHeaderService.saveRmanImputedCostRules(this.rmanRulesHeader).then((response: any) => {
                this.saveOrUpdate('Saved successfully');
            }).catch((err: any) => {
                this.notificationService.showError('Error occurred while saving data');
                this.loading = false;
            });
        } else {
            this.loading = true;
            const obj = this.prepareObjectToUpdate();
            this.rmanRulesHeaderService.updateRmanImputedCostRules(obj).then((response: any) => {
                this.saveOrUpdate('Updated successfully');
            }).catch((err: any) => {
                this.notificationService.showError('Error occurred while updating data');
                this.loading = false;
            });
        }
        this.rmanRulesHeader = new RmanImputedCostRulesImpl();
        this.displayDialog = false;
    }

    delete(rmanRulesHeader: any) {
        this.rmanRulesHeader = rmanRulesHeader;
        this.rmanRulesHeader.endDate = new Date(this.rmanRulesHeader.endDate);
        this.rmanRulesHeader.startDate = new Date(this.rmanRulesHeader.startDate);
        this.displayDialog = false;
        this.confirmationService.confirm({
            message: 'Are you sure you want to delete this record?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
                this.loading = true;
                this.rmanRulesHeaderService.deleteRmanImputedCostRules(this.rmanRulesHeader).then((response: any) => {
                    this.loading = false;
                    this.rmanRulesHeaderList.splice(this.findSelectedRmanRulesHeaderIndex(), 1);
                    this.notificationService.showSuccess('Deleted successfully');
                    this.rmanRulesHeader = new RmanImputedCostRulesImpl();
                    this.getAllRmanRulesHeader();
                }, error => {
                    this.notificationService.showError('Error occurred while deleting data');
                });
            },
            reject: () => {
                this.notificationService.showWarning('You have rejected');
            }
        });
    }


    editRow(rmanRulesHeader: any) {
        this.newRmanRulesHeader = false;
        this.rmanRulesHeader = this.cloneRmanRulesHeader(rmanRulesHeader);
        if (this.rmanRulesHeader.endDate) {
            this.rmanRulesHeader.endDate = new Date(this.rmanRulesHeader.endDate);
        }
        if (this.rmanRulesHeader.startDate) {
            this.rmanRulesHeader.startDate = new Date(this.rmanRulesHeader.startDate);
        }

        this.displayDialog = true;
        this.prepareRmanCostBasisObject();

    }


    findSelectedRmanRulesHeaderIndex(): number {
        return this.rmanRulesHeaderList.indexOf(this.selectedRmanRulesHeader);
    }

    onRowUnSelect() {
        this.childTable.parentCall('');
    }


    onRowSelect(event: any) {
        this.selectedRmanRulesHeader = event.data;
        this.pRuleHeaderId = this.selectedRmanRulesHeader.cstRuleId;
        this.childTable.parentCall(event.data);

    }

    cloneRmanRulesHeader(c: RmanImputedCostRuleHeader): RmanImputedCostRuleHeader {
        const rmanRulesHeader: any
            = new RmanImputedCostRulesImpl();
        for (const prop in c) {
            rmanRulesHeader[prop] = c[prop];
        }
        return rmanRulesHeader;
    }

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {

        this.rmanRulesHeaderSearch = new RmanImputedCostRulesImpl();

        if (this.isSerached == 0) {
            this.rmanRulesHeaderSearch = new RmanImputedCostRulesImpl();
        }
        this.displaySearchDialog = true;

    }

    cancelSearch() {
        this.displaySearchDialog = false;
        this.rmanRulesHeaderSearch = new RmanImputedCostRulesImpl();

    }

    cancelEdit() {
        this.displayDialog = false;
        this.rmanRulesHeader = new RmanImputedCostRulesImpl();
    }

    search() {
        this.isSerached = 1;
        this.rmanRulesHeader = this.rmanRulesHeaderSearch;
        this.paginationOptions = {};
        this.getAllRmanRulesHeader();
    }

    prepareRmanCostBasisObject() {
        const rmanCostBasisTempObj: any = [{label: '--Select Cost Basis(*)--', value: null}];
        this.rmanCostBasisList.forEach((rmanLookupsV) => {
            rmanCostBasisTempObj.push({label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode});
        });

        this.rmanCostBasisV = rmanCostBasisTempObj;

    }

    prepareRmanActivatedFlagObject() {
        const rmanActivatedFlagTempObj: any = [{label: '--Select Activated Flag(*)--', value: null}];
        this.rmanActivatedFlagList.forEach((rmanLookupsV) => {
            rmanActivatedFlagTempObj.push({label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode});
        });

        this.rmanActivatedFlagsV = rmanActivatedFlagTempObj;

    }


    onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }

    prepareObjectToUpdate() {
        const objToUpdate = new RmanImputedCostRulesImpl();
        objToUpdate.cstRuleName = this.rmanRulesHeader.cstRuleName;
        objToUpdate.endDate = this.rmanRulesHeader.endDate;
        objToUpdate.cstAdjustment = this.rmanRulesHeader.cstAdjustment;
        objToUpdate.cstBasis = this.rmanRulesHeader.cstBasis;
        objToUpdate.enabledFlag = this.rmanRulesHeader.enabledFlag;
        objToUpdate.startDate = this.rmanRulesHeader.startDate;
        objToUpdate.cstRuleId = this.rmanRulesHeader.cstRuleId;
        return objToUpdate;
    }
}

class RmanImputedCostRulesImpl {
    constructor(
        public dealFlag?: any,
        public ruleHeaderId?: any,
        public cstRuleId?: any,
        public cstRuleName?: any,
        public ruleMasterId?: any,
        public lastUpdateDate?: any,
        public endDate?: any,
        public cstAdjustment?: any,
        public approverId?: any,
        public cstBasis?: any,
        public enabledFlag?: any,
        public startDate?: any,
        public createdBy?: any,
        public lastUpdatedBy?: any,
        public creationDate?: any,
        ) {
    }
}

interface ILabels {
    [index: string]: string;
}
