<div class="content-section implementation">
</div>
<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>
<div class="card-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block">
                    <h2>Cost Parameters</h2>
                    <div class="pull-right icons-list">
                        <a  (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
                        <a *isAuthorized="['write','MICR']" (click)="showDialogToAdd()" title="Add"><em class="fa fa-plus-circle"></em></a>
                        <a  (click)="showDialogToSearch()" title="Search"><em class="fa fa-search"></em></a>
                        <a  (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
                        <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
                            <div class="user-popup">
                                <div class="content overflow">
                                    <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()"/>
                                    <label for="selectall">Select All</label>
                                    <a class="close" title="Close" (click)="closeConfigureColumns($event)" >&times;</a>
                                    <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                                        <ng-template let-col let-index="index" pTemplate="item">
                                            <div *ngIf="col.drag">
                                                <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens"
                                                     (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                                                    <div class="drag">
                                                        <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)"/>
                                                        <label>{{col.header}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div *ngIf="!col.drag">
                                                <div class="ui-helper-clearfix">
                                                    <div>
                                                        <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"/>
                                                        <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </ng-template>
                                    </p-listbox>

                                </div>

                                <div class="pull-right">
                                    <a class="configColBtn" (click)="saveColumns()">Save</a>
                                    <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="x-scroll">
                        <p-table class="ui-datatable arrangementMgrTbl" #dt id="arrangementRules-dt" [loading]="loading" [columns]="columns" [value]="rmanRulesHeaderList" selectionMode="single" [(selection)]="selectedRmanRulesHeader"
                                 (onRowSelect)="onRowSelect($event)"  (onRowUnselect)="onRowUnSelect()" (onLazyLoad)="getRmanRulesHeader($event)" [lazy]="true" [paginator]="true" [rows]="pageSize"
                                 [totalRecords]="totalElements"  scrollable="true" [resizableColumns]="true" columnResizeMode="expand">

                            <ng-template pTemplate="colgroup" let-columns>
                                <colgroup>
                                    <col>
                                    <col *ngFor="let col of columns">
                                </colgroup>
                            </ng-template>

                            <ng-template pTemplate="header" class="arrangementMgrTblHead">
                                <tr>
                                    <th></th>
                                    <ng-container *ngFor="let col of columns">
                                        <th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                                        <th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                                    </ng-container>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-rowData let-rmanRulesHeader let-columns="columns">
                                <tr [pSelectableRow]="rowData">
                                    <td>
                                        <a *isAuthorized="['write','MICR']" (click)="editRow(rmanRulesHeader)" class="icon-edit" title="Edit"> </a>
                                        <a  *isAuthorized="['write','MICR']" (click)="delete(rmanRulesHeader)" class="icon-delete" title="Delete"> </a>
                                    </td>
                                    <ng-container *ngFor="let col of columns" >
                                        <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field]}}
                                        </td>

                                        <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field]}}
                                        </td>

                                        <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                                        </td>

                                        <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field] | round}}
                                        </td>
                                    </ng-container>

                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage" let-columns>
                                <tr *ngIf="!columns">
                                    <td class="no-data">{{noData}}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<rmanImputedCostRuleParameterValue-data [pCstRuleId]='pRuleHeaderId' [pRuleCategory]='pRuleCategory'>loading..</rmanImputedCostRuleParameterValue-data>




<p-dialog header="Search" width="800" [(visible)]="displaySearchDialog"  showEffect="fade" [modal]="true" [draggable]="true" [blockScroll]="true" (onHide)="cancelSearch()">
    <form>
        <div class="ui-grid ui-grid-responsive ui-fluid">
            <div class="ui-g-12">
                <div class="ui-g-6">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Rule Name </span>
                            <input pInputText class="textbox" placeholder="Rule Name" name="cstRuleName" id="cstRuleName" [ngModelOptions]="{standalone: true}" [(ngModel)]="rmanRulesHeaderSearch.cstRuleName"
                            />
                        </span>
                </div>
                <div class="ui-g-6 pull-right">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Cost Basis </span>
                            <p-dropdown [options]="rmanCostBasisV" [(ngModel)]="rmanRulesHeaderSearch.cstBasis" name="costBasis" [filter]="true" appendTo="body">
                            </p-dropdown>
                        </span>
                </div>
            </div>
            <div class="ui-g-12">
                <div class="ui-g-6">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Cost Adjustment % </span>
                            <input pInputText class="textbox" placeholder="Cost Adjustment %" name="cstAdjustment" id="cstAdjustment" [ngModelOptions]="{standalone: true}" [(ngModel)]="rmanRulesHeaderSearch.cstAdjustment"
                            />
                        </span>
                </div>
                <div class="ui-g-6 pull-right">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Activated Flag </span>
                            <p-dropdown [options]="rmanActivatedFlagsV" [(ngModel)]="rmanRulesHeaderSearch.enabledFlag" name="enabledFlag" [filter]="true"
                                        appendTo="body">
                            </p-dropdown>
                        </span>
                </div>
            </div>
            <!--<div class="ui-g-12">
                <div class="ui-g-6">
                        <span class="md-inputfield">
                            <span class="selectSpan"> Rule Start Date </span>
                            <p-calendar showAnim="slideDown" inputStyleClass="textbox" name="startDate" id="startDate" [(ngModel)]="rmanRulesHeaderSearch.startDate" [monthNavigator]="true"
                                        [yearNavigator]="true" yearRange="1950:2030" appendTo="body" dateFormat="yy-mm-dd"
                                        placeholder="Rule Start Date">

                        </p-calendar>
                        </span>
                </div>
                <div class="ui-g-6 pull-right">
                    <span class="selectSpan"> Rule End Date </span>
                    <p-calendar showAnim="slideDown" inputStyleClass="textbox" name="endDate" id="endDate" [(ngModel)]="rmanRulesHeaderSearch.endDate" [monthNavigator]="true"
                                [yearNavigator]="true" yearRange="1950:2030" appendTo="body" dateFormat="yy-mm-dd"
                                placeholder="Rule End Date">
                    </p-calendar>
                </div>
            </div>-->
        </div>

    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" class="primary-btn" pButton label="Search" (click)="search()"></button>
            <button type="button" class="secondary-btn" pButton (click)="displaySearchDialog=false" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>
<p-dialog header="{{(newRmanRulesHeader) ? 'Create Rule' : 'Edit Rule'}}" width="800" [(visible)]="displayDialog"
          showEffect="fade" [modal]="true" [draggable]="true" [blockScroll]="true" (onHide)="cancelEdit()">
    <form (ngSubmit)="save()" [formGroup]="arrangementRulesForm" novalidate>
        <div class="ui-grid ui-grid-responsive ui-fluid" *ngIf="rmanRulesHeader">
            <div class="ui-g">
                <div class="ui-g-12">
                    <div class="ui-g-6">
                            <span class="md-inputfield">
                                <span class="selectSpan"> Rule Name  <span class="red-color">*</span> </span>
                                <input pInputText class="textbox" placeholder="Rule Name" name="cstRuleName" id="cstRuleName" [(ngModel)]="rmanRulesHeader.cstRuleName" formControlName="cstRuleName"
                                />
                                <div *ngIf="formErrors.cstRuleName" class="ui-message ui-messages-error ui-corner-all">
                                    {{ formErrors.cstRuleName }}
                                </div>
                            </span>
                    </div>
                    <div class="ui-g-6 pull-right">
                        <span class="selectSpan"> Cost Basis <span class="red-color">*</span> </span>
                        <p-dropdown [options]="rmanCostBasisV" [(ngModel)]="rmanRulesHeader.cstBasis" name="costBasis" [filter]="true" formControlName="cstBasis"
                                    appendTo="body">

                        </p-dropdown>
                        <div *ngIf="formErrors.cstBasis" class="ui-message ui-messages-error ui-corner-all">
                            {{ formErrors.cstBasis }}
                        </div>
                    </div>
                </div>
                <div class="ui-g-12">

                    <div class="ui-g-6">
                            <span class="md-inputfield">
                                <span class="selectSpan"> Cost Adjustment % <span class="red-color">*</span> </span>
                                <input pInputText name="ajjustment" class="textbox" placeholder="Adjustment" id="cstAdjustment" formControlName="cstAdjustment"
                                       [(ngModel)]="rmanRulesHeader.cstAdjustment" />
                                    <div *ngIf="formErrors.cstAdjustment" class="ui-message ui-messages-error ui-corner-all">
                                        {{ formErrors.cstAdjustment }}
                                    </div>
                            </span>
                    </div>

                    <div class="ui-g-6 pull-right">
                            <span class="md-inputfield">
                                <span class="selectSpan"> Activated Flag <span class="red-color">*</span> </span>
                        <p-dropdown [options]="rmanActivatedFlagsV" [(ngModel)]="rmanRulesHeader.enabledFlag" name="enabledFlag" [filter]="true" formControlName="enabledFlag"
                                    appendTo="body">

                        </p-dropdown>
                        <div *ngIf="formErrors.enabledFlag" class="ui-message ui-messages-error ui-corner-all">
                            {{ formErrors.enabledFlag }}
                        </div>
                            </span>
                    </div>

                </div>
                <div class="ui-g-12">

                    <div class="ui-g-6">
                        <span class="selectSpan">Rule Start Date  <span class="red-color">*</span></span>
                        <p-calendar showAnim="slideDown" inputStyleClass="textbox" name="startDate" id="startDate" [(ngModel)]="rmanRulesHeader.startDate" [monthNavigator]="true"
                                    [yearNavigator]="true" yearRange="1950:2030" appendTo="body" formControlName="startDate" dateFormat="yy-mm-dd"
                                    placeholder="Rule Start Date*">
                            <div *ngIf="formErrors.startDate" class="ui-message ui-messages-error ui-corner-all">
                                {{ formErrors.startDate }}
                            </div>
                        </p-calendar>
                    </div>

                    <div class="ui-g-6 pull-right">
                        <span class="selectSpan">Rule End Date</span>
                        <p-calendar inputStyleClass="textbox" showAnim="slideDown" name="endDate" id="endDate" [ngModelOptions]="{standalone: true}" [(ngModel)]="rmanRulesHeader.endDate"
                                    [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" appendTo="body" dateFormat="yy-mm-dd"
                                    placeholder="Rule End Date"></p-calendar>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" class="primary-btn" pButton label="Save" (click)="save()" [disabled]="!arrangementRulesForm.valid"></button>
            <button type="button" class="secondary-btn" pButton (click)="displayDialog=false" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>


<div class="modal fade dialogueBox" id="FieldsMandatory" role="dialog" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">{{'Error'}}</h4>
            </div>
            <form class="form-horizontal">
                <div class="modal-body clearfix">
                    <div class="col-md-12 col-sm-12 col-xs-12 col-lg-12">

                        <p *ngIf="showMsg==5">Please enter Rule Name</p>
                        <p *ngIf="showMsg==6">Please enter Rule Category</p>
                        <p *ngIf="showMsg==7">Please enter Rule Start Date</p>


                    </div>

                </div>
                <div class="modal-footer" style="padding:3px;">
                    <button class="btn btn-primary pull-right" data-dismiss="modal">OK</button>
                </div>
            </form>
        </div>
    </div>
</div>
