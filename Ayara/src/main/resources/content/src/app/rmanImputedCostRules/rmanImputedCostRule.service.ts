import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {DatePipe} from '@angular/common';
declare var require: any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type': 'application/json',
  })
};


@Injectable({
  providedIn: 'root'
})
export class RmanImputedCostRuleService {

  constructor(private http: HttpClient, private datePipe: DatePipe) { }

  getAllRmanImputedCostRules(paginationOptions: any, rmanRulesHeaderSearchObject: any): Promise<any[]> {

    let serviceUrl = appSettings.apiUrl + '/ayaraImputedCostRulesSearch?';

    let searchString = '';
    const searchFeilds = ['ruleHeaderId', "cstRuleName", "endDate", "startDate", "cstAdjustment", "approverId", "cstBasis", "enabledFlag", "attribute4", "attribute5", "attribute6", "attribute7", "attribute8", "attribute9", "attribute10", "attribute11", "attribute12", "attribute13", "attribute14", "attribute15", "attribute16", "attribute17", "attribute18", "attribute19", "attribute20", "attribute21", "attribute22", "attribute23", "attribute24", "attribute25", "attribute26", "attribute27", "attribute28", "attribute29", "attribute30", "creationDate", "createdBy", "lastUpdateDate", "description", "ruleMasterId", "approverId", "sequenceNumber", "dealFlag"];
    searchFeilds.forEach((obj) => {
      if (rmanRulesHeaderSearchObject[obj] != undefined && rmanRulesHeaderSearchObject[obj] != "") {
        if ( rmanRulesHeaderSearchObject[obj] instanceof Date) {
          searchString = searchString + obj + ':' + this.datePipe.transform(rmanRulesHeaderSearchObject[obj],'yyyyMMdd') + ',';
        } else {
          searchString = searchString + obj + ':' + rmanRulesHeaderSearchObject[obj] + ',';
        }
      }
    });

    if (searchString == '') {
      serviceUrl = serviceUrl + 'search=%25';
    }
    else {
      serviceUrl = serviceUrl + 'search=' + searchString;
    }

    if (paginationOptions.pageNumber != undefined && paginationOptions.pageNumber != "" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber != 0) {
      serviceUrl = serviceUrl + '&page=' + paginationOptions.pageNumber + '&size=' + paginationOptions.pageSize;
    }

    return this.http.get(serviceUrl).toPromise().then((data: any) => {
      return data;
    });
  }

  saveRmanImputedCostRules(rmanRulesHeader: any): Promise<any[]> {
    let body = JSON.stringify(rmanRulesHeader);
    return this.http.post<any[]>(appSettings.apiUrl + '/AYARA_IMPUTED_COST_RULES', body, httpOptions).toPromise().then(data => {
      return data;
    });
  }

  updateRmanImputedCostRules(rmanRulesHeader: any): Promise<any[]> {
    const cstRuleId = rmanRulesHeader.cstRuleId;
    delete rmanRulesHeader._links;
    delete rmanRulesHeader.interests;
    delete rmanRulesHeader.cstRuleId;
    let body = JSON.stringify(rmanRulesHeader);
    return this.http.put<any[]>(appSettings.apiUrl + '/AYARA_IMPUTED_COST_RULES/' + cstRuleId, body, httpOptions).toPromise().then(data => {
      return data;
    });

  }

  deleteRmanImputedCostRules(rmanRulesHeader: any): Promise<any[]> {
    let deleteUrl = appSettings.apiUrl + '/AYARA_IMPUTED_COST_RULES/' + rmanRulesHeader.cstRuleId;
    return this.http.delete(deleteUrl, httpOptions).toPromise().then((data: any) => {
      return data;
    });
  }
}
