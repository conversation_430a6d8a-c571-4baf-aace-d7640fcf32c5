export interface RmanArrgSummaryReportV {
    dealArrangementNumber: any;
    revEndBal: any;
    postBillingDeferrals: any;
    revenueUnbilled: any;
    pbAllocationAmountFc: any;
    deliveredAllocationAmount: any;
    revCurrBal: any;
    revBegBalFc: any;
    salesTheater: any;
    salesTerritory: any;
    dealArrangementName: any;
    allocationAmount: any;
    billToCustomerNumber: any;
    unamortizedAmount: any;
    revCurrBalFc: any;
    salesRegion: any;
    shipToCustomerNumber: any;
    carveInOutAmount: any;
    billToCustomer: any;
    sspAmount: any;
    revBegBal: any;
    bookedAmount: any;
    postBillingDeferralsFc: any;
    shipToCustomer: any;
    dealArrangementSource: any;
    bookingCurrency: any;
    revEndBalFc: any;
    customerPoNum: any;
    dealArrangementId: any;
    billedAmount: any;
    allocationAmountFc: any;
    deliveredAllocationAmountFc: any;
    legalEntityName: any;
    grossMarginFc: any;
    postBillingAllocationAmount: any;
    unamortizedAmountFc: any;
    deliveredAmount: any;
    totalCostFc: any;
    dealArrangementStatus: any;
    dealArrangementBasis: any;
    creationDate: any;
    contractAsset: any;
    contractLiability: any;
    deferredRevenue: any;
    clearing: any;
    contractAssetFc: any;
    contractLiabilityFc: any;
    deferredRevenueFc: any;
    clearingFc: any;
    lastUpdateDate: any;
    lastArrgModifiedDate: any;
    salesContact: any;
    salesContract: any;
}
