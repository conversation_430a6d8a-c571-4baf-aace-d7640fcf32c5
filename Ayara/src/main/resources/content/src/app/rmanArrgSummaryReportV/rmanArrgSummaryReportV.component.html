<div class="content-section implementation">
</div>

<div class="card-wrapper">
      <div class="container-fluid">
            <div class="row">
                  <div class="col-md-12">
                        <div class="card-block">


                              <p-panel header="Consolidated Arrangements Summary Report" [toggleable]="false" (onBeforeToggle)=onBeforeToggle($event)>
                                    <p-header>
                                          <div class="pull-right icons-list" *ngIf="collapsed">
                                                <a (click)="goToOperationReports()" class="add-column">
                                                      <em class="fa fa-reply"></em>Back</a>
                                                <a (click)="onConfiguringColumns($event)" class="add-column">
                                                      <em class="fa fa-cog"></em>Columns</a>
                                                <a (click)="showDialogToSearch()" title="Search">
                                                      <em class="fa fa-search"></em>
                                                </a>
                                                <a (click)="reset(dt)" title="Reset">
                                                      <em class="fa fa-refresh"></em>
                                                </a>
                                                <a (click)="exportExcel()" title="Export" *ngIf="!disableExport">
                                                      <em class="fa fa-external-link"></em>
                                                </a>
                                                <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
                                                      <div class="user-popup">
                                                            <div class="content overflow">
                                                                  <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()" />
                                                                  <label for="selectall">Select All</label>
                                                                  <a class="close" title="Close" (click)="closeConfigureColumns($event)">&times;</a>

                                                                  <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                                                                        <ng-template let-col let-index="index" pTemplate="item">
                                                                              <div *ngIf="col.drag">
                                                                                    <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                                                                                          <div class="drag">
                                                                                                <input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" />
                                                                                                <label>{{col.header}}</label>
                                                                                          </div>
                                                                                    </div>
                                                                              </div>
                                                                              <div *ngIf="!col.drag">
                                                                                    <div class="ui-helper-clearfix">
                                                                                          <div>
                                                                                                <input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"
                                                                                                />
                                                                                                <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                                                                                          </div>
                                                                                    </div>
                                                                              </div>
                                                                        </ng-template>
                                                                  </p-listbox>
                                                            </div>
                                                            <div class="pull-right">
                                                                  <a class="configColBtn" (click)="saveColumns()">Save</a>
                                                                  <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
                                                            </div>
                                                      </div>
                                                </div>
                                          </div>
                                    </p-header>

                                    <div class="x-scroll">
                                          <p-table class="ui-datatable arrangementMgrTbl" #dt id="arrgSummary-dt" [loading]="loading" [value]="rmanArrgSummaryReportVList"
                                                selectionMode="single" (onRowSelect)="onRowSelect($event)" scrollable="true" [paginator]="showPaginator"
                                                [lazy]="true" [rows]="pageSize" [totalRecords]="totalElements" (onLazyLoad)="getRmanArrgSummaryReportV($event)"
                                                [columns]="columns" [resizableColumns]="true" columnResizeMode="expand">

                                                <ng-template pTemplate="colgroup" let-columns>
                                                      <colgroup>
                                                            <col *ngFor="let col of columns">
                                                      </colgroup>
                                                </ng-template>

                                                <ng-template pTemplate="header" class="arrangementMgrTblHead">
                                                      <tr>
                                                            <ng-container *ngFor="let col of columns">
                                                                  <th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                                                                  <th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}"
                                                                        title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                                                            </ng-container>
                                                      </tr>
                                                </ng-template>
                                                <ng-template pTemplate="body" let-rowData let-revmantra>
                                                      <tr [pSelectableRow]="rowData">

                                                            <ng-container *ngFor="let col of columns">

                                                                  <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                                                                        {{rowData[col.field]}}
                                                                  </td>

                                                                  <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
                                                                        {{rowData[col.field]}}
                                                                  </td>

                                                                  <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
                                                                        {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                                                                  </td>

                                                                  <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
                                                                        {{rowData[col.field] | round}}
                                                                  </td>

                                                            </ng-container>

                                                      </tr>
                                                </ng-template>

                                                <ng-template pTemplate="emptymessage" let-columns>
                                                      <tr>
                                                            <td [attr.colspan]="columns.length" class="no-data">{{noData}}</td>
                                                      </tr>
                                                </ng-template>
                                          </p-table>
                                    </div>

                              </p-panel>
                        </div>
                  </div>
            </div>
      </div>
</div>
<p-dialog header="Search" width="800" [(visible)]="displaySearchDialog" [draggable]="true" showEffect="fade" [modal]="true">
      <form>
            <div class="ui-grid ui-grid-responsive ui-fluid">
                  <div class="ui-g-12">
                        <div class="ui-g-6">
                              <span class="md-inputfield">
                                    <span class="selectSpan">Revenue Contract Number From</span>
                                    <!--<input pInputText name="from" id="from" class="textbox" placeholder="Revenue Contract Number From" [ngModelOptions]="{standalone: true}"
                                          [(ngModel)]="searchFields.fromArrgId" />-->
                                    <p-autoComplete  inputStyleClass="textbox" [suggestions]="dealArrangementNumbers" 
										(completeMethod)="searchRCList($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								 placeholder="RC Number Type atleast 3 Characters" id="from" [(ngModel)]="searchFields.fromArrgId"></p-autoComplete>      
                              </span>
                        </div>
                        <div class="ui-g-6 pull-right">
                              <span class="md-inputfield">
                                    <span class="selectSpan">Revenue Contract Number To</span>
                                    <!--<input pInputText name="to" id="to" class="textbox" placeholder="Revenue Contract Number To" [ngModelOptions]="{standalone: true}"
                                          [(ngModel)]="searchFields.toArrgId" />-->
                                    <p-autoComplete  inputStyleClass="textbox" [suggestions]="dealArrangementNumbers" 
										(completeMethod)="searchRCList($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								 placeholder="RC Number Type atleast 3 Characters" id="to" [(ngModel)]="searchFields.toArrgId"></p-autoComplete>      
                              </span>
                        </div>
                  </div>


                  <div class="ui-g-12">
                        <div class="ui-g-6">
                              <span class="selectSpan">From Period</span>
                              <p-calendar showAnim="slideDown" inputStyleClass="textbox" name="fromPeriod" id="fromPeriod" [monthNavigator]="true" [yearNavigator]="true"
                                    yearRange="1950:2030" appendTo="body" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.fromPeriod"
                                    dateFormat="yy-mm-dd" placeholder="From Period"></p-calendar>
                        </div>

                        <div class="ui-g-6 pull-right">
                              <span class="selectSpan">To Period</span>
                              <p-calendar showAnim="slideDown" name="toPeriod" inputStyleClass="textbox" id="toPeriod" [monthNavigator]="true" [yearNavigator]="true"
                                    yearRange="1950:2030" appendTo="body" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.toPeriod"
                                    dateFormat="yy-mm-dd" placeholder="To Period"></p-calendar>
                        </div>

                  </div>
				  
				   <div class="ui-g-12">
                        <div class="ui-g-6">
                              <span class="md-inputfield">
                                    <span class="selectSpan">Customer Name</span>
                                    <p-autoComplete  inputStyleClass="textbox" [suggestions]="customerNames" 
										(completeMethod)="searchCustomerNames($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Customer Name Type atleast 3 Characters" id="billToCustomer" [(ngModel)]="searchFields.billToCustomer" ></p-autoComplete>
                                    <!--<input pInputText name="billToCustomer" id="billToCustomer" class="textbox" placeholder="Customer Name" [ngModelOptions]="{standalone: true}"
                                          [(ngModel)]="searchFields.billToCustomer" />-->
                              </span>
                        </div>
                        <div class="ui-g-6 pull-right">
                              <span class="md-inputfield">
                                    <span class="selectSpan">Customer Number</span>
                                    <p-autoComplete  inputStyleClass="textbox" [suggestions]="customerNumbers" 
										(completeMethod)="searchCustomerNumbers($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Customer Number Type atleast 3 Characters" id="billToCustomerNumber" [(ngModel)]="searchFields.billToCustomerNumber" ></p-autoComplete>
                                    
                                    <!--<input pInputText name="billToCustomerNumber" id="billToCustomerNumber" class="textbox" placeholder="Customer Number" [ngModelOptions]="{standalone: true}"
                                          [(ngModel)]="searchFields.billToCustomerNumber" />-->
                              </span>
                        </div>
                  </div>
                  
                   <div class="ui-g-12">
                        <div class="ui-g-6">
                              <span class="md-inputfield">
                                    <span class="selectSpan">Billing Region</span>
                                    <p-autoComplete inputStyleClass="textbox" [suggestions]="regions" 
										(completeMethod)="searchRegions($event)" styleClass="wid100"
       								appendTo="body" [minLength]="2" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Billing Region Type atleast 2 Characters" id="salesRegion"  [(ngModel)]="searchFields.salesRegion" ></p-autoComplete>
                                    
                                    <!--<input pInputText name="salesRegion" id="salesRegion" class="textbox" placeholder="Billing Region" [ngModelOptions]="{standalone: true}"
                                          [(ngModel)]="searchFields.salesRegion" />-->
                              </span>
                        </div>
                        <div class="ui-g-6 pull-right">
                              <span class="md-inputfield">
                                    <span class="selectSpan">Billing Company</span>
                                    <!--<input pInputText name="legalEntityName" id="legalEntityName" class="textbox" placeholder="Billing Company" [ngModelOptions]="{standalone: true}"
                                          [(ngModel)]="searchFields.legalEntityName" />-->
                                          <p-autoComplete  inputStyleClass="textbox" [suggestions]="legalEntities" 
										(completeMethod)="searchLegalEntities($event)" styleClass="wid100"
       								appendTo="body" [minLength]="2" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Billing Company Type atleast 2 Characters" id="legalEntityName" [(ngModel)]="searchFields.legalEntityName"></p-autoComplete>
                                    
                              </span>
                        </div>
                  </div>
				  
                  <div class="ui-g-12">
                        <!--<div class="ui-g-6">
                              <span class="md-inputfield">
                                    <span class="selectSpan">Customer PO Number</span>
                                    <input pInputText name="customerPoNum" id="customerPoNum" class="textbox" placeholder="Customer PO Number" [ngModelOptions]="{standalone: true}"
                                          [(ngModel)]="searchFields.customerPoNum" />
                              </span>
                        </div>-->
                        
                        <div class="ui-g-6">
                              <span class="md-inputfield">
                                    <span class="selectSpan">Sales Contract Number</span>
                                    <input pInputText name="salesContract" id="salesContract" class="textbox" placeholder="Sales Contract Number" [ngModelOptions]="{standalone: true}"
                                          [(ngModel)]="searchFields.salesContract" />
                              </span>
                        </div>


                  </div>


            </div>

      </form>
      <p-footer>
            <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
                  <button type="submit" pButton class="primary-btn" label="Search" (click)="search()"></button>
                  <button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>
            </div>
      </p-footer>
</p-dialog>