export interface RmanContTransLines {
    attribute30: any;
    invoiceHold: any;
    sourceLineId: any;
    ruleHeaderId: any;
    releaseCogs: any;
    applyType: any;
    sourceHeaderId: any;
    lastUpdateDate: any;
    event: any;
    ranking: any;
    status: any;
    attribute29: any;
    attribute28: any;
    attribute27: any;
    avgContAcceptDur: any;
    attribute26: any;
    attribute3: any;
    createdBy: any;
    transHeaderId: any;
    maxDuration: any;
    attribute2: any;
    lastUpdatedBy: any;
    attribute1: any;
    applicationLevel: any;
    soHeaderId: any;
    revenue: any;
    creationDate: any;
    attribute9: any;
    attribute8: any;
    attribute7: any;
    productName: any;
    attribute6: any;
    attribute5: any;
    releaseType: any;
    attribute4: any;
    dealNum: any;
    soLineId: any;
    releaseRevenue: any;
    attribute10: any;
    attribute14: any;
    attribute13: any;
    attribute12: any;
    contEventType: any;
    application: any;
    attribute11: any;
    cogs: any;
    comments: any;
    templateId: any;
    ruleCategory: any;
    description: any;
    deferredMethod: any;
    attribute21: any;
    attribute20: any;
    transLineId: any;
    attribute25: any;
    attribute24: any;
    attribute23: any;
    attribute22: any;
    autoReleaseDays: any;
    percentage: any;
    dealArrangementId: any;
    element: any;
    attribute18: any;
    attribute17: any;
    attribute16: any;
    attribute15: any;
    attribute19: any;
}
