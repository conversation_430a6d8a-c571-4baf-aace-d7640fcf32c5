import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require:any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type':  'application/json',
  })
};




@Injectable()
export class RmanCustomersInterfaceService {

    constructor(private http: HttpClient) {}
    
    getServiceUrl(paginationOptions:any,rmanCustomersInterfaceSearchObject:any,  exportFlag: any, exportCols: any) {
        
        let serviceUrl = appSettings.apiUrl + '/customersInterfaceExport?';

    	 if (exportFlag == 0) {
      	 	serviceUrl = appSettings.apiUrl + '/rmanCustomersInterfaceSearch?';
    	 }

        let searchString='';


        if (rmanCustomersInterfaceSearchObject.customerId!=undefined && rmanCustomersInterfaceSearchObject.customerId!="") {
            searchString=searchString+'customerId:'+rmanCustomersInterfaceSearchObject.customerId+',';
        }

        if (rmanCustomersInterfaceSearchObject.customerName!=undefined && rmanCustomersInterfaceSearchObject.customerName!="") {
            searchString=searchString+'customerName:'+encodeURIComponent(rmanCustomersInterfaceSearchObject.customerName)+',';
        }

        if (rmanCustomersInterfaceSearchObject.customerNumber!=undefined && rmanCustomersInterfaceSearchObject.customerNumber!="") {
            searchString=searchString+'customerNumber:'+rmanCustomersInterfaceSearchObject.customerNumber+',';
        }

        if (rmanCustomersInterfaceSearchObject.status!=undefined && rmanCustomersInterfaceSearchObject.status!="") {
            searchString=searchString+'status:'+rmanCustomersInterfaceSearchObject.status+',';
        }

        if (rmanCustomersInterfaceSearchObject.lastUpdateDate!=undefined && rmanCustomersInterfaceSearchObject.lastUpdateDate!="") {
            searchString=searchString+'lastUpdateDate:'+rmanCustomersInterfaceSearchObject.lastUpdateDate+',';
        }

        if (rmanCustomersInterfaceSearchObject.lastUpdatedBy!=undefined && rmanCustomersInterfaceSearchObject.lastUpdatedBy!="") {
            searchString=searchString+'lastUpdatedBy:'+rmanCustomersInterfaceSearchObject.lastUpdatedBy+',';
        }

        if (rmanCustomersInterfaceSearchObject.creationDate!=undefined && rmanCustomersInterfaceSearchObject.creationDate!="") {
            searchString=searchString+'creationDate:'+rmanCustomersInterfaceSearchObject.creationDate+',';
        }

        if (rmanCustomersInterfaceSearchObject.createdBy!=undefined && rmanCustomersInterfaceSearchObject.createdBy!="") {
            searchString=searchString+'createdBy:'+rmanCustomersInterfaceSearchObject.createdBy+',';
        }

        if (rmanCustomersInterfaceSearchObject.customerType!=undefined && rmanCustomersInterfaceSearchObject.customerType!="") {
            searchString=searchString+'customerType:'+rmanCustomersInterfaceSearchObject.customerType+',';
        }

        if (rmanCustomersInterfaceSearchObject.customerClassCode!=undefined && rmanCustomersInterfaceSearchObject.customerClassCode!="") {
            searchString=searchString+'customerClassCode:'+rmanCustomersInterfaceSearchObject.customerClassCode+',';
        }

        if (rmanCustomersInterfaceSearchObject.primarySalesrepName!=undefined && rmanCustomersInterfaceSearchObject.primarySalesrepName!="") {
            searchString=searchString+'primarySalesrepName:'+rmanCustomersInterfaceSearchObject.primarySalesrepName+',';
        }

        if (rmanCustomersInterfaceSearchObject.salesChannelCode!=undefined && rmanCustomersInterfaceSearchObject.salesChannelCode!="") {
            searchString=searchString+'salesChannelCode:'+rmanCustomersInterfaceSearchObject.salesChannelCode+',';
        }

        if (rmanCustomersInterfaceSearchObject.attributeCategory!=undefined && rmanCustomersInterfaceSearchObject.attributeCategory!="") {
            searchString=searchString+'attributeCategory:'+rmanCustomersInterfaceSearchObject.attributeCategory+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute1!=undefined && rmanCustomersInterfaceSearchObject.attribute1!="") {
            searchString=searchString+'attribute1:'+rmanCustomersInterfaceSearchObject.attribute1+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute2!=undefined && rmanCustomersInterfaceSearchObject.attribute2!="") {
            searchString=searchString+'attribute2:'+rmanCustomersInterfaceSearchObject.attribute2+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute3!=undefined && rmanCustomersInterfaceSearchObject.attribute3!="") {
            searchString=searchString+'attribute3:'+rmanCustomersInterfaceSearchObject.attribute3+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute4!=undefined && rmanCustomersInterfaceSearchObject.attribute4!="") {
            searchString=searchString+'attribute4:'+rmanCustomersInterfaceSearchObject.attribute4+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute5!=undefined && rmanCustomersInterfaceSearchObject.attribute5!="") {
            searchString=searchString+'attribute5:'+rmanCustomersInterfaceSearchObject.attribute5+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute6!=undefined && rmanCustomersInterfaceSearchObject.attribute6!="") {
            searchString=searchString+'attribute6:'+rmanCustomersInterfaceSearchObject.attribute6+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute7!=undefined && rmanCustomersInterfaceSearchObject.attribute7!="") {
            searchString=searchString+'attribute7:'+rmanCustomersInterfaceSearchObject.attribute7+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute8!=undefined && rmanCustomersInterfaceSearchObject.attribute8!="") {
            searchString=searchString+'attribute8:'+rmanCustomersInterfaceSearchObject.attribute8+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute9!=undefined && rmanCustomersInterfaceSearchObject.attribute9!="") {
            searchString=searchString+'attribute9:'+rmanCustomersInterfaceSearchObject.attribute9+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute10!=undefined && rmanCustomersInterfaceSearchObject.attribute10!="") {
            searchString=searchString+'attribute10:'+rmanCustomersInterfaceSearchObject.attribute10+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute11!=undefined && rmanCustomersInterfaceSearchObject.attribute11!="") {
            searchString=searchString+'attribute11:'+rmanCustomersInterfaceSearchObject.attribute11+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute12!=undefined && rmanCustomersInterfaceSearchObject.attribute12!="") {
            searchString=searchString+'attribute12:'+rmanCustomersInterfaceSearchObject.attribute12+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute13!=undefined && rmanCustomersInterfaceSearchObject.attribute13!="") {
            searchString=searchString+'attribute13:'+rmanCustomersInterfaceSearchObject.attribute13+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute14!=undefined && rmanCustomersInterfaceSearchObject.attribute14!="") {
            searchString=searchString+'attribute14:'+rmanCustomersInterfaceSearchObject.attribute14+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute15!=undefined && rmanCustomersInterfaceSearchObject.attribute15!="") {
            searchString=searchString+'attribute15:'+rmanCustomersInterfaceSearchObject.attribute15+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute16!=undefined && rmanCustomersInterfaceSearchObject.attribute16!="") {
            searchString=searchString+'attribute16:'+rmanCustomersInterfaceSearchObject.attribute16+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute17!=undefined && rmanCustomersInterfaceSearchObject.attribute17!="") {
            searchString=searchString+'attribute17:'+rmanCustomersInterfaceSearchObject.attribute17+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute18!=undefined && rmanCustomersInterfaceSearchObject.attribute18!="") {
            searchString=searchString+'attribute18:'+rmanCustomersInterfaceSearchObject.attribute18+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute19!=undefined && rmanCustomersInterfaceSearchObject.attribute19!="") {
            searchString=searchString+'attribute19:'+rmanCustomersInterfaceSearchObject.attribute19+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute20!=undefined && rmanCustomersInterfaceSearchObject.attribute20!="") {
            searchString=searchString+'attribute20:'+rmanCustomersInterfaceSearchObject.attribute20+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute21!=undefined && rmanCustomersInterfaceSearchObject.attribute21!="") {
            searchString=searchString+'attribute21:'+rmanCustomersInterfaceSearchObject.attribute21+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute22!=undefined && rmanCustomersInterfaceSearchObject.attribute22!="") {
            searchString=searchString+'attribute22:'+rmanCustomersInterfaceSearchObject.attribute22+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute23!=undefined && rmanCustomersInterfaceSearchObject.attribute23!="") {
            searchString=searchString+'attribute23:'+rmanCustomersInterfaceSearchObject.attribute23+',';
        }

        if (rmanCustomersInterfaceSearchObject.attribute24!=undefined && rmanCustomersInterfaceSearchObject.attribute24!="") {
            searchString=searchString+'attribute24:'+rmanCustomersInterfaceSearchObject.attribute24+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttributeCategory!=undefined && rmanCustomersInterfaceSearchObject.globalAttributeCategory!="") {
            searchString=searchString+'globalAttributeCategory:'+rmanCustomersInterfaceSearchObject.globalAttributeCategory+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute1!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute1!="") {
            searchString=searchString+'globalAttribute1:'+rmanCustomersInterfaceSearchObject.globalAttribute1+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute2!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute2!="") {
            searchString=searchString+'globalAttribute2:'+rmanCustomersInterfaceSearchObject.globalAttribute2+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute4!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute4!="") {
            searchString=searchString+'globalAttribute4:'+rmanCustomersInterfaceSearchObject.globalAttribute4+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute3!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute3!="") {
            searchString=searchString+'globalAttribute3:'+rmanCustomersInterfaceSearchObject.globalAttribute3+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute5!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute5!="") {
            searchString=searchString+'globalAttribute5:'+rmanCustomersInterfaceSearchObject.globalAttribute5+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute6!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute6!="") {
            searchString=searchString+'globalAttribute6:'+rmanCustomersInterfaceSearchObject.globalAttribute6+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute7!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute7!="") {
            searchString=searchString+'globalAttribute7:'+rmanCustomersInterfaceSearchObject.globalAttribute7+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute8!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute8!="") {
            searchString=searchString+'globalAttribute8:'+rmanCustomersInterfaceSearchObject.globalAttribute8+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute9!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute9!="") {
            searchString=searchString+'globalAttribute9:'+rmanCustomersInterfaceSearchObject.globalAttribute9+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute10!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute10!="") {
            searchString=searchString+'globalAttribute10:'+rmanCustomersInterfaceSearchObject.globalAttribute10+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute11!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute11!="") {
            searchString=searchString+'globalAttribute11:'+rmanCustomersInterfaceSearchObject.globalAttribute11+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute12!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute12!="") {
            searchString=searchString+'globalAttribute12:'+rmanCustomersInterfaceSearchObject.globalAttribute12+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute13!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute13!="") {
            searchString=searchString+'globalAttribute13:'+rmanCustomersInterfaceSearchObject.globalAttribute13+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute14!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute14!="") {
            searchString=searchString+'globalAttribute14:'+rmanCustomersInterfaceSearchObject.globalAttribute14+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute15!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute15!="") {
            searchString=searchString+'globalAttribute15:'+rmanCustomersInterfaceSearchObject.globalAttribute15+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute16!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute16!="") {
            searchString=searchString+'globalAttribute16:'+rmanCustomersInterfaceSearchObject.globalAttribute16+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute17!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute17!="") {
            searchString=searchString+'globalAttribute17:'+rmanCustomersInterfaceSearchObject.globalAttribute17+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute18!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute18!="") {
            searchString=searchString+'globalAttribute18:'+rmanCustomersInterfaceSearchObject.globalAttribute18+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute19!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute19!="") {
            searchString=searchString+'globalAttribute19:'+rmanCustomersInterfaceSearchObject.globalAttribute19+',';
        }

        if (rmanCustomersInterfaceSearchObject.globalAttribute20!=undefined && rmanCustomersInterfaceSearchObject.globalAttribute20!="") {
            searchString=searchString+'globalAttribute20:'+rmanCustomersInterfaceSearchObject.globalAttribute20+',';
        }

        if (rmanCustomersInterfaceSearchObject.origSystemReference!=undefined && rmanCustomersInterfaceSearchObject.origSystemReference!="") {
            searchString=searchString+'origSystemReference:'+rmanCustomersInterfaceSearchObject.origSystemReference+',';
        }

        if (rmanCustomersInterfaceSearchObject.personFirstName!=undefined && rmanCustomersInterfaceSearchObject.personFirstName!="") {
            searchString=searchString+'personFirstName:'+rmanCustomersInterfaceSearchObject.personFirstName+',';
        }

        if (rmanCustomersInterfaceSearchObject.personMiddleName!=undefined && rmanCustomersInterfaceSearchObject.personMiddleName!="") {
            searchString=searchString+'personMiddleName:'+rmanCustomersInterfaceSearchObject.personMiddleName+',';
        }

        if (rmanCustomersInterfaceSearchObject.personLastName!=undefined && rmanCustomersInterfaceSearchObject.personLastName!="") {
            searchString=searchString+'personLastName:'+rmanCustomersInterfaceSearchObject.personLastName+',';
        }

        if (rmanCustomersInterfaceSearchObject.personNameSuffix!=undefined && rmanCustomersInterfaceSearchObject.personNameSuffix!="") {
            searchString=searchString+'personNameSuffix:'+rmanCustomersInterfaceSearchObject.personNameSuffix+',';
        }

        if (rmanCustomersInterfaceSearchObject.personTitle!=undefined && rmanCustomersInterfaceSearchObject.personTitle!="") {
            searchString=searchString+'personTitle:'+rmanCustomersInterfaceSearchObject.personTitle+',';
        }

        if (rmanCustomersInterfaceSearchObject.personAcademicTitle!=undefined && rmanCustomersInterfaceSearchObject.personAcademicTitle!="") {
            searchString=searchString+'personAcademicTitle:'+rmanCustomersInterfaceSearchObject.personAcademicTitle+',';
        }

        if (rmanCustomersInterfaceSearchObject.personPreviousLastName!=undefined && rmanCustomersInterfaceSearchObject.personPreviousLastName!="") {
            searchString=searchString+'personPreviousLastName:'+rmanCustomersInterfaceSearchObject.personPreviousLastName+',';
        }

        if (rmanCustomersInterfaceSearchObject.knownAs!=undefined && rmanCustomersInterfaceSearchObject.knownAs!="") {
            searchString=searchString+'knownAs:'+rmanCustomersInterfaceSearchObject.knownAs+',';
        }

        if (rmanCustomersInterfaceSearchObject.personIdenType!=undefined && rmanCustomersInterfaceSearchObject.personIdenType!="") {
            searchString=searchString+'personIdenType:'+rmanCustomersInterfaceSearchObject.personIdenType+',';
        }

        if (rmanCustomersInterfaceSearchObject.personIdentifier!=undefined && rmanCustomersInterfaceSearchObject.personIdentifier!="") {
            searchString=searchString+'personIdentifier:'+rmanCustomersInterfaceSearchObject.personIdentifier+',';
        }

        if (rmanCustomersInterfaceSearchObject.groupType!=undefined && rmanCustomersInterfaceSearchObject.groupType!="") {
            searchString=searchString+'groupType:'+rmanCustomersInterfaceSearchObject.groupType+',';
        }

        if (rmanCustomersInterfaceSearchObject.country!=undefined && rmanCustomersInterfaceSearchObject.country!="") {
            searchString=searchString+'country:'+rmanCustomersInterfaceSearchObject.country+',';
        }

        if (rmanCustomersInterfaceSearchObject.address1!=undefined && rmanCustomersInterfaceSearchObject.address1!="") {
            searchString=searchString+'address1:'+rmanCustomersInterfaceSearchObject.address1+',';
        }

        if (rmanCustomersInterfaceSearchObject.address2!=undefined && rmanCustomersInterfaceSearchObject.address2!="") {
            searchString=searchString+'address2:'+rmanCustomersInterfaceSearchObject.address2+',';
        }

        if (rmanCustomersInterfaceSearchObject.address3!=undefined && rmanCustomersInterfaceSearchObject.address3!="") {
            searchString=searchString+'address3:'+rmanCustomersInterfaceSearchObject.address3+',';
        }

        if (rmanCustomersInterfaceSearchObject.address4!=undefined && rmanCustomersInterfaceSearchObject.address4!="") {
            searchString=searchString+'address4:'+rmanCustomersInterfaceSearchObject.address4+',';
        }

        if (rmanCustomersInterfaceSearchObject.city!=undefined && rmanCustomersInterfaceSearchObject.city!="") {
            searchString=searchString+'city:'+rmanCustomersInterfaceSearchObject.city+',';
        }

        if (rmanCustomersInterfaceSearchObject.postalCode!=undefined && rmanCustomersInterfaceSearchObject.postalCode!="") {
            searchString=searchString+'postalCode:'+rmanCustomersInterfaceSearchObject.postalCode+',';
        }

        if (rmanCustomersInterfaceSearchObject.state!=undefined && rmanCustomersInterfaceSearchObject.state!="") {
            searchString=searchString+'state:'+rmanCustomersInterfaceSearchObject.state+',';
        }

        if (rmanCustomersInterfaceSearchObject.province!=undefined && rmanCustomersInterfaceSearchObject.province!="") {
            searchString=searchString+'province:'+rmanCustomersInterfaceSearchObject.province+',';
        }

        if (rmanCustomersInterfaceSearchObject.county!=undefined && rmanCustomersInterfaceSearchObject.county!="") {
            searchString=searchString+'county:'+rmanCustomersInterfaceSearchObject.county+',';
        }

        if (rmanCustomersInterfaceSearchObject.sicCodeType!=undefined && rmanCustomersInterfaceSearchObject.sicCodeType!="") {
            searchString=searchString+'sicCodeType:'+rmanCustomersInterfaceSearchObject.sicCodeType+',';
        }

        if (rmanCustomersInterfaceSearchObject.emailAddress!=undefined && rmanCustomersInterfaceSearchObject.emailAddress!="") {
            searchString=searchString+'emailAddress:'+rmanCustomersInterfaceSearchObject.emailAddress+',';
        }

        if (rmanCustomersInterfaceSearchObject.doNotMailFlag!=undefined && rmanCustomersInterfaceSearchObject.doNotMailFlag!="") {
            searchString=searchString+'doNotMailFlag:'+rmanCustomersInterfaceSearchObject.doNotMailFlag+',';
        }

        if (rmanCustomersInterfaceSearchObject.dunsNumber!=undefined && rmanCustomersInterfaceSearchObject.dunsNumber!="") {
            searchString=searchString+'dunsNumber:'+rmanCustomersInterfaceSearchObject.dunsNumber+',';
        }

        if (rmanCustomersInterfaceSearchObject.requestId!=undefined && rmanCustomersInterfaceSearchObject.requestId!="") {
            searchString=searchString+'requestId:'+rmanCustomersInterfaceSearchObject.requestId+',';
        }

        if (rmanCustomersInterfaceSearchObject.primaryPhoneNumber!=undefined && rmanCustomersInterfaceSearchObject.primaryPhoneNumber!="") {
            searchString=searchString+'primaryPhoneNumber:'+rmanCustomersInterfaceSearchObject.primaryPhoneNumber+',';
        }

        if (rmanCustomersInterfaceSearchObject.faxNumber!=undefined && rmanCustomersInterfaceSearchObject.faxNumber!="") {
            searchString=searchString+'faxNumber:'+rmanCustomersInterfaceSearchObject.faxNumber+',';
        }

        if (rmanCustomersInterfaceSearchObject.financeContact!=undefined && rmanCustomersInterfaceSearchObject.financeContact!="") {
            searchString=searchString+'financeContact:'+rmanCustomersInterfaceSearchObject.financeContact+',';
        }

        if (rmanCustomersInterfaceSearchObject.paymentTerms!=undefined && rmanCustomersInterfaceSearchObject.paymentTerms!="") {
            searchString=searchString+'paymentTerms:'+rmanCustomersInterfaceSearchObject.paymentTerms+',';
        }

        if (rmanCustomersInterfaceSearchObject.revAccountId!=undefined && rmanCustomersInterfaceSearchObject.revAccountId!="") {
            searchString=searchString+'revAccountId:'+rmanCustomersInterfaceSearchObject.revAccountId+',';
        }

        if (rmanCustomersInterfaceSearchObject.cogsAccountId!=undefined && rmanCustomersInterfaceSearchObject.cogsAccountId!="") {
            searchString=searchString+'cogsAccountId:'+rmanCustomersInterfaceSearchObject.cogsAccountId;
        }

		if (rmanCustomersInterfaceSearchObject.interfaceProcessId!=undefined && rmanCustomersInterfaceSearchObject.interfaceProcessId!="") {
            searchString=searchString+'interfaceProcessId:'+rmanCustomersInterfaceSearchObject.interfaceProcessId+',';
        }

        searchString=searchString+'interfaceStatus:E'+',';


        if (searchString == '') {
            serviceUrl=serviceUrl+'search=%25';
        }
        else {
            serviceUrl=serviceUrl+'search='+searchString;
        }
        
        if (paginationOptions.pageNumber!=undefined && paginationOptions.pageNumber!="" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber!=0) {
           serviceUrl=serviceUrl+'&page='+paginationOptions.pageNumber+'&size='+paginationOptions.pageSize;
        }
                
        if (exportCols != undefined && exportCols != "") {
      		serviceUrl = serviceUrl + '&exportCols=' + exportCols;
    	}
   		
   		return serviceUrl;
    }
    
	getAllRmanCustomersInterface(paginationOptions:any,rmanCustomersInterfaceSearchObject:any, exportCols: any): Promise<any[]> {
        
        let serviceUrl = this.getServiceUrl(paginationOptions, rmanCustomersInterfaceSearchObject, 0, exportCols);
        return this.http.get(serviceUrl).toPromise().then((data:any) => {
            return data;
        });
        
    }

    deleteSelectedLines(exceptionLines: any[]){

        let delUrl = appSettings.apiUrl + '/bulkDelCustomerExceptions?customerIds='+exceptionLines;
        return this.http.delete(delUrl,{responseType : "text"}).toPromise().then((data:any) => {
          return data;
        });

    }
    updateExceptionLogs(exception: any, event: string) {
          return this.http.post(
            `${appSettings.apiUrl}/updateCustomerExceptionLogs/${exception.customerId}`,
            exception,
            {
              headers: {
                'Content-Type': 'application/json'
              },
              params: {
                event: event,
                documentCategory: exception.documentCategory,
                documentSource: exception.documentSource
              }
            }
          );
        }


}
