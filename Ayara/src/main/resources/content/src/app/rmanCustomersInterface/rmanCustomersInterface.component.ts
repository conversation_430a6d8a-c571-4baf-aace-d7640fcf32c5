import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { Table } from 'primeng/table';
import { NotificationService } from '../shared/notifications.service';
import { UploadService } from '../shared/upload.service';
import { RmanCustomersInterface } from './rmanCustomersInterface';
import { RmanCustomersInterfaceService } from './rmanCustomersInterfaceservice';
import { CommonSharedService } from '../shared/common.service';
import { AllExceptionsService } from '../exceptions/allExceptions/allExceptions.service';

declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Component({
    templateUrl: './rmanCustomersInterface.component.html',
    selector: 'rmanCustomersInterface-data',
    providers: [RmanCustomersInterfaceService, ConfirmationService]
})

export class RmanCustomersInterfaceComponent {

    displayDialog: boolean;

    displaySearchDialog: boolean;

    uploadLoading: boolean = false;

    rmanCustomersInterface: RmanCustomersInterface = new RmanCustomersInterfaceImpl();

    rmanCustomersInterfaceSearch: RmanCustomersInterface = new RmanCustomersInterfaceImpl();

    isSerached: number = 0;

    selectedRmanCustomersInterface: RmanCustomersInterface;

    newRmanCustomersInterface: boolean;

	displayCustomersInterfaceDialog: boolean = false;

    rmanCustomersInterfaceList: RmanCustomersInterface[];

    cols: any[];
    //columns: ILabels;

    columnOptions: any[];

    paginationOptions = {};

    pages: {};


    datasource: any[];
    pageSize: number;
    totalElements: number;
    collapsed: boolean = true;

    customerForm: FormGroup;
    noData = appSettings.noData;
    loading: boolean;
    rmanCustomersInterfacetatus: any[];
    regions:any[];
    customerTypes:any[];
    isSelectAllChecked = true;
    globalCols: any[];
    clonedCols: any[];
    userId: number;
    showPaginator: boolean = true;
    startIndex: number;
    showAddColumns = true;
    columns: any[];

    exportCols: string[] = [];
    disableExport: boolean = true;
    selectedLines: any[] = [];
	  exceptionsList: any[] = [];
    deletedCusotmers: any[]=[];



    constructor(
        private rmanCustomersInterfaceService: RmanCustomersInterfaceService,
        private confirmationService: ConfirmationService,
        private formBuilder: FormBuilder,
        private notificationService:NotificationService,
        public _uploadService:UploadService,  private commonSharedService: CommonSharedService
    ) {

        // generate list code
        this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };


    }

    ngOnInit() {
      this.selectedLines = [];
        this.globalCols = [
            { field: 'customerNumber', header: 'CUSTOMER_NUMBER', showField: true, drag: false, display: "table-cell",type:'text'},
            { field: 'customerName', header: 'CUSTOMER_NAME', showField: true, drag: true, display: "table-cell",type:'text' },
            { field: 'status', header: 'STATUS', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'primarySalesrepName', header: 'PRIMARY_SALESREP_NAME', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'region', header: 'REGION', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'customerType', header: 'CUSTOMER_TYPE', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'interfaceProcessId', header: 'INTERFACE_PROCESS_ID', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'interfaceError', header: 'INTERFACE_ERROR', showField: true, drag: true, display: "table-cell",type:'text'}
        ];

        this.columns = [];
        this.getTableColumns("rmanCustomersInterface", "CustomersInterface");
    }

    prepareRmanStatus() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectStat, value: null }];
        this.rmanCustomersInterfacetatus.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode });
        });
        this.rmanCustomersInterfacetatus = rmanLookupsVTempObj;
    }

    prepareRegions() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectRegion, value: null }];
        this.regions.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupDescription });
        });
        this.regions = rmanLookupsVTempObj;
    }

    prepareCustomerTypes() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectCustomerType, value: null }];
        this.customerTypes.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupDescription });
        });
        this.customerTypes = rmanLookupsVTempObj;
    }

    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
          if (response && response != null && response.userId) {
            this.columns = [];
            let colsList = response.tableColumns.split(",");
            if (colsList.length > 0) {
              colsList.forEach((item, index) => {
                if (item) {
                  this.startIndex = this.globalCols.findIndex(col => col.field == item);
                  this.onDrop(index);
                }
              });
            }
            this.globalCols.forEach(col => {
              if (response.tableColumns.indexOf(col.field) !== -1) {
                this.columns.push(col);
              } else {
                col.showField = false;
              }
            });
            if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
            this.showPaginator = this.columns.length !== 0;
            this.userId = response.userId;
          } else {
            this.columns = this.globalCols;
          }
        }).catch(() => {
          this.notificationService.showError('Error occured while getting table columns data');
          this.loading = false;
        });
      }

      saveColumns() {
        let selectedCols = "";
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
          if (col.showField) {
            selectedCols += col.field;
            if (index < colLength) {
              selectedCols += ",";
            }
          }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns("rmanCustomersInterface", "CustomersInterface", selectedCols, this.userId).then((response) => {
          this.columns = this.globalCols.filter(item => item.showField);
          this.userId = response["userId"];
          this.showPaginator = this.columns.length !== 0;
          this.loading = false;
        }).catch(() => {
          this.notificationService.showError('Error occured while getting data');
          this.loading = false;
        });
      }

      onDragStart(index: number) {
        this.startIndex = index;
      }

      onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
        //console.log(this.globalCols);
      }

      selectColumns(col: any) {
        let cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
          this.isSelectAllChecked = false;
        } else {
          this.isSelectAllChecked = true;
        }
      }

      onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
          if (this.isSelectAllChecked) {
            col.showField = true;
          } else {
            if (col.drag) {
              col.showField = false;
            }
          }
        });
      }

      onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
      }

      closeConfigureColumns(event: any) {
      this.showAddColumns = true;
      this.globalCols = this.clonedCols;
      let configCol = this.globalCols.filter(item => !item.showField);
      this.isSelectAllChecked = !(configCol.length > 0);
      }



    getAllRmanCustomersInterface() {
        this.loading = true;
        this.rmanCustomersInterfaceService.getAllRmanCustomersInterface(this.paginationOptions, this.rmanCustomersInterface, this.exportCols).then((rmanCustomersInterfaceList: any) => {
            this.loading = false;
            this.datasource = rmanCustomersInterfaceList.content;
            this.rmanCustomersInterfaceList = rmanCustomersInterfaceList.content;
            this.totalElements = rmanCustomersInterfaceList.totalElements;
            this.pageSize = rmanCustomersInterfaceList.size;
            this.displaySearchDialog = false;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    getRmanCustomersInterface(event: any) {
        this.loading = true;
        let first: number = event.first;
        let rows: number = event.rows;
        let pageNumber: number = first / rows;
        this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
        this.rmanCustomersInterfaceService.getAllRmanCustomersInterface(this.paginationOptions, this.rmanCustomersInterface, this.exportCols).then((rmanCustomersInterfaceList: any) => {
            this.loading = false;
            this.datasource = rmanCustomersInterfaceList.content;
            this.rmanCustomersInterfaceList = rmanCustomersInterfaceList.content;
            this.totalElements = rmanCustomersInterfaceList.totalElements;
            this.pageSize = rmanCustomersInterfaceList.size;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });

    }

    exportExcel() {
	    this.exportCols = [];
	    for (let index = 0; index < this.columns.length; index++) {
	      if (this.columns[index].showField) {
	        this.exportCols.push(this.columns[index].field);
	      }
	    }

	    let serviceUrl = this.rmanCustomersInterfaceService.getServiceUrl(this.paginationOptions, this.rmanCustomersInterface, 1, this.exportCols);
	    window.location.href = serviceUrl;
   }



   onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }


    reset(dt: Table) {
        this.paginationOptions = {};
        this.rmanCustomersInterface = new RmanCustomersInterfaceImpl();
        this.rmanCustomersInterfaceSearch = new RmanCustomersInterfaceImpl();
        this.selectedLines = [];
        this.deletedCusotmers=[];
        dt.reset();
    }



    findSelectedRmanCustomersInterfaceIndex(): number {
        return this.rmanCustomersInterfaceList.indexOf(this.selectedRmanCustomersInterface);
    }

    onRowSelect(event: any) {

    }


    hideColumnMenu: boolean = true;

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    showFilter: boolean = false;

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {
        if (this.isSerached == 0) {
            this.rmanCustomersInterfaceSearch = new RmanCustomersInterfaceImpl();
        }
        this.rmanCustomersInterfaceSearch = new RmanCustomersInterfaceImpl();
        this.displaySearchDialog = true;

    }

	search() {

        this.isSerached = 1;
        this.rmanCustomersInterface = this.rmanCustomersInterfaceSearch;
        this.paginationOptions={};
        this.getAllRmanCustomersInterface();
    }


    showUploadLoader(){
    	this.uploadLoading = true;
    }
    deleteSelected(dt: Table) {

        this.exceptionsList = [];
        console.log(this.selectedLines);

        for (let line of this.selectedLines) {
            this.exceptionsList.push(line.customerId);
            this.deletedCusotmers.push(line);
        }
        if (this.exceptionsList.length > 0) {

        this.confirmationService.confirm({
            message: 'Are you sure you want to delete?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
              this.loading = true;
              this.rmanCustomersInterfaceService.deleteSelectedLines(this.exceptionsList).then((response: any) => {
                for(let customers of this.deletedCusotmers){
                  this.rmanCustomersInterfaceService.updateExceptionLogs(customers,'deleted').subscribe();
                }
                this.notificationService.showSuccess('Selected records are deleted Successfully  ');
                this.deletedCusotmers=[];
            }).then(() => {
                this.reset(dt);
            }).catch((err: any) => {
                this.loading = false;
                this.notificationService.showError('Getting error While delete records');
            });
            },
            reject: () => {
              this.notificationService.showWarning('You have rejected');
            }
          });

        } else {
            this.notificationService.showInfo('Select at least one record to delete');
        }
    }


}


export class RmanCustomersInterfaceImpl implements RmanCustomersInterface {
    constructor(public groupType?: any, public globalAttribute10?: any, public lastUpdateDate?: any, public globalAttribute5?: any, public globalAttribute4?: any, public personTitle?: any, public status?: any, public globalAttribute7?: any, public globalAttribute6?: any, public globalAttribute1?: any, public country?: any, public emailAddress?: any, public knownAs?: any, public globalAttribute3?: any, public globalAttribute2?: any, public personPreviousLastName?: any, public personNameSuffix?: any, public globalAttribute9?: any, public personFirstName?: any, public globalAttribute8?: any, public county?: any, public createdBy?: any, public attribute3?: any, public lastUpdatedBy?: any, public attribute2?: any, public origSystemReference?: any, public attribute1?: any, public customerNumber?: any, public customerClassCode?: any, public personIdentifier?: any, public revAccountId?: any, public customerId?: any, public creationDate?: any, public attribute9?: any, public attribute8?: any, public attribute7?: any, public attribute6?: any, public attribute5?: any, public attribute4?: any, public attributeCategory?: any, public personMiddleName?: any, public attribute10?: any, public attribute14?: any, public attribute13?: any, public attribute12?: any, public customerType?: any, public attribute11?: any, public state?: any, public dunsNumber?: any, public requestId?: any, public primarySalesrepName?: any, public financeContact?: any, public postalCode?: any, public paymentTerms?: any, public salesChannelCode?: any, public sicCodeType?: any, public address1?: any, public primaryPhoneNumber?: any, public address3?: any, public address2?: any, public attribute21?: any, public cogsAccountId?: any, public attribute20?: any, public address4?: any, public attribute24?: any, public attribute23?: any, public attribute22?: any, public globalAttribute20?: any, public personAcademicTitle?: any, public globalAttributeCategory?: any, public globalAttribute17?: any, public globalAttribute18?: any, public globalAttribute15?: any, public personLastName?: any, public globalAttribute16?: any, public faxNumber?: any, public customerName?: any, public globalAttribute13?: any, public city?: any, public doNotMailFlag?: any, public globalAttribute14?: any, public globalAttribute11?: any, public personIdenType?: any, public globalAttribute12?: any, public attribute18?: any, public attribute17?: any, public attribute16?: any, public province?: any, public attribute15?: any, public globalAttribute19?: any, public attribute19?: any, public region?: any) { }
}

interface ILabels {
    [index: string]: string;
}
