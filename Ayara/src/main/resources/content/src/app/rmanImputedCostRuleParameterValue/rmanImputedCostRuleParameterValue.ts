export interface RmanImputedCostRuleParameterValue {
    attribute10: any;
    dealFlag: any;
    attribute30: any;
    attribute14: any;
    ruleHeaderId: any;
    attribute13: any;
    attribute12: any;
    attribute11: any;
    lastUpdateDate: any;
    attribute29: any;
    attribute28: any;
    attribute27: any;
    attribute26: any;
    parameterValueId: any;
    parameterId: any;
    attribute3: any;
    attribute21: any;
    createdBy: any;
    attribute2: any;
    attribute20: any;
    lastUpdatedBy: any;
    attribute1: any;
    attribute25: any;
    attribute24: any;
    attribute23: any;
    attribute22: any;
    creationDate: any;
    attribute9: any;
    attribute8: any;
    attribute7: any;
    attribute6: any;
    attribute5: any;
    attribute4: any;
    parameterGroup: any;
    attribute18: any;
    attribute17: any;
    attribute16: any;
    attribute15: any;
    qualifier: any;
    andOr: any;
    parameterValue: any;
    paramterGroup: any;
    attribute19: any;
}
