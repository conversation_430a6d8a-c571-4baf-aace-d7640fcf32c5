import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require: any;
const appSettings = require('../appsettings');


const httpOptions = {
    headers: new HttpHeaders({
        'Content-Type': 'application/json',
    })
};


@Injectable()
export class RmanImputedCostRuleParameterValueservice {

    constructor(private http: HttpClient) { }

    getAllRmanRuleParameterValue(paginationOptions: any, rmanRuleParameterValueSearchObject: any): Promise<any[]> {

        let serviceUrl = appSettings.apiUrl + '/ayaraImputedCostRuleParametersSearch?';

        let searchString = '';

        if (rmanRuleParameterValueSearchObject.parameterValueId != undefined && rmanRuleParameterValueSearchObject.parameterValueId != "") {
            searchString = searchString + 'parameterValueId:' + rmanRuleParameterValueSearchObject.parameterValueId + ',';
        }

        if (rmanRuleParameterValueSearchObject.cstRuleId != undefined && rmanRuleParameterValueSearchObject.cstRuleId != "") {
            searchString = searchString + 'cstRuleId:' + rmanRuleParameterValueSearchObject.cstRuleId + ',';
        }
        if (rmanRuleParameterValueSearchObject.paramterGroup != undefined && rmanRuleParameterValueSearchObject.paramterGroup != "") {
            searchString = searchString + 'paramterGroup:' + rmanRuleParameterValueSearchObject.paramterGroup + ',';
        }

        if (rmanRuleParameterValueSearchObject.parameterId != undefined && rmanRuleParameterValueSearchObject.parameterId != "") {
            searchString = searchString + 'parameterId:' + rmanRuleParameterValueSearchObject.parameterId + ',';
        }

        if (rmanRuleParameterValueSearchObject.qualifier != undefined && rmanRuleParameterValueSearchObject.qualifier != "") {
            searchString = searchString + 'qualifier:' + rmanRuleParameterValueSearchObject.qualifier + ',';
        }

        if (rmanRuleParameterValueSearchObject.parameterValue != undefined && rmanRuleParameterValueSearchObject.parameterValue != "") {
            searchString = searchString + 'parameterValue:' + rmanRuleParameterValueSearchObject.parameterValue + ',';
        }



        if (rmanRuleParameterValueSearchObject.creationDate != undefined && rmanRuleParameterValueSearchObject.creationDate != "") {
            searchString = searchString + 'creationDate:' + rmanRuleParameterValueSearchObject.creationDate + ',';
        }

        if (rmanRuleParameterValueSearchObject.createdBy != undefined && rmanRuleParameterValueSearchObject.createdBy != "") {
            searchString = searchString + 'createdBy:' + rmanRuleParameterValueSearchObject.createdBy + ',';
        }

        if (rmanRuleParameterValueSearchObject.lastUpdateDate != undefined && rmanRuleParameterValueSearchObject.lastUpdateDate != "") {
            searchString = searchString + 'lastUpdateDate:' + rmanRuleParameterValueSearchObject.lastUpdateDate + ',';
        }

        if (rmanRuleParameterValueSearchObject.lastUpdatedBy != undefined && rmanRuleParameterValueSearchObject.lastUpdatedBy != "") {
            searchString = searchString + 'lastUpdatedBy:' + rmanRuleParameterValueSearchObject.lastUpdatedBy + ',';
        }

        if (rmanRuleParameterValueSearchObject.parameterName != undefined && rmanRuleParameterValueSearchObject.parameterName != "") {
            searchString = searchString + 'parameterName:' + rmanRuleParameterValueSearchObject.parameterName + ',';
        }

        if (rmanRuleParameterValueSearchObject.andOr != undefined && rmanRuleParameterValueSearchObject.andOr != "") {
            searchString = searchString + 'andOr:' + rmanRuleParameterValueSearchObject.andOr + ',';
        }

        if (rmanRuleParameterValueSearchObject.dealFlag != undefined && rmanRuleParameterValueSearchObject.dealFlag != "") {
            searchString = searchString + 'dealFlag:' + rmanRuleParameterValueSearchObject.dealFlag;
        }



        if (searchString == '') {
            serviceUrl = serviceUrl + 'search=%25';
        }
        else {
            serviceUrl = serviceUrl + 'search=' + searchString;
        }

        if (paginationOptions.pageNumber != undefined && paginationOptions.pageNumber != "" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber != 0) {
            serviceUrl = serviceUrl + '&page=' + paginationOptions.pageNumber + '&size=' + paginationOptions.pageSize;
        }

        return this.http.get(serviceUrl).toPromise().then((data: any) => {
            return data;
        });
    }

    searchRmanRuleParameterValue(rmanRuleParameterValue: any): Promise<any[]> {
        return this.http.get(appSettings.apiUrl + '/AYARA_IMPUTED_COST_RULE_PARAMETERS/search/findByParameterValueIdAndRuleHeaderIdAndParameterIdAndQualifierAndParameterValueAndAttribute1AndAttribute2AndAttribute3AndAttribute4AndAttribute5AndAttribute6AndAttribute7AndAttribute8AndAttribute9AndAttribute10AndAttribute11AndAttribute12AndAttribute13AndAttribute14AndAttribute15AndAttribute16AndAttribute17AndAttribute18AndAttribute19AndAttribute20AndAttribute21AndAttribute22AndAttribute23AndAttribute24AndAttribute25AndAttribute26AndAttribute27AndAttribute28AndAttribute29AndAttribute30AndCreationDateAndCreatedByAndLastUpdateDateAndLastUpdatedByAndParameterGroupAndAndOrAndDealFlag?parameterValueId=' + rmanRuleParameterValue.parameterValueId + '&ruleHeaderId=' + rmanRuleParameterValue.ruleHeaderId + '&parameterId=' + rmanRuleParameterValue.parameterId + '&qualifier=' + rmanRuleParameterValue.qualifier + '&parameterValue=' + rmanRuleParameterValue.parameterValue + '&attribute1=' + rmanRuleParameterValue.attribute1 + '&attribute2=' + rmanRuleParameterValue.attribute2 + '&attribute3=' + rmanRuleParameterValue.attribute3 + '&attribute4=' + rmanRuleParameterValue.attribute4 + '&attribute5=' + rmanRuleParameterValue.attribute5 + '&attribute6=' + rmanRuleParameterValue.attribute6 + '&attribute7=' + rmanRuleParameterValue.attribute7 + '&attribute8=' + rmanRuleParameterValue.attribute8 + '&attribute9=' + rmanRuleParameterValue.attribute9 + '&attribute10=' + rmanRuleParameterValue.attribute10 + '&attribute11=' + rmanRuleParameterValue.attribute11 + '&attribute12=' + rmanRuleParameterValue.attribute12 + '&attribute13=' + rmanRuleParameterValue.attribute13 + '&attribute14=' + rmanRuleParameterValue.attribute14 + '&attribute15=' + rmanRuleParameterValue.attribute15 + '&attribute16=' + rmanRuleParameterValue.attribute16 + '&attribute17=' + rmanRuleParameterValue.attribute17 + '&attribute18=' + rmanRuleParameterValue.attribute18 + '&attribute19=' + rmanRuleParameterValue.attribute19 + '&attribute20=' + rmanRuleParameterValue.attribute20 + '&attribute21=' + rmanRuleParameterValue.attribute21 + '&attribute22=' + rmanRuleParameterValue.attribute22 + '&attribute23=' + rmanRuleParameterValue.attribute23 + '&attribute24=' + rmanRuleParameterValue.attribute24 + '&attribute25=' + rmanRuleParameterValue.attribute25 + '&attribute26=' + rmanRuleParameterValue.attribute26 + '&attribute27=' + rmanRuleParameterValue.attribute27 + '&attribute28=' + rmanRuleParameterValue.attribute28 + '&attribute29=' + rmanRuleParameterValue.attribute29 + '&attribute30=' + rmanRuleParameterValue.attribute30 + '&creationDate=' + rmanRuleParameterValue.creationDate + '&createdBy=' + rmanRuleParameterValue.createdBy + '&lastUpdateDate=' + rmanRuleParameterValue.lastUpdateDate + '&lastUpdatedBy=' + rmanRuleParameterValue.lastUpdatedBy + '&parameterGroup=' + rmanRuleParameterValue.parameterGroup + '&andOr=' + rmanRuleParameterValue.andOr + '&dealFlag=' + rmanRuleParameterValue.dealFlag).toPromise().then((data: any) => {
            return data;
        });
    }

    saveRmanRuleParameterValue(rmanRuleParameterValue: any): Promise<any[]> {
        let body = JSON.stringify(rmanRuleParameterValue);
        return this.http.post(appSettings.apiUrl + '/AYARA_IMPUTED_COST_RULE_PARAMETERS', body, httpOptions).toPromise().then((data: any) => {
            return data;
        });
    }

    updateRmanRuleParameterValue(rmanRuleParameterValue: any): Promise<any[]> {

        delete rmanRuleParameterValue._links;
        delete rmanRuleParameterValue.interests;
        let body = JSON.stringify(rmanRuleParameterValue);
        return this.http.put(appSettings.apiUrl + '/AYARA_IMPUTED_COST_RULE_PARAMETERS/' + rmanRuleParameterValue.cstRuleParamId, body, httpOptions).toPromise().then((data: any) => {
            return data;
        });

    }

    deleteRmanRuleParameterValue(rmanRuleParameterValue: any): Promise<any[]> {
        let deleteUrl = appSettings.apiUrl + '/AYARA_IMPUTED_COST_RULE_PARAMETERS/' + rmanRuleParameterValue.cstRuleParamId;
        return this.http.delete(deleteUrl, httpOptions).toPromise().then((data: any) => {
            return data;
        });
    }

}
