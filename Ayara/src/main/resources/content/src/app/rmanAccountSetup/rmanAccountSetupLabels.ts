interface ILabels {
         [index: string]: string;
}

export class RmanAccountSetupLabels {

        fieldLabels: ILabels;

    constructor() {

    this.fieldLabels = {};

        this.fieldLabels["accountingStructure"] = "Accounting Structure";
        this.fieldLabels["revenue"] = "Revenue";
        this.fieldLabels["cogs"] = "COGS";
        this.fieldLabels["deferredCogs"] = "Deferred COGS";
        this.fieldLabels["provision"] = "Variable Consideration";
        this.fieldLabels["amortizationDeferrals"] = "Amortization Deferrals";
        this.fieldLabels["name"] = "Name";
        this.fieldLabels["deferredRevenue"] = "Deferred Revenue";
        this.fieldLabels["defRevenueContingency"] = "Deferred Revenue Contingency";
        this.fieldLabels["cearingAccount"] = "Clearing Account";
        this.fieldLabels["contractLiability"] = "Contract Liability";
        this.fieldLabels["contractAsset"] = "Contract Asset";
        this.fieldLabels["acctSetupId"] = "acctSetup Id";
        this.fieldLabels["legalEntityId"] = "legalEntity Id";
        // this.fieldLabels["legalEntityName"] = "Legal Entity Name";
        this.fieldLabels["legalEntityName"] = "Legal Entity";

    }

}
