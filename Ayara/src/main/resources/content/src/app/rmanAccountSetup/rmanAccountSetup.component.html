<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>

<div class="content-section implementation">
</div>


<div class="card-wrapper">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card-block">
					<p-panel header="Account Setup" [toggleable]="false" (onBeforeToggle)=onBeforeToggle($event)>
						<p-header>
							<div class="pull-right icons-list" *ngIf="collapsed">
								<a (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
								<a *isAuthorized="['write','MAS']" (click)="showDialogToAdd()" title="Add">
									<em class="fa fa-plus-circle"></em>
								</a>
								<a (click)="showDialogToSearch()" title="Search">
									<em class="fa fa-search"></em>
								</a>
								<a (click)="reset(dt)" title="Reset">
									<em class="fa fa-refresh"></em>
								</a>
								<div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
										<div class="user-popup">
										  <div class="content overflow">
											<input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()"/>
											<label for="selectall">Select All</label>
											<a class="close" title="Close" (click)="closeConfigureColumns($event)" >&times;</a>
											<p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
											  <ng-template let-col let-index="index" pTemplate="item">
												<div *ngIf="col.drag">
												<div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" 
												 (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
												  <div class="drag">
													<input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)"/>
													<label>{{col.header}}</label>
												  </div>
												</div>
												</div>
												<div *ngIf="!col.drag">
												<div class="ui-helper-clearfix">
												  <div>
													<input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"/>
													<label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
												  </div>
												</div>
												</div>
											  </ng-template>
											  </p-listbox>
									
										  </div>
									
										  <div class="pull-right">
											<a class="configColBtn" (click)="saveColumns()">Save</a>
											<a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
										  </div>
									
										</div>
									  </div>
							</div>
						</p-header>

						<div class="x-scroll">
							<p-table [loading]="loading" class="ui-datatable arrangementMgrTbl" #dt id="accountSetup-dt" [value]="rmanAccountSetupList"
							 selectionMode="single" [paginator]="true" (onRowSelect)="onRowSelect($event)" (onLazyLoad)="getRmanAccountSetup($event)"
							 [lazy]="true" [rows]="pageSize" [totalRecords]="totalElements" scrollable="true" [columns]="columns" [resizableColumns]="true" columnResizeMode="expand" >

								 <ng-template pTemplate="colgroup" let-columns>
									<colgroup>
									  <col>
									  <col *ngFor="let col of columns">
									</colgroup>
								  </ng-template>

								  <ng-template pTemplate="header" class="arrangementMgrTblHead">
										<tr>
											<th></th>
										  <ng-container *ngFor="let col of columns">
											<th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
											<th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
										  </ng-container>
										</tr>
									  </ng-template>
						
									  <ng-template pTemplate="body" let-rowData let-rmanAccountSetup let-columns="columns">
										<tr [pSelectableRow]="rowData" [pSelectableRowIndex]="rowIndex">
										 
												<td>
														<a *isAuthorized="['write','MAS']" (click)="editRow(rmanAccountSetup)" class="icon-edit"> </a>
														<a *isAuthorized="['write','MAS']" (click)="delete(rmanAccountSetup)" class="icon-delete"> </a>
													</td>

										  <ng-container *ngFor="let col of columns" >
											<td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
											  {{rowData[col.field]}}
											</td>
										  
											<td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
											  {{rowData[col.field]}}
											</td>
										  
											<td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
											  {{rowData[col.field] | date: 'MM/dd/yyyy'}}
											</td>
										  
											<td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
											  {{rowData[col.field] | round}}
											</td>
										  </ng-container>
												
										</tr>                 
									  </ng-template>
									  
								<ng-template pTemplate="emptymessage" let-columns>
									<tr *ngIf="!columns">
										<td class="no-data">{{noData}}</td>
									</tr>
								</ng-template>
							</p-table>
						</div>
					</p-panel>
				</div>
			</div>
		</div>
	</div>
</div>

<p-dialog header="Search" width="800" [(visible)]="displaySearchDialog" [draggable]="true" showEffect="fade" [modal]="true"
 [blockScroll]="true">
	<form>
		<div class="ui-grid ui-grid-responsive ui-fluid">

			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Accounting Structure</span>
						<input pInputText type="text" name="accountingStructure" class="textbox" placeholder="Accounting Structure" id="accountingStructure"
						 [(ngModel)]="rmanAccountSetupSearch.accountingStructure" />
					</span>
				</div>

				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">Name</span>
						<input pInputText type="text" name="name" id="name" class="textbox" placeholder="Name" [(ngModel)]="rmanAccountSetupSearch.name"
						/>
					</span>
				</div>

			</div>


		</div>
	</form>
	<p-footer>
		<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
			<button type="submit" pButton class="primary-btn" (click)="search()" label="Search"></button>
			<button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>
		</div>
	</p-footer>

</p-dialog>
<p-dialog header="{{(newRmanAccountSetup) ? 'Create Account Setup' : 'Edit Account Setup'}}" width="800" [draggable]="true"
 [(visible)]="displayDialog" showEffect="fade" [modal]="true" [blockScroll]="true" (onHide)="cancelAddEdit()">
	<form>
		<div class="ui-grid ui-grid-responsive ui-fluid" *ngIf="rmanAccountSetup">
			<div class="ui-g-12 form-group">

				<div class="ui-g-12">
					<div class="ui-g-6">
						<span class="selectSpan">Legal Entity </span>
						<p-dropdown [options]="rmanLegalEntities" name="legalEntityId" id="legalEntityId" [(ngModel)]="rmanAccountSetup.legalEntityId"
						 [ngModelOptions]="{standalone: true}" appendTo="body"></p-dropdown>
					</div>
					<div class="ui-g-6 pull-right">
						<span class="md-inputfield">
							<span class="selectSpan">Accounting Structure</span>
							<input pInputText type="text" name="accountingStructure" class="textbox" placeholder="Accounting Structure" id="accountingStructure"
							 [(ngModel)]="rmanAccountSetup.accountingStructure" />
						</span>
					</div>



				</div>

				<div class="ui-g-12">
					<div class="ui-g-6">
						<span class="md-inputfield">
							<span class="selectSpan">Name</span>
							<input pInputText type="text" name="name" id="name" class="textbox" placeholder="Name" [(ngModel)]="rmanAccountSetup.name"
							/>
						</span>
					</div>
					<div class="ui-g-6  pull-right">
						<span class="md-inputfield">
							<span class="selectSpan">Revenue</span>
							<input pInputText type="text" name="revenue" id="revenue" class="textbox" placeholder="Revenue" [(ngModel)]="rmanAccountSetup.revenue"
							/>
						</span>
					</div>



				</div>

				<div class="ui-g-12">
					<div class="ui-g-6">
						<span class="md-inputfield">
							<span class="selectSpan">Deferred Revenue</span>
							<input pInputText type="text" name="deferredRevenue" class="textbox" placeholder="Deferred Revenue" id="deferredRevenue"
							 [(ngModel)]="rmanAccountSetup.deferredRevenue" />
						</span>
					</div>
					<div class="ui-g-6 pull-right">
						<span class="md-inputfield">
							<span class="selectSpan">Clearing Account</span>
							<input pInputText type="text" name="cearingAccount" class="textbox" placeholder="Clearing Account" id="cearingAccount" [(ngModel)]="rmanAccountSetup.cearingAccount"
							/>
						</span>
					</div>


				</div>


				<div class="ui-g-12">
					<div class="ui-g-6 ">
						<span class="md-inputfield">
							<span class="selectSpan">Deferred Revenue Contingency</span>
							<input pInputText type="text" name="defRevenueContingency" class="textbox" placeholder="Deferred Revenue Contingency" id="defRevenueContingency"
							 [(ngModel)]="rmanAccountSetup.defRevenueContingency" />
						</span>
					</div>
					<div class="ui-g-6 pull-right">
						<span class="md-inputfield">
							<span class="selectSpan">Contract Liability</span>
							<input pInputText type="text" name="contractLiability" class="textbox" placeholder="Contract Liability" id="contractLiability"
							 [(ngModel)]="rmanAccountSetup.contractLiability" />
						</span>
					</div>

				</div>
				<div class="ui-g-12">
					<div class="ui-g-6 ">
						<span class="md-inputfield">
							<span class="selectSpan">Contract Asset</span>
							<input pInputText type="text" name="contractAsset" class="textbox" placeholder="Contract Asset" id="contractAsset" [(ngModel)]="rmanAccountSetup.contractAsset"
							/>
						</span>
					</div>
					<div class="ui-g-6 pull-right">
						<span class="md-inputfield">
							<span class="selectSpan">Provision</span>
							<input pInputText type="text" name="provision" class="textbox" placeholder="Provision" id="provision"
							 [(ngModel)]="rmanAccountSetup.provision" />
						</span>
					</div>
				</div>
				
				<div class="ui-g-12">
					<div class="ui-g-6 ">
						<span class="md-inputfield">
							<span class="selectSpan">Deferred Revenue ST</span>
							<input pInputText type="text" name="deferredRevenueSt" class="textbox" placeholder="Deferred Revenue ST" id="deferredRevenueSt" [(ngModel)]="rmanAccountSetup.deferredRevenueSt"
							/>
						</span>
					</div>
					<div class="ui-g-6 pull-right">
						<span class="md-inputfield">
							<span class="selectSpan">Deferred Revenue LT</span>
							<input pInputText type="text" name="deferredRevenueLt" class="textbox" placeholder="Deferred Revenue LT" id="deferredRevenueLt"
							 [(ngModel)]="rmanAccountSetup.deferredRevenueLt" />
						</span>
					</div>
				</div>
			</div>
		</div>
	</form>
	<p-footer>
		<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
			<button type="submit" pButton class="primary-btn" label="Save" (click)="save()"></button>
			<button type="button" pButton class="secondary-btn" (click)="cancelAddEdit()" label="Cancel"></button>
		</div>
	</p-footer>
</p-dialog>