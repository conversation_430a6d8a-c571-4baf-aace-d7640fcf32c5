export interface RmanProducts {
    attribute30: any;
    endDate: any;
    elementType: any;
    lastUpdateDate: any;
    productOrgId: any;
    productDescription: any;
    attribute29: any;
    attribute28: any;
    allocationFlag: any;
    attribute27: any;
    attribute26: any;
    allowRevrecWoInvoice: any;
    attribute3: any;
    createdBy: any;
    attribute2: any;
    lastUpdatedBy: any;
    productOrgName: any;
    attribute1: any;
    revAccountId: any;
    creationDate: any;
    attribute9: any;
    shippableFlag: any;
    attribute8: any;
    productName: any;
    attribute7: any;
    invoiceEnabledFlag: any;
    attribute6: any;
    attribute5: any;
    attribute4: any;
    directCv: any;
    productStatus: any;
    residualValue: any;
    source: any;
    attribute10: any;
    cvPercent: any;
    sourceProductId: any;
    revrecType: any;
    attribute14: any;
    attribute13: any;
    attribute12: any;
    attribute11: any;
    acctRuleName: any;
    productRevAccount: any;
    productCogsAccount: any;
    productOrgCode: any;
    attribute21: any;
    cogsAccountId: any;
    attribute20: any;
    attribute25: any;
    attribute24: any;
    fvOptional: any;
    attribute23: any;
    attribute22: any;
    startDate: any;
    productId: any;
    revenueTemplateId: any;
    productCost: any;
    productType: any;
    uom: any;
    attribute18: any;
    attribute17: any;
    revenueCategory: any;
    attribute16: any;
    attribute15: any;
    attribute19: any;
    bundleFlag: any;
    ltStFlag: any;
    productGroup:any;
    productFamily:any;
    productLine:any;
    productModel:any;
    productNumber:any;
}
