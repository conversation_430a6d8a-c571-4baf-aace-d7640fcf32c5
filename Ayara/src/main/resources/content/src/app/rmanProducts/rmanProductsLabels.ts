interface ILabels {
    [index: string]: string;
}

export class RmanProductsLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["attribute30"] = "Level 4";
        this.fieldLabels["endDate"] = "End Date";
        this.fieldLabels["elementType"] = "Product Element";
        this.fieldLabels["lastUpdateDate"] = "Last Update Date";
        this.fieldLabels["productOrgId"] = "Product Org ID";
        this.fieldLabels["productDescription"] = "Product Description";
        this.fieldLabels["attribute29"] = "Level 3";
        this.fieldLabels["attribute28"] = "Level 2";
        this.fieldLabels["allocationFlag"] = "Allocation Flag";
        this.fieldLabels["attribute27"] = "Level 1";
        this.fieldLabels["attribute26"] = "Attribute26";
        this.fieldLabels["allowRevrecWoInvoice"] = "Allow RevRec WO Invoice";
        this.fieldLabels["attribute3"] = "Product Line";
        this.fieldLabels["createdBy"] = "Created By";
        this.fieldLabels["attribute2"] = "Product Family";
        this.fieldLabels["lastUpdatedBy"] = "Last Updated By";
        this.fieldLabels["productOrgName"] = "Product Organization";
        this.fieldLabels["attribute1"] = "Product Portfolio";
        this.fieldLabels["revAccountId"] = "Rev Account ID";
        this.fieldLabels["creationDate"] = "Creation Date";
        this.fieldLabels["attribute9"] = "Attribute9";
        this.fieldLabels["shippableFlag"] = "Shippable Flag";
        this.fieldLabels["attribute8"] = "Attribute8";
        this.fieldLabels["productName"] = "Product Name";
        this.fieldLabels["attribute7"] = "Attribute7";
        this.fieldLabels["invoiceEnabledFlag"] = "Invoice Enabled Flag";
        this.fieldLabels["attribute6"] = "Product Family2";
        this.fieldLabels["attribute5"] = "Product/Service";
        this.fieldLabels["attribute4"] = "Attribute4";
        this.fieldLabels["directCv"] = "Direct CV";
        this.fieldLabels["productStatus"] = "Item Status";
        this.fieldLabels["residualValue"] = "Residual Value";
        this.fieldLabels["source"] = "Source";
        this.fieldLabels["attribute10"] = "Attribute10";
        this.fieldLabels["cvPercent"] = "CV Percent";
        this.fieldLabels["sourceProductId"] = "Source ID";
        this.fieldLabels["revrecType"] = "RevRec Type";
        this.fieldLabels["attribute14"] = "Attribute14";
        this.fieldLabels["attribute13"] = "Attribute13";
        this.fieldLabels["attribute12"] = "Attribute12";
        this.fieldLabels["attribute11"] = "Invoicable Item";
        this.fieldLabels["acctRuleName"] = "Invoicing Rule";
        this.fieldLabels["productRevAccount"] = "Product Rev Account";
        this.fieldLabels["productCogsAccount"] = "Product COGS Account";
        this.fieldLabels["productOrgCode"] = "Business Unit";
        this.fieldLabels["attribute21"] = "Attribute21";
        this.fieldLabels["cogsAccountId"] = "COGS Account ID";
        this.fieldLabels["attribute20"] = "Attribute20";
        this.fieldLabels["attribute25"] = "Attribute25";
        this.fieldLabels["attribute24"] = "Attribute24";
        this.fieldLabels["fvOptional"] = "FV Optional";
        this.fieldLabels["attribute23"] = "Attribute23";
        this.fieldLabels["attribute22"] = "Attribute22";
        this.fieldLabels["startDate"] = "Start Date";
        this.fieldLabels["productId"] = "Product ID";
        this.fieldLabels["revenueTemplateId"] = "Revenue Template";
        this.fieldLabels["productCost"] = "Product Cost";
        this.fieldLabels["productType"] = "Item Type";
        this.fieldLabels["uom"] = "Unit of Measurement";
        this.fieldLabels["attribute18"] = "Attribute18";
        this.fieldLabels["attribute17"] = "Attribute17";
        this.fieldLabels["revenueCategory"] = "Revenue Category";
        this.fieldLabels["attribute16"] = "Attribute16";
        this.fieldLabels["attribute15"] = "Attribute15";
        this.fieldLabels["attribute19"] = "Attribute19";
        this.fieldLabels["bundleFlag"] = "Bundled Item";
    }

}
