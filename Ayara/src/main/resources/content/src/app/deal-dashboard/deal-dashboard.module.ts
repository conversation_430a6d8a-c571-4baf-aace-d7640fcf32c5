import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {CardModule} from 'primeng/card';
import {DropdownModule} from 'primeng/dropdown';
import {FormsModule} from '@angular/forms';
import {ProgressSpinnerModule} from 'primeng/progressspinner';
import {GraphSharedModule} from '../shared/graphShared.module';
import {HTTP_INTERCEPTORS, HttpClientModule, HttpClientXsrfModule} from '@angular/common/http';
import {RouterModule, Routes} from '@angular/router';
import {LandingDashboardService} from '../landingDashboard/landingDashboard.service';
import {RmanFiscalPeriodsService} from '../rmanFiscalPeriods/rmanFiscalPeriodsservice';
import {CsrfTokenInterceptorService} from '../csrftoken-interceptor.service';
import {DealDashboardComponent} from './deal-dashboard.component';


const routes: Routes = [
  { path: '', component: DealDashboardComponent }
];


@NgModule({
  declarations: [DealDashboardComponent],
  imports: [CardModule, DropdownModule, FormsModule, CommonModule, ProgressSpinnerModule, GraphSharedModule, HttpClientModule,
    RouterModule.forChild(routes),
    HttpClientXsrfModule.withOptions({
      cookieName: 'CSRF-TOKEN',
      headerName: 'X-CSRF-TOKEN'
    })
  ],
  exports: [CardModule, DropdownModule, FormsModule, ProgressSpinnerModule],
  providers: [LandingDashboardService, RmanFiscalPeriodsService,
    { provide: HTTP_INTERCEPTORS,
      useClass: CsrfTokenInterceptorService,
      multi: true
    }
  ],
})
export class DealDashboardModule { }
