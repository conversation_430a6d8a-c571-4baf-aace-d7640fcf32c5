import {Component, OnInit, ViewChild} from '@angular/core';
import {DataChartComponent} from '../reports/analyticalReports/report/chart/chart.component';
import {DrillDownService} from '../reports/analyticalReports/services/drill-down.service';
import {NavigationEnd, Router} from '@angular/router';
import {RmanFiscalPeriodsService} from '../rmanFiscalPeriods/rmanFiscalPeriodsservice';
import {NotificationService} from '../shared/notifications.service';
import {DealDashboardService} from './deal-dashboard.service';
import {CommonSharedService} from '../shared/common.service';

declare var require: any;
const appSettings = require('../appsettings');

@Component({
    selector: 'app-deal-dashboard',
    templateUrl: './deal-dashboard.component.html',
    styleUrls: ['./deal-dashboard.component.css']
})
export class DealDashboardComponent implements OnInit {

    paginationOptions: any;
    addReports = false;
    showChart = false;


    @ViewChild('dataChart', {
        read: DataChartComponent,
        static: false,
    } as any)
    dataChart: DataChartComponent;

    currentUrl = '';
    reportsList: any[] = [];
    showLoading = false;
    saveLoading = false;
    noReports = false;

    rmanFYears: any[];
    rmanFYList: any[] = [];
    currentFY: any;

    rmanPeriods: any[];
    rmanQuarters: any[];
    rmanPeriod: any;
    rmanQuarter: any;

    rmanPeriodDimensions: any[];
    rmanPeriodDimensionsList: any[] = [];
    currentPeriodDimension: any;
    noOverviewData = false;
    overviewDataLoading = false;

    overviewFigures: any = null;


    dealGuidanceBarReportReady = false;

    reportsDataArray = [];
    dropdownReportList = [];

    constructor(public drillDownService: DrillDownService, private router: Router,
                private dealDashboardService: DealDashboardService, private rmanFiscalPeriodsService: RmanFiscalPeriodsService,
                private notificationService: NotificationService, private commonSharedService: CommonSharedService) {
        this.paginationOptions = {'pageNumber': 0, 'pageSize': '10000'};
        this.router.events.subscribe((event) => {
            if (event instanceof NavigationEnd) {
                this.currentUrl = event.url;
            }
        });

        this.overviewDataLoading = true;
        this.rmanFiscalPeriodsService.getFinancialYears().then((fYears: any) => {
            this.rmanFYList = fYears;
            const rmanFYTemp: any = [{label: appSettings.dropDownOptions.selectYear, value: null}];
            for (let q = 0; q < this.rmanFYList.length; q++) {
                rmanFYTemp.push({label: this.rmanFYList[q], value: this.rmanFYList[q]});
            }
            this.rmanFYears = rmanFYTemp;
        });

        this.rmanFiscalPeriodsService.getPeriodDimensions().then((pDimensions: any) => {
            this.rmanPeriodDimensionsList = pDimensions;
            this.currentPeriodDimension = this.rmanPeriodDimensionsList[0];
            const rmanPeriodDimensionsTemp: any = [{label: appSettings.dropDownOptions.selectPeriod, value: null}];
            for (let q = 0; q < this.rmanPeriodDimensionsList.length; q++) {
                rmanPeriodDimensionsTemp.push({label: this.rmanPeriodDimensionsList[q], value: this.rmanPeriodDimensionsList[q]});
            }
            this.rmanPeriodDimensions = rmanPeriodDimensionsTemp;
            // this.loadArrangementData();
            this.rmanFiscalPeriodsService.getCurrentfiscalperiod().then(fiscalPeriods => {
                this.currentFY = fiscalPeriods.periodYear;
                this.rmanPeriod = fiscalPeriods.periodName;
                this.preparePeriods(this.currentFY);
                this.loadArrangementData();
                this.dealDashboardService.getListOfReports().subscribe(res => {
                    this.dropdownReportList = res.content;
                    if (this.dropdownReportList.length === 0) {
                        this.noReports = true;
                    } else {
                        this.loadReports();
                        // this.getDealGuidanceReport(res.content);
                    }
                });
            });
        });

        // this.showLoading = true;
    }

    ngOnInit(): void {

    }

    loadReports() {
        let fYear = this.currentFY;
        let pDimension = this.currentPeriodDimension;
        let period = this.rmanPeriod;
        let quarter = this.rmanQuarter;
        if (fYear === undefined) {
            fYear = '';
        }
        if (pDimension === undefined) {
            pDimension = '';
        }
        if (period === undefined) {
            period = '';
        }
        if (quarter === undefined) {
            quarter = '';
        }
        if (!period && quarter) {
            period = quarter;
        }
        this.reportsDataArray = [];
        this.dropdownReportList.forEach(report => {
            if (report.reportCode === 'dealsByQuarter') {
                this.dealDashboardService.dealsByQuarter(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'quarter', 'noOfDeals', report.reportName, false,
                        'Number of Deals', report.reportCode, report.showOnDashboard, '', false);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'allocatedMarginGuidanceDeals') {
                this.dealDashboardService.allocatedMarginGuidanceDeals(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'guidance', 'noOfDeals', report.reportName, false,
                        'Number of Deals(Margin Guidance) ', report.reportCode, report.showOnDashboard, '', false);
                    this.dealGuidanceBarReportReady = true;
                });

            }
            if (report.reportCode === 'allocatedMarginGuidancePercentByAllocatedGMGuidance') {
                this.dealDashboardService.allocatedMarginGuidancePercentByAllocatedGMGuidance(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'allocatedMarginGuidance', 'allocatedMarginPercent',
                        report.reportName, false, 'Margin Guidance', report.reportCode, report.showOnDashboard, '%', false);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'allocatedMarginPercentByProduct') {
                this.dealDashboardService.allocatedMarginPercentByProduct(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'product', 'allocatedMarginPercent', report.reportName,
                        false, 'Product', report.reportCode, report.showOnDashboard, '%', false);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'allocationAmtByRegion') {
                this.dealDashboardService.allocationAmtByRegion(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'region', 'allocationAmount', report.reportName, false,
                        'Amount by Regions', report.reportCode, report.showOnDashboard, '', true);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'allocationAmtByPeriod') {
                this.dealDashboardService.allocationAmtByPeriod(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'period', 'allocationAmount', report.reportName, false,
                        'Amount by Periods', report.reportCode, report.showOnDashboard, '', true);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'allocationAmtByAccount') {
                this.dealDashboardService.allocationAmtByAccount(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'account', 'allocationAmount', report.reportName, false,
                        'Amount by Accounts', report.reportCode, report.showOnDashboard, '', true);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'allocationAmtByProductCategory') {
                this.dealDashboardService.allocationAmtByProductCategory(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'productCategory', 'allocationAmount', report.reportName,
                        false, 'Revenue Amount ', report.reportCode, report.showOnDashboard, '', true);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'allocationGuidanceDeals') {
                this.dealDashboardService.allocationGuidanceDeals(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'guidance', 'noOfDeals', report.reportName, false,
                        'Number of Deals(Allocation Guidance)', report.reportCode, report.showOnDashboard, '', false);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'discountGuidanceDeals') {
                this.dealDashboardService.discountGuidanceDeals(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'guidance', 'noOfDeals', report.reportName, false,
                        'Number of Deals(Discount Guidance)', report.reportCode, report.showOnDashboard, '', false);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'allocationMarginPercentByAllocationGuidance') {
                this.dealDashboardService.allocationMarginPercentByAllocationGuidance(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'allocationGuidance', 'allocationGuidancePercent',
                        report.reportName, false, 'Allocation Guidance', report.reportCode, report.showOnDashboard, '%', false);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'discountPercentByDiscountGuidance') {
                this.dealDashboardService.discountPercentByDiscountGuidance(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'discountGuidance', 'discountGuidancePercent', report.reportName,
                        false, 'Discount Guidance', report.reportCode, report.showOnDashboard, '%', false);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'dealGuidanceReport') {
                this.dealDashboardService.dealGuidanceReport(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'guidance', 'noOfGuidances', report.reportName, false,
                        'Number of Deals', report.reportCode, report.showOnDashboard, '', false);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            /**/
            if (report.reportCode === 'allocatedmarginPercentByRegion') {
                this.dealDashboardService.allocatedmarginPercentByRegion(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'region', 'allocatedMarginPercent', report.reportName, false,
                        'Region', report.reportCode, report.showOnDashboard, '%', false);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'allocatedmarginPercentByAccount') {
                this.dealDashboardService.allocatedmarginPercentByAccount(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'account', 'allocatedMarginPercent', report.reportName, false,
                        'Account Name', report.reportCode, report.showOnDashboard, '%', false);
                    this.dealGuidanceBarReportReady = true;
                });
            }
            if (report.reportCode === 'allocatedmarginPercentByProductCategory') {
                this.dealDashboardService.allocatedmarginPercentByProductCategory(fYear, pDimension, period).subscribe(data => {
                    this.buildReports(data, 'productCategory', 'allocatedMarginPercent', report.reportName, false,
                        'Product Category', report.reportCode, report.showOnDashboard, '%', false);
                    this.dealGuidanceBarReportReady = true;
                });
            }
        });
    }

    loadArrangementData() {
        this.dealDashboardService.getOverviewFigures(this.currentFY, this.currentPeriodDimension, this.rmanPeriod, this.rmanQuarter).then((response) => {
            if (response == null) {
                this.overviewDataLoading = false;
                this.noOverviewData = true;
                this.overviewFigures = null;
                this.notificationService.showError('Failed to load Overview Data');
            } else {
                // @ts-ignore
                this.overviewFigures = response;
                this.overviewDataLoading = false;
            }
        });
    }

    onAddingReports(ev: any) {
        this.addReports = !this.addReports;
    }

    closeAddReports(ev: any) {
        this.addReports = false;
    }

    onReportChange(report) {
        this.reportsDataArray.forEach(item => {
            if (item.reportCode === report.reportCode) {
                if (report.showOnDashboard === 'Y') {
                    item.showOnDashboard = 'N';
                    report.showOnDashboard = 'N';
                } else {
                    item.showOnDashboard = 'Y';
                    report.showOnDashboard = 'Y';
                }
            }
        });
    }

    saveDashboardReports() {
        this.saveLoading = true;
        this.dealDashboardService.saveDashboardReportsLayout(this.dropdownReportList).then((reports) => {
            this.saveLoading = false;
            this.notificationService.showSuccess('Reports Updated Successfully');
        }).catch((err: any) => {
            this.saveLoading = false;
            this.notificationService.showError('Error occurred while updating Data');
        });

    }

    transformValues(value: any) {
        return value != null ? Math.round(value) : 0;
    }

    onSelectChange() {
        if (this.currentFY != null) {
            this.preparePeriods(this.currentFY);
            this.prepareQuarters(this.currentFY);
            this.rmanPeriod = null;
            this.rmanQuarter = null;
            this.overviewDataLoading = false;
            this.loadArrangementData();
            this.loadReports();
        } else if (this.currentPeriodDimension != null) {
            this.rmanPeriod = null;
            this.rmanQuarter = null;
            this.overviewDataLoading = false;
            this.loadArrangementData();
            this.loadReports();
        }
    }

    onDimensionChange() {
        this.rmanPeriod = null;
        this.rmanQuarter = null;
        this.onPeriodsChange();
    }

    onPeriodsChange() {
        this.overviewDataLoading = false;
        this.loadArrangementData();
        this.loadReports();
    }

    prepareQuarters(year) {
        if (year != null) {
            const quarters: any = [{label: appSettings.dropDownOptions.selectQuarterName, value: null}];
            for (let i = 0; i < 4; i++) {
                quarters.push({
                    label:
                        i === 0 ? 'Q1-' + year :
                            i === 1 ? 'Q2-' + year :
                                i === 2 ? 'Q3-' + year :
                                    i === 3 ? 'Q4-' + year : ''
                    , value:
                        i === 0 ? 'Q1-' + year :
                            i === 1 ? 'Q2-' + year :
                                i === 2 ? 'Q3-' + year :
                                    i === 3 ? 'Q4-' + year : ''
                });
            }
            this.rmanQuarters = quarters;
        }
    }

    preparePeriods(year) {
        if (year != null) {
            const year1 = year.toString().substr(2, 4);
            const periods: any = [{label: appSettings.dropDownOptions.selectedPeriodName, value: null}];
            for (let i = 0; i < 12; i++) {
                periods.push({
                    label:
                        i === 0 ? 'Jan-' + year1 :
                            i === 1 ? 'Feb-' + year1 :
                                i === 2 ? 'Mar-' + year1 :
                                    i === 3 ? 'Apr-' + year1 :
                                        i === 4 ? 'May-' + year1 :
                                            i === 5 ? 'Jun-' + year1 :
                                                i === 6 ? 'Jul-' + year1 :
                                                    i === 7 ? 'Aug-' + year1 :
                                                        i === 8 ? 'Sep-' + year1 :
                                                            i === 9 ? 'Oct-' + year1 :
                                                                i === 10 ? 'Nov-' + year1 :
                                                                    i === 11 ? 'Dec-' + year1 : ''
                    , value:
                        i === 0 ? 'Jan-' + year1 :
                            i === 1 ? 'Feb-' + year1 :
                                i === 2 ? 'Mar-' + year1 :
                                    i === 3 ? 'Apr-' + year1 :
                                        i === 4 ? 'May-' + year1 :
                                            i === 5 ? 'Jun-' + year1 :
                                                i === 6 ? 'Jul-' + year1 :
                                                    i === 7 ? 'Aug-' + year1 :
                                                        i === 8 ? 'Sep-' + year1 :
                                                            i === 9 ? 'Oct-' + year1 :
                                                                i === 10 ? 'Nov-' + year1 :
                                                                    i === 11 ? 'Dec-' + year1 : ''
                });
            }
            this.rmanPeriods = periods;
        }
    }

    gotoPage(guidance) {
        this.commonSharedService.guidance = guidance.guidance;
        this.router.navigate(['/deal-manager']);
    }

    buildReports(data, xAxisValue, yAxisValue, titleText, showTitle, chartLabel, reportCode, showOnDashboard, tickLetter, showAmountsInMn) {
        const barChartData = [];
        const pieChartData = [];
        const pieChartLabels = [];
        const _this = this;
        data.forEach(item => {
            barChartData.push({data: [item[yAxisValue]], label: item[xAxisValue]});
            pieChartData.push(item[yAxisValue]);
            pieChartLabels.push([item[xAxisValue]]);
        });
        const barChartOptions: any = {
            scaleShowVerticalLines: false,
            responsive: true,
            legend: {
                display: true,
                position: 'bottom',
            },
            title: {
                display: showTitle,
                text: chartLabel
            },
            scales: {
                yAxes: [{
                    ticks: {
                        suggestedMin: 0,
                        callback: function (value, index, values) {
                            if (tickLetter) {
                                return value + tickLetter;
                            } else if (showAmountsInMn) {
                                const sign = Math.sign(Number(value));
                                // Nine Zeroes for Billions
                                return '$' + _this.formatAmounts(value, sign);
                            } else {
                                return value;
                            }

                        }
                    }
                }]
            },
            tooltips: {
                enabled: true,
                callbacks: {
                    label: function (tooltipItem, value) {
                        const count = value.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];
                        if (tickLetter) {
                            return count + tickLetter;
                        } else if (showAmountsInMn) {
                           //  return count + 'Mn';

                            const sign = Math.sign(Number(count));
                            // Nine Zeroes for Billions
                            return '$' + (Math.abs(Number(count)) >= 1.0e9
                                ? (sign * (Math.abs(Number(count)) / 1.0e9)).toFixed(2) + " Bn"
                                : // Six Zeroes for Millions
                                Math.abs(Number(count)) >= 1.0e6
                                    ? (sign * (Math.abs(Number(count)) / 1.0e6)).toFixed(2) + " Mn"
                                    : // Three Zeroes for Thousands
                                    Math.abs(Number(count)) >= 1.0e3
                                        ? (sign * (Math.abs(Number(count)) / 1.0e3)).toFixed(2) + " K"
                                        : Math.abs(Number(count)));
                        } else {
                            return count;
                        }
                    }
                }
            }
        };

        const pieChartOptions: any = {
            legend: {
                display: true,
                position: 'bottom',
            },
            tooltips: {
                enabled: true,
                callbacks: {
                    label: function (tooltipItem, value) {
                        const count = value.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];
                        if (tickLetter) {
                            return count + tickLetter;
                        } else if (showAmountsInMn) {
                            //  return count + 'Mn';

                            const sign = Math.sign(Number(count));
                            // Nine Zeroes for Billions
                            return '$' + (Math.abs(Number(count)) >= 1.0e9
                                ? (sign * (Math.abs(Number(count)) / 1.0e9)).toFixed(2) + " Bn"
                                : // Six Zeroes for Millions
                                Math.abs(Number(count)) >= 1.0e6
                                    ? (sign * (Math.abs(Number(count)) / 1.0e6)).toFixed(2) + " Mn"
                                    : // Three Zeroes for Thousands
                                    Math.abs(Number(count)) >= 1.0e3
                                        ? (sign * (Math.abs(Number(count)) / 1.0e3)).toFixed(2) + " K"
                                        : Math.abs(Number(count)));
                        } else {
                            return count;
                        }
                    }
                }
            }
        };

        const reportData = {
            name: titleText, chartType: 'bar', chartLabels: [chartLabel],
            chartData: barChartData, chartOptions: barChartOptions, chartLegend: true, pieChartData, pieChartLabels,
            reportCode, showOnDashboard, pieChartOptions
        };
        this.reportsDataArray.push(reportData);
    }

    formatAmounts (value, sign) {
        let finalValue;
        let precisionValue;
        if (Math.abs(Number(value)) >= 1.0e9) {
            precisionValue = (Math.abs(Number(value)) / 1.0e9).toPrecision(2);
            finalValue = sign * precisionValue + ' Bn';
        } else if (Math.abs(Number(value)) >= 1.0e6) {
            precisionValue = (Math.abs(Number(value)) / 1.0e6).toPrecision(2);
            finalValue = sign * precisionValue + ' Mn';
        } else if (Math.abs(Number(value)) >= 1.0e3) {
            precisionValue = (Math.abs(Number(value)) / 1.0e3).toPrecision(2);
            finalValue = sign * precisionValue + ' K';
        } else {
            finalValue = Math.abs(Number(value)).toPrecision(2);
        }
        return finalValue;
    }

    toggleData(report, chartType: string, displayTitle: boolean) {
        report.chartType = chartType;
        report.chartOptions.title.display = displayTitle;
    }

}
