<div class="content-section implementation">
</div>

<div class="card-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block card-padding card-overview">
                    <p-card>
                        <div class="cardHeader row mx-0">
                            <h2>Overview</h2>
                            <p-dropdown class="ml-auto" [options]="rmanFYears" [(ngModel)]="currentFY"
                                        [ngModelOptions]="{standalone: true}" (onChange)="onSelectChange()" name="FYear"
                                        [filter]="false"></p-dropdown>
                            <p-dropdown [options]="rmanPeriodDimensions" [(ngModel)]="currentPeriodDimension"
                                        [ngModelOptions]="{standalone: true}" (onChange)="onDimensionChange()"
                                        name="periodDimension" [filter]="false"></p-dropdown>
                            <p-dropdown *ngIf="currentFY != null && currentPeriodDimension == 'PTD'"
                                        [options]="rmanPeriods" [(ngModel)]="rmanPeriod"
                                        [ngModelOptions]="{standalone: true}" (onChange)="onPeriodsChange()"
                                        name="periodName" [filter]="false"></p-dropdown>
                            <p-dropdown *ngIf="currentFY != null && currentPeriodDimension == 'QTD'"
                                        [options]="rmanQuarters" [(ngModel)]="rmanQuarter"
                                        [ngModelOptions]="{standalone: true}" (onChange)="onPeriodsChange()"
                                        name="quarterName" [filter]="false"></p-dropdown>
                        </div>
                        <div class="row">
                            <h6 *ngIf="overviewFigures === null" class="no-data-found">No data found</h6>
                            <span class="card-container row mx-0 width-100 height-135"
                                  *ngIf="!overviewDataLoading && overviewFigures !== null">
                            <div class="col" style="padding-right: 10px!important;">
                              <div class="card">
                                <p class="font-12">Cumulative Deal Value</p>
                                  <h6>&nbsp;</h6>
                                <h6>$ {{transformValues(overviewFigures.totalDealValue) | number: '1.0-2'}}</h6>
                              </div>
                            </div>
                            <div class="col" style="padding-right: 10px!important;">
                              <div class="card">
                                <p class="font-12">Count of Deals</p>
                                  <h6>&nbsp;</h6>
                                <h6> {{transformValues(overviewFigures.noOfDeals)}}</h6>
                              </div>
                            </div>
                            <div class="col" style="padding-right: 10px!important;">
                              <div class="card">
                                <p class="font-12">Count of Quotes</p>
                                  <h6>&nbsp;</h6>
                                <h6> {{transformValues(overviewFigures.noOfQuotes)}}</h6>
                              </div>
                            </div>
                            <div class="col" style="padding-right: 10px!important;">
                              <div class="card text-right">
                                <p class="font-12">Deal Guidance</p>
                                   <h6 *ngIf="overviewFigures.dealGuidance?.length === 0 || !overviewFigures.dealGuidance">&nbsp;</h6>
                                  <h6 *ngIf="overviewFigures.dealGuidance?.length === 1 || !overviewFigures.dealGuidance; else loggedOut">&nbsp;</h6>
                                  <ng-template #loggedOut>
                                  <h6 *ngIf="overviewFigures.dealGuidance?.length === 0 || !overviewFigures.dealGuidance">&nbsp;</h6>
                                  </ng-template>
                                  <div class="justify-content-end">
                                        <h6 *ngFor="let guidance of overviewFigures.dealGuidance"><a
                                                (click)="gotoPage(guidance)" class="pointer" style="text-decoration: underline!important;"
                                                [style.color]="guidance.guidance">{{guidance.guidance}}
                                            - {{transformValues(guidance.noOfGuidance)}}</a></h6>
                                  </div>
                              </div>
                            </div>
                            <div class="col" style="padding-right: 10px!important;">
                              <div class="card">
                                <p class="font-12">Cumulative Allocated Margin</p>
                                <h6>$ {{transformValues(overviewFigures.totalGrossMarginAmt) | number: '1.0-2'}}</h6>
                                <h6 [style.color]="'#262d74'"> {{transformValues(overviewFigures.totalGrossMarginPercent)}}
                                    %</h6>
                              </div>
                            </div>
                            <div class="col">
                              <div class="card">
                                <p class="font-12">Cumulative Gross Margin</p>
                                <h6>$ {{transformValues(overviewFigures.totalAllocatedGrossMarginAmt) | number: '1.0-2'}}</h6>
                                  <h6 [style.color]="'#262d74'"> {{transformValues(overviewFigures.totalAllocatedGrossMarginPercent)}}
                                      %</h6>
                              </div>
                            </div>

                          </span>
                            <p-progressSpinner *ngIf="overviewDataLoading" class="overviewFigures-load-spinner"
                                               styleClass="custom-spinner" strokeWidth="4"></p-progressSpinner>
                        </div>
                    </p-card>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card-wrapper" *ngIf="reportsList">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block card-padding d-flex">
                    <p-card>
                        <h2>Reports</h2>
                        <div class="pull-right icons-list">
                            <a *ngIf="!noReports" (click)="onAddingReports($event)" class="add-column"><em
                                    class="fa fa-plus-circle"></em>List of Reports</a>
                            <div id="add-column-popup" class="dashboard-reports-popup" *ngIf="addReports">
                                <div class="user-popup right-align">
                                    <div class="content overflow">
                                        <a class="close" title="Close" (click)="closeAddReports($event)">&times;</a>
                                        <ol *ngFor="let report of dropdownReportList">
                                            <li>
                                                <input type="checkbox" name="column"
                                                       [checked]="report.showOnDashboard == 'Y'"
                                                       (change)="onReportChange(report)"/>
                                                <label for="{{report.id}}">{{report.reportName}}</label>
                                            </li>
                                        </ol>
                                    </div>
                                    <div class="add-column-btns">
                                        <a class="primary-btn pointer" (click)="saveDashboardReports()">
                                            Save
                                        </a>
                                    </div>
                                    <p-progressSpinner *ngIf="saveLoading" class="save-dialog-spinner"
                                                       styleClass="custom-spinner" strokeWidth="4"></p-progressSpinner>
                                </div>
                            </div>
                        </div>
                        <p-progressSpinner *ngIf="showLoading" class="reports-load-splinner" styleClass="custom-spinner"
                                           strokeWidth="4"></p-progressSpinner>
                        <div *ngIf="noReports">
                            <strong style="text-align: center;">No reports found</strong>
                        </div>

                        <div class="dashboard-graphs-row" *ngIf="dealGuidanceBarReportReady">
                            <div *ngFor="let report of reportsDataArray">
                                <div class="col-md-4 reports-card-main" *ngIf="report.showOnDashboard === 'Y'">
                                    <div class="card">
                                        <h6 class="dashboard-report-name">
                                            <span>{{report.name}}</span>
                                            <div class="pull-right chart-switch"
                                                 (click)="report.chartType === 'bar' ? this.toggleData(report,'pie', true): this.toggleData(report,'bar', false)">
                                                <em [ngClass]="report.chartType === 'pie'? 'fa fa-bar-chart' : 'fa fa-pie-chart'"></em>
                                            </div>
                                        </h6>
                                        <canvas baseChart height="200" [datasets]="report.chartData"
                                                [labels]="report.chartLabels" [options]="report.chartOptions"
                                                [legend]="report.chartLegend" [chartType]="report.chartType"
                                                *ngIf="report.chartType === 'bar' && report.chartData.length > 0"></canvas>
                                        <div class="no_chartData" *ngIf="report.chartType === 'bar' && report.chartData.length === 0">
                                            No Transaction Exist in the Selected Period Range
                                        </div>
                                        <!--<canvas baseChart height="200" [data]="report.pieChartData"
                                                [labels]="report.pieChartLabels" [chartType]="report.chartType"
                                                [options]="report.chartOptions" [legend]="true"
                                                *ngIf="report.chartType === 'pie'">
                                        </canvas>-->
                                        <canvas *ngIf="report.chartType === 'pie' && report.pieChartData.length > 0" baseChart height="200" [options]="report.pieChartOptions"
                                                [data]="report.pieChartData" [labels]="report.pieChartLabels" [chartType]="'doughnut'"
                                                ></canvas>
                                        <div class="no_chartData" *ngIf="report.chartType === 'pie' && report.pieChartData.length === 0">
                                            No Transaction Exist in the Selected Period Range
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </p-card>
                </div>
            </div>
        </div>
    </div>
</div>
