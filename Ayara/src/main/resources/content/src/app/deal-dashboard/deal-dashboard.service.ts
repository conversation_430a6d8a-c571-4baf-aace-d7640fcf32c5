import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable} from 'rxjs';
import {error} from 'protractor';

declare var require: any;
const appSettings = require('../appsettings');


const httpOptions = {
    headers: new HttpHeaders({
        'Content-Type': 'application/json',
    })
};

@Injectable({
    providedIn: 'root'
})
export class DealDashboardService {

    reportUrls = {
        'dealGuidanceReport': '/reports/dealGuidanceReport',
        'dealsByQuarter': '/reports/dealsByQuarter',
        'allocatedMarginGuidancePercentByAllocatedGMGuidance': '/reports/allocatedMarginGuidancePercentByAllocatedGMGuidance',
        'allocatedMarginGuidanceDeals': '/reports/allocatedMarginGuidanceDeals',
        'allocatedMarginPercentByProduct': '/reports/allocatedMarginPercentByProduct',
        'allocationAmtByRegion': '/reports/allocationAmtByRegion',
        'allocationAmtByPeriod': '/reports/allocationAmtByPeriod',
        'allocationAmtByAccount': '/reports/allocationAmtByAccount',
        'allocationAmtByProductCategory': '/reports/allocationAmtByProductCategory',
        'allocationGuidanceDeals': '/reports/allocationGuidanceDeals',
        'allocationMarginPercentByAllocationGuidance': '/reports/allocationMarginPercentByAllocationGuidance',
        'discountGuidanceDeals': '/reports/discountGuidanceDeals',
        'discountPercentByDiscountGuidance': '/reports/discountPercentByDiscountGuidance',
        'allocatedmarginPercentByRegion': '/reports/allocatedmarginPercentByRegion',
        'allocatedmarginPercentByAccount': '/reports/allocatedmarginPercentByAccount',
        'allocatedmarginPercentByProductCategory': '/reports/allocatedmarginPercentByProductCategory'
    };

    otherUrls = {
        getListOfReports: '/ayaraDealDashboardReports?search=%25page=0&size=100'
    };

    constructor(private http: HttpClient) {
    }

    saveDashboardReportsLayout(checkedReport: any) {
        const serviceUrl = appSettings.apiUrl + '/saveayaraDealDashboardReports';
        // const body = JSON.stringify(checkedReport);
        return this.http.post<any[]>(serviceUrl, checkedReport, httpOptions).toPromise().then(data => {
            return data;
        }, err => {
            console.log(err);
        });
    }

    getOverviewFigures(fYear: any, pDimension: any, period: any, quarter: any) {
        if (fYear === undefined) {
            fYear = '';
        }
        if (pDimension === undefined) {
            pDimension = '';
        }
        if (period === undefined) {
            period = '';
        }
        if (quarter === undefined) {
            quarter = '';
        }
        if (!period && quarter) {
            period = quarter;
        }
        const serviceUrl =
            appSettings.apiUrl + '/dealDashBoardOverView?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear;
        return this.http.get(serviceUrl).toPromise().then((data: any) => {
            return data;
        });
    }

    getListOfReports(): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.otherUrls.getListOfReports);
    }

    dealGuidanceReport(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.dealGuidanceReport + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    dealsByQuarter(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.dealsByQuarter + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocatedMarginGuidancePercentByAllocatedGMGuidance(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocatedMarginGuidancePercentByAllocatedGMGuidance + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocatedMarginGuidanceDeals(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocatedMarginGuidanceDeals + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocatedMarginPercentByProduct(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocatedMarginPercentByProduct + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocationAmtByRegion(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocationAmtByRegion + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocationAmtByPeriod(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocationAmtByPeriod + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocationAmtByAccount(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocationAmtByAccount + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocationAmtByProductCategory(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocationAmtByProductCategory + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocationGuidanceDeals(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocationGuidanceDeals + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocationMarginPercentByAllocationGuidance(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocationMarginPercentByAllocationGuidance + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    discountGuidanceDeals(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.discountGuidanceDeals + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    discountPercentByDiscountGuidance(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.discountPercentByDiscountGuidance + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocatedmarginPercentByRegion(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocatedmarginPercentByRegion + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocatedmarginPercentByAccount(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocatedmarginPercentByAccount + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

    allocatedmarginPercentByProductCategory(fYear: any, pDimension: any, period: any): Observable<any> {
        return this.http.get(appSettings.apiUrl + this.reportUrls.allocatedmarginPercentByProductCategory + '?periodType=' + pDimension + '&period=' + period + '&fiscalYear=' + fYear);
    }

}
