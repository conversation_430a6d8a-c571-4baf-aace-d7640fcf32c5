interface ILabels {
    [index: string]: string;
}

export class RmanRevenueTemplatesLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["amortRuleRevenue"] = "Amortization Rule Revenue";
        this.fieldLabels["createdDate"] = "Created Date";
        this.fieldLabels["endDate"] = "End Date";
        this.fieldLabels["amortMethodRevenue"] = "Amortization Method Revenue";
        this.fieldLabels["amortRuleCogs"] = "Amortization Rule COGS";
        this.fieldLabels["revenueReserveSubaccount"] = "Revenue Reserve Sub Account";
        this.fieldLabels["defCogsSubaccount"] = "Deferred COGS Sub Account";
        this.fieldLabels["cogsSubaccount"] = "COGS Sub Account";
        this.fieldLabels["description"] = "Description";
        this.fieldLabels["revAmortAccount"] = "Revenue Amortization Account";
        this.fieldLabels["accRuleCogs"] = "Accounting Rule COGS";
        this.fieldLabels["revAmortSubaccount"] = "Revenue Amortization Sub Account";
        this.fieldLabels["accTriggerCogs"] = "Accounting Trigger COGS";
        this.fieldLabels["createdBy"] = "Created By";
        this.fieldLabels["lastUpdatedBy"] = "Last Updated By";
        this.fieldLabels["activeFlag"] = "Active Flag";
        this.fieldLabels["amortMethodCogs"] = "Amortization Method COGS";
        this.fieldLabels["cogsAccount"] = "COGS Account";
        this.fieldLabels["revAccount"] = "Revenue Account";
        this.fieldLabels["startDate"] = "Start Date";
        this.fieldLabels["revenueTemplateId"] = "Revenue Template ID";
        this.fieldLabels["lastUpdatedDate"] = "Last Updated Date";
        this.fieldLabels["templateName"] = "Name";
        this.fieldLabels["revSubaccount"] = "Revenue Sub Account";
        this.fieldLabels["defCogsAccount"] = "Deferred COGS Account";
        this.fieldLabels["accTriggerRevenue"] = "Accounting Trigger Revenue";
        this.fieldLabels["accRuleRevenue"] = "Accounting Rule Revenue";
        this.fieldLabels["defRevSubaccount"] = "Deferred Revenue Sub Account";
        this.fieldLabels["defRevAccount"] = "Deferred Revenue Account";
        this.fieldLabels["revReserveAccount"] = "Revenue Reserve Account";
        this.fieldLabels["defRevenueContAccount"] = "Deferred Revenue Contingency Account";
        this.fieldLabels["defRevenueContSubaccount"] = "Deferred Revenue Contingency Sub Account";
    }

}
