export interface RmanRevenueTemplates {
    amortRuleRevenue: any;
    createdDate: any;
    endDate: any;
    amortMethodRevenue: any;
    amortRuleCogs: any;
    revenueReserveSubaccount: any;
    defCogsSubaccount: any;
    cogsSubaccount: any;
    description: any;
    revAmortAccount: any;
    accRuleCogs: any;
    revAmortSubaccount: any;
    accTriggerCogs: any;
    createdBy: any;
    lastUpdatedBy: any;
    activeFlag: any;
    amortMethodCogs: any;
    cogsAccount: any;
    revAccount: any;
    startDate: any;
    revenueTemplateId: any;
    lastUpdatedDate: any;
    templateName: any;
    revSubaccount: any;
    defCogsAccount: any;
    accTriggerRevenue: any;
    accRuleRevenue: any;
    defRevSubaccount: any;
    defRevAccount: any;
    revReserveAccount: any;
    defRevenueContAccount: any;
    defRevenueContSubaccount: any;
    ltstFlag: any;
    additionalAttribute5: any;
    revenueTrigger: any;
}
