interface ILabels {
    [index: string]: string;
}

export class RmanLegalEntitiesLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["reportingCurrency"] = "Reporting Currency";
        this.fieldLabels["defRevCogsId"] = "Def Rev Cogs Id";
        this.fieldLabels["lastUpdateDate"] = "Last Update Date";
        this.fieldLabels["ledgerName"] = "Ledger Name";
        this.fieldLabels["currencyCode"] = "Currency Code";
        this.fieldLabels["createdBy"] = "Created By";
        this.fieldLabels["attribute3"] = "Attribute3";
        this.fieldLabels["cogsAccountId"] = "Cogs Account Id";
        this.fieldLabels["allocationCurrency"] = "Allocation Currency";
        this.fieldLabels["lastUpdatedBy"] = "Last Updated By";
        this.fieldLabels["attribute2"] = "Attribute2";
        this.fieldLabels["attribute1"] = "Attribute1";
        this.fieldLabels["periodType"] = "Period Type";
        this.fieldLabels["defRevAccountId"] = "Def Rev Account Id";
        this.fieldLabels["legalEntityId"] = "Legal Entity Id";
        this.fieldLabels["roundingPrecision"] = "Rounding Precision";
        this.fieldLabels["revAccountId"] = "Rev Account Id";
        this.fieldLabels["defCogsAccountId"] = "Def Cogs Account Id";
        this.fieldLabels["creationDate"] = "Creation Date";
        this.fieldLabels["enabledFlag"] = "Enabled Flag";
        this.fieldLabels["currencyFormat"] = "Currency Format";
        this.fieldLabels["attribute5"] = "Attribute5";
        this.fieldLabels["name"] = "Name";
        this.fieldLabels["attribute4"] = "Attribute4";
        this.fieldLabels["locale"] = "Locale";
        this.fieldLabels["functionalCurrency"] = "Functional Currency";
        this.fieldLabels["deferredCogs"] = "Deferred Cogs";
        this.fieldLabels["calendar"] = "Calendar";
        this.fieldLabels["defferedAccount"] = "Deffered Account";
        this.fieldLabels["companyCode"] = "Company Code";
        this.fieldLabels["description"] = "Description";
    }

}
