import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { Table } from 'primeng/table';
import { NotificationService } from '../shared/notifications.service';
import { UploadService } from '../shared/upload.service';
import { RmanConversionRatesInt } from './rmanConversionRatesInt';
import { RmanConversionRatesIntService } from './rmanConversionRatesIntservice';
import { CommonSharedService } from '../shared/common.service';

declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Component({
    templateUrl: './rmanConversionRatesInt.component.html',
    selector: 'rmanConversionRatesInt-data',
    providers: [RmanConversionRatesIntService, ConfirmationService]
})

export class RmanConversionRatesIntComponent {

    displayDialog: boolean;

    displaySearchDialog: boolean;
    
    uploadLoading: boolean = false;

    rmanConversionRatesInt: RmanConversionRatesInt = new RmanConversionRatesIntImpl();

    rmanConversionRatesIntSearch: RmanConversionRatesInt = new RmanConversionRatesIntImpl();

    isSerached: number = 0;

    selectedRmanConversionRatesInt: RmanConversionRatesInt;

    newRmanConversionRatesInt: boolean;
	
	displayConversionRatesIntDialog: boolean = false;
	
    rmanConversionRatesIntList: RmanConversionRatesInt[];

    cols: any[];
    //columns: ILabels;

    columnOptions: any[];

    paginationOptions = {};

    pages: {};


    datasource: any[];
    pageSize: number;
    totalElements: number;
    collapsed: boolean = true;
    
    customerForm: FormGroup;
    noData = appSettings.noData;
    loading: boolean;
    rmanConversionRatesInttatus: any[];
    regions:any[];
    customerTypes:any[];
    isSelectAllChecked = true;
    globalCols: any[];
    clonedCols: any[];
    userId: number;
    showPaginator: boolean = true;
    startIndex: number;
    showAddColumns = true;
    columns: any[];
    
    exportCols: string[] = [];
  	disableExport: boolean = true;
    selectedLines: any[] = [];
	  exceptionsList: any[] = [];



    constructor(
        private rmanConversionRatesIntService: RmanConversionRatesIntService,
        private confirmationService: ConfirmationService,
        private formBuilder: FormBuilder,
        private notificationService:NotificationService,
        public _uploadService:UploadService,  private commonSharedService: CommonSharedService
    ) {

        // generate list code
        this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };


    }

    ngOnInit() {
      this.selectedLines = [];
        this.globalCols = [
            { field: 'fromCurrency', header: 'FROM_CURRENCY', showField: true, drag: false, display: "table-cell",type:'text'},
            { field: 'toCurrency', header: 'TO_CURRENCY', showField: true, drag: true, display: "table-cell",type:'text' },
            { field: 'conversionDate', header: 'CONVERSION_DATE', showField: true, drag: true, display: "table-cell",type:'date'},
            { field: 'conversionType', header: 'CONVERSION_TYPE', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'conversionStartDate', header: 'CONVERSION_START_DATE', showField: true, drag: true, display: "table-cell",type:'date'},
            { field: 'conversionEndDate', header: 'CONVERSION_END_DATE', showField: true, drag: true, display: "table-cell",type:'date'},
			{ field: 'conversionRate', header: 'CONVERSION_RATE', showField: true, drag: true, display: "table-cell",type:'number'},
			{ field: 'statusCode', header: 'STATUS_CODE', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'processId', header: 'PROCESS_ID', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'exceptionMessage', header: 'EXCEPTION_MESSAGE', showField: true, drag: true, display: "table-cell",type:'text'}

        ];

        this.columns = [];
        this.getTableColumns("rmanConversionRatesInt", "ConversionRatesInt");
    }

    prepareRmanStatus() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectStat, value: null }];
        this.rmanConversionRatesInttatus.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode });
        });
        this.rmanConversionRatesInttatus = rmanLookupsVTempObj;
    }

    prepareRegions() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectRegion, value: null }];
        this.regions.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupDescription });
        });
        this.regions = rmanLookupsVTempObj;
    }

    prepareCustomerTypes() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectCustomerType, value: null }];
        this.customerTypes.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupDescription });
        });
        this.customerTypes = rmanLookupsVTempObj;
    }

    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
          if (response && response != null && response.userId) {
            this.columns = [];
            let colsList = response.tableColumns.split(",");
            if (colsList.length > 0) {
              colsList.forEach((item, index) => {
                if (item) {
                  this.startIndex = this.globalCols.findIndex(col => col.field == item);
                  this.onDrop(index);
                }
              });
            }
            this.globalCols.forEach(col => {
              if (response.tableColumns.indexOf(col.field) !== -1) {
                this.columns.push(col);
              } else {
                col.showField = false;
              }
            });
            if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
            this.showPaginator = this.columns.length !== 0;
            this.userId = response.userId;
          } else {
            this.columns = this.globalCols;
          }
        }).catch(() => {
          this.notificationService.showError('Error occured while getting table columns data');
          this.loading = false;
        });
      }

      saveColumns() {
        let selectedCols = "";
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
          if (col.showField) {
            selectedCols += col.field;
            if (index < colLength) {
              selectedCols += ",";
            }
          }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns("rmanConversionRatesInt", "ConversionRatesInt", selectedCols, this.userId).then((response) => {
          this.columns = this.globalCols.filter(item => item.showField);
          this.userId = response["userId"];
          this.showPaginator = this.columns.length !== 0;
          this.loading = false;
        }).catch(() => {
          this.notificationService.showError('Error occured while getting data');
          this.loading = false;
        });
      }

      onDragStart(index: number) {
        this.startIndex = index;
      }

      onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
        //console.log(this.globalCols);
      }

      selectColumns(col: any) {
        let cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
          this.isSelectAllChecked = false;
        } else {
          this.isSelectAllChecked = true;
        }
      }

      onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
          if (this.isSelectAllChecked) {
            col.showField = true;
          } else {
            if (col.drag) {
              col.showField = false;
            }
          }
        });
      }

      onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
      }

      closeConfigureColumns(event: any) {
      this.showAddColumns = true;
      this.globalCols = this.clonedCols;
      let configCol = this.globalCols.filter(item => !item.showField);
      this.isSelectAllChecked = !(configCol.length > 0);
      }



    getAllRmanConversionRatesInt() {
        this.loading = true;
        this.rmanConversionRatesIntService.getAllRmanConversionRatesInt(this.paginationOptions, this.rmanConversionRatesInt, this.exportCols).then((rmanConversionRatesIntList: any) => {
            this.loading = false;
            this.datasource = rmanConversionRatesIntList.content;
            this.rmanConversionRatesIntList = rmanConversionRatesIntList.content;
            this.totalElements = rmanConversionRatesIntList.totalElements;
            this.pageSize = rmanConversionRatesIntList.size;
            this.displaySearchDialog = false;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    getRmanConversionRatesInt(event: any) {
        this.loading = true;
        let first: number = event.first;
        let rows: number = event.rows;
        let pageNumber: number = first / rows;
        this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
        this.rmanConversionRatesIntService.getAllRmanConversionRatesInt(this.paginationOptions, this.rmanConversionRatesInt, this.exportCols).then((rmanConversionRatesIntList: any) => {
            this.loading = false;
            this.datasource = rmanConversionRatesIntList.content;
            this.rmanConversionRatesIntList = rmanConversionRatesIntList.content;
            this.totalElements = rmanConversionRatesIntList.totalElements;
            this.pageSize = rmanConversionRatesIntList.size;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });

    }

	exportExcel() {
	    this.exportCols = [];

	    for (let index = 0; index < this.columns.length; index++) {
	      if (this.columns[index].showField) {
	        this.exportCols.push(this.columns[index].field);
	      }
	    }

	    let serviceUrl = this.rmanConversionRatesIntService.getServiceUrl(this.paginationOptions, this.rmanConversionRatesInt, 1, this.exportCols);
	    window.location.href = serviceUrl;
 	}

   onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }


    reset(dt: Table) {
        this.paginationOptions = {};
        this.rmanConversionRatesInt = new RmanConversionRatesIntImpl();
        this.rmanConversionRatesIntSearch = new RmanConversionRatesIntImpl();
        this.selectedLines = [];
        dt.reset();
    }
    deleteSelected(dt: Table) {

        this.exceptionsList = [];

        for (let line of this.selectedLines) {
            this.exceptionsList.push(line.recordId);
        }
        if (this.exceptionsList.length > 0) {

        this.confirmationService.confirm({
            message: 'Are you sure you want to delete?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
              this.loading = true;
              this.rmanConversionRatesIntService.deleteSelectedLines(this.exceptionsList).then((response: any) => {
                this.notificationService.showSuccess('Selected records are deleted Successfully  ');
            }).then(() => {
                this.reset(dt);
            }).catch((err: any) => {
                this.loading = false;
                this.notificationService.showError('Getting error While delete records');
            });
            },
            reject: () => {
              this.notificationService.showWarning('You have rejected');
            }
          });

        } else {
            this.notificationService.showInfo('Select at least one record to delete');
        }
    }



    
    findSelectedRmanConversionRatesIntIndex(): number {
        return this.rmanConversionRatesIntList.indexOf(this.selectedRmanConversionRatesInt);
    }

    onRowSelect(event: any) {

    }

    
    hideColumnMenu: boolean = true;

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    showFilter: boolean = false;

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {
        if (this.isSerached == 0) {
            this.rmanConversionRatesIntSearch = new RmanConversionRatesIntImpl();
        }
        this.rmanConversionRatesIntSearch = new RmanConversionRatesIntImpl();
        this.displaySearchDialog = true;

    }
    
	search() {

        this.isSerached = 1;
        this.rmanConversionRatesInt = this.rmanConversionRatesIntSearch;
        this.paginationOptions={};
        this.getAllRmanConversionRatesInt();
    }
    
    
    showUploadLoader(){
    	this.uploadLoading = true;
    }
	
}


export class RmanConversionRatesIntImpl implements RmanConversionRatesInt {
    constructor(public attribute10?: any, public conversionStartDate?: any, public attribute14?: any, public attribute13?: any, public createdDate?: any, public attribute12?: any, public attribute11?: any, public conversionType?: any, public toCurrency?: any, public attribute3?: any, public createdBy?: any, public attribute2?: any, public lastUpdatedBy?: any, public attribute1?: any, public conversionRate?: any, public attribute9?: any, public attribute8?: any, public attribute7?: any, public attribute6?: any, public attribute5?: any, public attribute4?: any, public lastUpdatedDate?: any, public fromCurrency?: any, public attribute15?: any, public conversionEndDate?: any, public conversionDate?: any, public statusCode?: any) { }
}

interface ILabels {
    [index: string]: string;
}
