import { DatePipe } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require:any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type':  'application/json',
  })
};




@Injectable()
export class RmanConversionRatesIntService {

    constructor(private http: HttpClient,private datePipe: DatePipe) {}
    
    getServiceUrl(paginationOptions:any,rmanConversionRatesIntSearchObject:any,  exportFlag: any, exportCols: any) {
        
       let serviceUrl = appSettings.apiUrl + '/fxRatesInterfaceExport?';

       if (exportFlag == 0) {
      	 serviceUrl = appSettings.apiUrl + '/rmanConversionRatesIntSearch?';
       }
       
       let searchString='';

        if (rmanConversionRatesIntSearchObject.fromCurrency!=undefined && rmanConversionRatesIntSearchObject.fromCurrency!="") {
            searchString=searchString+'fromCurrency:'+rmanConversionRatesIntSearchObject.fromCurrency+',';
        }

        if (rmanConversionRatesIntSearchObject.toCurrency!=undefined && rmanConversionRatesIntSearchObject.toCurrency!="") {
            searchString=searchString+'toCurrency:'+rmanConversionRatesIntSearchObject.toCurrency+',';
        }

        if (rmanConversionRatesIntSearchObject.conversionType!=undefined && rmanConversionRatesIntSearchObject.conversionType!="") {
            searchString=searchString+'conversionType:'+rmanConversionRatesIntSearchObject.conversionType+',';
        }

        if (rmanConversionRatesIntSearchObject.conversionRate!=undefined && rmanConversionRatesIntSearchObject.conversionRate!="") {
            searchString=searchString+'conversionRate:'+rmanConversionRatesIntSearchObject.conversionRate+',';
        }

        if (rmanConversionRatesIntSearchObject.conversionStartDate!=undefined && rmanConversionRatesIntSearchObject.conversionStartDate!="") {
            searchString=searchString+'conversionStartDate:'+rmanConversionRatesIntSearchObject.conversionStartDate+',';
        }

        if (rmanConversionRatesIntSearchObject.conversionEndDate!=undefined && rmanConversionRatesIntSearchObject.conversionEndDate!="") {
            searchString=searchString+'conversionEndDate:'+rmanConversionRatesIntSearchObject.conversionEndDate+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute1!=undefined && rmanConversionRatesIntSearchObject.attribute1!="") {
            searchString=searchString+'attribute1:'+rmanConversionRatesIntSearchObject.attribute1+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute2!=undefined && rmanConversionRatesIntSearchObject.attribute2!="") {
            searchString=searchString+'attribute2:'+rmanConversionRatesIntSearchObject.attribute2+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute3!=undefined && rmanConversionRatesIntSearchObject.attribute3!="") {
            searchString=searchString+'attribute3:'+rmanConversionRatesIntSearchObject.attribute3+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute4!=undefined && rmanConversionRatesIntSearchObject.attribute4!="") {
            searchString=searchString+'attribute4:'+rmanConversionRatesIntSearchObject.attribute4+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute5!=undefined && rmanConversionRatesIntSearchObject.attribute5!="") {
            searchString=searchString+'attribute5:'+rmanConversionRatesIntSearchObject.attribute5+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute6!=undefined && rmanConversionRatesIntSearchObject.attribute6!="") {
            searchString=searchString+'attribute6:'+rmanConversionRatesIntSearchObject.attribute6+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute7!=undefined && rmanConversionRatesIntSearchObject.attribute7!="") {
            searchString=searchString+'attribute7:'+rmanConversionRatesIntSearchObject.attribute7+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute8!=undefined && rmanConversionRatesIntSearchObject.attribute8!="") {
            searchString=searchString+'attribute8:'+rmanConversionRatesIntSearchObject.attribute8+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute9!=undefined && rmanConversionRatesIntSearchObject.attribute9!="") {
            searchString=searchString+'attribute9:'+rmanConversionRatesIntSearchObject.attribute9+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute10!=undefined && rmanConversionRatesIntSearchObject.attribute10!="") {
            searchString=searchString+'attribute10:'+rmanConversionRatesIntSearchObject.attribute10+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute11!=undefined && rmanConversionRatesIntSearchObject.attribute11!="") {
            searchString=searchString+'attribute11:'+rmanConversionRatesIntSearchObject.attribute11+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute12!=undefined && rmanConversionRatesIntSearchObject.attribute12!="") {
            searchString=searchString+'attribute12:'+rmanConversionRatesIntSearchObject.attribute12+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute13!=undefined && rmanConversionRatesIntSearchObject.attribute13!="") {
            searchString=searchString+'attribute13:'+rmanConversionRatesIntSearchObject.attribute13+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute14!=undefined && rmanConversionRatesIntSearchObject.attribute14!="") {
            searchString=searchString+'attribute14:'+rmanConversionRatesIntSearchObject.attribute14+',';
        }

        if (rmanConversionRatesIntSearchObject.attribute15!=undefined && rmanConversionRatesIntSearchObject.attribute15!="") {
            searchString=searchString+'attribute15:'+rmanConversionRatesIntSearchObject.attribute15+',';
        }

        if (rmanConversionRatesIntSearchObject.createdDate!=undefined && rmanConversionRatesIntSearchObject.createdDate!="") {
            searchString=searchString+'createdDate:'+rmanConversionRatesIntSearchObject.createdDate+',';
        }

        if (rmanConversionRatesIntSearchObject.createdBy!=undefined && rmanConversionRatesIntSearchObject.createdBy!="") {
            searchString=searchString+'createdBy:'+rmanConversionRatesIntSearchObject.createdBy+',';
        }

        if (rmanConversionRatesIntSearchObject.lastUpdatedDate!=undefined && rmanConversionRatesIntSearchObject.lastUpdatedDate!="") {
            searchString=searchString+'lastUpdatedDate:'+rmanConversionRatesIntSearchObject.lastUpdatedDate+',';
        }

        if (rmanConversionRatesIntSearchObject.lastUpdatedBy!=undefined && rmanConversionRatesIntSearchObject.lastUpdatedBy!="") {
            searchString=searchString+'lastUpdatedBy:'+rmanConversionRatesIntSearchObject.lastUpdatedBy;
        }

        if (rmanConversionRatesIntSearchObject.statusCode!=undefined && rmanConversionRatesIntSearchObject.statusCode!="") {
            searchString=searchString+'statusCode:'+rmanConversionRatesIntSearchObject.statusCode;
        }

        if (rmanConversionRatesIntSearchObject.conversionDate!=undefined && rmanConversionRatesIntSearchObject.conversionDate!="") {
             searchString=searchString+'conversionDate:'+ this.datePipe.transform(rmanConversionRatesIntSearchObject.conversionDate,'yyyyMMdd')+',';
        }
		
		if (rmanConversionRatesIntSearchObject.processId!=undefined && rmanConversionRatesIntSearchObject.processId!="") {
            searchString=searchString+'processId:'+rmanConversionRatesIntSearchObject.processId+',';
        }
		
		searchString=searchString+'interfaceStatus:E';


        if (searchString == '') {
            serviceUrl=serviceUrl+'search=%25';
        }
        else {
            serviceUrl=serviceUrl+'search='+searchString;
        }
        
        if (paginationOptions.pageNumber!=undefined && paginationOptions.pageNumber!="" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber!=0) {
           serviceUrl=serviceUrl+'&page='+paginationOptions.pageNumber+'&size='+paginationOptions.pageSize;
        }
        
        if (exportCols != undefined && exportCols != "") {
      		serviceUrl = serviceUrl + '&exportCols=' + exportCols;
    	}
   		
   		return serviceUrl;

	}
    
    getAllRmanConversionRatesInt(paginationOptions:any,rmanConversionRatesIntSearchObject:any, exportCols:any): Promise<any[]> {
         let serviceUrl = this.getServiceUrl(paginationOptions, rmanConversionRatesIntSearchObject, 0, exportCols);
        return this.http.get(serviceUrl).toPromise().then((data:any) => {
            return data;
        });
    }
    deleteSelectedLines(exceptionLines: any[]){

        let delUrl = appSettings.apiUrl + '/bulkDelFxRatesExceptions?recIds='+exceptionLines;
        return this.http.delete(delUrl,{responseType : "text"}).toPromise().then((data:any) => {
          return data;
        });

    }


	
}
