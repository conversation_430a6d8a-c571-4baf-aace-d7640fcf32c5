export interface RmanConversionRatesInt {
    attribute10?: any;
    conversionStartDate?: any;
    attribute14?: any;
    attribute13?: any;
    createdDate?: any;
    attribute12?: any;
    attribute11?: any;
    conversionType?: any;
    toCurrency?: any;
    attribute3?: any;
    createdBy?: any;
    attribute2?: any;
    lastUpdatedBy?: any;
    attribute1?: any;
    conversionRate?: any;
    attribute9?: any;
    attribute8?: any;
    attribute7?: any;
    attribute6?: any;
    attribute5?: any;
    attribute4?: any;
    lastUpdatedDate?: any;
    fromCurrency?: any;
    attribute15?: any;
    conversionEndDate?: any;
    conversionDate?: any;
    statusCode?: any;
	processId? :any;
	interfaceStatus?: any;
	exceptionMessage?: any;
}
