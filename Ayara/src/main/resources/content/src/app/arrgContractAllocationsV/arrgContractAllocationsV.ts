export interface ArrgContractAllocationsV {
  dealLineId: any;
  quantity: any;
  elementType: any;
  allocationAmount: any;
  parentLineNumber: any;
  totalCost: any;
  dealCurrencyCode: any;
  unitSellingPrice: any;
  unitListPrice: any;
  fmvCategory: any;
  cvInOutAmount: any;
  bundleAttribitionList: any;
  dealNumber: any;
  lineAmount: any;
  repCurrCode: any;
  fmvRuleName: any;
  pobGrouping: any;
  dealLineNumber: any;
  productGroup: any;
  fmvMin: any;
  fmvMean: any;
  fmvAmount: any;
  dealHeaderId: any;
  solution: any;
  parentLineId: any;
  productName: any;
  dealArrangementId: any;
  trxCurrencyCode: any;
  accountingScope: any;
  productType: any;
  fmvMax: any;
  accountingStandard: any;
  uomCode: any;
  allocationInclusive: any;
  bundleAttribitionNet: any;
  bundleFlag: any;
  fmvType: any;
  vc: any;
  arrgLineNumber: any;
  sspLow: any;
  sspHigh: any;
  ssp: any;
  netPriceDiscount: any;
  fxRate: any;
  fxDate: any;
  fxCurrency: any;
  allocationAmountFc: any;
  orderNumber: any;
  sourceLineNumber: any;
  customerPoNum: any;
  allocationFlag: any;
}
