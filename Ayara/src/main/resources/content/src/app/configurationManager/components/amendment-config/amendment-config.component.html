<div class="card-wrapper" *ngIf="configId">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block" *ngIf="{configs: amendmentConfigs$ | async} as data">
                    <p-panel [toggleable]="false" [style]="{'margin-bottom':'20px'}">
                        <div>
                            <h2>Amendment Treatment Configuration</h2>
                            <div class="pull-right icons-list">
                                <a *isAuthorized="['write','MAMENDS']" (click)="showDialogToAdd()" title="Add"><em class="fa fa-plus-circle"></em></a>
                                <a (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
                            </div>
                        </div>
                        <div class="x-scroll">
                            <p-table #dt class="ui-datatable arrangementMgrTbl" [value]="data.configs"
                                     (onLazyLoad)="getAmendmentsConfigs(configId, $event)"
                                     [loading]="isLoading"
                                     [lazy]="true" [paginator]="true" [rows]="pageSize" [totalRecords]="totalElements"
                                     scrollable="true">

                                <ng-template pTemplate="header" class="arrangementMgrTblHead">
                                    <tr>
                                        <th></th>
                                        <th>
                                            Sr. No.
                                        </th>
                                        <th>
                                            Parameter
                                        </th>
                                        <th>
                                            Qualifier
                                        </th>
                                        <th>
                                            Value
                                        </th>
                                        <th>
                                            Criteria
                                        </th>
                                        <th>
                                            Enabled
                                        </th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-config let-rowIndex="rowIndex">
                                    <tr>
                                        <td>
                                            <span>
												<a *isAuthorized="['write','MAMENDS']" (click)="editRow(config)" class="icon-edit"> </a>
											</span>
                                            <span>
												<a *isAuthorized="['write','MAMENDS']" (click)="delete(config)" class="icon-delete"> </a>
											</span>
                                        </td>
                                        <td>
                                            <a>{{rowIndex + 1}}</a>
                                        </td>
                                        <td>
                                            <a>{{config.parameterName}}</a>
                                        </td>
                                        <td>
                                            <a>{{config.qualifier}}</a>
                                        </td>
                                        <td>
                                            <a>{{config.parameterValue}}</a>
                                        </td>
                                        <td>
                                            <a>{{config.andOr}}</a>
                                        </td>
                                        <td>
                                            <a>{{config.enabledFlag}}</a>
                                        </td>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="emptymessage">
                                    <tr>
                                        <td class="no-data">{{noData}}</td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>

                    </p-panel>

                </div>
            </div>
        </div>
    </div>
</div>

<p-dialog header="{{(isCreate) ? 'Create' : 'Edit'}}" [(visible)]="displayDialog"
          [draggable]="true" [modal]="true" [blockScroll]="true" (onHide)="cancel()">
    <form (ngSubmit)="save()" [formGroup]="configsForm" novalidate>
        <div class="ui-g ui-fluid">
            <div class="ui-g-12 form-group">
                <div class="ui-grid-row form-container">
                    <div class="ui-g-6">
                        <span class="selectSpan">Parameter Name*</span>
                        <p-dropdown class="ml-auto" [options]="paramNamesList"
                                    placeholder="Select Parameter Name*"
                                    [(ngModel)]="selectedParamName"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="lookupDescription"
                                    (onChange)="onParamNameChange($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Parameter Value*</span>
                        <input  pInputText type="text" class="textbox" placeholder="Type Parameter Value*" formControlName="parameterValue">
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Qualifier*</span>
                        <p-dropdown class="ml-auto" [options]="qualifiersList"
                                    placeholder="Select Qualifier"
                                    [(ngModel)]="selectedQualifier"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="lookupDescription"
                                    (onChange)="onQualifiersChange($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Criteria*</span>
                        <p-dropdown class="ml-auto" [options]="andOrList"
                                    placeholder="Select Criteria*"
                                    [(ngModel)]="selectedAndOr"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="description"
                                    (onChange)="onChangeAndOr($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Enabled Flag*</span>
                        <p-dropdown class="ml-auto" [options]="enabledFlagList"
                                    placeholder="Select Flag*"
                                    [(ngModel)]="selectedFlag"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="lookupDescription"
                                    (onChange)="onFlagChange($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                </div>

            </div>
        </div>
    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" pButton class="primary-btn" label="Save" (click)="save()"
                    [disabled]="!configsForm.valid"></button>
            <button type="button" pButton class="secondary-btn" (click)="cancel()" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>
