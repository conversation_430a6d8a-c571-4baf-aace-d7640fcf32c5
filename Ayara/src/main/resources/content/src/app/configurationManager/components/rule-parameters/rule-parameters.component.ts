import {Component, Input, OnChanges, OnInit} from '@angular/core';
import {Observable} from 'rxjs';
import {AssignRuleParam} from '../../models/assignRuleParam';
import {noData} from '../../../appsettings';
import {finalize, first, map, tap} from 'rxjs/operators';
import {NotificationService} from '../../../shared/notifications.service';
import {RmanLookupsVService} from '../../../rmanLookupsV/rmanLookupsVservice';
import {ConfirmationService} from 'primeng/api';
import {Table} from 'primeng/table';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {RmanEntityParametersVService} from '../../../rmanEntityParametersV/rmanEntityParametersVservice';
import {AssignRulesService} from '../../services/assignRules.service';

@Component({
    selector: 'app-rule-parameters',
    templateUrl: './rule-parameters.component.html',
    styleUrls: ['./rule-parameters.component.css']
})
export class RuleParametersComponent implements OnInit, OnChanges {

    @Input() assignRuleId: number;

    assignRuleParams$: Observable<AssignRuleParam[] | []>;
    totalElements: number;
    pageSize = 10;
    noData = noData;
    isLoading = false;
    displayDialog = false;
    isCreate: boolean;

    qualifiersList = [];
    paramNamesList = [];
    andOrList = [
        {name: 'AND', description: 'AND'},
        {name: 'OR', description: 'OR'}
    ];

    selectedQualifier = {lookupCode: '', lookupDescription: ''};
    selectedParamName = {parameterName: '', entityParameterId: ''};
    selectedAndOr = {name: '', description: ''};

    editingParamId = null;

    paramsForm = new FormGroup({
        parameterName: new FormControl('', Validators.required),
        parameterGroup: new FormControl(null, Validators.required),
        qualifier: new FormControl('', Validators.required),
        paramterValue: new FormControl(''),
        andOr: new FormControl('', Validators.required),
        parameterId: new FormControl('')
    });

    get parameterName() {
        return this.paramsForm.get('parameterName');
    }

    get parameterGroup() {
        return this.paramsForm.get('parameterGroup');
    }

    get qualifier() {
        return this.paramsForm.get('qualifier');
    }

    get paramterValue() {
        return this.paramsForm.get('paramterValue');
    }

    get andOr() {
        return this.paramsForm.get('andOr');
    }
    
    get parameterId() {
        return this.paramsForm.get('parameterId');
    }

    constructor(
        private notificationService: NotificationService,
        private assignRulesService: AssignRulesService,
        private entityParametersService: RmanEntityParametersVService,
        private lookupsVService: RmanLookupsVService,
        private confirmationService: ConfirmationService
    ) {
    }

    ngOnInit(): void {
        this.getQualifiers();
        this.getParameterNames();
    }

    ngOnChanges(changes: any) {
        const id = changes.assignRuleId.currentValue;
        if (this.assignRuleId && changes.assignRuleId.currentValue) {
            this.getAssignRuleParams(id);
        }
    }

    getQualifiers() {
        this.lookupsVService.getAllRmanLookupsV(null, {'lookupTypeName': 'RULE_QUALIFIER'})
            .then((data: any) => this.qualifiersList = data.content.map(item => ({
                lookupCode: item.lookupCode,
                lookupDescription: item.lookupDescription
            })));
    }

    getParameterNames() {
        this.entityParametersService.getAllRmanEntityParametersV({pageNumber: 0, pageSize: 10}, {'entityName': 'DEAL_ASSIGNMENT_RULES'})
            .then((data: any) => {
                this.paramNamesList = data.content.map(item => ({
                    parameterName: item.parameterName,
                    entityParameterId: item.entityParameterId
                }));
            });
    }

    getAssignRuleParams(assignRuleId: number, $event?: any) {
        this.isLoading = true;
        this.assignRuleParams$ = this.assignRulesService.getAssignRuleParamsById(assignRuleId)
            .pipe(
                tap(data => this.totalElements = data.totalElements),
                map(data => data.content),
                finalize(() => {
                    this.isLoading = false;
                }),
            );
    }

    showDialogToAdd() {
        this.resetDropdownModels();
        this.displayDialog = true;
        this.isCreate = true;
    }

    reset(dt: Table) {
        dt.reset();
    }

    resetDropdownModels() {
        this.selectedQualifier = {lookupCode: '', lookupDescription: ''};
        this.selectedParamName = {parameterName: '', entityParameterId: ''};
        this.selectedAndOr = {name: '', description: ''};
    }

    onQualifiersChange(event: any) {
        this.selectedQualifier = {lookupCode: event.value.lookupCode, lookupDescription: event.value.lookupDescription};
        this.qualifier.setValue(this.selectedQualifier.lookupCode);
    }

    onParamNameChange(event: any) {
        this.selectedParamName = {parameterName: event.value.parameterName, entityParameterId: event.value.entityParameterId};
        this.parameterName.setValue(this.selectedParamName.parameterName);
        this.parameterId.setValue(this.selectedParamName.entityParameterId);
    }

    onChangeAndOr(event: any) {
        this.selectedAndOr = {name: event.value.name, description: event.value.description};
        this.andOr.setValue(event.value.name);
    }

    editRow(ruleParam: AssignRuleParam) {
        this.isCreate = false;
        this.editingParamId = ruleParam.ruleParameterId;

        const {parameterName, parameterGroup, paramterValue, qualifier, andOr, parameterId} = ruleParam;
        this.paramsForm.setValue({parameterName, parameterGroup, paramterValue, qualifier, andOr, parameterId});

        const paramOption = this.paramNamesList.find(option => option.parameterName === parameterName);
        const qualifierOption = this.qualifiersList.find(option => option.lookupCode === qualifier);

        this.selectedAndOr = {name: andOr, description: andOr};
        this.selectedParamName = paramOption;
        this.selectedQualifier = qualifierOption;

        this.displayDialog = true;
    }

    delete(assignRuleParam: AssignRuleParam) {
        this.displayDialog = false;
        this.confirmationService.confirm({
            message: 'Are you sure you want to delete this record?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
                this.assignRulesService.deleteAssignRuleParam(assignRuleParam.ruleParameterId)
                    .pipe(first())
                    .subscribe(
                        () => {
                            this.notificationService.showSuccess('Deleted successfully');
                            this.getAssignRuleParams(this.assignRuleId);
                        },
                        (error) => this.notificationService.showError(error.message)
                    );
            },
            reject: () => {
                this.notificationService.showWarning('You have rejected');
            }
        });
    }

    cancel() {
        this.paramsForm.reset();
        this.displayDialog = false;
    }

    save() {
        const data = this.paramsForm.value;
        if (this.isCreate) {
            this.assignRulesService.createAssignRuleParam({...data, assignRuleId: this.assignRuleId})
                .subscribe(
                    () => {
                        this.notificationService.showSuccess('Saved successfully');
                        this.getAssignRuleParams(this.assignRuleId);
                    },
                    (error) => this.notificationService.showError(error.message)
                );
        } else {
            const putData = {ruleParameterId: this.editingParamId, assignRuleId: this.assignRuleId, ...data};
            this.assignRulesService.updateAssignRuleParam(this.editingParamId, putData)
                .subscribe(
                    () => {
                        this.notificationService.showSuccess('Updated successfully');
                        this.getAssignRuleParams(this.assignRuleId);
                    },
                    (error) => this.notificationService.showError(error.message)
                );
        }
        this.displayDialog = false;
    }
}
