import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {TableData} from '../../shared/models/tableData';
import {AssigneeRule} from '../models/assigneeRule';
import {apiUrl} from '../../appsettings';
import {Observable} from 'rxjs';
import {AssignRuleParam} from '../models/assignRuleParam';

@Injectable({
    providedIn: 'root'
})
export class AssignRulesService {

    constructor(private http: HttpClient) {
    }

    getAssigneeRules(page = 0, size = 10): Observable<TableData<AssigneeRule[]>> {
        return this.http.get<TableData<AssigneeRule[]>>(apiUrl + `/ayaraDealAssignRulesSearch?search=%25&page=${page}&size=${size}`);
    }

    getUserRoles() {
        return this.http.get(apiUrl + '/rmanResponsibilitiesSearch?search=%25');
    }

    createAssigneeRule(data: Partial<AssigneeRule>): Observable<Object> {
        return this.http.post(apiUrl + '/AYARA_DEAL_ASSIGN_RULES', data);
    }

    updateAssigneeRule(id: number, data: Partial<AssigneeRule>): Observable<Object> {
        return this.http.put(apiUrl + `/AYARA_DEAL_ASSIGN_RULES/${id}`, data);
    }

    deleteAssigneeRule(id: number): Observable<Object> {
        return this.http.delete(apiUrl + `/AYARA_DEAL_ASSIGN_RULES/${id}`);
    }

    getAssignRuleParamsById(ruleId: number): Observable<TableData<AssignRuleParam[]>> {
        return this.http.get<TableData<AssignRuleParam[]>>(apiUrl + `/ayaraDealAssignRuleParametersSearch?search=assignRuleId:${ruleId}`);
    }

    createAssignRuleParam(data: Partial<AssigneeRule>): Observable<Object> {
        return this.http.post(apiUrl + '/AYARA_DEAL_ASSIGN_RULE_PARAMETERS', data);
    }

    updateAssignRuleParam(id: number, data: Partial<AssigneeRule>): Observable<Object> {
        return this.http.put(apiUrl + `/AYARA_DEAL_ASSIGN_RULE_PARAMETERS/${id}`, data);
    }

    deleteAssignRuleParam(id: number): Observable<Object> {
        return this.http.delete(apiUrl + `/AYARA_DEAL_ASSIGN_RULE_PARAMETERS/${id}`);
    }
}
