import {Component, OnInit} from '@angular/core';
import {Observable} from 'rxjs';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {noData} from '../../../appsettings';
import {NotificationService} from '../../../shared/notifications.service';
import {RmanLookupsVService} from '../../../rmanLookupsV/rmanLookupsVservice';
import {ConfirmationService} from 'primeng/api';
import {finalize, first, map, tap} from 'rxjs/operators';
import {Table} from 'primeng/table';
import {AmendmentsService} from '../../services/amendments.service';
import {Amendment} from '../../models/amendment';

@Component({
    selector: 'app-amendments-treatment',
    templateUrl: './amendments-treatment.component.html',
    styleUrls: ['./amendments-treatment.component.css']
})
export class AmendmentsTreatmentComponent implements OnInit {
    amendments$: Observable<Amendment[] | []>;
    totalElements: number;
    pageSize = 10;
    noData = noData;
    isLoading = false;
    displayDialog = false;
    isCreate: boolean;

    amendmentsTypesList = [];
    enabledFlagList = [];
    editingAmendmentId = null;

    selectedAmendmentId: number;
    selectedFlag = {lookupCode: '', lookupDescription: ''};
    selectedType = {lookupCode: '', lookupDescription: ''};

    amendmentForm = new FormGroup({
        amendmentTreatmentType: new FormControl('', Validators.required),
        enabledFlag: new FormControl('', Validators.required),
        activeStartDate: new FormControl('', Validators.required),
        activeEndDate: new FormControl('', []),
        rank: new FormControl('', Validators.required)
    });

    get amendmentTreatmentType() {
        return this.amendmentForm.get('amendmentTreatmentType');
    }
    
    get rank() {
        return this.amendmentForm.get('rank');
    }

    get enabledFlag() {
        return this.amendmentForm.get('enabledFlag');
    }

    get activeStartDate() {
        return this.amendmentForm.get('activeStartDate');
    }

    get activeEndDate() {
        return this.amendmentForm.get('activeEndDate');
    }


    constructor(private amendmentsService: AmendmentsService,
                private notificationService: NotificationService,
                private lookupsVService: RmanLookupsVService,
                private confirmationService: ConfirmationService) {
    }

    ngOnInit(): void {
        this.getAmendmentTypes();
        this.getEnabledFlags();
    }

    resetDropdownModels() {
        this.selectedFlag = {lookupCode: '', lookupDescription: ''};
        this.selectedType = {lookupCode: '', lookupDescription: ''};
    }

    getAmendments($event?: any) {
        this.isLoading = true;
        this.amendments$ = this.amendmentsService.getAmendments()
            .pipe(
                tap(data => this.totalElements = data.totalElements),
                map(data => data.content),
                finalize(() => {
                    this.isLoading = false;
                }),
            )
        ;
    }

    getAmendmentTypes() {
        this.lookupsVService.getAllRmanLookupsV(null, {'lookupTypeName': 'AMENDMENT_TREATMENT_TYPE'})
            .then((data: any) => {
                this.amendmentsTypesList = data.content.map(type => ({
                    lookupCode: type.lookupCode,
                    lookupDescription: type.lookupDescription
                }));
            });
    }

    getEnabledFlags() {
        this.lookupsVService.getAllRmanLookupsV(null, {'lookupTypeName': 'ENABLED_FLAG'})
            .then((data: any) => {
                this.enabledFlagList = data.content.map(type => ({
                    lookupCode: type.lookupCode,
                    lookupDescription: type.lookupDescription
                }));
            });

    }

    showDialogToAdd() {
        this.resetDropdownModels();
        this.displayDialog = true;
        this.isCreate = true;
    }

    reset(dt: Table) {
        dt.reset();
    }

    editRow(amendment: Amendment) {
        this.isCreate = false;
        console.log(amendment);
        amendment.activeStartDate = amendment.activeStartDate !=null ? new Date(amendment.activeStartDate) : null;
        amendment.activeEndDate = amendment.activeStartDate!=null ? new Date(amendment.activeEndDate) : null;
        const {rank, amendmentTreatmentType, enabledFlag, activeStartDate, activeEndDate} = amendment;
        this.editingAmendmentId = amendment.configId;
        this.amendmentForm.setValue({rank, amendmentTreatmentType, enabledFlag, activeStartDate, activeEndDate});
        const flagDesc = enabledFlag === 'Y' ? 'Yes' : 'No';
        this.selectedType = this.amendmentsTypesList.find(type => type.lookupCode === amendmentTreatmentType);
        this.selectedFlag = {lookupCode: enabledFlag, lookupDescription: flagDesc};
        this.displayDialog = true;
    }

    delete(amendment: Amendment) {
        this.displayDialog = false;
        this.confirmationService.confirm({
            message: 'Are you sure you want to delete this record?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
                this.amendmentsService.deleteAmendment(amendment.configId)
                    .pipe(first())
                    .subscribe(
                        () => {
                            this.notificationService.showSuccess('Deleted successfully');
                            this.getAmendments();
                        },
                        (error) => this.notificationService.showError(error.message)
                    );
            },
            reject: () => {
                this.notificationService.showWarning('You have rejected');
            }
        });
    }

    cancel() {
        this.amendmentForm.reset();
        this.displayDialog = false;
    }

    onTypesChange(event: any) {
        this.selectedType = {lookupCode: event.value.lookupCode, lookupDescription: event.value.lookupDescription};
        this.amendmentTreatmentType.setValue(this.selectedType.lookupCode);
    }

    onFlagChange(event: any) {
        this.selectedFlag = {lookupCode: event.value.lookupCode, lookupDescription: event.value.lookupDescription};
        this.enabledFlag.setValue(this.selectedFlag.lookupCode);
    }

    save() {
        const data = this.amendmentForm.value;
        console.log(data);
        if (this.isCreate) {
            this.amendmentsService.createAmendment(data)
                .subscribe(
                    () => {
                        this.notificationService.showSuccess('Saved successfully');
                        this.getAmendments();
                    },
                    (error) => this.notificationService.showError(error.message)
                );
        } else {
            this.amendmentsService.updateAmendment(this.editingAmendmentId, data)
                .subscribe(
                    () => {
                        this.notificationService.showSuccess('Updated successfully');
                        this.getAmendments();
                    },
                    (error) => this.notificationService.showError(error.message)
                );
        }
        this.displayDialog = false;
    }

    onRowSelect(event: any) {
        this.selectedAmendmentId = event.data.configId;
    }

    onRowUnSelect() {
        this.selectedAmendmentId = null;
    }
}
