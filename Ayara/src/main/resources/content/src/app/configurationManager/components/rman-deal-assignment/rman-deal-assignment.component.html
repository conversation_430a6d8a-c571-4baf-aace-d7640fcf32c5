<div class="card-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block" *ngIf="{assigneeRules: assignRules$ | async} as data">
                    <p-panel header="Deal Assignment Configuration" [toggleable]="false"
                             [style]="{'margin-bottom':'20px'}">
                        <p-header>
                            <div class="pull-right icons-list">
                                <a *isAuthorized="['write','MDAR']" (click)="showDialogToAdd()" title="Add"><em class="fa fa-plus-circle"></em></a>
                                <a (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
                            </div>
                        </p-header>
                        <div class="x-scroll">
                            <p-table #dt class="ui-datatable arrangementMgrTbl" [value]="data.assigneeRules"
                                     (onLazyLoad)="getAssigneeRules($event)"
                                     [loading]="isLoading"
                                     [lazy]="true" [paginator]="true" [rows]="pageSize" [totalRecords]="totalElements"
                                     selectionMode="single" [(selection)]="selectedRuleId" (onRowSelect)="onRowSelect($event)" (onRowUnselect)="onRowUnSelect()"
                                     scrollable="true">

                                <ng-template pTemplate="header" class="arrangementMgrTblHead">
                                    <tr>
                                        <th></th>
                                        <th>
                                            <a>Ranking</a>
                                        </th>
                                        <th>
                                            <a>UserRole</a>
                                        </th>
                                        <th>
                                            <a>Assignee Type</a>
                                        </th>
                                        <th>
                                            <a>Assignee Group</a>
                                        </th>
                                        <th>
                                            <a>Enabled Flag</a>
                                        </th>

                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-assignRule>
                                    <tr [pSelectableRow]="assignRule">
                                        <td>
                                            <span>
												<a *isAuthorized="['write','MDAR']" (click)="editRow(assignRule)" class="icon-edit"> </a>
											</span>
                                            <span>
												<a *isAuthorized="['write','MDAR']" (click)="delete(assignRule)" class="icon-delete"> </a>
											</span>
                                        </td>
                                        <td>
                                            <a>{{assignRule.ranking}}</a>
                                        </td>
                                        <td>
                                            <a>{{assignRule.userRole}}</a>
                                        </td>
                                        <td>
                                            <a>{{assignRule.assigneeType}}</a>
                                        </td>
                                        <td>
                                            <a>{{assignRule.assigneeGroup}}</a>
                                        </td>
                                        <td>
                                            <a>{{assignRule.enabledFlag}}</a>
                                        </td>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="emptymessage">
                                    <tr>
                                        <td class="no-data">{{noData}}</td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>

                    </p-panel>

                </div>
            </div>
        </div>
    </div>
</div>

<p-dialog header="{{(isCreate) ? 'Create' : 'Edit'}}" [(visible)]="displayDialog"
          [draggable]="true" [modal]="true" [blockScroll]="true" (onHide)="cancel()">
    <form (ngSubmit)="save()" [formGroup]="rulesForm" novalidate>
        <div class="ui-g ui-fluid">
            <div class="ui-g-12 form-group">
                <div class="ui-grid-row form-container">
                    <div class="ui-g-6">
                        <div class="md-inputfield">
                            <span class="selectSpan">Ranking</span>
                            <input pInputText type="number" min="1" max="3" class="textbox" placeholder="Type Ranking"
                                   formControlName="ranking"/>
                            <div *ngIf="ranking.errors && (ranking.dirty || ranking.touched)"
                                 class="ui-message ui-messages-error ui-corner-all">
                                Field is required
                            </div>
                        </div>

                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">User Role</span>
                        <p-dropdown class="ml-auto" [options]="userRolesList"
                                    placeholder="Select Role"
                                    [(ngModel)]="selectedRole"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="responsibilityName"
                                    (onChange)="onRoleChange($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Assignee Type</span>
                        <p-dropdown class="ml-auto" [options]="assigneeTypesList"
                                    placeholder="Select Type"
                                    [(ngModel)]="selectedType"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="lookupDescription"
                                    (onChange)="onTypesChange($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Enabled Flag</span>
                        <p-dropdown class="ml-auto" [options]="enabledFlagList"
                                    placeholder="Select Flag"
                                    [(ngModel)]="selectedFlag"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="lookupDescription"
                                    (onChange)="onFlagChange($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                    <div class="ui-g-6" *ngIf="isGroupTypeChosen">
                        <span class="selectSpan">Assignee Group</span>
                        <p-dropdown class="ml-auto" [options]="assigneeGroupsList"
                                    placeholder="Select Group"
                                    [(ngModel)]="selectedGroup"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="lookupDescription"
                                    (onChange)="onGroupChange($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                </div>

            </div>
        </div>
    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" pButton class="primary-btn" label="Save" (click)="save()"
                    [disabled]="!rulesForm.valid"></button>
            <button type="button" pButton class="secondary-btn" (click)="cancel()" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>

<p-confirmDialog header="Confirmation" icon="fa fa-question-circle"></p-confirmDialog>

<app-rule-parameters [assignRuleId]="selectedRuleId"></app-rule-parameters>
