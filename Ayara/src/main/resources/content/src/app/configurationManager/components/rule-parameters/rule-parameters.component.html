<div class="card-wrapper" *ngIf="assignRuleId">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block" *ngIf="{assignRuleParams: assignRuleParams$ | async} as data">
                    <p-panel header="Deal Assignment Rule Parameters" [toggleable]="false"
                             [style]="{'margin-bottom':'20px'}">
                        <p-header>
                            <div class="pull-right icons-list">
                                <a *isAuthorized="['write','MDAR']" (click)="showDialogToAdd()" title="Add"><em class="fa fa-plus-circle"></em></a>
                                <a (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
                            </div>
                        </p-header>
                        <div class="x-scroll">
                            <p-table #dt class="ui-datatable arrangementMgrTbl" [value]="data.assignRuleParams"
                                     (onLazyLoad)="getAssignRuleParams(assignRuleId, $event)"
                                     [loading]="isLoading"
                                     [lazy]="true" [paginator]="true" [rows]="pageSize" [totalRecords]="totalElements"
                                     scrollable="true">

                                <ng-template pTemplate="header" class="arrangementMgrTblHead">
                                    <tr>
                                        <th></th>
                                        <th>
                                            <a>Parameter Group</a>
                                        </th>
                                        <th>
                                            <a>Parameter Name</a>
                                        </th>
                                        <th>
                                            <a>Qualifier</a>
                                        </th>
                                        <th>
                                            <a>Parameter Value</a>
                                        </th>
                                        <th>
                                            <a>And/Or</a>
                                        </th>

                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-ruleParam>
                                    <tr>
                                        <td>
                                            <span>
												<a *isAuthorized="['write','MDAR']" (click)="editRow(ruleParam)" class="icon-edit"> </a>
											</span>
                                            <span>
												<a *isAuthorized="['write','MDAR']" (click)="delete(ruleParam)" class="icon-delete"> </a>
											</span>
                                        </td>
                                        <td>
                                            <a>{{ruleParam.parameterGroup}}</a>
                                        </td>
                                        <td>
                                            <a>{{ruleParam.parameterName}}</a>
                                        </td>
                                        <td>
                                            <a>{{ruleParam.qualifier}}</a>
                                        </td>
                                        <td>
                                            <a>{{ruleParam.paramterValue}}</a>
                                        </td>
                                        <td>
                                            <a>{{ruleParam.andOr}}</a>
                                        </td>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="emptymessage">
                                    <tr>
                                        <td class="no-data">{{noData}}</td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>

                    </p-panel>

                </div>
            </div>
        </div>
    </div>
</div>

<p-dialog header="{{(isCreate) ? 'Create' : 'Edit'}}" [(visible)]="displayDialog"
          [draggable]="true" [modal]="true" [blockScroll]="true" (onHide)="cancel()">
    <form (ngSubmit)="save()" [formGroup]="paramsForm" novalidate>
        <div class="ui-g ui-fluid">
            <div class="ui-g-12 form-group">
                <div class="ui-grid-row form-container">
                    <div class="ui-g-6">
                        <div class="md-inputfield">
                            <span class="selectSpan">Parameter Group</span>
                            <input pInputText type="number" min="1" max="3" class="textbox" placeholder="Type Parameter Group"
                                   formControlName="parameterGroup"/>
                            <div *ngIf="parameterGroup.errors && (parameterGroup.dirty || parameterGroup.touched)"
                                 class="ui-message ui-messages-error ui-corner-all">
                                Field is required
                            </div>
                        </div>

                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Parameter Name</span>
                        <p-dropdown class="ml-auto" [options]="paramNamesList"
                                    placeholder="Select Parameter Name"
                                    [(ngModel)]="selectedParamName"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="parameterName"
                                    (onChange)="onParamNameChange($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Qualifier</span>
                        <p-dropdown class="ml-auto" [options]="qualifiersList"
                                    placeholder="Select Qualifier"
                                    [(ngModel)]="selectedQualifier"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="lookupDescription"
                                    (onChange)="onQualifiersChange($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Qualifier</span>
                        <p-dropdown class="ml-auto" [options]="andOrList"
                                    placeholder="Select And/Or"
                                    [(ngModel)]="selectedAndOr"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="description"
                                    (onChange)="onChangeAndOr($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Parameter Value</span>
                        <input  pInputText type="text" class="textbox" placeholder="Type Parameter Value" formControlName="paramterValue">
                    </div>
                </div>

            </div>
        </div>
    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" pButton class="primary-btn" label="Save" (click)="save()"
                    [disabled]="!paramsForm.valid"></button>
            <button type="button" pButton class="secondary-btn" (click)="cancel()" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>
