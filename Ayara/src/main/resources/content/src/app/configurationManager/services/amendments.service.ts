import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {Amendment} from '../models/amendment';
import {apiUrl} from '../../appsettings';
import {AssigneeRule} from '../models/assigneeRule';
import {AmendmentConfig} from '../models/amendmentConfig';
import {TableData} from '../../shared/models/tableData';

@Injectable({
    providedIn: 'root'
})
export class AmendmentsService {

    constructor(private http: HttpClient) {
    }

    getAmendments(): Observable<TableData<Amendment[]>> {
        return this.http.get<TableData<Amendment[]>>(apiUrl + '/fetchAyaraAmendmentConfigHeaders?search=%25');
    }

    createAmendment(data: Partial<Amendment>): Observable<object> {
        return this.http.post(apiUrl + '/AYARA_AMENDMENT_CONFIG_HEADERS', data);
    }

    updateAmendment(id: number, data: Partial<Amendment>): Observable<Object> {
        return this.http.put(apiUrl + `/AYARA_AMENDMENT_CONFIG_HEADERS/${id}`, data);
    }

    deleteAmendment(id: number): Observable<Object> {
        return this.http.delete(apiUrl + `/AYARA_AMENDMENT_CONFIG_HEADERS/${id}`);
    }

    getAmendmentConfigs(amendmentId: number): Observable<TableData<AmendmentConfig[]>> {
        return this.http.get<TableData<AmendmentConfig[]>>(apiUrl + `/fetchAyaraAmendmentConfigLines?search=configId:${amendmentId.toString()}`);
    }

    createAmendmentConfig(data: Partial<Amendment>): Observable<object> {
        return this.http.post(apiUrl + '/AYARA_AMENDMENT_CONFIG_LINES', data);
    }

    updateAmendmentConfig(id: number, data: Partial<Amendment>): Observable<Object> {
        return this.http.put(apiUrl + `/AYARA_AMENDMENT_CONFIG_LINES/${id}`, data);
    }

    deleteAmendmentConfig(id: number): Observable<Object> {
        return this.http.delete(apiUrl + `/AYARA_AMENDMENT_CONFIG_LINES/${id}`);
    }

}
