import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RmanContHeaderComponent } from '../rmanContHeader/rmanContHeader.component';
import { RmanContTemplateComponent } from '../rmanContTemplate/rmanContTemplate.component';
import { RmanConversionRatesComponent } from '../rmanConversionRates/rmanConversionRates.component';
import { RmanFcBucketsComponent } from '../rmanFcBuckets/rmanFcBuckets.component';
import { RmanFiscalPeriodsComponent } from '../rmanFiscalPeriods/rmanFiscalPeriods.component';
import { RmanLookupsVService } from '../rmanLookupsV/rmanLookupsVservice';
import { RmanPobMappingComponent } from '../rmanPobMapping/rmanPobMapping.component';
import { RmanRuleParameterValueComponent } from '../rmanRuleParameterValue/rmanRuleParameterValue.component';
import { RmanRulesHeaderComponent } from '../rmanRulesHeader/rmanRulesHeader.component';
import {RmanApprovalRulesComponent } from '../rmanApprovalRules/rmanApprovalRules.component';
import { NewSharedModule } from '../shared/shared.module';
import { ConfigurationManagerComponent } from './configurationManager.component';
import {RmanDealAssignmentComponent} from './components/rman-deal-assignment/rman-deal-assignment.component';
import {AssignRulesService} from './services/assignRules.service';
import {ConfirmationService} from 'primeng/api';
import { RuleParametersComponent } from './components/rule-parameters/rule-parameters.component';
import {RmanEntityParametersService} from '../rmanEntityParameters/rmanEntityParametersservice';
import {RmanEntityParametersVService} from '../rmanEntityParametersV/rmanEntityParametersVservice';
import { RmanImputedCostRulesComponent } from '../rmanImputedCostRules/rmanImputedCostRules.component';
import {RmanImputedCostRuleParameterValueComponent} from '../rmanImputedCostRuleParameterValue/rmanImputedCostRuleParameterValue.component';
import {AyaraOpportunitiesComponent} from '../ayaraOpportunities/ayaraOpportunities.component';
import { AmendmentsTreatmentComponent } from './components/amendments-treatment/amendments-treatment.component';
import { AmendmentConfigComponent } from './components/amendment-config/amendment-config.component';
import { RuleEngineConditionsComponent } from '../rule-engine-conditions/rule-engine-conditions.component';
import { AyaraRulesComponent } from '../ayara-rules/ayara-rules.component';



const routes: Routes =[
  {path:'',component:ConfigurationManagerComponent},
  {path:'rmanRulesHeader',component:RmanRulesHeaderComponent},
  {path:'rmanContHeader',component:RmanContHeaderComponent},
  {path:'rmanContTemplate',component:RmanContTemplateComponent},
  {path:'rmanFcBuckets',component:RmanFcBucketsComponent},
  {path:'rmanFiscalPeriods',component:RmanFiscalPeriodsComponent},
  {path:'rmanPobMappings',component:RmanPobMappingComponent},
  {path: 'rmanConversionRates', component: RmanConversionRatesComponent},
  {path: 'rmanApprovalRules', component: RmanApprovalRulesComponent},
  {path: 'rmanDealAssignment', component: RmanDealAssignmentComponent},
  {path: 'rmanImputedCostRules', component: RmanImputedCostRulesComponent},
  {path: 'ayaraOpportunities', component: AyaraOpportunitiesComponent},
  {path: 'amendmentRules', component: AmendmentsTreatmentComponent},
  {path: 'ayaraRules', component: AyaraRulesComponent}
]

@NgModule({
  imports:[NewSharedModule,RouterModule.forChild(routes)],
  declarations:[RmanConversionRatesComponent, ConfigurationManagerComponent,RmanRulesHeaderComponent,RmanRuleParameterValueComponent,RmanContHeaderComponent,RmanContTemplateComponent,RmanFiscalPeriodsComponent,RmanPobMappingComponent,RmanFcBucketsComponent, RmanApprovalRulesComponent, RmanImputedCostRulesComponent,
    RmanImputedCostRuleParameterValueComponent,RmanDealAssignmentComponent,RuleParametersComponent, AyaraOpportunitiesComponent, AmendmentsTreatmentComponent, AmendmentConfigComponent, AyaraRulesComponent, RuleEngineConditionsComponent],
    providers: [RmanLookupsVService, AssignRulesService, ConfirmationService, RmanEntityParametersService, RmanEntityParametersVService]
})

export class configurationManagerModule {
}
