<div class="card-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block" *ngIf="{assigneeRules: amendments$ | async} as data">
                    <p-panel [toggleable]="false" [style]="{'margin-bottom':'20px'}">
                        <div>
                            <h2>Amendment Treatment</h2>
                            <div class="pull-right icons-list">
                                <a *isAuthorized="['write','MAMENDS']" (click)="showDialogToAdd()" title="Add"><em class="fa fa-plus-circle"></em></a>
                                <a (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
                            </div>
                        </div>
                        <div class="x-scroll">
                            <p-table #dt class="ui-datatable arrangementMgrTbl" [value]="data.assigneeRules"
                                     (onLazyLoad)="getAmendments($event)"
                                     [loading]="isLoading"
                                     [lazy]="true" [paginator]="true" [rows]="pageSize" [totalRecords]="totalElements"
                                     selectionMode="single" [(selection)]="selectedAmendmentId"
                                     (onRowSelect)="onRowSelect($event)" (onRowUnselect)="onRowUnSelect()"
                                     scrollable="true">

                                <ng-template pTemplate="header" class="arrangementMgrTblHead">
                                    <tr>
                                        <th></th>
                                        <th>
                                            Sr. No.
                                        </th>
                                        <th>
                                            Rank
                                        </th>
                                        <th>
                                            Amendment Treatment
                                        </th>
                                        <th>
                                            Rule Start Date
                                        </th>
                                        <th>
                                            Rule End Date
                                        </th>
                                        <th>
                                            Enabled
                                        </th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-amendment let-rowIndex="rowIndex">
                                    <tr [pSelectableRow]="amendment">
                                        <td>
                                            <span>
												<a *isAuthorized="['write','MAMENDS']" (click)="editRow(amendment)" class="icon-edit"> </a>
											</span>
                                            <span>
												<a *isAuthorized="['write','MAMENDS']" (click)="delete(amendment)" class="icon-delete"> </a>
											</span>
                                        </td>
                                        <td>
                                            <a>{{rowIndex + 1}}</a>
                                        </td>
                                        <td>
                                            <a>{{amendment.rank}}</a>
                                        </td>
                                        <td>
                                            <a>{{amendment.amendmentTreatmentType}}</a>
                                        </td>
                                        <td>
                                            <a>{{amendment.activeStartDate |  date: 'MM/dd/yyyy'}}</a>
                                        </td>
                                        <td>
                                            <a>{{amendment.activeEndDate |  date: 'MM/dd/yyyy'}}</a>
                                        </td>
                                        <td>
                                            <a>{{amendment.enabledFlag}}</a>
                                        </td>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="emptymessage">
                                    <tr>
                                        <td class="no-data">{{noData}}</td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>

                    </p-panel>

                </div>
            </div>
        </div>
    </div>
</div>

<p-dialog header="{{(isCreate) ? 'Create' : 'Edit'}}" [(visible)]="displayDialog"
          [draggable]="true" [modal]="true" [blockScroll]="true" (onHide)="cancel()">
    <form (ngSubmit)="save()" [formGroup]="amendmentForm">
        <div class="ui-g ui-fluid">
            <div class="ui-g-12 form-group">
                <div class="ui-grid-row form-container">
                    <div class="ui-g-6">
                        <span class="selectSpan">Amendment Treatment Type<span class="red-color">*</span></span>
                        <p-dropdown class="ml-auto" [options]="amendmentsTypesList"
                                    placeholder="Select Type*"
                                    [(ngModel)]="selectedType"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="lookupDescription"
                                    (onChange)="onTypesChange($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                    <div class="ui-g-6">
                        <div class="md-inputfield">
                            <span class="selectSpan">Rank</span>
                            <input pInputText type="number" class="textbox" placeholder="Rank"
                                   formControlName="rank"/>
                            
                        </div>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Active Start Date  <span class="red-color">*</span></span>
                        <p-calendar showAnim="slideDown" inputStyleClass="textbox" id="startDate"
                                    [monthNavigator]="true"
                                    [yearNavigator]="true" yearRange="1950:2030" appendTo="body"
                                    formControlName="activeStartDate" dateFormat="yy-mm-dd"
                                    placeholder="Active Start Date*">
                        </p-calendar>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Active End Date</span>
                        <p-calendar showAnim="slideDown" inputStyleClass="textbox" id="endDate" [monthNavigator]="true"
                                    [yearNavigator]="true" yearRange="1950:2030" appendTo="body"
                                    formControlName="activeEndDate" dateFormat="yy-mm-dd"
                                    placeholder="Active End Date">
                        </p-calendar>
                    </div>
                    <div class="ui-g-6">
                        <span class="selectSpan">Enabled Flag <span class="red-color">*</span></span>
                        <p-dropdown class="ml-auto" [options]="enabledFlagList"
                                    placeholder="Select Flag*"
                                    [(ngModel)]="selectedFlag"
                                    [ngModelOptions]="{standalone: true}"
                                    optionLabel="lookupDescription"
                                    (onChange)="onFlagChange($event)"
                                    appendTo="body">
                        </p-dropdown>
                    </div>
                </div>

            </div>
        </div>
    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" pButton class="primary-btn" label="Save" (click)="save()"
                    [disabled]="!amendmentForm.valid"></button>
            <button type="button" pButton class="secondary-btn" (click)="cancel()" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>

<p-confirmDialog header="Confirmation" icon="fa fa-question-circle"></p-confirmDialog>

<app-amendment-config [configId]="selectedAmendmentId"></app-amendment-config>
