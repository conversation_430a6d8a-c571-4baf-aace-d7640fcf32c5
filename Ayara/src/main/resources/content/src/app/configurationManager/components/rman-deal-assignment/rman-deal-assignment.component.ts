import {Component, OnInit} from '@angular/core';
import {AssigneeRule} from '../../models/assigneeRule';
import {Table} from 'primeng/table';
import {AssignRulesService} from '../../services/assignRules.service';
import {noData} from '../../../appsettings';
import {Observable} from 'rxjs';
import {finalize, first, map, tap} from 'rxjs/operators';
import {NotificationService} from '../../../shared/notifications.service';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {RmanLookupsVService} from '../../../rmanLookupsV/rmanLookupsVservice';
import {ConfirmationService} from 'primeng/api';

@Component({
    selector: 'app-rman-deal-assignment',
    templateUrl: './rman-deal-assignment.component.html',
    styleUrls: ['./rman-deal-assignment.component.css']
})
export class RmanDealAssignmentComponent implements OnInit {

    assignRules$: Observable<AssigneeRule[] | []>;
    totalElements: number;
    pageSize = 10;
    noData = noData;
    isLoading = false;
    displayDialog = false;
    isCreate: boolean;

    assigneeTypesList = [];
    enabledFlagList = [];
    userRolesList = [];
    assigneeGroupsList = [];
    isGroupTypeChosen = false;
    editingRuleId = null;

    selectedRuleId: number;
    selectedRole = {responsibilityId: null, responsibilityName: ''};
    selectedFlag = {lookupCode: '', lookupDescription: ''};
    selectedType = {lookupCode: '', lookupDescription: ''};
    selectedGroup = {lookupCode: '', lookupDescription: ''};

    rulesForm = new FormGroup({
        ranking: new FormControl(null, Validators.required),
        userRole: new FormControl('', Validators.required),
        responsibilityId: new FormControl('', Validators.required),
        assigneeType: new FormControl('', Validators.required),
        assigneeGroup: new FormControl(''),
        enabledFlag: new FormControl('', Validators.required)
    });

    get ranking() {
        return this.rulesForm.get('ranking');
    }

    get userRole() {
        return this.rulesForm.get('userRole');
    }

    get responsibilityId() {
        return this.rulesForm.get('responsibilityId');
    }

    get assigneeType() {
        return this.rulesForm.get('assigneeType');
    }

    get assigneeGroup() {
        return this.rulesForm.get('assigneeGroup');
    }

    get enabledFlag() {
        return this.rulesForm.get('enabledFlag');
    }


    constructor(private assignRulesService: AssignRulesService,
                private notificationService: NotificationService,
                private lookupsVService: RmanLookupsVService,
                private confirmationService: ConfirmationService) {
    }

    ngOnInit(): void {
        this.getAssigneeTypes();
        this.getUserRoles();
        this.getEnabledFlags();
        this.getAssigneeGroups();
    }

    resetDropdownModels() {
        this.selectedRole = {responsibilityId: null, responsibilityName: ''};
        this.selectedFlag = {lookupCode: '', lookupDescription: ''};
        this.selectedType = {lookupCode: '', lookupDescription: ''};
        this.selectedGroup = {lookupCode: '', lookupDescription: ''};
    }

    getAssigneeRules($event?: any) {
        this.isLoading = true;
        this.assignRules$ = this.assignRulesService.getAssigneeRules()
            .pipe(
                tap(data => this.totalElements = data.totalElements),
                map(data => data.content),
                finalize(() => {
                    this.isLoading = false;
                }),
            )
        ;
    }

    getUserRoles() {
        this.assignRulesService.getUserRoles().subscribe((data: any) => {
            this.userRolesList = data.content.map(role => ({
                responsibilityName: role.responsibilityName,
                responsibilityId: role.responsibilityId
            }));
        });
    }

    getAssigneeTypes() {
        this.lookupsVService.getAllRmanLookupsV(null, {'lookupTypeName': 'ASSIGNEE_TYPE'})
            .then((data: any) => {
                this.assigneeTypesList = data.content.map(type => ({
                    lookupCode: type.lookupCode,
                    lookupDescription: type.lookupDescription
                }));
            });
    }

    getAssigneeGroups() {
        this.lookupsVService.getAllRmanLookupsV(null, {'lookupTypeName': 'DEPARTMENT_NAME'})
            .then((data: any) => {
                this.assigneeGroupsList = data.content.map(type => ({
                    lookupCode: type.lookupCode,
                    lookupDescription: type.lookupDescription
                }));
            });
    }

    getEnabledFlags() {
        this.lookupsVService.getAllRmanLookupsV(null, {'lookupTypeName': 'ENABLED_FLAG'})
            .then((data: any) => {
                this.enabledFlagList = data.content.map(type => ({
                    lookupCode: type.lookupCode,
                    lookupDescription: type.lookupDescription
                }));
            });

    }

    showDialogToAdd() {
        this.resetDropdownModels();
        this.displayDialog = true;
        this.isCreate = true;
    }

    reset(dt: Table) {
        dt.reset();
    }

    editRow(assignRule: AssigneeRule) {
        this.isCreate = false;
        const {ranking, responsibilityId, assigneeType, assigneeGroup, enabledFlag, userRole} = assignRule;
        this.editingRuleId = assignRule.assignRuleId;
        this.rulesForm.setValue({ranking, userRole, responsibilityId, assigneeType, assigneeGroup, enabledFlag});
        const flagDesc = enabledFlag === 'Y' ? 'Yes' : 'No';
        this.selectedType = {lookupCode: assigneeType, lookupDescription: assigneeType};
        this.selectedRole = {responsibilityId: responsibilityId, responsibilityName: userRole};
        this.selectedFlag = {lookupCode: enabledFlag, lookupDescription: flagDesc};
        this.displayDialog = true;
    }

    delete(assignRule: AssigneeRule) {
        this.displayDialog = false;
        this.confirmationService.confirm({
            message: 'Are you sure you want to delete this record?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
                this.assignRulesService.deleteAssigneeRule(assignRule.assignRuleId)
                    .pipe(first())
                    .subscribe(
                        () => {
                            this.notificationService.showSuccess('Deleted successfully');
                            this.getAssigneeRules();
                        },
                        (error) => this.notificationService.showError(error.message)
                    );
            },
            reject: () => {
                this.notificationService.showWarning('You have rejected');
            }
        });
    }

    cancel() {
        this.rulesForm.reset();
        this.displayDialog = false;
    }

    onTypesChange(event: any) {
        this.selectedType = {lookupCode: event.value.lookupCode, lookupDescription: event.value.lookupDescription};
        this.assigneeType.setValue(this.selectedType.lookupCode);
        this.isGroupTypeChosen = this.selectedType.lookupCode === 'GROUP';
        if (!this.isGroupTypeChosen) {
            this.assigneeGroup.setValue('');
        }
    }

    onRoleChange(event: any) {
        this.selectedRole = {responsibilityId: event.value.responsibilityId, responsibilityName: event.value.responsibilityName};
        this.responsibilityId.setValue(this.selectedRole.responsibilityId);
        this.userRole.setValue(this.selectedRole.responsibilityName);
    }

    onFlagChange(event: any) {
        this.selectedFlag = {lookupCode: event.value.lookupCode, lookupDescription: event.value.lookupDescription};
        this.enabledFlag.setValue(this.selectedFlag.lookupCode);
    }


    onGroupChange(event: any) {
        this.selectedGroup = {lookupCode: event.value.lookupCode, lookupDescription: event.value.lookupDescription};
        this.assigneeGroup.setValue(this.selectedGroup.lookupCode);
    }

    save() {
        const data = this.rulesForm.value;
        if (this.isCreate) {
            this.assignRulesService.createAssigneeRule(data)
                .subscribe(
                    () => {
                        this.notificationService.showSuccess('Saved successfully');
                        this.getAssigneeRules();
                    },
                    (error) => this.notificationService.showError(error.message)
                );
        } else {
            this.assignRulesService.updateAssigneeRule(this.editingRuleId, data)
                .subscribe(
                    () => {
                        this.notificationService.showSuccess('Updated successfully');
                        this.getAssigneeRules();
                    },
                    (error) => this.notificationService.showError(error.message)
                );
        }
        this.displayDialog = false;
    }

    onRowSelect(event: any) {
        this.selectedRuleId = event.data.assignRuleId;
    }

    onRowUnSelect() {
        this.selectedRuleId = null;
    }
}
