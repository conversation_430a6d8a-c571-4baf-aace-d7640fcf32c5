import {Component, Input, OnChanges, OnInit} from '@angular/core';
import {Observable} from 'rxjs';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {noData} from '../../../appsettings';
import {NotificationService} from '../../../shared/notifications.service';
import {RmanLookupsVService} from '../../../rmanLookupsV/rmanLookupsVservice';
import {ConfirmationService} from 'primeng/api';
import {finalize, first, map, tap} from 'rxjs/operators';
import {Table} from 'primeng/table';
import {AmendmentConfig} from '../../models/amendmentConfig';
import {AmendmentsService} from '../../services/amendments.service';

@Component({
  selector: 'app-amendment-config',
  templateUrl: './amendment-config.component.html',
  styleUrls: ['./amendment-config.component.css']
})
export class AmendmentConfigComponent implements OnInit, OnChanges{
  @Input() configId: number;

  amendmentConfigs$: Observable<AmendmentConfig[] | []>;
  totalElements: number;
  pageSize = 10;
  noData = noData;
  isLoading = false;
  displayDialog = false;
  isCreate: boolean;

  qualifiersList = [];
  paramNamesList = [];
  enabledFlagList = [];
  andOrList = [
    {name: 'AND', description: 'AND'},
    {name: 'OR', description: 'OR'}
  ];

  selectedQualifier = {lookupCode: '', lookupDescription: ''};
  selectedParamName = {lookupCode: '', lookupDescription: ''};
  selectedAndOr = {name: '', description: ''};
  selectedFlag = {lookupCode: '', lookupDescription: ''};

  editingConfigId = null;

  configsForm = new FormGroup({
    parameterName: new FormControl('', Validators.required),
    qualifier: new FormControl('', Validators.required),
    parameterValue: new FormControl('', Validators.required),
    andOr: new FormControl('', Validators.required),
    enabledFlag: new FormControl('', Validators.required)
  });

  get parameterName() {
    return this.configsForm.get('parameterName');
  }

  get qualifier() {
    return this.configsForm.get('qualifier');
  }

  get parameterValue() {
    return this.configsForm.get('parameterValue');
  }

  get andOr() {
    return this.configsForm.get('andOr');
  }

  get enabledFlag() {
    return this.configsForm.get('enabledFlag');
  }

  constructor(
      private notificationService: NotificationService,
      private amendmentsService: AmendmentsService,
      private lookupsVService: RmanLookupsVService,
      private confirmationService: ConfirmationService
  ) {
  }

  ngOnInit(): void {
    this.getQualifiers();
    this.getParameterNames();
    this.getEnabledFlags();
  }

  ngOnChanges(changes: any) {
    const id = changes.configId.currentValue;
    if (this.configId && changes.configId.currentValue) {
      this.getAmendmentsConfigs(id);
    }
  }

  getQualifiers() {
    this.lookupsVService.getAllRmanLookupsV(null, {'lookupTypeName': 'RULE_QUALIFIER'})
        .then((data: any) => this.qualifiersList = data.content.map(item => ({
          lookupCode: item.lookupCode,
          lookupDescription: item.lookupDescription
        })));
  }

  getParameterNames() {
    this.lookupsVService.getAllRmanLookupsV(null, {'lookupTypeName': 'AMENDMENT_PARAMETERS'})
        .then((data: any) => {
          this.paramNamesList = data.content.map(item => ({
            lookupCode: item.lookupCode,
            lookupDescription: item.lookupDescription
          }));
        });
  }

  getEnabledFlags() {
    this.lookupsVService.getAllRmanLookupsV(null, {'lookupTypeName': 'ENABLED_FLAG'})
        .then((data: any) => {
          this.enabledFlagList = data.content.map(type => ({
            lookupCode: type.lookupCode,
            lookupDescription: type.lookupDescription
          }));
        });

  }

  getAmendmentsConfigs(amendmentId: number, $event?: any) {
    this.isLoading = true;
    this.amendmentConfigs$ = this.amendmentsService.getAmendmentConfigs(amendmentId)
        .pipe(
            tap(data => this.totalElements = data.totalElements),
            map(data => data.content),
            finalize(() => {
              this.isLoading = false;
            }),
        );
  }

  showDialogToAdd() {
    this.resetDropdownModels();
    this.displayDialog = true;
    this.isCreate = true;
  }

  reset(dt: Table) {
    dt.reset();
  }

  resetDropdownModels() {
    this.selectedParamName = {lookupCode: '', lookupDescription: ''};
    this.selectedFlag = {lookupCode: '', lookupDescription: ''};
    this.selectedQualifier = {lookupCode: '', lookupDescription: ''};
    this.selectedAndOr = {name: '', description: ''};
    this.parameterName.reset();
  }

  onQualifiersChange(event: any) {
    this.selectedQualifier = {lookupCode: event.value.lookupCode, lookupDescription: event.value.lookupDescription};
    this.qualifier.setValue(this.selectedQualifier.lookupCode);
  }

  onParamNameChange(event: any) {
    this.selectedParamName = {lookupCode: event.value.lookupCode, lookupDescription: event.value.lookupDescription};
    this.parameterName.setValue(this.selectedParamName.lookupCode);
  }

  onChangeAndOr(event: any) {
    this.selectedAndOr = {name: event.value.name, description: event.value.description};
    this.andOr.setValue(event.value.name);
  }

  onFlagChange(event: any) {
    this.selectedFlag = {lookupCode: event.value.lookupCode, lookupDescription: event.value.lookupDescription};
    this.enabledFlag.setValue(this.selectedFlag.lookupCode);
  }

  editRow(config: AmendmentConfig) {
    this.isCreate = false;
    this.editingConfigId = config.configParamId;

    const {parameterName, parameterValue, qualifier, andOr, enabledFlag} = config;
    this.configsForm.setValue({parameterName, parameterValue, qualifier, andOr, enabledFlag});

    const paramOption = this.paramNamesList.find(option => option.lookupCode === parameterName);
    const qualifierOption = this.qualifiersList.find(option => option.lookupCode === qualifier);
    const flagDesc = enabledFlag === 'Y' ? 'Yes' : 'No';

    this.selectedFlag = {lookupCode: enabledFlag, lookupDescription: flagDesc};
    this.selectedAndOr = {name: andOr, description: andOr};
    this.selectedParamName = paramOption;
    this.selectedQualifier = qualifierOption;

    this.displayDialog = true;
  }

  delete(config: AmendmentConfig) {
    this.displayDialog = false;
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete this record?',
      header: 'Confirmation',
      icon: '',
      accept: () => {
        this.amendmentsService.deleteAmendmentConfig(config.configParamId)
            .pipe(first())
            .subscribe(
                () => {
                  this.notificationService.showSuccess('Deleted successfully');
                  this.getAmendmentsConfigs(this.configId);
                },
                (error) => this.notificationService.showError(error.message)
            );
      },
      reject: () => {
        this.notificationService.showWarning('You have rejected');
      }
    });
  }

  cancel() {
    this.configsForm.reset();
    this.displayDialog = false;
  }

  save() {
    const data = this.configsForm.value;
    if (this.isCreate) {
      this.amendmentsService.createAmendmentConfig({...data, configId: this.configId})
          .subscribe(
              () => {
                this.notificationService.showSuccess('Saved successfully');
                this.getAmendmentsConfigs(this.configId);
              },
              (error) => this.notificationService.showError(error.message)
          );
    } else {
      const putData = {ruleParameterId: this.editingConfigId, configId: this.configId, ...data};
      this.amendmentsService.updateAmendmentConfig(this.editingConfigId, putData)
          .subscribe(
              () => {
                this.notificationService.showSuccess('Updated successfully');
                this.getAmendmentsConfigs(this.configId);
              },
              (error) => this.notificationService.showError(error.message)
          );
    }
    this.displayDialog = false;
  }
}
