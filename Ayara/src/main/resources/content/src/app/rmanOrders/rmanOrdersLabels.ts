interface ILabels {
    [index: string]: string;
}

export class RmanOrdersLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["origRmanLineId"] = "ORIG RMAN LINE ID";
        this.fieldLabels["sourceLineId"] = "SOURCE LINE ID";
        this.fieldLabels["carveInOrOut"] = "CARVE IN OR OUT";
        this.fieldLabels["repCurrExtendedAmt"] = "REP CURR EXTENDED AMT";
        this.fieldLabels["elementType"] = "ELEMENT TYPE";
        this.fieldLabels["salesrep"] = "SALESREP";
        this.fieldLabels["shippingEntityId"] = "SHIPPING ENTITY ID";
        this.fieldLabels["productOrgId"] = "PRODUCT ORG ID";
        this.fieldLabels["billToCountry"] = "BILL TO COUNTRY";
        this.fieldLabels["unitSellingPrice"] = "UNIT SELLING PRICE";
        this.fieldLabels["billToLocation"] = "BILL TO LOCATION";
        this.fieldLabels["glStatus"] = "GL STATUS";
        this.fieldLabels["scheduleShipDate"] = "SCHEDULE SHIP DATE";
        this.fieldLabels["customerPoNum"] = "CUSTOMER PO NUM";
        this.fieldLabels["arrangementId"] = "ARRANGEMENT ID";
        this.fieldLabels["rmanAcctRuleId"] = "RMAN ACCT RULE ID";
        this.fieldLabels["fmvRuleDefId"] = "FMV RULE DEF ID";
        this.fieldLabels["orderedQuantity"] = "ORDERED QUANTITY";
        this.fieldLabels["glUpdateDate"] = "GL UPDATE DATE";
        this.fieldLabels["shippingOrgCode"] = "SHIPPING ORG CODE";
        this.fieldLabels["taskId"] = "TASK ID";
        this.fieldLabels["lineAttribute6"] = "LINE ATTRIBUTE6";
        this.fieldLabels["lineAttribute5"] = "LINE ATTRIBUTE5";
        this.fieldLabels["headerAttribute10"] = "HEADER ATTRIBUTE10";
        this.fieldLabels["lineAttribute8"] = "LINE ATTRIBUTE8";
        this.fieldLabels["headerAttribute11"] = "HEADER ATTRIBUTE11";
        this.fieldLabels["lineAttribute7"] = "LINE ATTRIBUTE7";
        this.fieldLabels["headerAttribute12"] = "HEADER ATTRIBUTE12";
        this.fieldLabels["soldToCustomer"] = "SOLD TO CUSTOMER";
        this.fieldLabels["lineAttribute2"] = "LINE ATTRIBUTE2";
        this.fieldLabels["headerAttribute13"] = "HEADER ATTRIBUTE13";
        this.fieldLabels["customerPoLineNum"] = "CUSTOMER PO LINE NUM";
        this.fieldLabels["lineAttribute1"] = "LINE ATTRIBUTE1";
        this.fieldLabels["headerAttribute14"] = "HEADER ATTRIBUTE14";
        this.fieldLabels["lineAttribute4"] = "LINE ATTRIBUTE4";
        this.fieldLabels["headerAttribute15"] = "HEADER ATTRIBUTE15";
        this.fieldLabels["repUnitSellingPrice"] = "REP UNIT SELLING PRICE";
        this.fieldLabels["lineAttribute3"] = "LINE ATTRIBUTE3";
        this.fieldLabels["allocationAmount"] = "ALLOCATION AMOUNT";
        this.fieldLabels["lineAttribute9"] = "LINE ATTRIBUTE9";
        this.fieldLabels["contingencyCode"] = "CONTINGENCY CODE";
        this.fieldLabels["fxRate"] = "FX RATE";
        this.fieldLabels["lineAttribute12"] = "LINE ATTRIBUTE12";
        this.fieldLabels["shippedQuantity"] = "SHIPPED QUANTITY";
        this.fieldLabels["lineAttribute13"] = "LINE ATTRIBUTE13";
        this.fieldLabels["lineAttribute10"] = "LINE ATTRIBUTE10";
        this.fieldLabels["lineAttribute11"] = "LINE ATTRIBUTE11";
        this.fieldLabels["shipToLocation"] = "SHIP TO LOCATION";
        this.fieldLabels["dealNumber"] = "Deal Number";
        this.fieldLabels["expenditureType"] = "EXPENDITURE TYPE";
        this.fieldLabels["billToCustomer"] = "BILL TO CUSTOMER";
        this.fieldLabels["lineAttribute14"] = "LINE ATTRIBUTE14";
        this.fieldLabels["projectId"] = "PROJECT ID";
        this.fieldLabels["lineAttribute15"] = "LINE ATTRIBUTE15";
        this.fieldLabels["shipToCustomer"] = "SHIP TO CUSTOMER";
        this.fieldLabels["dealLineNumber"] = "Deal Line Number";
        this.fieldLabels["bookingCurrency"] = "BOOKING CURRENCY";
        this.fieldLabels["lineType"] = "LINE TYPE";
        this.fieldLabels["projectNumber"] = "PROJECT NUMBER";
        this.fieldLabels["orderType"] = "ORDER TYPE";
        this.fieldLabels["revrecAcctRule"] = "REVREC ACCT RULE";
        this.fieldLabels["parentLineId"] = "PARENT LINE ID";
        this.fieldLabels["roleName"] = "ROLE NAME";
        this.fieldLabels["bookedDate"] = "BOOKED DATE";
        this.fieldLabels["accountingRuleName"] = "ACCOUNTING RULE NAME";
        this.fieldLabels["repCurrUlp"] = "REP CURR ULP";
        this.fieldLabels["serviceEndDate"] = "SERVICE END DATE";
        this.fieldLabels["poHeaderId"] = "PO HEADER ID";
        this.fieldLabels["taskNumber"] = "TASK NUMBER";
        this.fieldLabels["shipToCountry"] = "SHIP TO COUNTRY";
        this.fieldLabels["revenueCategory"] = "REVENUE CATEGORY";
        this.fieldLabels["eitfSop"] = "EITF SOP";
        this.fieldLabels["revrecStDate"] = "REVREC ST DATE";
        this.fieldLabels["bundleFlag"] = "BUNDLE FLAG";
        this.fieldLabels["fxDate"] = "FX DATE";
        this.fieldLabels["revrecAcctScope"] = "REVREC ACCT SCOPE";
        this.fieldLabels["priceList"] = "PRICE LIST";
        this.fieldLabels["repCurrUsp"] = "REP CURR USP";
        this.fieldLabels["orginalOrderNumber"] = "ORGINAL ORDER NUMBER";
        this.fieldLabels["sourceHeaderId"] = "SOURCE HEADER ID";
        this.fieldLabels["lastUpdateDate"] = "LAST UPDATE DATE";
        this.fieldLabels["orginalLineId"] = "ORGINAL LINE ID";
        this.fieldLabels["fob"] = "FOB";
        this.fieldLabels["unitListPrice"] = "UNIT LIST PRICE";
        this.fieldLabels["allocationFlag"] = "ALLOCATION FLAG";
        this.fieldLabels["createdBy"] = "CREATED BY";
        this.fieldLabels["lastUpdatedBy"] = "LAST UPDATED BY";
        this.fieldLabels["revrecDelay"] = "REVREC DELAY";
        this.fieldLabels["sourceLineNumber"] = "SOURCE LINE NUMBER";
        this.fieldLabels["taskName"] = "TASK NAME";
        this.fieldLabels["creationDate"] = "CREATION DATE";
        this.fieldLabels["shippableFlag"] = "SHIPPABLE FLAG";
        this.fieldLabels["productName"] = "Product in SO";
        this.fieldLabels["lineStatus"] = "LINE STATUS";
        this.fieldLabels["orginalLineNumber"] = "ORGINAL LINE NUMBER";
        this.fieldLabels["repUnitListPrice"] = "REP UNIT LIST PRICE";
        this.fieldLabels["rmanLineId"] = "RMAN LINE ID";
        this.fieldLabels["deliveredFlag"] = "DELIVERED FLAG";
        this.fieldLabels["attributedListPrice"] = "ATTRIBUTED LIST PRICE";
        this.fieldLabels["dealLineId"] = "DEAL LINE ID";
        this.fieldLabels["headerAttribute1"] = "HEADER ATTRIBUTE1";
        this.fieldLabels["headerAttribute6"] = "HEADER ATTRIBUTE6";
        this.fieldLabels["headerAttribute7"] = "HEADER ATTRIBUTE7";
        this.fieldLabels["headerAttribute8"] = "HEADER ATTRIBUTE8";
        this.fieldLabels["headerAttribute9"] = "HEADER ATTRIBUTE9";
        this.fieldLabels["headerAttribute2"] = "HEADER ATTRIBUTE2";
        this.fieldLabels["fmvHistoryFlag"] = "FMV HISTORY FLAG";
        this.fieldLabels["headerAttribute3"] = "HEADER ATTRIBUTE3";
        this.fieldLabels["servicePeriod"] = "SERVICE PERIOD";
        this.fieldLabels["headerAttribute4"] = "HEADER ATTRIBUTE4";
        this.fieldLabels["projectName"] = "PROJECT NAME";
        this.fieldLabels["headerAttribute5"] = "HEADER ATTRIBUTE5";
        this.fieldLabels["revrecEndDate"] = "REVREC END DATE";
        this.fieldLabels["subElementType"] = "SUB ELEMENT TYPE";
        this.fieldLabels["fulfilledQuantity"] = "FULFILLED QUANTITY";
        this.fieldLabels["orderStatus"] = "ORDER STATUS";
        this.fieldLabels["serviceStartDate"] = "SERVICE START DATE";
        this.fieldLabels["endCustomer"] = "END CUSTOMER";
        this.fieldLabels["repCurrCode"] = "REP CURR CODE";
        this.fieldLabels["actualFulfilledDate"] = "ACTUAL FULFILLED DATE";
        this.fieldLabels["serviceDuration"] = "SERVICE DURATION";
        this.fieldLabels["conversionRate"] = "CONVERSION RATE";
        this.fieldLabels["productId"] = "PRODUCT ID";
        this.fieldLabels["invoiceFlag"] = "INVOICE FLAG";
        this.fieldLabels["dealArrangementId"] = "DEAL ARRANGEMENT ID";
        this.fieldLabels["bookingEntityId"] = "BOOKING ENTITY ID";
        this.fieldLabels["attributedSellPrice"] = "ATTRIBUTED SELL PRICE";
        this.fieldLabels["revrecAccount"] = "REVREC ACCOUNT";
        this.fieldLabels["orderNumber"] = "SO Number";
        this.fieldLabels["orderedDate"] = "ORDERED DATE";
        this.fieldLabels["parentLine"] = "PARENT LINE";
        this.fieldLabels["lineCost"] = "LINE COST";
        this.fieldLabels["poLineId"] = "PO LINE ID";
        this.fieldLabels["exceptionFlag"] = "Exception Flag";
        this.fieldLabels["exceptionMessage"] = "Exception Message";
    }

}
