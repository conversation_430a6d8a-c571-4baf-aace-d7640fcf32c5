export interface BsbReport {
    productFamily2 :any;
    serviceCode :any;
    geography10k :any;
    reasonForExclusion :any;
    billToCountry :any;
    fob :any;
    username :any;
    concatenate :any;
    extendedWarranty :any;
    billToState :any;
    region :any;
    shippedAmount :any;
    productLine :any;
    customerPo :any;
    snExists :any;
    source :any;
    financialMonth :any;
    je :any;
    serviceMaintNewParts :any;
    shipToCustomerName :any;
    extendedCost :any;
    warrantyTransferred :any;
    freightTerms :any;
    freightCarrier :any;
    division :any;
    invoiceLine :any;
    productService :any;
    note :any;
    shipDate :any;
    shippedQuantity :any;
    orderLine :any;
    productNumber :any;
    serviceStartDate :any;
    billToCustomerName :any;
    endCustomer :any;
    reasonForExclusion2 :any;
    itemType :any;
    reasonForExclusion3 :any;
    serviceMaintLegacy :any;
    orderType :any;
    invoiceNumber :any;
    isRevenue :any;
    totalMonths :any;
    serviceEndDate :any;
    productFamily :any;
    shipToCountry :any;
    orderNumber :any;
    subOrderType :any;
    salesrepName :any;
}
