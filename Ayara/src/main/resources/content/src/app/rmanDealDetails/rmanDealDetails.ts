export interface RmanDealDetails {
    shipToSiteNum: any;
    financeContactId: any;
    elementType: any;
    productOrgId: any;
    directCvAmount: any;
    parentLineNumber: any;
    billToCountry: any;
    lineId: any;
    changeReason: any;
    unitSellingPrice: any;
    opportunityName: any;
    endCustomerNum: any;
    durationUom: any;
    dealName: any;
    attribute3: any;
    attribute2: any;
    attribute1: any;
    salesOpsId: any;
    fmvAmount: any;
    dealHeaderId: any;
    attribute9: any;
    attribute8: any;
    childSellPrice: any;
    attribute7: any;
    attribute6: any;
    attribute5: any;
    attribute4: any;
    directCv: any;
    uomCode: any;
    salesOpsName: any;
    agreementCode: any;
    fmvRuleDefId: any;
    attribute10: any;
    cvPercent: any;
    endCustomerName: any;
    serviceDurationUom: any;
    fvPercent: any;
    attribute14: any;
    attribute13: any;
    repUnitSellingPrice: any;
    attribute12: any;
    customerType: any;
    partnerName: any;
    attribute11: any;
    contAppQty: any;
    dealType: any;
    allocationAmount: any;
    salesChannel: any;
    contingencyCode: any;
    opportunityNum: any;
    dealCurrencyCode: any;
    conversionType: any;
    dealNumber: any;
    dealLineNumber: any;
    dealLineCost: any;
    parentLineId: any;
    expectedDuration: any;
    serviceEndDate: any;
    lastUpdatedDate: any;
    accountingScope: any;
    shipToCountry: any;
    attribute15: any;
    salesrepName: any;
    eitfSop: any;
    contTransHeaderId: any;
    bundleFlag: any;
    quantity: any;
    createdDate: any;
    priceList: any;
    analystId: any;
    billToCustomerNum: any;
    approverId: any;
    billToSiteNum: any;
    unitListPrice: any;
    cvInOutAmount: any;
    expectedStartDate: any;
    allocationFlag: any;
    createdBy: any;
    lastUpdatedBy: any;
    legalEntityId: any;
    productName: any;
    forecastCode: any;
    dealStatus: any;
    trxCurrencyCode: any;
    repUnitListPrice: any;
    conversionDate: any;
    dealLineId: any;
    salesTerritory: any;
    financeContact: any;
    paymentTerms: any;
    subElementType: any;
    bundlePercent: any;
    serviceStartDate: any;
    billToCustomerName: any;
    repCurrCode: any;
    trxAllocUnitAmt: any;
    agreementName: any;
    expectedSplit: any;
    serviceDuration: any;
    conversionRate: any;
    argBasisLineNumber: any;
    bespPerUnit: any;
    dealArrangementId: any;
    agreementNumber: any;
    accounintRuleName: any;
    childListPrice: any;
    expectedEndDate: any;
    dealDate: any;
    contractStartDate: any;
    contractEndDate: any;
    lineAmount: any;
}
