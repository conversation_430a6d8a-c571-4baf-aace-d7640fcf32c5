interface ILabels {
  [index: string]: string;
}

export class RmanDealDetailsLabels {

  fieldLabels: ILabels;

  constructor() {

    this.fieldLabels = {};

    this.fieldLabels["shipToSiteNum"] = "Ship to Site#";
    this.fieldLabels["financeContactId"] = "Finance Contact Id";
    this.fieldLabels["elementType"] = "Element Type";
    this.fieldLabels["productOrgId"] = "Product OrgID";
    this.fieldLabels["directCvAmount"] = "Direct CV Amount";
    this.fieldLabels["parentLineNumber"] = "Parent Product";
    this.fieldLabels["billToCountry"] = "Bill to Country";
    this.fieldLabels["lineId"] = "Line ID";
    this.fieldLabels["changeReason"] = "Change Reason";
    this.fieldLabels["unitSellingPrice"] = "Unit Selling Price";
    this.fieldLabels["opportunityName"] = "Opportunity Name";
    this.fieldLabels["endCustomerNum"] = "Customer#";
    this.fieldLabels["durationUom"] = "Duration UOM";
    this.fieldLabels["dealName"] = "Deal Name";
    this.fieldLabels["attribute3"] = "Solution";
    this.fieldLabels["attribute2"] = "ATTRIBUTE2";
    this.fieldLabels["attribute1"] = "ATTRIBUTE1";
    this.fieldLabels["salesOpsId"] = "Sales Operation ID";
    this.fieldLabels["fmvAmount"] = "FMV Amount";
    this.fieldLabels["dealHeaderId"] = "Deal Header Id";
    this.fieldLabels["attribute9"] = "Unit Cost";
    this.fieldLabels["attribute8"] = "Attribute8";
    this.fieldLabels["childSellPrice"] = "Attribution Net";
    this.fieldLabels["attribute7"] = "Attribute7";
    this.fieldLabels["attribute6"] = "Attribute6";
    this.fieldLabels["attribute5"] = "Attribute5";
    this.fieldLabels["attribute4"] = "Attribute4";
    this.fieldLabels["directCv"] = "Direct CV";
    this.fieldLabels["uomCode"] = "UOM";
    this.fieldLabels["salesOpsName"] = "Sales Ops Name";
    this.fieldLabels["agreementCode"] = "Agreement Code";
    this.fieldLabels["fmvRuleDefId"] = "FMV Rule ID";
    this.fieldLabels["attribute10"] = "Attribute10";
    this.fieldLabels["cvPercent"] = "CV Percent";
    this.fieldLabels["endCustomerName"] = "Customer Name";
    this.fieldLabels["serviceDurationUom"] = "Service Duration UOM";
    this.fieldLabels["fvPercent"] = "FV Percent";
    this.fieldLabels["attribute14"] = "Milestone";
    this.fieldLabels["attribute13"] = "Variable Consideration";
    this.fieldLabels["repUnitSellingPrice"] = "REP Unit Selling Price";
    this.fieldLabels["attribute12"] = "Allocation Inclusive";
    this.fieldLabels["customerType"] = "Customer Type";
    this.fieldLabels["partnerName"] = "Partner Name";
    this.fieldLabels["attribute11"] = "POB ID";
    this.fieldLabels["contAppQty"] = "Contengency Apply Qty";
    this.fieldLabels["dealType"] = "Deal Type";
    this.fieldLabels["allocationAmount"] = "Allocation Amount";
    this.fieldLabels["salesChannel"] = "Sales Channel";
    this.fieldLabels["contingencyCode"] = "Contingency Code";
    this.fieldLabels["opportunityNum"] = "Opportunity";
    this.fieldLabels["dealCurrencyCode"] = "Currency";
    this.fieldLabels["conversionType"] = "Conversion Type";
    this.fieldLabels["dealNumber"] = "Deal/Contract#";
    this.fieldLabels["dealLineNumber"] = "Line#";
    this.fieldLabels["dealLineCost"] = "Total Cost";
    this.fieldLabels["parentLineId"] = "Parent Product";
    this.fieldLabels["expectedDuration"] = "Expected Duration";
    this.fieldLabels["serviceEndDate"] = "Service End Date";
    this.fieldLabels["lastUpdatedDate"] = "Last Update Date";
    this.fieldLabels["accountingScope"] = "Accounting Scope";
    this.fieldLabels["shipToCountry"] = "Ship to Country";
    this.fieldLabels["attribute15"] = "Attribute15";
    this.fieldLabels["salesrepName"] = "SalesRep Name";
    this.fieldLabels["eitfSop"] = "ETIF-SOP";
    this.fieldLabels["contTransHeaderId"] = "Cont transHeader ID";
    this.fieldLabels["bundleFlag"] = "Bundle Flag";
    this.fieldLabels["quantity"] = "Quantity";
    this.fieldLabels["createdDate"] = "CREATED DATE";
    this.fieldLabels["priceList"] = "PRICE LIST";
    this.fieldLabels["analystId"] = "ANALYST ID";
    this.fieldLabels["billToCustomerNum"] = "BILL TO CUSTOMER NUM";
    this.fieldLabels["approverId"] = "APPROVER ID";
    this.fieldLabels["billToSiteNum"] = "BILL TO SITE NUM";
    this.fieldLabels["unitListPrice"] = "Unit List Price ";
    this.fieldLabels["cvInOutAmount"] = "CV IN OUT AMOUNT";
    this.fieldLabels["expectedStartDate"] = "EXPECTED START DATE";
    this.fieldLabels["allocationFlag"] = "ALLOCATION FLAG";
    this.fieldLabels["createdBy"] = "CREATED BY";
    this.fieldLabels["lastUpdatedBy"] = "LAST UPDATED BY";
    this.fieldLabels["legalEntityId"] = "LEGAL ENTITY ID";
    this.fieldLabels["productName"] = "Product Name";
    this.fieldLabels["forecastCode"] = "FORECAST CODE";
    this.fieldLabels["dealStatus"] = "DEAL STATUS";
    this.fieldLabels["trxCurrencyCode"] = "TRX CURRENCY CODE";
    this.fieldLabels["repUnitListPrice"] = "REP UNIT LIST PRICE";
    this.fieldLabels["conversionDate"] = "CONVERSION DATE";
    this.fieldLabels["dealLineId"] = "DEAL LINE ID";
    this.fieldLabels["salesTerritory"] = "SALES TERRITORY";
    this.fieldLabels["financeContact"] = "FINANCE CONTACT";
    this.fieldLabels["paymentTerms"] = "PAYMENT TERMS";
    this.fieldLabels["subElementType"] = "SUB ELEMENT TYPE";
    this.fieldLabels["bundlePercent"] = "BUNDLE PERCENT";
    this.fieldLabels["serviceStartDate"] = "Service Start Date";
    this.fieldLabels["billToCustomerName"] = "BILL TO CUSTOMER NAME";
    this.fieldLabels["repCurrCode"] = "REP CURR CODE";
    this.fieldLabels["trxAllocUnitAmt"] = "TRX ALLOC UNIT AMT";
    this.fieldLabels["agreementName"] = "AGREEMENT NAME";
    this.fieldLabels["expectedSplit"] = "EXPECTED SPLIT";
    this.fieldLabels["serviceDuration"] = "Service Duration";
    this.fieldLabels["conversionRate"] = "Exchange Rate";
    this.fieldLabels["argBasisLineNumber"] = "ARG BASIS LINE NUMBER";
    this.fieldLabels["bespPerUnit"] = "BESP PER UNIT";
    this.fieldLabels["dealArrangementId"] = "DEAL ARRANGEMENT ID";
    this.fieldLabels["agreementNumber"] = "AGREEMENT NUMBER";
    this.fieldLabels["accounintRuleName"] = "Accounting Rule";
    this.fieldLabels["childListPrice"] = "CHILD LIST PRICE";
    this.fieldLabels["expectedEndDate"] = "EXPECTED END DATE";
    this.fieldLabels["dealDate"] = "DEAL DATE";
    this.fieldLabels["contractStartDate"] = "CONTRACT START DATE";
    this.fieldLabels["contractEndDate"] = "CONTRACT END DATE";
    this.fieldLabels["lineAmount"] = "Line Amount";
    this.fieldLabels["productDescription"] = "Product Description";
  }

}
