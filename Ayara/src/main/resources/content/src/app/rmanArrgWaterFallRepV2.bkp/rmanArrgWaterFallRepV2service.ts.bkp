import {Injectable} from '@angular/core';
import { HttpClient,HttpHeaders} from '@angular/common/http';
import { DatePipe } from '@angular/common';
declare var require:any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type':  'application/json',
  })
}




@Injectable()
export class RmanArrgWaterFallRepV2Service {

    constructor(private http: HttpClient) {}

    getServiceUrl(paginationOptions: any, rmanArrgWaterFallRepV2SearchObject: any, exportFlag: any) {

      let serviceUrl = appSettings.apiUrl + '/rmanArrgWaterFallRepV2Export?';

      if (exportFlag == 0) {
        serviceUrl = appSettings.apiUrl + '/rmanArrgWaterFallRepV2Search?';
      }


        let searchString='';

        if (rmanArrgWaterFallRepV2SearchObject.sno!=undefined && rmanArrgWaterFallRepV2SearchObject.sno!="") {
            searchString=searchString+'sno:'+rmanArrgWaterFallRepV2SearchObject.sno+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.msaNumber!=undefined && rmanArrgWaterFallRepV2SearchObject.msaNumber!="") {
            searchString=searchString+'msaNumber:'+rmanArrgWaterFallRepV2SearchObject.msaNumber+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.msaName!=undefined && rmanArrgWaterFallRepV2SearchObject.msaName!="") {
            searchString=searchString+'msaName:'+rmanArrgWaterFallRepV2SearchObject.msaName+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.dealArrangementName!=undefined && rmanArrgWaterFallRepV2SearchObject.dealArrangementName!="") {
            searchString=searchString+'dealArrangementName:'+rmanArrgWaterFallRepV2SearchObject.dealArrangementName+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.dealArrangementNumber!=undefined && rmanArrgWaterFallRepV2SearchObject.dealArrangementNumber!="") {
            searchString=searchString+'dealArrangementNumber:'+rmanArrgWaterFallRepV2SearchObject.dealArrangementNumber+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.dealArrangementId!=undefined && rmanArrgWaterFallRepV2SearchObject.dealArrangementId!="") {
            searchString=searchString+'dealArrangementId:'+rmanArrgWaterFallRepV2SearchObject.dealArrangementId+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.legalEntityName!=undefined && rmanArrgWaterFallRepV2SearchObject.legalEntityName!="") {
            searchString=searchString+'legalEntityName:'+rmanArrgWaterFallRepV2SearchObject.legalEntityName+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.legalEntityId!=undefined && rmanArrgWaterFallRepV2SearchObject.legalEntityId!="") {
            searchString=searchString+'legalEntityId:'+rmanArrgWaterFallRepV2SearchObject.legalEntityId+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.creationDate!=undefined && rmanArrgWaterFallRepV2SearchObject.creationDate!="") {
            searchString=searchString+'creationDate:'+rmanArrgWaterFallRepV2SearchObject.creationDate+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.createdBy!=undefined && rmanArrgWaterFallRepV2SearchObject.createdBy!="") {
            searchString=searchString+'createdBy:'+rmanArrgWaterFallRepV2SearchObject.createdBy+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.revAccountantId!=undefined && rmanArrgWaterFallRepV2SearchObject.revAccountantId!="") {
            searchString=searchString+'revAccountantId:'+rmanArrgWaterFallRepV2SearchObject.revAccountantId+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.revManagerId!=undefined && rmanArrgWaterFallRepV2SearchObject.revManagerId!="") {
            searchString=searchString+'revManagerId:'+rmanArrgWaterFallRepV2SearchObject.revManagerId+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.solution!=undefined && rmanArrgWaterFallRepV2SearchObject.solution!="") {
            searchString=searchString+'solution:'+rmanArrgWaterFallRepV2SearchObject.solution+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.dealName!=undefined && rmanArrgWaterFallRepV2SearchObject.dealName!="") {
            searchString=searchString+'dealName:'+rmanArrgWaterFallRepV2SearchObject.dealName+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.productName!=undefined && rmanArrgWaterFallRepV2SearchObject.productName!="") {
            searchString=searchString+'productName:'+rmanArrgWaterFallRepV2SearchObject.productName+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.productDescription!=undefined && rmanArrgWaterFallRepV2SearchObject.productDescription!="") {
            searchString=searchString+'productDescription:'+rmanArrgWaterFallRepV2SearchObject.productDescription+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.elementType!=undefined && rmanArrgWaterFallRepV2SearchObject.elementType!="") {
            searchString=searchString+'elementType:'+rmanArrgWaterFallRepV2SearchObject.elementType+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.productGroup!=undefined && rmanArrgWaterFallRepV2SearchObject.productGroup!="") {
            searchString=searchString+'productGroup:'+rmanArrgWaterFallRepV2SearchObject.productGroup+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.productType!=undefined && rmanArrgWaterFallRepV2SearchObject.productType!="") {
            searchString=searchString+'productType:'+rmanArrgWaterFallRepV2SearchObject.productType+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.contractQuantity!=undefined && rmanArrgWaterFallRepV2SearchObject.contractQuantity!="") {
            searchString=searchString+'contractQuantity:'+rmanArrgWaterFallRepV2SearchObject.contractQuantity+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.dealLineNumber!=undefined && rmanArrgWaterFallRepV2SearchObject.dealLineNumber!="") {
            searchString=searchString+'dealLineNumber:'+rmanArrgWaterFallRepV2SearchObject.dealLineNumber+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.unitSellingPrice!=undefined && rmanArrgWaterFallRepV2SearchObject.unitSellingPrice!="") {
            searchString=searchString+'unitSellingPrice:'+rmanArrgWaterFallRepV2SearchObject.unitSellingPrice+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.orderNumber!=undefined && rmanArrgWaterFallRepV2SearchObject.orderNumber!="") {
            searchString=searchString+'orderNumber:'+rmanArrgWaterFallRepV2SearchObject.orderNumber+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.sourceLineNumber!=undefined && rmanArrgWaterFallRepV2SearchObject.sourceLineNumber!="") {
            searchString=searchString+'sourceLineNumber:'+rmanArrgWaterFallRepV2SearchObject.sourceLineNumber+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.orderedQuantity!=undefined && rmanArrgWaterFallRepV2SearchObject.orderedQuantity!="") {
            searchString=searchString+'orderedQuantity:'+rmanArrgWaterFallRepV2SearchObject.orderedQuantity+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.invoiceNumber!=undefined && rmanArrgWaterFallRepV2SearchObject.invoiceNumber!="") {
            searchString=searchString+'invoiceNumber:'+rmanArrgWaterFallRepV2SearchObject.invoiceNumber+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.invoiceLineNumber!=undefined && rmanArrgWaterFallRepV2SearchObject.invoiceLineNumber!="") {
            searchString=searchString+'invoiceLineNumber:'+rmanArrgWaterFallRepV2SearchObject.invoiceLineNumber+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.distributionAmount!=undefined && rmanArrgWaterFallRepV2SearchObject.distributionAmount!="") {
            searchString=searchString+'distributionAmount:'+rmanArrgWaterFallRepV2SearchObject.distributionAmount+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.currentPeriod!=undefined && rmanArrgWaterFallRepV2SearchObject.currentPeriod!="") {
            searchString=searchString+'currentPeriod:'+rmanArrgWaterFallRepV2SearchObject.currentPeriod+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.revenueStartDate!=undefined && rmanArrgWaterFallRepV2SearchObject.revenueStartDate!="") {
            searchString=searchString+'revenueStartDate:'+rmanArrgWaterFallRepV2SearchObject.revenueStartDate+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.revenueEndDate!=undefined && rmanArrgWaterFallRepV2SearchObject.revenueEndDate!="") {
            searchString=searchString+'revenueEndDate:'+rmanArrgWaterFallRepV2SearchObject.revenueEndDate+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period1!=undefined && rmanArrgWaterFallRepV2SearchObject.period1!="") {
            searchString=searchString+'period1:'+rmanArrgWaterFallRepV2SearchObject.period1+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period2!=undefined && rmanArrgWaterFallRepV2SearchObject.period2!="") {
            searchString=searchString+'period2:'+rmanArrgWaterFallRepV2SearchObject.period2+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period3!=undefined && rmanArrgWaterFallRepV2SearchObject.period3!="") {
            searchString=searchString+'period3:'+rmanArrgWaterFallRepV2SearchObject.period3+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period4!=undefined && rmanArrgWaterFallRepV2SearchObject.period4!="") {
            searchString=searchString+'period4:'+rmanArrgWaterFallRepV2SearchObject.period4+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period5!=undefined && rmanArrgWaterFallRepV2SearchObject.period5!="") {
            searchString=searchString+'period5:'+rmanArrgWaterFallRepV2SearchObject.period5+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period6!=undefined && rmanArrgWaterFallRepV2SearchObject.period6!="") {
            searchString=searchString+'period6:'+rmanArrgWaterFallRepV2SearchObject.period6+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period7!=undefined && rmanArrgWaterFallRepV2SearchObject.period7!="") {
            searchString=searchString+'period7:'+rmanArrgWaterFallRepV2SearchObject.period7+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period8!=undefined && rmanArrgWaterFallRepV2SearchObject.period8!="") {
            searchString=searchString+'period8:'+rmanArrgWaterFallRepV2SearchObject.period8+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period9!=undefined && rmanArrgWaterFallRepV2SearchObject.period9!="") {
            searchString=searchString+'period9:'+rmanArrgWaterFallRepV2SearchObject.period9+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period10!=undefined && rmanArrgWaterFallRepV2SearchObject.period10!="") {
            searchString=searchString+'period10:'+rmanArrgWaterFallRepV2SearchObject.period10+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period11!=undefined && rmanArrgWaterFallRepV2SearchObject.period11!="") {
            searchString=searchString+'period11:'+rmanArrgWaterFallRepV2SearchObject.period11+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.period12!=undefined && rmanArrgWaterFallRepV2SearchObject.period12!="") {
            searchString=searchString+'period12:'+rmanArrgWaterFallRepV2SearchObject.period12+',';
        }

        if (rmanArrgWaterFallRepV2SearchObject.future!=undefined && rmanArrgWaterFallRepV2SearchObject.future!="") {
            searchString=searchString+'future:'+rmanArrgWaterFallRepV2SearchObject.future;
        }



        if (searchString == '') {
            serviceUrl=serviceUrl+'search=%25';
        }
        else {
            serviceUrl=serviceUrl+'search='+searchString;
        }

        if (paginationOptions.pageNumber!=undefined && paginationOptions.pageNumber!="" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber!=0) {
           serviceUrl=serviceUrl+'&page='+paginationOptions.pageNumber+'&size='+paginationOptions.pageSize;
        }

      return serviceUrl;
    }

      getAllRmanArrgWaterFallRepV2(paginationOptions:any,rmanArrgWaterFallRepV2SearchObject:any) {
        let serviceUrl = this.getServiceUrl(paginationOptions, rmanArrgWaterFallRepV2SearchObject, 0);
        return this.http.get(serviceUrl).toPromise().then((data: any) => {
          return data.json();
        });

      }


    saveRmanArrgWaterFallRepV2(rmanArrgWaterFallRepV2:any) {
        let body = JSON.stringify(rmanArrgWaterFallRepV2);
      
        return this.http.post(appSettings.apiUrl+'/RMAN_ARRG_WATER_FALL_REP_V2',body,httpOptions).toPromise().then(data => {
            return data;
        });
    }

    updateRmanArrgWaterFallRepV2(rmanArrgWaterFallRepV2:any) {

        delete rmanArrgWaterFallRepV2._links;
        delete rmanArrgWaterFallRepV2.interests;
        let body = JSON.stringify(rmanArrgWaterFallRepV2);
        
        return this.http.put(appSettings.apiUrl+'/RMAN_ARRG_WATER_FALL_REP_V2/'+rmanArrgWaterFallRepV2.sno,body,httpOptions).toPromise().then(data => {
            return data;
        });

    }

    deleteRmanArrgWaterFallRepV2(rmanArrgWaterFallRepV2:any) {
        let deleteUrl=appSettings.apiUrl+'/RMAN_ARRG_WATER_FALL_REP_V2/'+rmanArrgWaterFallRepV2.sno;
      
        return this.http.delete(deleteUrl,httpOptions).toPromise().then(data => {
            return data;
        });
    }

}
