export interface RmanArrgWaterFallRepV2 {
    period7?:any;
    dealArrangementNumber?:any;
    period8?:any;
    period5?:any;
    revManagerId?:any;
    period6?:any;
    currentPeriod?:any;
    period3?:any;
    period4?:any;
    msaName?:any;
    period1?:any;
    period2?:any;
    elementType?:any;
    dealArrangementName?:any;
    contractQuantity?:any;
    future?:any;
    period9?:any;
    productDescription?:any;
    sno?:any;
    unitSellingPrice?:any;
    dealName?:any;
    createdBy?:any;
    period12?:any;
    msaNumber?:any;
    period11?:any;
    productGroup?:any;
    dealLineNumber?:any;
    period10?:any;
    sourceLineNumber?:any;
    legalEntityId?:any;
    revAccountantId?:any;
    creationDate?:any;
    invoiceNumber?:any;
    solution?:any;
    productName?:any;
    dealArrangementId?:any;
    productType?:any;
    revenueStartDate?:any;
    legalEntityName?:any;
    invoiceLineNumber?:any;
    orderNumber?:any;
    orderedQuantity?:any;
    distributionAmount?:any;
    revenueEndDate?:any;
}
