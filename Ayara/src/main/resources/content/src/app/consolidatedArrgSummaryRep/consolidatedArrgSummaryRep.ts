export interface ConsolidatedArrgSummaryRep {
    arrangementSource?:any;
    quantity?:any;
    analystId?:any;
    priceList?:any;
    endDate?:any;
    elementType?:any;
    status?:any;
    cvInOutAmount?:any;
    dealName?:any;
    transactionCurrencyCode?:any;
    approvedBy?:any;
    arrgLegalEntityId?:any;
    approvalDate?:any;
    customerNumber?:any;
    netPrice?:any;
    productName?:any;
    managerId?:any;
    dealStatus?:any;
    uomCode?:any;
    agreementCode?:any;
    transactionSellingPrice?:any;
    listPrice?:any;
    dealLineId?:any;
    msaName?:any;
    transactionListPrice?:any;
    approvalStatus?:any;
    lineNumber?:any;
    allocationAmount?:any;
    billToCustomerNumber?:any;
    legalEntity?:any;
    arrangementName?:any;
    dealNumber?:any;
    billToCustomerName?:any;
    arrangementBasis?:any;
    trxAllocUnitAmt?:any;
    currency?:any;
    salesNodeLevel4?:any;
    msaNumber?:any;
    salesNodeLevel2?:any;
    salesNodeLevel3?:any;
    dealLineNumber?:any;
    arrangementNumber?:any;
    dealLineCost?:any;
    arrangementCreationDate?:any;
    startDate?:any;
    parentLineId?:any;
    salesNodeLevel1?:any;
    dealArrangementId?:any;
    arrangementType?:any;
    agreementNumber?:any;
    accountingScope?:any;
    customerName?:any;
    arrangementCreatedBy?:any;
}
