interface ILabels {
         [index: string]: string;
}

export class ConsolidatedArrgSummaryRepLabels {

        fieldLabels: ILabels;

    constructor() {

    this.fieldLabels = {};

        this.fieldLabels["arrangementSource"] = "Arrangement Source";
        this.fieldLabels["quantity"] = "Quantity";
        this.fieldLabels["analystId"] = "Analyst ID";
        this.fieldLabels["priceList"] = "Price List";
        this.fieldLabels["endDate"] = "End Date";
        this.fieldLabels["elementType"] = "Element Type";
        this.fieldLabels["status"] = "Status";
        this.fieldLabels["cvInOutAmount"] = "Carve In/Carve Out Amount";
        this.fieldLabels["dealName"] = "Deal Name";
        this.fieldLabels["transactionCurrencyCode"] = "Transaction Currency";
        this.fieldLabels["approvedBy"] = "Approved By";
        this.fieldLabels["arrgLegalEntityId"] = "Arrg Legal Entity ID";
        this.fieldLabels["approvalDate"] = "Approval Date";
        this.fieldLabels["customerNumber"] = "Customer Number";
        this.fieldLabels["netPrice"] = "Net Price";
        this.fieldLabels["productName"] = "Product Name";
        this.fieldLabels["managerId"] = "Manager ID";
        this.fieldLabels["dealStatus"] = "Deal Status";
        this.fieldLabels["uomCode"] = "UOM";
        this.fieldLabels["agreementCode"] = "Agreement Name";
        this.fieldLabels["transactionSellingPrice"] = "Transaction Selling Price";
        this.fieldLabels["listPrice"] = "List Price";
        this.fieldLabels["dealLineId"] = "Deal Line ID";
        this.fieldLabels["msaName"] = "MSA Name";
        this.fieldLabels["transactionListPrice"] = "Transaction List Price";
        this.fieldLabels["approvalStatus"] = "Approval Status";
        this.fieldLabels["lineNumber"] = "Line Number";
        this.fieldLabels["allocationAmount"] = "Allocation Amount";
        this.fieldLabels["billToCustomerNumber"] = "Bill To Customer Number";
        this.fieldLabels["legalEntity"] = "Legal Entity";
        this.fieldLabels["arrangementName"] = "Arrangement Name";
        this.fieldLabels["dealNumber"] = "Deal Number";
        this.fieldLabels["billToCustomerName"] = "Bill To Customer Name";
        this.fieldLabels["arrangementBasis"] = "Arrangement Basis";
        this.fieldLabels["trxAllocUnitAmt"] = "TRX Alloc Unit Amt";
        this.fieldLabels["currency"] = "Currency";
        this.fieldLabels["salesNodeLevel4"] = "Sales Node Level 4";
        this.fieldLabels["msaNumber"] = "MSA Number";
        this.fieldLabels["salesNodeLevel2"] = "Sales Region";
        this.fieldLabels["salesNodeLevel3"] = "Sales Teritory";
        this.fieldLabels["dealLineNumber"] = "Deal Line Number";
        this.fieldLabels["arrangementNumber"] = "Arrangement Number";
        this.fieldLabels["dealLineCost"] = "Total Cost";
        this.fieldLabels["arrangementCreationDate"] = "Arrangement Creation Date";
        this.fieldLabels["startDate"] = "Start Date";
        this.fieldLabels["parentLineId"] = "Parent Line ID";
        this.fieldLabels["salesNodeLevel1"] = "Sales Theatre";
        this.fieldLabels["dealArrangementId"] = "Deal Arrangement ID";
        this.fieldLabels["arrangementType"] = "Arrangement Type";
        this.fieldLabels["agreementNumber"] = "Agreement Number";
        this.fieldLabels["accountingScope"] = "Accounting Scope";
        this.fieldLabels["customerName"] = "Customer Name";
        this.fieldLabels["arrangementCreatedBy"] = "Arrangement Created By";
    }

}
