import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require: any;
const appSettings = require('../appsettings');


@Injectable()
export class AyaraActiveDealsARMReportService {

    constructor(private http: HttpClient) { }


    getAyaraActiveDealsARMReport(dealNumber: any, quoteNumber: any, geo: any, quoteStatus: any, fromDate: any, toDate: any) {

        if (fromDate == undefined) {
            fromDate = '';
        }
        if (toDate == undefined) {
            toDate = '';
        }
        if (dealNumber == undefined) {
            dealNumber = '';
        }
		if (quoteNumber == undefined) {
            quoteNumber = '';
        }
        if (quoteStatus == undefined) {
            quoteStatus = '';
        }
        
        if (geo == undefined) {
            geo = '';
        }

        let serviceUrl = appSettings.apiUrl + '/reports/activeDealsArmReport?dealNumber=' + dealNumber +'&quoteNumber=' + quoteNumber + '&geo=' + geo + '&quoteStatus=' + quoteStatus + '&fromDate=' + fromDate + '&toDate=' + toDate;
        return this.http.get(serviceUrl).toPromise().then((data: any) => {
            return data;
        });
    }

    getAyaraActiveDealsARMReportFile(dealNumber: any,quoteNumber: any, geo: any, quoteStatus: any, fromDate: any, toDate: any, exportCols: any) {

        if (fromDate == undefined) {
            fromDate = '';
        }
        if (toDate == undefined) {
            toDate = '';
        }
        if (dealNumber == undefined) {
            dealNumber = '';
        }
		if (quoteNumber == undefined) {
            quoteNumber = '';
        }
       if (quoteStatus == undefined) {
            quoteStatus = '';
        }
        
        if (geo == undefined) {
            geo = '';
        }

        
        let serviceUrl = appSettings.apiUrl + '/reports/exportActiveDealsArmReport?dealNumber=' + dealNumber +'&quoteNumber=' + quoteNumber +'&geo=' + geo + '&quoteStatus=' + quoteStatus + '&fromDate=' + fromDate + '&toDate=' + toDate;

        if (exportCols != undefined && exportCols != "") {
            serviceUrl = serviceUrl + '&exportCols=' + encodeURIComponent(exportCols);
        }
        return serviceUrl;
    }

    getAyaraActiveDealsARMChart(dealNumber: any, quoteNumber: any, geo: any,  quoteStatus: any, fromDate: any, toDate: any) {
        
        if (fromDate == undefined) {
            fromDate = '';
        }
        if (toDate == undefined) {
            toDate = '';
        }
		if (dealNumber == undefined) {
            dealNumber = '';
        }
		if (quoteNumber == undefined) {
            quoteNumber = '';
        }
       
        if (quoteStatus == undefined) {
            quoteStatus = '';
        }
        
        if (geo == undefined) {
            geo = '';
        }

        let serviceUrl = appSettings.apiUrl + '/reports/generateActiveDealsARMChart?dealNumber=' + dealNumber +'&quoteNumber=' + quoteNumber + '&geo=' + geo + '&quoteStatus=' + quoteStatus + '&fromDate=' + fromDate + '&toDate=' + toDate;
        return this.http.get(serviceUrl).toPromise().then((data: any) => {
            return data;
        });
    }
}
