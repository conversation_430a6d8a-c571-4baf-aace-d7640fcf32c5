export interface RmanOrderBookingsV {
    customerNumber: any;
    billToLocation: any;
    so: any;
    priceList: any;
    bookingCurrency: any;
    customerPoNum: any;
    arrangementId: any;
    funcCurrencyCode: any;
    linkedDate: any;
    ouName: any;
    orderBookedDate: any;
    orderTrxAmount: any;
    shipToLocation: any;
    endCustomer: any;
    orderAcctdAmount: any;
    orderSource: any;
    refOrderNumber: any;
    serviceRefOrder: any;
    serviceRefOrdLineNum: any;
    orderNumModifier: any;
    sno: any;
    salesrep: any;
}
