<form class="clearfix" name="changePasswordForm" [formGroup]="changePasswordForm" novalidate>      

      <div class="ui-g ui-fluid">        
        <div class="ui-g-12 md-inputfield">  
          <span class="selectSpan">Old Password<span class="red-color">*</span> </span>            
              <input pInputText type="password" class="textbox" id="oldPwd" name="oldPwd" placeholder="Old Password" [(ngModel)]="pwd.oldPassword" formControlName="oldPwd"/>
              <div *ngIf="formErrors.oldPwd" class="ui-message ui-messages-error ui-corner-all">
                {{ formErrors.oldPwd }}
              </div>                   
        </div>

        <div class="ui-g-12 md-inputfield">        
          <span class="selectSpan">New Password<span class="red-color">*</span> </span>        
            <input pInputText type="password" class="textbox" id="newPwd" name="newPwd" placeholder="New Password" [(ngModel)]="pwd.newPassword" formControlName="newPwd"/>
            <div *ngIf="formErrors.newPwd" class="ui-message ui-messages-error ui-corner-all">
              {{ formErrors.newPwd }}
            </div>       
        </div>

        <div class="ui-g-12 md-inputfield">      
          <span class="selectSpan">Confirm Password<span class="red-color">*</span> </span>             
            <input pInputText type="password" class="textbox" id="cnfrmPwd" name="cnfrmPwd" placeholder="Confirm Password" [(ngModel)]="pwd.cnfrmPassword" formControlName="cnfrmPwd"/>
            <div *ngIf="formErrors.cnfrmPwd" class="ui-message ui-messages-error ui-corner-all">
              {{ formErrors.cnfrmPassword }}
            </div>
        </div>


        <div class="ui-g-12">
          <h5>Password Hint:</h5>
          <ul>
            <li>Password must be of minimum 8 Characters.</li>
            <li>Must contain at least one lower case letter,one upper case letter, one digit and one special character.
            </li>
            <li>Valid special characters are !@#$%^&*</li>
          </ul>

        </div>
      </div>

    </form>
    <p-footer>
      <div style="text-align:right; margin-top:15px;">
        <button type="submit" pButton (click)="validate()"class="primary-btn" label="Confirm" 
          [disabled]="!changePasswordForm.valid"></button>        
        <button type="button" pButton (click)="cancel()" class="secondary-btn" label="Cancel"></button>
      </div>
      
    </p-footer>

