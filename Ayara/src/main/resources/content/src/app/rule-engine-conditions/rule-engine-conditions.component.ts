import {Component, Input, OnChanges, OnInit, SimpleChange, SimpleChanges} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {ConfirmationService, Message} from 'primeng/api';
import {Table} from 'primeng/table';
import {RmanEntityParametersVService} from '../rmanEntityParametersV/rmanEntityParametersVservice';
import {RmanLookupsVService} from '../rmanLookupsV/rmanLookupsVservice';
import {CommonSharedService} from '../shared/common.service';
import {NotificationService} from '../shared/notifications.service';
import { RuleEngineConditionsHeader } from './rule-engine-conditionsHeader';
import { RuleEngineParametersService } from './rule-engine-conditions.service';


declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');
@Component({
  selector: 'app-rule-engine-conditions',
  templateUrl: './rule-engine-conditions.component.html',
  providers: [RuleEngineParametersService, RmanEntityParametersVService, ConfirmationService]
})
export class RuleEngineConditionsComponent implements OnInit,OnChanges {

displayDialog: boolean;

    displaySearchDialog: boolean;

    displayConfirmation: boolean;

    showMsg: any;

    msgs: Message[] = [];


    pParameterValueId: any;

    ayaraRuleParameters: any = new AyaraRuleParametersImpl();

    ayaraRuleParametersSearch: any = new AyaraRuleParametersImpl();

    isSerached: number = 0;

    selectedAyaraRuleParameters: RuleEngineParametersService;

    newAyaraRuleParameters: boolean;

    @Input() pRuleId: number;
    
    ayaraRuleParametersList: any[];

    cols: any[];
    columns: any[];

    columnOptions: any[];

    paginationOptions: any;

    pages: {};

    datasource: any[];
    pageSize: number;
    totalElements: number;

    globalCols: any[];
    clonedCols: any[];

    showAddColumns = true;
    isSelectAllChecked = true;

    userId: number;
    showPaginator: boolean = true;
    startIndex: number;


    rmanLookupsVList: any[] = [];
    rmanLookupsV: any[];
    rmanLookupsV1List: any[] = [];
    rmanLookupsV1: any[];
    rmanEntityParametersVList: any[] = [];
    rmanEntityParametersV: any[];

    collapsedRuleParameters: boolean = true;

    ruleParametersForm: FormGroup;
    noData = appSettings.noData;
    loading: boolean;
    masterData: any;

    constructor(private ayaraRuleParametersService: RuleEngineParametersService, private rmanLookupsVService: RmanLookupsVService, private rmanEntityParametersVService: RmanEntityParametersVService, private formBuilder: FormBuilder, private confirmationService: ConfirmationService,
                private notificationService: NotificationService, private commonSharedService: CommonSharedService) {

        // generate list code
        this.paginationOptions = {'pageNumber': 0, 'pageSize': '10000'};

        this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, {'lookupTypeName': 'RULE_QUALIFIER'}).then((rmanLookupsVList: any) => {
            this.rmanLookupsVList = rmanLookupsVList.content;
            this.prepareRmanLookupsVObject();

            this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, {'lookupTypeName': 'RULE_CONDITION'}).then((rmanLookupsV1List: any) => {
                this.rmanLookupsV1List = rmanLookupsV1List.content;
                this.prepareRmanLookupsV1Object();
            }).catch((err: any) => {
                this.notificationService.showError('Error occured while getting "AND/OR" data');
                this.loading = false;
            });

        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting "Qualifier" data');
            this.loading = false;
        });

    }

    ngOnChanges(changes: { [propKey: string]: SimpleChange }) {
    }


    ngOnInit() {
        this.globalCols = [
            {field: 'parmeterGroup', header: 'Parameter Group', showField: true, display: 'table-cell', type: 'text', drag: false},
            {field: 'parameterName', header: 'Parameter Name', showField: true, display: 'table-cell', type: 'text', drag: false},
            {field: 'qualifier', header: 'Qualifier', showField: true, display: 'table-cell', type: 'text', drag: true},
            {field: 'parameterValue', header: 'Parameter Value', showField: true, display: 'table-cell', type: 'text', drag: true},
            {field: 'andOr', header: 'And/Or', showField: true, display: 'table-cell', type: 'text', drag: true},
        ];
        this.columns = [];
        this.getTableColumns('AyaraRuleParameters', 'Ayara Rule Parameters');
        this.buildForm();
    }

    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
            if (response && response != null && response.userId) {
                this.columns = [];
                let colsList = response.tableColumns.split(',');
                if (colsList.length > 0) {
                    colsList.forEach((item, index) => {
                        if (item) {
                            this.startIndex = this.globalCols.findIndex(col => col.field == item);
                            this.onDrop(index);
                        }
                    });
                }
                this.globalCols.forEach(col => {
                    if (response.tableColumns.indexOf(col.field) !== -1) {
                        this.columns.push(col);
                    } else {
                        col.showField = false;
                    }
                });
                if (this.columns.length != this.globalCols.length) {
                    this.isSelectAllChecked = false;
                }
                this.showPaginator = this.columns.length !== 0;
                this.userId = response.userId;
            } else {
                this.columns = this.globalCols;
            }
        }).catch(() => {
            this.notificationService.showError('Error occured while getting table columns data');
            this.loading = false;
        });
    }

    saveColumns() {
        let selectedCols = '';
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
            if (col.showField) {
                selectedCols += col.field;
                if (index < colLength) {
                    selectedCols += ',';
                }
            }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns('AyaraRuleParameters', 'Ayara Rule Parameters', selectedCols, this.userId).then((response) => {
            this.columns = this.globalCols.filter(item => item.showField);
            this.userId = response['userId'];
            this.showPaginator = this.columns.length !== 0;
            this.loading = false;
        }).catch(() => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    onDragStart(index: number) {
        this.startIndex = index;
    }

    onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
        
    }

    selectColumns(col: any) {
        let cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
            this.isSelectAllChecked = false;
        } else {
            this.isSelectAllChecked = true;
        }
    }

    onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
            if (this.isSelectAllChecked) {
                col.showField = true;
            } else {
                if (col.drag) {
                    col.showField = false;
                }
            }
        });
    }

    reset(dt: Table) {
        this.paginationOptions = {};
        this.ayaraRuleParameters = new AyaraRuleParametersImpl();
        dt.reset();

    }

    onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
    }

    closeConfigureColumns(event: any) {
        this.showAddColumns = true;
        this.globalCols = this.clonedCols;
        let configCol = this.globalCols.filter(item => !item.showField);
        this.isSelectAllChecked = !(configCol.length > 0);
    }

    buildForm() {
        this.ruleParametersForm = this.formBuilder.group({
            'qualifier': ['', [Validators.required]],
            'parameterId': ['', [Validators.required]],
            'parameterName': ['']
        });

        this.ruleParametersForm.valueChanges
            .subscribe(data => this.onValueChanged(data));

        this.onValueChanged();
    }

    onValueChanged(data?: any) {
        if (!this.ruleParametersForm) {
            return;
        }
        const form = this.ruleParametersForm;

        for (const field in this.formErrors) {
            // clear previous error message (if any)
            this.formErrors[field] = '';
            const control = form.get(field);
            if (control && control.dirty && !control.valid) {
                const messages = this.validationMessages[field];
                for (const key in control.errors) {
                    this.formErrors[field] += messages[key] + ' ';
                }
            }
        }
    }

    formErrors = {
        'qualifier': '', 'parameterId': ''
    };

    validationMessages = {
        'parameterId': {
            'required': 'Parameter ID is required'
        },
        'qualifier': {
            'required': 'Qualifier is Required'
        }
    };

    transformRmanLookupsV(rmanLookupsV: any) {
        if (rmanLookupsV) {
            return rmanLookupsV.lookupDescription;
        }
    }

    transformRmanLookupsV1(rmanLookupsV1: any) {
        if (rmanLookupsV1) {
            return rmanLookupsV1.lookupDescription;
        }
    }

    transformRmanEntityParametersV(rmanEntityParametersV: any) {
        if (rmanEntityParametersV) {
            return rmanEntityParametersV.parameterName;
        }
    }


    getAllAyaraRuleParameters(searchobj?: any) {
        
        this.ayaraRuleParameters.ruleId = this.pRuleId;
        this.loading = true;
        if ((this.pRuleId != null) && (this.pRuleId)) {
            this.ayaraRuleParametersService.getAllAyaraRuleParameters(this.paginationOptions, this.ayaraRuleParameters).then((ayaraRuleParametersList: any) => {
                this.loading = false;
                this.datasource = ayaraRuleParametersList.content;
                if (this.isSerached == 0) {
                    this.ayaraRuleParametersList = ayaraRuleParametersList.content.filter((item: any) => item.ruleId == this.pRuleId);
                } else {
                    this.ayaraRuleParametersList = ayaraRuleParametersList.content;
                    this.isSerached = 0;
                }
                this.totalElements = ayaraRuleParametersList.totalElements;
                this.pageSize = ayaraRuleParametersList.size;
                this.displaySearchDialog = false;
            }).catch((err: any) => {
                this.notificationService.showError('Error occured while getting data');
                this.loading = false;
            });
        }

    }


    getAyaraRuleParameters(event: any) {

        let first: number = event.first;
        let rows: number = event.rows;
        let pageNumber: number = first / rows;
        this.paginationOptions = {
            'pageNumber': pageNumber,
            'pageSize': this.pageSize,
            'sortField': event.sortField,
            'sortOrder': event.sortOrder
        };
        this.loading = false;
        if ((this.pRuleId != null) && (this.pRuleId)) {
            
            this.ayaraRuleParameters.ruleId = this.pRuleId;
            this.ayaraRuleParametersService.getAllAyaraRuleParameters(this.paginationOptions, this.ayaraRuleParameters).then((ayaraRuleParametersList: any) => {
                this.loading = false;
                this.datasource = ayaraRuleParametersList.content;
                if (this.pRuleId != null) {
                    
                    this.ayaraRuleParametersList = ayaraRuleParametersList.content.filter((item: any) => item.ruleId == this.pRuleId);
                } else {
                    this.ayaraRuleParametersList = ayaraRuleParametersList.content;
                }
                this.totalElements = ayaraRuleParametersList.totalElements;
                this.pageSize = ayaraRuleParametersList.size;
            }).catch((err: any) => {
                this.notificationService.showError('Error occured while getting data');
                this.loading = false;
            });
        }


    }

    showDialogToAdd() {

        this.newAyaraRuleParameters = true;
        this.ayaraRuleParameters = new AyaraRuleParametersImpl();
        if (this.pRuleId == null || this.pRuleId == 0) {
                this.displayDialog = false;
                

            this.notificationService.showInfo('Please select Ayara Rule');
            this.displayConfirmation = true;
        } else {
           
            this.ayaraRuleParameters.ruleId = this.pRuleId;
            this.displayDialog = true;
            this.prepareRmanLookupsVObject();
            this.prepareRmanLookupsV1Object();
            this.prepareRmanEntityParametersVObject();
        }
    }

    cancelEdit() {
        this.displayDialog = false;
        this.ayaraRuleParameters = new AyaraRuleParametersImpl();

    }

    cancelSearch() {
        this.displaySearchDialog = false;
        this.ayaraRuleParametersSearch = new AyaraRuleParametersImpl();
    }

    parentCall(data) {
        

        this.masterData = data;
        if (data != '') {
            
            this.pRuleId = data.ruleId;
            

          

            
            this.rmanEntityParametersVService.getAllRmanEntityParametersV(this.paginationOptions, {'entityName': 'Ayara Deals'}, 'Y').then((rmanEntityParametersVList: any) => {
                this.rmanEntityParametersVList = rmanEntityParametersVList.content;
              
                this.prepareRmanEntityParametersVObject();
            }).catch((err: any) => {
                
                this.notificationService.showError('Error occured while getting "Parameter ID" data');
                this.loading = false;
            });

            
            this.getAllAyaraRuleParameters();
        } else {
      
            this.pRuleId = null;
            this.ayaraRuleParametersList = [];
        }
    }

    saveOrUpdate(msg: any) {
        this.notificationService.showSuccess(msg);
        this.loading = false;
        this.getAllAyaraRuleParameters();
    }

    save() {
        const sourceName: any[] = this.rmanEntityParametersV.filter(obj => obj.value == this.ayaraRuleParameters.parameterId);
        this.ayaraRuleParameters.parameterName = sourceName[0].label;

        
        if (!this.ayaraRuleParameters.parmeterGroup) {
            this.ayaraRuleParameters.parmeterGroup = '';
        }

        if (this.newAyaraRuleParameters) {
            
            this.ayaraRuleParameters.ruleId = this.pRuleId;
            this.loading = true;
            this.ayaraRuleParametersService.saveAyaraRuleParameters(this.ayaraRuleParameters).then((response: any) => {
                this.saveOrUpdate('Saved successfully');
            }).catch((err: any) => {
                this.notificationService.showError('Error occured while saving data');
                this.loading = false;
            });

        } else {
            this.loading = true;
            this.ayaraRuleParametersService.updateAyaraRuleParameters(this.ayaraRuleParameters).then((response: any) => {
                this.saveOrUpdate('Updated successfully');
            }).catch((err: any) => {
                this.notificationService.showError('Error occured while updating the data');
                this.loading = false;
            });

        }

        this.ayaraRuleParameters = new AyaraRuleParametersImpl();

        this.displayDialog = false;

    }

    delete(ayaraRuleParameters: any) {
        this.ayaraRuleParameters = ayaraRuleParameters;
        this.displayDialog = false;
        this.confirmationService.confirm({
            message: 'Are you sure you want to delete this record?',
            header: 'Confirmation',
            icon: '',
            accept: () => {

                this.loading = true;
                this.ayaraRuleParametersService.deleteAyaraRuleParameters(this.ayaraRuleParameters).then((response: any) => {
                    this.loading = false;
                    this.ayaraRuleParametersList.splice(this.findSelectedAyaraRuleParametersIndex(), 1);
                    this.ayaraRuleParameters = new AyaraRuleParametersImpl();
                    this.notificationService.showSuccess('Deleted successfully');
                    this.getAllAyaraRuleParameters();
                }, error => {
                    this.notificationService.showError('Error occurred while deleting data');
                });
            },
            reject: () => {
                this.notificationService.showWarning('You have rejected');
            }
        });
    }

    editRow(ayaraRuleParameters: any) {
        this.newAyaraRuleParameters = false;
        this.ayaraRuleParameters = this.cloneAyaraRuleParameters(ayaraRuleParameters);
        this.displayDialog = true;
        this.ruleParametersForm.patchValue({
            parameterId: Number(this.ayaraRuleParameters.parameterId)
        });
        this.prepareRmanLookupsVObject();
        this.prepareRmanLookupsV1Object();
        this.prepareRmanEntityParametersVObject();
    }


    findSelectedAyaraRuleParametersIndex(): number {
        return this.ayaraRuleParametersList.indexOf(this.selectedAyaraRuleParameters);
    }

    onRowSelect(event: any) {

    }

    cloneAyaraRuleParameters(c: RuleEngineConditionsHeader): RuleEngineConditionsHeader {
        let ayaraRuleParameters: any
            = new AyaraRuleParametersImpl();
        for (let prop in c) {
            ayaraRuleParameters[prop] = c[prop];
        }
        return ayaraRuleParameters;
    }

    hideColumnMenu: boolean = true;

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    showFilter: boolean = false;

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {

        this.ayaraRuleParametersSearch = new AyaraRuleParametersImpl();

        if (this.isSerached == 0) {
            this.ayaraRuleParametersSearch = new AyaraRuleParametersImpl();
        }
        this.displaySearchDialog = true;

    }

    search() {

        this.isSerached = 1;
        this.ayaraRuleParameters = this.ayaraRuleParametersSearch;
        this.paginationOptions = {};
        this.getAllAyaraRuleParameters(this.ayaraRuleParameters);
    }

    prepareRmanLookupsVObject() {
        let rmanLookupsVTempObj: any = [{label: '--Select Qualifier(*)--', value: null}];
        this.rmanLookupsVList.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode});
        });

        this.rmanLookupsV = rmanLookupsVTempObj;

    }

    prepareRmanLookupsV1Object() {
        let rmanLookupsV1TempObj: any = [{label: '--Select And/Or--', value: null}];
        this.rmanLookupsV1List.forEach((rmanLookupsV1) => {
            rmanLookupsV1TempObj.push({label: rmanLookupsV1.lookupDescription, value: rmanLookupsV1.lookupCode});
        });

        this.rmanLookupsV1 = rmanLookupsV1TempObj;

    }

    prepareRmanEntityParametersVObject() {
        let rmanEntityParametersVTempObj: any = [{label: '--Select Parameter Name(*)--', value: null}];
        this.rmanEntityParametersVList.forEach((rmanEntityParametersV) => {
            rmanEntityParametersVTempObj.push({label: rmanEntityParametersV.parameterName, value: rmanEntityParametersV.entityParameterId});
        });

        this.rmanEntityParametersV = rmanEntityParametersVTempObj;

    }


    onBeforeToggleRuleParameters(evt: any) {
        this.collapsedRuleParameters = evt.collapsed;
    }
}


class AyaraRuleParametersImpl {
    constructor(
        public ruleParamId?: any,
        public ruleId?: any,
        public parameterGroup?: any,
        public parameterId?: any,
        public parameterName?: any, 
        public qualifier?: any,
        public parameterValue?: any,
        public andOr?: any
    ) {
    }
}

interface ILabels {
    [index: string]: string;
}


