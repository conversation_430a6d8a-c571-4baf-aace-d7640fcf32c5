import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {DatePipe} from '@angular/common';
declare var require: any;
const appSettings = require('../appsettings');

const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type': 'application/json',
  })
};


@Injectable({
  providedIn: 'root'
})
export class RuleEngineParametersService {

  constructor(private http: HttpClient, private datePipe: DatePipe) { }

  // Rule Parameters methods
  getAllAyaraRuleParameters(paginationOptions: any, ayaraRuleParameterSearchObject: any): Promise<any[]> {
    let serviceUrl = appSettings.apiUrl + '/ayaraRuleParametersSearch?';

    let searchString = '';
    const searchFields = ['ruleParameterId', 'ruleId', 'parameterName', 'parameterValue'];
    searchFields.forEach((obj) => {
      if (ayaraRuleParameterSearchObject[obj] != undefined && ayaraRuleParameterSearchObject[obj] != "") {
        if (ayaraRuleParameterSearchObject[obj] instanceof Date) {
          searchString = searchString + obj + ':' + this.datePipe.transform(ayaraRuleParameterSearchObject[obj],'yyyyMMdd') + ',';
        } else {
          searchString = searchString + obj + ':' + ayaraRuleParameterSearchObject[obj] + ',';
        }
      }
    });

    if (searchString == '') {
      serviceUrl = serviceUrl + 'search=%25';
    }
    else {
      serviceUrl = serviceUrl + 'search=' + searchString;
    }

    if (paginationOptions.pageNumber != undefined && paginationOptions.pageNumber != "" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber != 0) {
      serviceUrl = serviceUrl + '&page=' + paginationOptions.pageNumber + '&size=' + paginationOptions.pageSize;
    }

    return this.http.get(serviceUrl).toPromise().then((data: any) => {
      return data;
    });
  }

  saveAyaraRuleParameters(ayaraRuleParameter: any): Promise<any[]> {
    let body = JSON.stringify(ayaraRuleParameter);
    return this.http.post<any[]>(appSettings.apiUrl + '/AYARA_RULE_PARAMETERS', body, httpOptions).toPromise().then(data => {
      return data;
    });
  }

  updateAyaraRuleParameters(ayaraRuleParameter: any): Promise<any[]> {
    const ruleParamId = ayaraRuleParameter.ruleParamId;
    delete ayaraRuleParameter._links;
    delete ayaraRuleParameter.interests;
    delete ayaraRuleParameter.ruleParamId;
    let body = JSON.stringify(ayaraRuleParameter);
   
    return this.http.put<any[]>(appSettings.apiUrl + '/AYARA_RULE_PARAMETERS/' + ruleParamId, body, httpOptions).toPromise().then(data => {
      return data;
    });
  }

  deleteAyaraRuleParameters(ayaraRuleParameter: any): Promise<any[]> {
    const ruleParamId = ayaraRuleParameter.ruleParamId;
    let deleteUrl = appSettings.apiUrl + '/AYARA_RULE_PARAMETERS/' + ruleParamId;
    return this.http.delete(deleteUrl, httpOptions).toPromise().then((data: any) => {
      return data;
    });
  }
}
