<div class="content-section implementation">
</div>


<div class="card-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block">

                    <h2>Ayara Rule Parameters: {{masterData?.ruleName}}  </h2>
                    <div class="pull-right icons-list">
                        <a (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
                        <a (click)="showDialogToAdd()" title="Add"><em class="fa fa-plus-circle"></em></a>
                        <a (click)="showDialogToSearch()" title="Search"><em class="fa fa-search"></em></a>
                        <a (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
                        <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
                            <div class="user-popup">
                                <div class="content overflow">
                                    <input type="checkbox" [checked]="isSelectAllChecked" id="selectall"
                                           name="selectall" (click)="onSelectAll()"/>
                                    <label for="selectall">Select All</label>
                                    <a class="close" title="Close" (click)="closeConfigureColumns($event)">&times;</a>
                                    <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                                        <ng-template let-col let-index="index" pTemplate="item">
                                            <div *ngIf="col.drag">
                                                <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens"
                                                     (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                                                    <div class="drag">
                                                        <input type="checkbox" [checked]="col.showField"
                                                               [(ngModel)]="col.showField"
                                                               (change)="selectColumns(col)"/>
                                                        <label>{{col.header}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div *ngIf="!col.drag">
                                                <div class="ui-helper-clearfix">
                                                    <div>
                                                        <input type="checkbox" [checked]="col.showField"
                                                               [(ngModel)]="col.showField" (change)="selectColumns(col)"
                                                               [disabled]="!col.drag"/>
                                                        <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </ng-template>
                                    </p-listbox>

                                </div>

                                <div class="pull-right">
                                    <a class="configColBtn" (click)="saveColumns()">Save</a>
                                    <a class="configColBtn conf-cancel"
                                       (click)="closeConfigureColumns($event)">Cancel</a>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="x-scroll">
                        <p-table class="ui-datatable arrangementMgrTbl" #dt id="ruleParameter-dt" [loading]="loading"
                                 [columns]="columns" [value]="ayaraRuleParametersList" selectionMode="single"
                                 (onRowSelect)="onRowSelect($event)" (onLazyLoad)="getAyaraRuleParameters($event)"
                                 [lazy]="true" [paginator]="true"
                                 [rows]="pageSize" [totalRecords]="totalElements" scrollable="true"
                                 [resizableColumns]="true" columnResizeMode="expand">

                            <ng-template pTemplate="colgroup" let-columns>
                                <colgroup>
                                    <col>
                                    <col *ngFor="let col of columns">
                                </colgroup>
                            </ng-template>

                            <ng-template pTemplate="header" class="arrangementMgrTblHead">
                                <tr>
                                    <th></th>
                                    <ng-container *ngFor="let col of columns">
                                        <th *ngIf="col.type=='text' ||col.type=='date' "
                                            [ngStyle]="{'display': col.display}" title="{{col.header}}"
                                            pResizableColumn>{{col.header}}</th>
                                        <th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'"
                                            class="number" [ngStyle]="{'display': col.display}" title="{{col.header}}"
                                            pResizableColumn>{{col.header}}</th>
                                    </ng-container>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-rowData let-ayaraRuleParameters let-columns="columns">
                                <tr [pSelectableRow]="rowData">
                                    <td>
                                        <a (click)="editRow(ayaraRuleParameters)" class="icon-edit"
                                           title="Edit"> </a>
                                        <a (click)="delete(ayaraRuleParameters)" class="icon-delete"
                                           title="Delete"> </a>
                                    </td>
                                    <ng-container *ngFor="let col of columns">
                                        <td *ngIf="col.type == 'text' && col.field == 'parameterId'"
                                            title="{{transformRmanEntityParametersV(ayaraRuleParameters.rmanEntityParametersV)}}"
                                            [ngStyle]="{'display': col.display}">
                                            {{transformRmanEntityParametersV(ayaraRuleParameters.rmanEntityParametersV)}}
                                        </td>
                                        <td *ngIf="col.type == 'text' && col.field !== 'parameterId'"
                                            title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field]}}
                                        </td>

                                        <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number"
                                            [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field]}}
                                        </td>

                                        <td *ngIf="col.type == 'date'"
                                            title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}"
                                            [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                                        </td>

                                        <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}"
                                            class="number" [ngStyle]="{'display': col.display}">
                                            {{rowData[col.field] | round}}
                                        </td>
                                    </ng-container>

                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage" let-columns>
                                <div class="no-results-data">
                                    <p>{{noData}}</p>
                                </div>
                            </ng-template>
                        </p-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<p-dialog header="Search" width="800" [(visible)]="displaySearchDialog" [draggable]="true" showEffect="fade"
          [modal]="true" [blockScroll]="true" (onHide)="cancelSearch()">
    <form>
        <div class="ui-grid ui-grid-responsive ui-fluid">
            <div class="ui-g-12">
                <div class="ui-g-6">
                    <span class="selectSpan"> Parameter Group </span>
                    <span class="md-inputfield"><input pInputText class="textbox" name="parmeterGroup"
                                                       id="parmeterGroup" required [ngModelOptions]="{standalone: true}"
                                                       [(ngModel)]="ayaraRuleParametersSearch.parmeterGroup"
                                                       placeholder="Parameter Group"/></span>
                </div>
                <div class="ui-g-6 pull-right">
                    <span class="selectSpan"> Parameter Name </span>
                    <span class="md-inputfield"><input pInputText class="textbox" name="parameterName"
                                                       id="parameterName" required [ngModelOptions]="{standalone: true}"
                                                       [(ngModel)]="ayaraRuleParametersSearch.parameterName"
                                                       placeholder="Parameter Name"/></span>
                </div>
            </div>
            <div class="ui-g-12">
                <div class="ui-g-6">
                    <span class="selectSpan"> Qualifier </span>
                    <p-dropdown [options]="rmanLookupsV" [ngModelOptions]="{standalone: true}"
                                [(ngModel)]="ayaraRuleParametersSearch.qualifier" name="qualifier" [filter]="true"
                                appendTo="body"></p-dropdown>
                </div>
                <div class="ui-g-6 pull-right">
                    <span class="selectSpan"> And/Or </span>
                    <p-dropdown [options]="rmanLookupsV1" [ngModelOptions]="{standalone: true}"
                                [(ngModel)]="ayaraRuleParametersSearch.andOr" name="andOr" [filter]="true"
                                appendTo="body"></p-dropdown>
                </div>
            </div>
            <div class="ui-g-12">
                <div class="ui-g-6">
                    <span class="selectSpan"> Parameter Value </span>
                    <span class="md-inputfield"><input pInputText class="textbox" name="parameterValue"
                                                       id="parameterValue" required
                                                       [ngModelOptions]="{standalone: true}"
                                                       [(ngModel)]="ayaraRuleParametersSearch.parameterValue"
                                                       placeholder="Parameter Value"/></span>
                </div>
            </div>
        </div>

    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" class="primary-btn" pButton label="Search" (click)="search()"></button>
            <button type="button" class="secondary-btn" pButton (click)="displaySearchDialog=false"
                    label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>
<p-dialog header="{{(newAyaraRuleParameters) ? 'Create New Const Rule Condition' : 'Edit Const Rule Condition'}}"
          width="800" [(visible)]="displayDialog" [draggable]="true" showEffect="fade" [modal]="true"
          [blockScroll]="true" (onHide)="cancelEdit()">
    <form (ngSubmit)="save()" [formGroup]="ruleParametersForm" novalidate>
        <div class="ui-grid ui-grid-responsive ui-fluid" *ngIf="ayaraRuleParameters">
            <div class="ui-g">
                <div class="ui-g-12">
                    <div class="ui-g-6">
                        <span class="selectSpan"> Parameter Name <span class="red-color">*</span> </span>
                        <p-dropdown [options]="rmanEntityParametersV" [(ngModel)]="ayaraRuleParameters.parameterId"
                                    name="parameterId" [filter]="true" formControlName="parameterId" appendTo="body">

                        </p-dropdown>
                        <div *ngIf="formErrors.parameterId" class="ui-message ui-messages-error ui-corner-all">
                            {{ formErrors.parameterId }}
                        </div>
                    </div>
                    <div class="ui-g-6 pull-right">
                        <span class="selectSpan"> Qualifier <span class="red-color">*</span> </span>
                        <p-dropdown [options]="rmanLookupsV" [(ngModel)]="ayaraRuleParameters.qualifier"
                                    name="qualifier" [filter]="true" formControlName="qualifier" appendTo="body">

                        </p-dropdown>
                        <div *ngIf="formErrors.qualifier" class="ui-message ui-messages-error ui-corner-all">
                            {{ formErrors.qualifier }}
                        </div>
                    </div>
                </div>
                <div class="ui-g-12">
                    <div class="ui-g-6">
                        <span class="selectSpan"> Parameter Value </span>
                        <span class="md-inputfield"><input pInputText class="textbox" name="parameterValue"
                                                           id="parameterValue" required
                                                           [ngModelOptions]="{standalone: true}"
                                                           [(ngModel)]="ayaraRuleParameters.parameterValue"
                                                           placeholder="Parameter Value"/></span>
                    </div>


                    <div class="ui-g-6 pull-right">
                        <span class="selectSpan"> Parameter Group </span>
                        <span class="md-inputfield"><input pInputText class="textbox" name="parmeterGroup"
                                                           id="parmeterGroup" required
                                                           [ngModelOptions]="{standalone: true}"
                                                           [(ngModel)]="ayaraRuleParameters.parmeterGroup"
                                                           placeholder="Parameter Group"/></span>
                    </div>
                </div>
                <div class="ui-g-12">

                    <div class="ui-g-6">
                        <span class="selectSpan"> And/Or </span>
                        <p-dropdown [options]="rmanLookupsV1" [ngModelOptions]="{standalone: true}"
                                    [(ngModel)]="ayaraRuleParameters.andOr" name="andOr" [filter]="true"
                                    appendTo="body"></p-dropdown>
                    </div>
                </div>
            </div>


        </div>
    </form>
    <p-footer>
        <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
            <button type="submit" pButton class="primary-btn" label="Save" (click)="save()"
                    [disabled]="!ruleParametersForm.valid"></button>
            <button type="button" pButton class="secondary-btn" (click)="displayDialog=false" label="Cancel"></button>
        </div>
    </p-footer>
</p-dialog>

<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>
