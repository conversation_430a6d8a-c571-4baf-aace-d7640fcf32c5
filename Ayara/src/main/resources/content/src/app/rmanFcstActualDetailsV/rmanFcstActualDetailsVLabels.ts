interface ILabels {
    [index: string]: string;
}

export class RmanFcstActualDetailsVLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["glPeriod"] = "GL PERIOD";
        this.fieldLabels["expectedBookingAmount"] = "EXPECTED BOOKING AMOUNT";
        this.fieldLabels["recordType"] = "RECORD TYPE";
        this.fieldLabels["amount"] = "AMOUNT";
        this.fieldLabels["qty"] = "QTY";
        this.fieldLabels["netPrice"] = "NET PRICE";
        this.fieldLabels["elementType"] = "ELEMENT TYPE";
        this.fieldLabels["startDate"] = "START DATE";
        this.fieldLabels["arrangementName"] = "ARRANGEMENT NAME";
        this.fieldLabels["sku"] = "SKU";
        this.fieldLabels["arrangementId"] = "ARRANGEMENT ID";
        this.fieldLabels["quarterName"] = "QUARTER NAME";
    }

}
