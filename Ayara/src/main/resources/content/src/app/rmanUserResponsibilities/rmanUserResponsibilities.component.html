	<div class="content-section implementation">
	</div>
	<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>

	<div class="card-wrapper">
		<div class="container-fluid">
		  <div class="row">
			<div class="col-md-12">
			  <div class="card-block">

	<p-panel header="User Responsibilites" [toggleable]="false" (onBeforeToggle)="onBeforeToggle($event)">
		<p-header>
			<span class="masterData"><strong>{{masterData?.userName}}</strong></span>
			<div class="pull-right icons-list" *ngIf="collapsed">
				<a (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
				<a  *isAuthorized="['write','US']" (click)="showDialogToAdd()" title="Add"><em class="fa fa-plus-circle"></em></a>
				<a  (click)="showDialogToSearch()" title="Search"><em class="fa fa-search"></em></a>
				<a  (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
				<div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
					<div class="user-popup">
						<div class="content overflow">
							<input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()" />
							<label for="selectall">Select All</label>
							<a class="close" title="Close" (click)="closeConfigureColumns($event)">&times;</a>
							<p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
								<ng-template let-col let-index="index" pTemplate="item">
									<div *ngIf="col.drag">
										<div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
											<div class="drag">
												<input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" />
												<label>{{col.header}}</label>
											</div>
										</div>
									</div>
									<div *ngIf="!col.drag">
										<div class="ui-helper-clearfix">
											<div>
												<input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"
												/>
												<label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
											</div>
										</div>
									</div>
								</ng-template>
							</p-listbox>
						</div>
						<div class="pull-right">
							<a class="configColBtn" (click)="saveColumns()">Save</a>
							<a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
						</div>
					</div>
				</div>
			</div>
		</p-header>

		<div class="x-scroll">
		<p-table #dt [loading]="loading" class="ui-datatable arrangementMgrTbl" id="rmanUserResposibilities-dt" [value]="rmanUserResponsibilitiesList"
			selectionMode="single"   (onRowSelect)="onRowSelect($event)"
			(onLazyLoad)="getRmanUserResponsibilities($event)" [lazy]="true" [paginator]="true" [rows]="9"
			[totalRecords]="totalElements"  scrollable="true" [columns]="columns" [resizableColumns]="true" columnResizeMode="expand">

			<ng-template pTemplate="colgroup" let-columns>
				<colgroup>
					<col>
					<col *ngFor="let col of columns">
				</colgroup>
			</ng-template>


			<ng-template pTemplate="header" class="arrangementMgrTblHead">
				<tr>
					<th></th>
					<ng-container *ngFor="let col of columns">
						<th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
						<th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}"
							title="{{col.header}}" pResizableColumn>{{col.header}}</th>
					</ng-container>
				</tr>
			</ng-template>

			<ng-template pTemplate="body" let-rowData let-rmanUserResponsibilities let-columns="columns">
				<tr [pSelectableRow]="rowData" [pSelectableRowIndex]="rowIndex">
					<td>
						<a  *isAuthorized="['write','US']" (click)="editRow(rmanUserResponsibilities)" class="icon-edit"> </a>
						<a  *isAuthorized="['write','US']" (click)="delete(rmanUserResponsibilities)" class="icon-delete">
						</a>
					</td>
					<ng-container *ngFor="let col of columns">
						<td *ngIf="col.field == 'responsibilityId'" title="{{transformRmanResponsibilities(rowData)}}" [ngStyle]="{'display': col.display}">
							{{transformRmanResponsibilities(rowData)}}
						</td>
						<td *ngIf="col.type == 'text' && col.field !== 'responsibilityId'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
							{{rowData[col.field]}}
						</td>

						<td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
							{{rowData[col.field]}}
						</td>

						<td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
							{{rowData[col.field] | date: 'MM/dd/yyyy'}}
						</td>

						<td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
							{{rowData[col.field] | round}}
						</td>
					</ng-container>

				</tr>
			</ng-template>

			<ng-template pTemplate="emptymessage" let-columns>
				<tr *ngIf="!columns">
					<td class="no-data">{{noData}}</td>
				</tr>
			</ng-template>
			
		</p-table>
		</div>
	</p-panel>
	</div>
	</div>
	</div>
	</div>
	</div>

	<p-dialog header="Search" width="800" [(visible)]="displaySearchDialog" [draggable]="true" 
		showEffect="fade" [modal]="true" [blockScroll]="true">
		<form>
			<div class="ui-grid ui-grid-responsive ui-fluid">

				<div class="ui-g-12">
					<div class="ui-g-6">
						<span class="selectSpan"> Responsibility Name </span>
						<p-dropdown [options]="rmanResponsibilities" appendTo="body"
							[ngModelOptions]="{standalone: true}"
							[(ngModel)]="rmanUserResponsibilitiesSearch.responsibilityId" name="responsibilityId"
							[filter]="true"></p-dropdown>
					</div>
				</div>
			</div>

		</form>
		<p-footer>
			<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
				<button type="submit" pButton class="primary-btn" (click)="search()" label="Search"></button>
				<button type="button" pButton  class="secondary-btn" (click)="cancelSearch()" label="Cancel"></button>
			</div>
		</p-footer>
	</p-dialog>
	<p-dialog header="{{(newRmanUserResponsibilities) ? 'Create User Responsibility' : 'Edit User Responsibility'}}"
		width="800" [draggable]="true" [(visible)]="displayDialog"  showEffect="fade" [modal]="true"
		[blockScroll]="true" (onHide)="cancelEdit();">
		<form (ngSubmit)="save()" [formGroup]="userResponsibilitiesForm" novalidate>
			<div class="ui-grid ui-grid-responsive ui-fluid" *ngIf="rmanUserResponsibilities">

				<div class="ui-g-12">
					<div class="ui-g-6">
						<span class="selectSpan"> Responsibility Name </span>
						<p-dropdown [options]="rmanResponsibilities" appendTo="body"
							[(ngModel)]="rmanUserResponsibilities.responsibilityId" name="responsibilityId"
							[filter]="true" formControlName="rID">

						</p-dropdown>
						<div *ngIf="formErrors.rID" class="ui-message ui-messages-error ui-corner-all">
							{{ formErrors.rID }}
						</div>
					</div>
					<div class="ui-g-6 pull-right">
						<span class="selectSpan">Start Date</span>
						<p-calendar showAnim="slideDown" inputStyleClass="textbox" name="startDateActive" id="responsibilitystartDateActive"
							[monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" appendTo="body"
							[(ngModel)]="rmanUserResponsibilities.startDateActive" dateFormat="yy-mm-dd"
							placeholder="Start Date*" formControlName="startDate">

						</p-calendar>
						<div *ngIf="formErrors.startDate" class="ui-message ui-messages-error ui-corner-all">
							{{ formErrors.startDate }}
						</div>
					</div>
				</div>

				<div class="ui-g-12">
					<div class="ui-g-6">
						<span class="selectSpan">End Date</span>
						<p-calendar showAnim="slideDown" inputStyleClass="textbox" name="endDateActive" [monthNavigator]="true"
							[yearNavigator]="true" yearRange="1950:2030" appendTo="body"
							[ngModelOptions]="{standalone: true}" [(ngModel)]="rmanUserResponsibilities.endDateActive"
							dateFormat="yy-mm-dd" placeholder="End Date"></p-calendar>
					</div>
					<div class="ui-g-6 pull-right">
						<span class="selectSpan"> Enabled Flag </span>
						<p-dropdown  #dropdownRef [options]="rmanLookupsV" appendTo="body"  [(ngModel)]="rmanUserResponsibilities.enabledFlag" name="enabledFlag"  [filter]="true" formControlName="flag">
						</p-dropdown>
						<div *ngIf="formErrors.flag" class="ui-message ui-messages-error ui-corner-all">
						  {{ formErrors.flag }}
						</div>
					  </div>
				</div>

			</div>
		</form>
		<p-footer>
			<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
				<button type="submit" pButton class="primary-btn" label="Save" (click)="save()"
					[disabled]="!userResponsibilitiesForm.valid"></button>
				<button type="button" pButton class="secondary-btn" (click)="cancelEdit()" label="Cancel"></button>
			</div>
		</p-footer>
	</p-dialog>


<div class="modal fade dialogueBox" id="responsibilityFieldsMandatory" role="dialog" data-backdrop="static"
	data-keyboard="false">
	<div class="modal-dialog modal-sm">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">{{'Error'}}</h4>
			</div>
			<form class="form-horizontal">
				<div class="modal-body clearfix">
					<div class="col-md-12 col-sm-12 col-xs-12 col-lg-12">

						<p *ngIf="showMsg==43">Please select Responsibility Id</p>
						<p *ngIf="showMsg==42">Please enter Start Date</p>


					</div>

				</div>
				<div class="modal-footer" style="padding:3px;">
					<button class="btn btn-primary pull-right" data-dismiss="modal">OK</button>
				</div>
			</form>
		</div>
	</div>
</div>