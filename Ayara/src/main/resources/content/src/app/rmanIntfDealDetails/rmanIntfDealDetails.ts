export interface RmanIntfDealDetails {
    fxDate?: any;
    arrangementSource?: any;
    unitCost?: any;
    interfaceError?: any;
    quantity?: any;
    priceList?: any;
    endDate?: any;
    additionalAttribute1?: any;
    additionalAttribute2?: any;
    additionalAttribute3?: any;
    sourceLineRefId?: any;
    unitSellingPrice?: any;
    unitListPrice?: any;
    dealName?: any;
    durationUom?: any;
    entityName?: any;
    createdBy?: any;
    productName?: any;
    arrgBasis?: any;
    adjustmentAmount?: any;
    agreementId?: any;
    operationCode?: any;
    masterArrgName?: any;
    allocationExcludeFlag?: any;
    fvPercent?: any;
    msaName?: any;
    currencyCode?: any;
    interfaceStatus?: any;
    fxRate?: any;
    dealNumber?: any;
    billToCustomerName?: any;
    arrangementName?: any;
    arrangementBasis?: any;
    arrgSource?: any;
    agreementName?: any;
    salesNodeLevel4?: any;
    additionalLineAttribute3?: any;
    salesNodeLevel2?: any;
    additionalLineAttribute1?: any;
    msaNumber?: any;
    salesNodeLevel3?: any;
    additionalLineAttribute2?: any;
    dealLineNumber?: any;
    arrangementNumber?: any;
    bespPerUnit?: any;
    startDate?: any;
    parentLineId?: any;
    masterArrgFlag?: any;
    salesNodeLevel1?: any;
    accountingRuleName?: any;
    contractUnitType?: any;
    customerName?: any;
    duration?: any;
    eitfSop?: any;
    bundleFlag?: any;
}
