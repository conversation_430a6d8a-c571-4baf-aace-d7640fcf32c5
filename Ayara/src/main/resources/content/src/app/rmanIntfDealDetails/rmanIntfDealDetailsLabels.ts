interface ILabels {
    [index: string]: string;
}

export class RmanIntfDealDetailsLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["fxDate"] = "FX Date";
        this.fieldLabels["arrangementSource"] = "Revenue Contract Source";
        this.fieldLabels["unitCost"] = "Unit Cost";
        this.fieldLabels["interfaceError"] = "Error Message";
        this.fieldLabels["quantity"] = "Quantity";
        this.fieldLabels["priceList"] = "Price List";
        this.fieldLabels["endDate"] = "End Date";
        this.fieldLabels["additionalAttribute1"] = "Additional Attribute1";
        this.fieldLabels["additionalAttribute2"] = "Additional Attribute2";
        this.fieldLabels["additionalAttribute3"] = "Additional Attribute3";
        this.fieldLabels["sourceLineRefId"] = "Source Line Ref ID";
        this.fieldLabels["unitSellingPrice"] = "Unit Net Price";
        this.fieldLabels["unitListPrice"] = "Unit List Price";
        this.fieldLabels["dealName"] = "Deal Name";
        this.fieldLabels["durationUom"] = "Duration UOM";
        this.fieldLabels["entityName"] = "Entity Name";
        this.fieldLabels["createdBy"] = "Created BY";
        this.fieldLabels["productName"] = "Product Name";
        this.fieldLabels["arrgBasis"] = "Arrg Basis";
        this.fieldLabels["adjustmentAmount"] = "Adjustment Amount";
        this.fieldLabels["agreementId"] = "Agreement ID";
        this.fieldLabels["operationCode"] = "Operation Code";
        this.fieldLabels["masterArrgName"] = "Master Arrg Name";
        this.fieldLabels["allocationExcludeFlag"] = "Allocation Exclude Flag";
        this.fieldLabels["fvPercent"] = "FV Percent";
        this.fieldLabels["msaName"] = "MSA Name";
        this.fieldLabels["currencyCode"] = "Currency";
        this.fieldLabels["interfaceStatus"] = "Interface Status";
        this.fieldLabels["fxRate"] = "FX Rate";
        this.fieldLabels["dealNumber"] = "Deal Number";
        this.fieldLabels["billToCustomerName"] = "Bill To Customer Name";
        this.fieldLabels["arrangementName"] = "Revenue Contract Name";
        this.fieldLabels["arrangementBasis"] = "Revenue Contract Basis";
        this.fieldLabels["arrgSource"] = "Arrg Source";
        this.fieldLabels["agreementName"] = "Agreement Name";
        this.fieldLabels["salesNodeLevel4"] = "Sales Node Level4";
        this.fieldLabels["additionalLineAttribute3"] = "Additional Line Attribute3";
        this.fieldLabels["salesNodeLevel2"] = "Sales Region";
        this.fieldLabels["additionalLineAttribute1"] = "Additional Line Attribute1";
        this.fieldLabels["msaNumber"] = "MSA Number";
        this.fieldLabels["salesNodeLevel3"] = "Sales Territory";
        this.fieldLabels["additionalLineAttribute2"] = "Additional Line Attribute2";
        this.fieldLabels["dealLineNumber"] = "Deal Line Number";
        this.fieldLabels["arrangementNumber"] = "Revenue Contract Number";
        this.fieldLabels["bespPerUnit"] = "BESP Per Unit";
        this.fieldLabels["startDate"] = "Start Date";
        this.fieldLabels["parentLineId"] = "Parent Line ID";
        this.fieldLabels["masterArrgFlag"] = "Master Arrg FLAG";
        this.fieldLabels["salesNodeLevel1"] = "Sales Theatre";
        this.fieldLabels["accountingRuleName"] = "Accounting Rule Name";
        this.fieldLabels["contractUnitType"] = "UOM";
        this.fieldLabels["customerName"] = "Customer Name";
        this.fieldLabels["duration"] = "Duration";
        this.fieldLabels["eitfSop"] = "EITF SOP";
        this.fieldLabels["bundleFlag"] = "Bundle Flag";
    }

}
