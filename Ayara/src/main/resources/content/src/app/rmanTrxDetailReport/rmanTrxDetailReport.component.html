
        <div class="content-section implementation">
        </div>

		<div class="card-wrapper">
			<div class="container-fluid">
			  <div class="row">
				<div class="col-md-12">
				  <div class="card-block">
				  
		<p-panel header="Transaction Details Report" [toggleable]="false" (onBeforeToggle)="onBeforeToggle($event)">
                <p-header>

                        <div class="pull-right icons-list" *ngIf="collapsed">
                                <a  (click)="goToOperationReports()" class="add-column"><em class="fa fa-reply"></em>Back</a>
				<a  (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
                                <a  (click)="showDialogToSearch()" title="Search"><em class="fa fa-search"></em></a>
                                <a  (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
                                <a  (click)="exportExcel()" title="Export" *ngIf="!disableExport"><em class="fa fa-external-link"></em></a>

                                <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
                                <div class="user-popup">
                                        <div class="content overflow">
                                        <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()"/>
                                        <label for="selectall">Select All</label>
                                        <a class="close" title="Close" (click)="closeConfigureColumns($event)" >&times;</a>
                                        <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                                                <ng-template let-col let-index="index" pTemplate="item">
                                                  <div *ngIf="col.drag">
                                                  <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" 
                                                   (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                                                    <div class="drag">
                                                      <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)"/>
                                                      <label>{{col.header}}</label>
                                                    </div>
                                                  </div>
                                                  </div>
                                                  <div *ngIf="!col.drag">
                                                  <div class="ui-helper-clearfix">
                                                    <div>
                                                      <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"/>
                                                      <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                                                    </div>
                                                  </div>
                                                  </div>
                                                </ng-template>
                                                </p-listbox>
                                        </div>

                                        <div class="pull-right">
                                                <a class="configColBtn" (click)="saveColumns()">Save</a>
                                                <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
                                              </div>
                                </div>
                                </div>
                        </div>
                </p-header>

				<div class="x-scroll">
                <p-table class="ui-datatable arrangementMgrTbl" #dt id="trxDetail-dt" [loading]="loading" [value]="rmanTrxDetailReportList" selectionMode="single" (onRowSelect)="onRowSelect($event)"
                        (onLazyLoad)="getRmanTrxDetailReport($event)" [lazy]="true" [paginator]="showPaginator" [rows]="pageSize" [totalRecords]="totalElements"
                         scrollable="true"  [columns]="columns" [resizableColumns]="true" columnResizeMode="expand">
						
                        <ng-template pTemplate="colgroup" let-columns>
                                <colgroup>
                                        <col *ngFor="let col of columns">
                                </colgroup>
                        </ng-template>

                        <ng-template pTemplate="header" class="arrangementMgrTblHead">
                        <tr>
                                <ng-container *ngFor="let col of columns">
                                        <th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                                        <th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                                   </ng-container>
                        </tr>
                        </ng-template>
						
                        <ng-template pTemplate="body" let-rowData let-rmanTrxDetailReport>
                                <tr [pSelectableRow]="rowData">

                                        <ng-container *ngFor="let col of columns" >
                                                <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                                                        {{rowData[col.field]}}
                                                </td>
                                        
                                                <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
                                                        {{rowData[col.field]}}
                                                </td>
                                        
                                                <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
                                                        {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                                                </td>
                                        
                                                <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
                                                        {{rowData[col.field] | round}}
                                                </td>
                                        </ng-container>

                                </tr>
                             
                        </ng-template>

                        <ng-template pTemplate="emptymessage" let-columns>
                                <tr>
                                        <td [attr.colspan]="columns.length" class="no-data">{{noData}}</td>
                                </tr>
                        </ng-template>
                </p-table>
				</div>
		</p-panel>
		</div>
		</div>
		</div>
		</div>
		</div>

        <p-dialog header="Search" width="800" [(visible)]="displaySearchDialog" [draggable]="true" showEffect="fade" [modal]="true">
                <form (ngSubmit)="search()">
                        <div class="ui-grid ui-grid-responsive ui-fluid">
                                <div class="ui-g-12">
                                        <div class="ui-g-6">
                                                <span class="md-inputfield">
													<span class="selectSpan">Revenue Contract Number From</span>
                                                        <!--<input pInputText name="from" id="from" class="textbox" placeholder="Revenue Contract Number From" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.fromArrgId" />-->
                                                        <p-autoComplete  inputStyleClass="textbox" [suggestions]="dealArrangementNumbers" 
										(completeMethod)="searchRCList($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								 placeholder="RC Number Type atleast 3 Characters" id="from" [(ngModel)]="searchFields.fromArrgId"></p-autoComplete>
	
                                                </span>
                                        </div>
                                        <div class="ui-g-6 pull-right">
                                                <span class="md-inputfield">
													<span class="selectSpan">Revenue Contract Number To</span>
                                                        <!--<input pInputText name="to" id="to" class="textbox" placeholder="Revenue Contract Number To" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.toArrgId" />-->
                                                        <p-autoComplete  inputStyleClass="textbox" [suggestions]="dealArrangementNumbers" 
										(completeMethod)="searchRCList($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								 placeholder="RC Number Type atleast 3 Characters" id="to" [(ngModel)]="searchFields.toArrgId"></p-autoComplete>
	
                                                </span>
                                        </div>
                                </div>
                                
                                <div class="ui-g-12">
                                               <div class="ui-g-6">
                                                  <span class="selectSpan">From Period</span>
                                                  <p-calendar showAnim="slideDown" inputStyleClass="textbox" name="fromPeriod" id="fromPeriod" [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030"
                                                    appendTo="body" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.fromPeriod" dateFormat="yy-mm-dd"
                                                    placeholder="From Period"></p-calendar>
                                                </div>
                                      
                                                <div class="ui-g-6 pull-right">
                                                  <span class="selectSpan">To Period</span>
                                                  <p-calendar showAnim="slideDown" inputStyleClass="textbox" name="toPeriod" id="toPeriod" [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030"
                                                    appendTo="body" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.toPeriod" dateFormat="yy-mm-dd"
                                                    placeholder="To Period"></p-calendar>
                                                </div> 
                                              </div>
                                      
                                <div class="ui-g-12">
                                        <div class="ui-g-6">
                                                <!--<span class="md-inputfield">
													<span class="selectSpan">Period Name</span>
                                                        <p-autoComplete [(ngModel)]="searchFields.periodName" inputStyleClass="textbox" [suggestions]="filteredPeriodNameList" (completeMethod)="filterPeriodName($event)" styleClass="wid100"
                                                                [minLength]="3" [ngModelOptions]="{standalone: true}" placeholder="Type atleast 3 characters, ex: Jan-20" id="periodName">
                                                        </p-autoComplete>
                                                </span>-->
                                                <span class="md-inputfield">
													<span class="selectSpan">Customer Number</span>
                                                        <!--<input pInputText name="customerNumber" class="textbox" placeholder="Customer Number" id="customerNumber" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.customerNumber"
                                                        />-->
                                                        <p-autoComplete  inputStyleClass="textbox" [suggestions]="customerNumbers" 
										(completeMethod)="searchCustomerNumbers($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Customer Number Type atleast 3 Characters" id="customerNumber" [(ngModel)]="searchFields.customerNumber" ></p-autoComplete>
                                    
                                                        
                                                </span>
                                        </div>
                                        <div class="ui-g-6 pull-right">
                                                <span class="md-inputfield">
													<span class="selectSpan">Customer Name</span>
                                                        <!--<input pInputText name="customerName" class="textbox" placeholder="Customer Name" id="customerName" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.customerName"
                                                        />-->
                                                  <p-autoComplete  inputStyleClass="textbox" [suggestions]="customerNames" 
										(completeMethod)="searchCustomerNames($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Customer Name Type atleast 3 Characters" id="customerName" [(ngModel)]="searchFields.customerName" ></p-autoComplete>
                        
                                                </span>
                                        </div>
                                </div>
                                <div class="ui-g-12">
                                        <div class="ui-g-6">
                                                <span class="md-inputfield">
													<span class="selectSpan">Order Number</span>
                                                        <input pInputText name="so" id="so" class="textbox" placeholder="Order Number" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.orderNumber"
                                                        />
                                                </span>
                                        </div>
                                        <div class="ui-g-6 pull-right">
                                                <span class="md-inputfield">
													<span class="selectSpan">Sales Contract Number</span>
                                                        <input pInputText name="salesContract" id="salesContract" class="textbox" placeholder="Sales Contract Number" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.salesContractNumber" />
                                                </span>
                                        </div>
                                </div>
                                <div class="ui-g-12">
                                        <div class="ui-g-6">
                                                <span class="md-inputfield">
													<span class="selectSpan">Billing Region</span>
                                                        <!--<input pInputText name="region" id="region" class="textbox" placeholder="Region" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.region"
                                                        />-->
                                                        <p-autoComplete inputStyleClass="textbox" [suggestions]="regions" 
										(completeMethod)="searchRegions($event)" styleClass="wid100"
       								appendTo="body" [minLength]="2" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Billing Region Type atleast 2 Characters" id="region"  [(ngModel)]="searchFields.region" ></p-autoComplete>
                                    
                                                </span>
                                        </div>
                                        <div class="ui-g-6 pull-right">
                                                <span class="md-inputfield">
													<span class="selectSpan">Billing Company</span>
                                                        <!--<input pInputText name="entityName" id="entityName" class="textbox" placeholder="Billing Company" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.entityName" />-->
                                                            <p-autoComplete  inputStyleClass="textbox" [suggestions]="legalEntities" 
										(completeMethod)="searchLegalEntities($event)" styleClass="wid100"
       								appendTo="body" [minLength]="2" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Billing Company Type atleast 2 Characters" id="entityName" [(ngModel)]="searchFields.entityName"></p-autoComplete>
                                    
                                                </span>
                                        </div>
                                </div>
                                
                                <div class="ui-g-12">
                                        <div class="ui-g-6">
                                                <span class="md-inputfield">
													<span class="selectSpan">Product Family</span>
                                                        <!--<input pInputText name="productFamily" id="productFamily" class="textbox" placeholder="Product Family" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.productFamily"
                                                        />-->
                                                        <p-autoComplete  inputStyleClass="textbox" [suggestions]="productFamilies" 
										(completeMethod)="searchProductFamilies($event)" styleClass="wid100"
       								appendTo="body" [minLength]="2" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Product Family Type atleast 2 Characters" id="productFamily" [(ngModel)]="searchFields.productFamily"></p-autoComplete>
                                                </span>
                                        </div>
                                        <div class="ui-g-6 pull-right">
                                                <span class="md-inputfield">
													<span class="selectSpan">Product Line</span>
                                                        <!--<input pInputText name="productLine" id="productLine" class="textbox" placeholder="Product Line" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.productLine" />-->
                                                        <p-autoComplete  inputStyleClass="textbox" [suggestions]="productLines" 
										(completeMethod)="searchProductLines($event)" styleClass="wid100"
       								appendTo="body" [minLength]="2" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Product Line Type atleast 2 Characters" id="productLine" [(ngModel)]="searchFields.productLine"></p-autoComplete>
                                                </span>
                                        </div>
                                </div>
                                <div class="ui-g-12">
                                        <div class="ui-g-6">
                                                <span class="md-inputfield">
													<span class="selectSpan">Asset ID</span>
                                                        <input pInputText name="assetId" id="assetId" class="textbox" placeholder="Asset ID" [ngModelOptions]="{standalone: true}" [(ngModel)]="searchFields.assetId"
                                                        />
                                                </span>
                                        </div>
                                       
                                </div>
                                <!--<div class="ui-g-12">
                                        <div class="ui-g-6">
                                                <span class="selectSpan">{{columns['backlog']}}</span>
                                                <p-dropdown [options]="backlogOptions"  [(ngModel)]="searchFields.backlog" 
                                                [ngModelOptions]="{standalone: true}" appendTo=body name="backlog" [filter]="true"></p-dropdown>
                                        </div>
                                </div>-->
                        </div>
                        
                </form>
                <p-footer>
                                <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix pull-right">
										<button type="button" pButton class="primary-btn" (click)="search()" label="Search"></button>
                                        <button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>
                                </div>
                        </p-footer>
        </p-dialog>

        
