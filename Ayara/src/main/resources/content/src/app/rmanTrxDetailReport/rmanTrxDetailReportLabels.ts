interface ILabels {
	[index: string]: string;
}

export class RmanTrxDetailReportLabels {

	fieldLabels: ILabels;

	constructor() {

		this.fieldLabels = {};

		this.fieldLabels["dealArrangementNumber"] = "Arrangement #";
		this.fieldLabels["shippedQty"] = "Shipped Qty";
		this.fieldLabels["salesTheater"] = "Sales Theater";
		this.fieldLabels["territory"] = "Sales Territory";
		this.fieldLabels["billedAmount"] = "Billed Amt (TC)";
		this.fieldLabels["dealArrangementName"] = "Arrangement";
		this.fieldLabels["productDescription"] = "Product Description";
		this.fieldLabels["sno"] = "Sno";
		this.fieldLabels["revenueRecognize"] = "Revenue Recognize";
		this.fieldLabels["unitSellingPrice"] = "Sell $/U";
		this.fieldLabels["unitListPrice"] = "List $/U";
		this.fieldLabels["serviceStartDate"] = "SVC Sdate";
		this.fieldLabels["dealNumber"] = "Deal#";
		this.fieldLabels["itemType"] = "Item Type";
		this.fieldLabels["dealLineNumber"] = "Deal Line #";
		this.fieldLabels["customerNumber"] = "Cust#";
		this.fieldLabels["sourceLineNumber"] = "Source Line #";
		this.fieldLabels["productName"] = "Product Name";
		this.fieldLabels["allocatedPrice"] = "Allocate Price";
		this.fieldLabels["dealArrangementId"] = "Deal Arrangement Id";
		this.fieldLabels["region"] = "Sales Region";
		this.fieldLabels["arrangementType"] = "Arrangement Type";
		this.fieldLabels["serviceEndDate"] = "SVC Edate";
		this.fieldLabels["transactionPrice"] = "Line AMT$";
		this.fieldLabels["customerName"] = "Customer";
		this.fieldLabels["orderNumber"] = "SO Number";
		this.fieldLabels["orderedQuantity"] = "Booked Quantity";
		this.fieldLabels["po"] = "PO#";
		this.fieldLabels["ssp"] = "SSP";
		this.fieldLabels["fmvAmount"] = "SSP Total";
		this.fieldLabels["exceptionMessage"] = "Exception Message";
		this.fieldLabels["lineStatus"] = "Line Status";
		this.fieldLabels["sourceLineId"] = "Source Line Id";
		this.fieldLabels["sourceHeaderId"] = "Source Header Id";
		this.fieldLabels["bookedDate"] = "Booked Date";
		this.fieldLabels["discountPercent"] = "Discount %";
		this.fieldLabels["productPorfolio"] = "Product Portfolio";
		this.fieldLabels["productFamily"] = "Product Family";
		this.fieldLabels["productLine"] = "Product Line";
		this.fieldLabels["sspMin"] = "SSP Min";
		this.fieldLabels["sspMean"] = "SSP Trx";
		this.fieldLabels["sspMax"] = "SSP Max";
		this.fieldLabels["allocationAmount"] = "Allocated Amount (TC)";
		this.fieldLabels["contApply"] = "Cont Apply";
		this.fieldLabels["deliveredDate"] = "Delivered Date";
		this.fieldLabels["deliveredAmount"] = "Delivered Amt (TC)";
		this.fieldLabels["deliveredAllocAmount"] = "Delivered Allocated Price (TC)";
		this.fieldLabels["invoiceNumber"] = "Invoice#";
		this.fieldLabels["invoiceLineNumber"] = "Invoice Line#";
		this.fieldLabels["invoiceQuantity"] = "Invoiced Qty";
		this.fieldLabels["invoiceCurrency"] = "Invoiced Cur";
		this.fieldLabels["invoiceDate"] = "Invoiced Date";
		this.fieldLabels["deferredAmount"] = "Revenue Deferred(TC)";
		this.fieldLabels["deferredAmountFc"] = "Revenue Deferred(FC)";
		this.fieldLabels["billedAmountFc"] = "Billed Amt (FC)";
		this.fieldLabels["revenueUnbilledFc"] = "Revenue Unbilled (FC)";
		this.fieldLabels["allocationAmountFc"] = "Allocated Amount (FC)";
		this.fieldLabels["deliveredAllocAmountFc"] = "Delivered Allocated Price (FC)";
		this.fieldLabels["sspLow"] = "SSP Low";
		this.fieldLabels["sspHigh"] = "SSP High";
		this.fieldLabels["fxRate"] = "Fx Rate";
		this.fieldLabels["fxDate"] = "Fx Date";
		this.fieldLabels["fxCurrency"] = "Fx Currency";
		this.fieldLabels["bookedCurrency"] = "Transaction Currency";
		this.fieldLabels["periodYear"] = "Period Year";
		this.fieldLabels["quarterName"] = "Quarter Name";
		this.fieldLabels["periodName"] = "Period Name";
		this.fieldLabels["backlog"] = "Backlog";
		this.fieldLabels["contractAsset"] = "Contract Asset (TC)";
		this.fieldLabels["contractLiability"] = "Contract Liability (TC)";
		this.fieldLabels["deferredRevenue"] = "Deferred Revenue (TC)";
		this.fieldLabels["clearing"] = "Clearing (TC)";
		this.fieldLabels["contractAssetFc"] = "Contract Asset (FC)";
		this.fieldLabels["contractLiabilityFc"] = "Contract Liability (FC)";
		this.fieldLabels["deferredRevenueFc"] = "Deferred Revenue (FC)";
		this.fieldLabels["clearingFc"] = "Clearing (FC)";
		this.fieldLabels["beginBal"] = "Revenue Begin Balance (TC)";
		this.fieldLabels["currBal"] = "Revenue Current Balance (TC)";
		this.fieldLabels["endBal"] = "Revenue End Balance (TC)";
		this.fieldLabels["beginBalFc"] = "Revenue Begin Balance (FC)";
		this.fieldLabels["currBalFc"] = "Revenue Current Balance (FC)";
		this.fieldLabels["endBalFc"] = "Revenue End Balance (FC)";
		this.fieldLabels["contractModifiedDate"] = "Contract Modified Date";
		this.fieldLabels["cancelledDate"] = "Cancelled Date";
		this.fieldLabels["cancelledQty"] = "Cancelled Qty";
		this.fieldLabels["salesrep"] = "Sales Rep";
		this.fieldLabels["contReleaseDate"] = "Contingency Release Date";
		this.fieldLabels["note"] = "NOTE Trasaction type";
		this.fieldLabels["elementType"] = "Element Type";
		this.fieldLabels["division"] = "Division";
	}

}
