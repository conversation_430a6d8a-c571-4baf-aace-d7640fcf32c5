export interface RmanTrxDetailReport {
    dealArrangementNumber: any;
    dealArrangementName: any;
    arrangementType: any;
    salesTheater: any;
    region: any;
    territory: any;
    dealNumber: any;
    dealLineNumber: any;
    po: any;
    customerName: any;
    customerNumber: any;
    sourceHeaderId: any;
    orderNumber: any;
    sourceLineId: any;
    sourceLineNumber: any;
    productName: any;
    lineStatus: any;
    orderedQuantity: any;
    bookedCurrency: any;
    bookedDate: any;
    unitListPrice: any;
    unitSellingPrice: any;
    transactionPrice: any;
    discountPercent: any;
    productPorfolio: any;
    productFamily: any;
    productLine: any;
    serviceStartDate: any;
    serviceEndDate: any;
    sspMin: any;
    sspMean: any;
    sspMax: any;
    sspLow: any;
    sspHigh: any;
    ssp: any;
    fmvAmount: any;
    fxRate: any;
    fxCurrency: any;
    fxDate: any;
    allocatedPrice: any;
    allocationAmountFc: any;
    contApply: any;
    shippedQty: any;
    deliveredDate: any;
    deliveredAmount: any;
    invoiceNumber: any;
    invoiceLineNumber: any;
    invoiceQuantity: any;
    invoiceCurrency: any;
    invoiceDate: any;
    billedAmount: any;
    billedAmountFc: any;
    deliveredAllocAmount: any;
    deliveredAllocAmountFc: any;
    deferredAmount: any;
    deferredAmountFc: any;
    revenueRecognize: any;
    revenueUnbilledFc: any;
    exceptionMessage: any;
    periodYear: any;
    quarterName: any;
    periodName: any;
    backlog: any;
    contractAsset: any;
    contractLiability: any;
    deferredRevenue: any;
    clearing: any;
    contractAssetFc: any;
    contractLiabilityFc: any;
    deferredRevenueFc: any;
    clearingFc: any;
    beginBal: any;
    currBal: any;
    endBal: any;
    beginBalFc: any;
    currBalFc: any;
    endBalFc: any;
    activeFlag: any;
    contractModifiedDate: any;
    cancelledDate: any;
    cancelledQty: any;
    salesrep: any;

}
