export interface RmanOrdersInterface {
    origRmanLineId?: any;
    sourceLineId?: any;
    origLastUpdatedBy?: any;
    territory?: any;
    lineNum?: any;
    salesrep?: any;
    shippingEntityId?: any;
    productOrgId?: any;
    billToCountry?: any;
    cancelledQty?: any;
    unitSellingPrice?: any;
    topModelLineId?: any;
    fobPointCode?: any;
    origCreatedBy?: any;
    serviceRefOrder?: any;
    billToLocation?: any;
    scheduleShipDate?: any;
    customerPoNum?: any;
    interfaceProcessId?: any;
    region?: any;
    rmanAcctRuleId?: any;
    orderedQuantity?: any;
    shippingOrgCode?: any;
    taskId?: any;
    lineAttribute6?: any;
    lineAttribute5?: any;
    headerAttribute10?: any;
    lineAttribute8?: any;
    headerAttribute11?: any;
    lineAttribute7?: any;
    headerAttribute12?: any;
    soldToCustomer?: any;
    lineAttribute2?: any;
    headerAttribute13?: any;
    lineAttribute1?: any;
    headerAttribute14?: any;
    origCreationDate?: any;
    lineAttribute4?: any;
    headerAttribute15?: any;
    repUnitSellingPrice?: any;
    lineAttribute3?: any;
    lineAttribute9?: any;
    errorFlag?: any;
    cancelledDate?: any;
    fxRate?: any;
    objVersionNumber?: any;
    lineAttribute12?: any;
    shippedQuantity?: any;
    lineAttribute13?: any;
    lineAttribute10?: any;
    lineAttribute11?: any;
    shipToLocation?: any;
    expenditureType?: any;
    dealNumber?: any;
    billToCustomer?: any;
    lineAttribute14?: any;
    projectId?: any;
    lineAttribute15?: any;
    shipToCustomer?: any;
    refOrderNumber?: any;
    dealLineNumber?: any;
    bookingCurrency?: any;
    lineType?: any;
    projectNumber?: any;
    orderType?: any;
    revrecAcctRule?: any;
    roleName?: any;
    bookedDate?: any;
    accountingRuleName?: any;
    serviceEndDate?: any;
    domesticInternational?: any;
    taskNumber?: any;
    shipToCountry?: any;
    revenueCategory?: any;
    processedDate?: any;
    orderNumModifier?: any;
    revrecStDate?: any;
    fxDate?: any;
    revrecAcctScope?: any;
    priceList?: any;
    orginalOrderNumber?: any;
    projectCost?: any;
    sourceHeaderId?: any;
    lastUpdateDate?: any;
    orginalLineId?: any;
    fob?: any;
    unitListPrice?: any;
    bookingEntityName?: any;
    createdBy?: any;
    lastUpdatedBy?: any;
    revrecDelay?: any;
    productOrgName?: any;
    sourceLineNumber?: any;
    taskName?: any;
    creationDate?: any;
    shippableFlag?: any;
    productName?: any;
    lineStatus?: any;
    stdWarranty?: any;
    origLastUpdateDate?: any;
    linkToLineId?: any;
    orginalLineNumber?: any;
    repUnitListPrice?: any;
    rmanLineId?: any;
    headerAttribute1?: any;
    errorMessage?: any;
    headerAttribute6?: any;
    headerAttribute7?: any;
    headerAttribute8?: any;
    headerAttribute9?: any;
    headerAttribute2?: any;
    fmvHistoryFlag?: any;
    headerAttribute3?: any;
    lineCategoryCode?: any;
    servicePeriod?: any;
    headerAttribute4?: any;
    projectName?: any;
    headerAttribute5?: any;
    serviceRefOrdLineNum?: any;
    interfaceStatus?: any;
    revrecEndDate?: any;
    fulfilledQuantity?: any;
    orderStatus?: any;
    serviceStartDate?: any;
    endCustomer?: any;
    revrecHoldType?: any;
    actualFulfilledDate?: any;
    carveOutFlag?: any;
    serviceDuration?: any;
    serviceNumber?: any;
    productId?: any;
    invoiceFlag?: any;
    bookingEntityId?: any;
    extWarranty?: any;
    orderSource?: any;
    revrecAccount?: any;
    orderNumber?: any;
    orderedDate?: any;
    lineCost?: any;
    releasedStatus?: any;
}
