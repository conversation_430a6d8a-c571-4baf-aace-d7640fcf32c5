export interface RmanDealArrangements {
  dealCosedDate: any;
  attribute30: any;
  lastUpdateDate: any;
  dealArrangementBasis: any;
  dealArrangementKey: any;
  dealArrangementTotal: any;
  changeReason: any;
  attribute29: any;
  attribute28: any;
  dealArrangementType: any;
  attribute27: any;
  dealApprovalDate: any;
  attribute26: any;
  masterArrgId: any;
  dealName: any;
  attribute3: any;
  createdBy: any;
  attribute2: any;
  lastUpdatedBy: any;
  attribute1: any;
  dealArrangementSource: any;
  salesContact: any;
  legalEntityId: any;
  creationDate: any;
  attribute9: any;
  attribute8: any;
  attribute7: any;
  attribute6: any;
  attribute5: any;
  attribute4: any;
  allocationEligible: any;
  dealAgreementId: any;
  dealArrangementStatus: any;
  attribute10: any;
  endCustomerName: any;
  dealArrangementNumber: any;
  masterArrgName: any;
  revManagerId: any;
  attribute14: any;
  attribute13: any;
  arrangementCurrency: any;
  attribute12: any;
  msaName: any;
  attribute11: any;
  dealApprovedBy: any;
  dealArrangementName: any;
  endCustomerNumber: any;
  customerContact: any;
  dealId: any;
  dealArrangementSaMe: any;
  dealNumber: any;
  attribute21: any;
  salesNodeLevel4: any;
  attribute20: any;
  msaNumber: any;
  salesNodeLevel2: any;
  salesNodeLevel3: any;
  dealArrangementQtr: any;
  attribute25: any;
  attribute24: any;
  attribute23: any;
  revAccountantId: any;
  attribute22: any;
  reasonCode: any;
  masterArrgFlag: any;
  salesNodeLevel1: any;
  dealArrangementId: any;
  legalEntityName: any;
  attribute18: any;
  attribute17: any;
  attribute16: any;
  attribute15: any;
  dealAgreementName: any;
  attribute19: any;
  poNumber: any;
}
