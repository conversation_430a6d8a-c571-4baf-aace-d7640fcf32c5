.smart-pivot-table {
    max-height: 500px;
}

.smart-table-container tbody tr:hover, .smart-table-container tbody tr td:hover  {
     background: #ddf4ef !important 
}

.smart-table-container tfoot tr{
    background: #d9e1e7 !important;
}

.smart-table-container th{
    background: #d9e1e7 !important;
}

.noDataContainer{
    width: 100%;
    display: flex;
    justify-content: center;
    height: 500px;
    align-items: center;
}
.float{
    position: absolute;
    width: 43px;
    height: 25px;
    background-color: #262d74;
    color: #fff !important;
    border-radius: 50px;
    text-align: center;
    box-shadow: 2px 2px 3px #ded3de;
    z-index: 1;
    top: 145px;
    left: -8px;
}
.float:active{
    background-color: #262d74 !important;
    color: #fff !important;
}

.my-float{
	margin-top:22px;
}
.fa{
    padding: 4px !important;
}
