import { HttpClient,HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require: any;
const appSettings = require('../appsettings');

@Injectable()
export class ReportsService {

  constructor(private http: HttpClient) { }

  getAllReports(search:any): Promise<any[]> {
    let serviceUrl = appSettings.apiUrl + '/ayaraReportsSearch?';
    let searchString = search;

    if (searchString == '') {
      serviceUrl = serviceUrl + 'search=%25';
    }
    else {
      serviceUrl = serviceUrl + 'search=' + searchString;
    }

    return this.http.get(serviceUrl).toPromise().then((data: any) => {
      return data;
    });
  }
  filterExceptionReports(paginationOptions: any, filterData: any): Promise<any> {
          let serviceUrl = `${appSettings.apiUrl}/ayaraExceptionReportsSearch`;

          // Add pagination query params
          if (paginationOptions.pageNumber !== undefined && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageSize !== undefined) {
            serviceUrl += `?page=${paginationOptions.pageNumber}&size=${paginationOptions.pageSize}`;
          }

          // Convert filterData to HttpParams
          let params = new HttpParams();
          for (const key in filterData) {
            if (filterData[key] !== null && filterData[key] !== undefined && filterData[key] !== '') {
              params = params.set(key, filterData[key]);
            }
          }

          return this.http.get(serviceUrl, { params, responseType: 'text' }).toPromise().then((res: any) => {
            return JSON.parse(res); // returns full object with data, count, etc.
          });
        }

    downloadCsv(data: any[]) {
      return this.http.post(`${appSettings.apiUrl}/exportExceptionsReport`, data, {
        responseType: 'blob'
      });
    }
}