
    <div class="content-section implementation">
     
    </div>
    <div class="card-wrapper">
		<div class="container-fluid">
		  <div class="row">
			<div class="col-md-12">
			  <div class="card-block">
                <p-panel header="Analytical Reports - Favorites" [style]="{'margin-bottom':'20px'}">
                    <p-header>
                        <a  (click)="goToAllReports()" class="add-column pull-right icons-position">All Reports</a>
                        <div class="pull-right">
                        <button type="submit" pButton class="secondary-btn" [routerLink]="['/reports/analytical/report']" label="Create New Report"></button>
                        </div>
                    </p-header>
                <div class="x-scroll">
                    <p-table class="ui-datatable arrangementMgrTbl" #dt [loading]="loading" [value]="reportList" selectionMode="single" 
                    (onLazyLoad)="getAllReports($event)" [lazy]="true" [paginator]="true" [rows]="pageSize"
                    [totalRecords]="totalElements"  scrollable="true" >

                        <ng-template pTemplate="header" class="arrangementMgrTblHead">
                            <tr>
                            <th>{{columns['sno']}}</th>
                            <th>{{columns['reportName']}}</th>
                            <th>{{columns['createdDate']}}</th>
                            <th>{{columns['createdBy']}}</th>
                            <th>{{columns['lastModifyDate']}}</th>
                            <th>{{columns['lastModifyBy']}}</th>
                            <th></th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-rowData let-reports let-i = rowIndex>
                            <tr [pSelectableRow]="rowData">
                            <td>{{i+1}}</td>
                            <td>
                                <a style="text-decoration: none;" [routerLink]="['/reports/analytical/report',reports.id]" title="Open">{{reports.reportName}}</a>
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <a  class="star-icon" (click)="markUnmarkAsFavorite(reports)" title="Favorite">
                                  <em [ngClass]="(reports.favoriteFlag == 'Y') ? 'fa fa-star' : 'fa fa-star-o'"></em>
                                </a>
                                <a class="icon-edit" [routerLink]="['/reports/analytical/report',reports.id]" title="Edit"></a>
                                <a class="icon-delete" (click)="deleteReport(i)" title="Delete"></a>
                            </td>
                        </tr>
                        </ng-template>
                        <ng-template pTemplate="emptymessage" let-columns>
                            <tr *ngIf="!columns">
                              <td class="no-data">{{noData}}</td>
                            </tr>
                          </ng-template>
                    </p-table>
                </div>
                </p-panel>
              </div>
            </div>
          </div>
        </div>
    </div>

<router-outlet></router-outlet>
