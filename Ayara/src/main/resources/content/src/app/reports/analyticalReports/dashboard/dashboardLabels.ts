interface ILabels {
    [index: string]: string;
}

export class DashboardLabels {

   fieldLabels: ILabels;

constructor() {

this.fieldLabels = {};

   this.fieldLabels["sno"] = "SNO";
   this.fieldLabels["reportName"] = "Report Name";
   this.fieldLabels["createdDate"] = "Created Date";
   this.fieldLabels["createdBy"] = "Created By";
   this.fieldLabels["lastModifyDate"] = "Last Modified Date";
   this.fieldLabels["lastModifyBy"] = "Last Modified By";
}

}
