.headerDrillDown {
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 26px;
    /*background: #337ab7;*/
    color: white;
}
.timeRangeText { 
    padding: 16px;
    font-size: 18px;
}
.headerTitle {
    position: absolute;
    width: 100%;
    text-align: center;
}
.reportContainer {
    display: flex;
    justify-content: flex-start;
    padding: 32px;
    flex-wrap: wrap;
}
.reportItem {
    flex-direction: column;
    background: white;
    position: relative;
    display: flex;
    justify-content: space-between;
    box-shadow: 4px 4px 8px rgba(0,0,0,0.2);
    margin-left: 16px;
    font-size: 14px;
    margin-bottom: 16px;
}
.dimensionGroup {
    padding: 0px 16px;
}
.dimensionItems {
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    border: 1px solid #eee;
}
.dimensionTitle {
    padding: 12px 6px 4px 0px;
    font-size: 14px;
}
.dimensionItems span {
    color: brown;
    margin: 4px;
    padding: 4px;
    font-size: 12px;
}
.timeRangeDashboard {
    padding: 32px 16px 8px 18px;
}
.reportTitle {
    padding: 16px;
    margin-top: 16px;
    color: white;
    background: #337ab7;
    cursor: pointer;
    display: flex;
    justify-content: space-around;
}
.fa-eye{
    font-size: 16px;
}
.reportItem .fa.fa-trash {
    position: absolute;
    color: #337ab7;
    right: 24px;
    top: 24px;
    cursor: pointer;
    font-size: 16px;
}
.titleText {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
.reportTitle:hover .titleText {
    white-space: normal;
}
.reportNew span{
    color: #337ab7;
    font-size: 16px;
}
.reportNew {
    width: 234px;
    cursor: pointer;
    min-height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.reportNew .fa.fa-plus {
    font-size: 32px;
    color: #337ab7;
    margin: 16px;
    margin-top: -8px;
}
/** .lds-spinner {
    position: fixed;
    top: 40%;
    left: 48%;
} */

.lds-spinner{
    padding: 0px;
    position: absolute;
    padding-left: 600px;
}
.noDataContainer{
    width: 100%;
    display: flex;
    justify-content: center;
    height: 75px;
    align-items: center;
    text-shadow: 0 0 black;
}