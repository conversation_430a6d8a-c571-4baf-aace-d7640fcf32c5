
    <div class="content-section implementation">
      <p-toast [preventDuplicates]='true'></p-toast>
    </div>
    <div *ngIf="!drillDownService.allAndFavoritesSwitch" class="card-wrapper">
		<div class="container-fluid">
		  <div class="row">
			<div class="col-md-12">
			  <div class="card-block">
                <p-panel header="Analytical Reports" [style]="{'margin-bottom':'20px'}">
                    <p-header>
                        <a  (click)="goToFavorites()" class="add-column pull-right icons-position">Favorites</a>
                        <div class="pull-right">
                        <button type="submit" pButton class="secondary-btn" [routerLink]="['/reports/analytical/report']" label="Create New Report"></button>
                        </div>
                    </p-header>
                <div class="x-scroll">
                    <p-table class="ui-datatable arrangementMgrTbl" #dt [loading]="loading" [value]="reportList" selectionMode="single" 
                    (onLazyLoad)="getAllReports($event)" [lazy]="true" [paginator]="true" [rows]="pageSize"
                    [totalRecords]="totalElements"  scrollable="true" >

                        <ng-template pTemplate="header" class="arrangementMgrTblHead">
                            <tr>
                            <th>{{columns['sno']}}</th>
                            <th>{{columns['reportName']}}</th>
                            <th>{{columns['createdDate']}}</th>
                            <th>{{columns['createdBy']}}</th>
                            <th>{{columns['lastModifyDate']}}</th>
                            <th>{{columns['lastModifyBy']}}</th>
                            <th>Action</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-rowData let-reports let-i = rowIndex>
                            <tr [pSelectableRow]="rowData">
                            <td>{{i+1}}</td>
                            <td>
                                <a style="text-decoration: none;">{{reports.reportName}}</a>
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <a class="star-icon" (click)="markUnmarkAsFavorite(reports)" title="Favorite">
                                  <em [ngClass]="(reports.favoriteFlag == 'Y') ? 'fa fa-star' : 'fa fa-star-o'"></em>
                                </a>
                                <a class="star-icon" [routerLink]="['/reports/analytical/report',reports.id]" title="Open"><em class="fa fa-folder-open"></em></a>
                                <a  class="star-icon" [routerLink]="['/reports/analytical/report',reports.id]" title="Edit"><em class="fa fa-pencil-square-o" aria-hidden="true"></em></a>
                                <a  class="star-icon" (click)="deleteReport(i)" title="Delete"><em class="fa fa-trash" aria-hidden="true"></em></a>
                            </td>
                        </tr>
                        </ng-template>
                        <ng-template pTemplate="emptymessage" let-columns>
                            <tr *ngIf="!columns">
                              <td class="no-data">{{noData}}</td>
                            </tr>
                          </ng-template>
                    </p-table>
                </div>
                </p-panel>
              </div>
            </div>
          </div>
        </div>
    </div>
<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>

<dashborad-favorites-reports *ngIf="drillDownService.allAndFavoritesSwitch">loading...</dashborad-favorites-reports>
