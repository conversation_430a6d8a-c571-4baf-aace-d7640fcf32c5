interface ILabels {
    [index: string]: string;
}

export class RmanDefRollforwardReportVLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["dealArrangementNumber"] = "Arrangement Number";
        this.fieldLabels["dealArrangementName"] = "Arrangement Name";
        this.fieldLabels["dealArrangementBasis"] = "Arrangement Basis";
        this.fieldLabels["dealArrangementStatus"] = "Status";
        this.fieldLabels["legalEntityName"] = "Legal Entity";
        this.fieldLabels["dealArrangementSource"] = "Arrangement Source";
        this.fieldLabels["customerPoNum"] = "PO#";
        this.fieldLabels["dealName"] = "Deal Name";
        this.fieldLabels["dealNumber"] = "Deal Number";
        this.fieldLabels["dealLineNumber"] = "Deal Line #";
        this.fieldLabels["sourceHeaderId"] = "Source Header ID";
        this.fieldLabels["orderNumber"] = "SO#";
        this.fieldLabels["sourceLineId"] = "Source Line ID";
        this.fieldLabels["sourceLineNumber"] = "SO Line #";
        this.fieldLabels["productName"] = "Product Name";
        this.fieldLabels["orderedQuantity"] = "Ordered Qty";
        this.fieldLabels["uom"] = "UOM";
        this.fieldLabels["transactionCurrency"] = "T-Curr";
        this.fieldLabels["unitListPrice"] = "List $/U";
        this.fieldLabels["unitNetPrice"] = "SP $/U";
        this.fieldLabels["lineAmount"] = "Line Amount";
        this.fieldLabels["discountPercent"] = "Discount %";
        this.fieldLabels["productPortfolio"] = "Product Portfolio";
        this.fieldLabels["productFamily"] = "Product Family";
        this.fieldLabels["productLine"] = "Product Line";
        this.fieldLabels["shipToCustomer"] = "Ship To Customer Name";
        this.fieldLabels["shipToCustomerNumber"] = "Ship To Customer Number";
        this.fieldLabels["billToCustomer"] = "Customer";
        this.fieldLabels["billToCustomerNumber"] = "Cust #";
        this.fieldLabels["salesTheater"] = "Sales Theater";
        this.fieldLabels["salesRegion"] = "Sales Region";
        this.fieldLabels["salesTeritory"] = "Sales Territory";
        this.fieldLabels["lineCost"] = "Line Cost";
        this.fieldLabels["bundleAttributedList"] = "Bundle Attributed List";
        this.fieldLabels["bundleAttributedNet"] = "Bundle Attributed Net";
        this.fieldLabels["startDate"] = "Start Date";
        this.fieldLabels["endDate"] = "End Date";
        this.fieldLabels["sspAmount"] = "SSP Amt";
        this.fieldLabels["allocationAmount"] = "Allocation Amt (TC)";
        this.fieldLabels["bookedAmount"] = "Booked Amt";
        this.fieldLabels["deliveredAmount"] = "Delivered Amt";
        this.fieldLabels["deliveredAllocationAmount"] = "Del Allocation Amt (TC)";
        this.fieldLabels["postBillingDeferrals"] = "Post Billing Def (TC)";
        this.fieldLabels["beginingBalance"] = "Beginning BL (FC)";
        this.fieldLabels["additionAmount"] = "Addition (FC)";
        this.fieldLabels["releaseAmount"] = "Release (FC)";
        this.fieldLabels["endingBalance"] = "Ending BL (FC)";
        this.fieldLabels["revenueRecognized"] = "Revenue Recognized (FC)";
        this.fieldLabels["deliveredAllocationAmountFc"] = "Del Allocation Amt (FC)";
        this.fieldLabels["postBillingDeferralsFc"] = "Post Billing Def (FC)";
        this.fieldLabels["deliveredDate"] = "Delivered Date";
        this.fieldLabels["contName"] = "Cont Apply";
        this.fieldLabels["elementType"] = "Element Type";
        this.fieldLabels["invoiceNum"] = "Invoice #";
        this.fieldLabels["note"] = "NOTE Trasaction type";
        this.fieldLabels["division"] = "Division";
        this.fieldLabels["allocationAmountFc"] = "Allocation Amt (FC)";

    }

}
