export interface RmanDefRollforwardReportV {
    sourceLineId: any;
    deliveredAllocationAmount: any;
    transactionCurrency: any;
    endDate: any;
    sourceHeaderId: any;
    bundleAttributedNet: any;
    salesTeritory: any;
    periodName: any;
    salesRegion: any;
    dealArrangementBasis: any;
    productPortfolio: any;
    unitListPrice: any;
    shipToCustomerNumber: any;
    dealName: any;
    lineAmount: any;
    sspAmount: any;
    bookedAmount: any;
    dealArrangementSource: any;
    sourceLineNumber: any;
    periodYear: any;
    unitNetPrice: any;
    customerPoNum: any;
    productName: any;
    quarterName: any;
    releaseAmount: any;
    productLine: any;
    orderedQuantity: any;
    endingBalance: any;
    revenueRecognized: any;
    dealArrangementStatus: any;
    dealArrangementNumber: any;
    postBillingDeferrals: any;
    salesTheater: any;
    dealArrangementName: any;
    allocationAmount: any;
    billToCustomerNumber: any;
    discountPercent: any;
    dealNumber: any;
    billToCustomer: any;
    bundleAttributedList: any;
    shipToCustomer: any;
    beginingBalance: any;
    dealLineNumber: any;
    startDate: any;
    periodDetail: any;
    dealArrangementId: any;
    uom: any;
    additionAmount: any;
    legalEntityName: any;
    productFamily: any;
    orderNumber: any;
    lineCost: any;
    deliveredAmount: any;
    deliveredAllocationAmountFc: any;
    postBillingDeferralsFc: any;
}
