interface ILabels {
    [index: string]: string;
}

export class RmanGlAccountsLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["createdBy"] = "Created By";
        this.fieldLabels["lastUpdatedBy"] = "Last Updated By";
        this.fieldLabels["segment10"] = "Segment10";
        this.fieldLabels["accountType"] = "Account Type";
        this.fieldLabels["accountId"] = "Account ID";
        this.fieldLabels["sourceLedgerId"] = "Source Ledger ID";
        this.fieldLabels["creationDate"] = "Creation Date";
        this.fieldLabels["segment2"] = "Division";
        this.fieldLabels["segment1"] = "Company";
        this.fieldLabels["enabledFlag"] = "Enabled Flag";
        this.fieldLabels["lastUpdateDate"] = "Last Updated Date";
        this.fieldLabels["segment8"] = "Segment8";
        this.fieldLabels["segment7"] = "Segment7";
        this.fieldLabels["segment9"] = "Segment9";
        this.fieldLabels["accountingString"] = "Accounting String";
        this.fieldLabels["segment4"] = "Account";
        this.fieldLabels["ledgerId"] = "Ledger ID";
        this.fieldLabels["segment3"] = "Department";
        this.fieldLabels["segment6"] = "Unspecified";
        this.fieldLabels["segment5"] = "TCID";
        this.fieldLabels["sourceAccountId"] = "Source Account ID";
    }

}
