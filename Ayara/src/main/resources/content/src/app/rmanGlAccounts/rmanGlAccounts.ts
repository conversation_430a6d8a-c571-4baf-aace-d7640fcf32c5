export interface RmanGlAccounts {
    createdBy?: any;
    lastUpdatedBy?: any;
    segment10?: any;
    accountType?: any;
    accountId?: any;
    sourceLedgerId?: any;
    creationDate?: any;
    segment2?: any;
    segment1?: any;
    enabledFlag?: any;
    lastUpdateDate?: any;
    segment8?: any;
    segment7?: any;
    segment9?: any;
    accountingString?: any;
    segment4?: any;
    ledgerId?: any;
    segment3?: any;
    segment6?: any;
    segment5?: any;
    sourceAccountId?: any;
}
