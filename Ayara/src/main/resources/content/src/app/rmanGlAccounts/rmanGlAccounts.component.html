	<div class="content-section implementation">
	</div>

	<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>


     <div class="card-wrapper">
          <div class="container-fluid">
            <div class="row">
              <div class="col-md-12">
                <div class="card-block">
	<p-panel header="GL Accounts"  [toggleable]="false" (onBeforeToggle)=onBeforeToggle($event)>

	<p-header>
		 <div class="pull-right icons-list" *ngIf="collapsed">
        <a (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
          <a  *isAuthorized="['write','MAC']" (click)="showDialogToSearch()" title="Search"><em class="fa fa-search"></em></a>
          <a  *isAuthorized="['upload','MAC']" (click)="importFile()" title="Import"><em class="fa fa-upload"></em></a>
          <a  (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
          <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
              <div class="user-popup">
                <div class="content overflow">
                  <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()"/>
                  <label for="selectall">Select All</label>
                  <a class="close" title="Close" (click)="closeConfigureColumns($event)" >&times;</a>
                  <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                    <ng-template let-col let-index="index" pTemplate="item">
                      <div *ngIf="col.drag">
                      <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" 
                       (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                        <div class="drag">
                          <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)"/>
                          <label>{{col.header}}</label>
                        </div>
                      </div>
                      </div>
                      <div *ngIf="!col.drag">
                      <div class="ui-helper-clearfix">
                        <div>
                          <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"/>
                          <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                        </div>
                      </div>
                      </div>
                    </ng-template>
                    </p-listbox>
          
                </div>
          
                <div class="pull-right">
                  <a class="configColBtn" (click)="saveColumns()">Save</a>
                  <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
                </div>
          
              </div>
            </div>
    
		</div>
	</p-header>

     <div class="x-scroll">
  <p-table  [loading]="loading" class="ui-datatable arrangementMgrTbl" #dt id="glaccounts-dt" [value]="rmanGlAccountsList" selectionMode="single"   
  (onRowSelect)="onRowSelect($event)"  (onLazyLoad)="getRmanGlAccounts($event)" [lazy]="true" 
  [paginator]="true" [rows]="pageSize" [totalRecords]="totalElements"  
  scrollable="true" [columns]="columns" [resizableColumns]="true" columnResizeMode="expand">
  <ng-template pTemplate="colgroup" let-columns>
      <colgroup>
        <col *ngFor="let col of columns">
      </colgroup>
    </ng-template>

     
    <ng-template pTemplate="header" class="arrangementMgrTblHead">
      <tr>
        <ng-container *ngFor="let col of columns">
          <th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
          <th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
        </ng-container>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-rowData let-rmanGlAccounts let-columns="columns">
      <tr [pSelectableRow]="rowData" [pSelectableRowIndex]="rowIndex">
       
        <ng-container *ngFor="let col of columns" >
          <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
            {{rowData[col.field]}}
          </td>
        
          <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
            {{rowData[col.field]}}
          </td>
        
          <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
            {{rowData[col.field] | date: 'MM/dd/yyyy'}}
          </td>
        
          <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
            {{rowData[col.field] | round}}
          </td>
        </ng-container>
              
      </tr>                 
    </ng-template>
  <ng-template pTemplate="emptymessage" let-columns>
      <tr *ngIf="!columns">
          <td class="no-data">{{noData}}</td>
      </tr>
  </ng-template>
  </p-table>  
  </div>
</p-panel>
</div>
</div>
</div>
</div>
     </div>

<div class="lds-roller" style="top: 0px;" *ngIf="uploadLoading"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
<p-dialog header="Upload  GL Accounts " width="500" [(visible)]="_uploadService.uploadDialog" [draggable]="true" 
  showEffect="fade" [modal]="true" (onAfterHide)="cancelGLAccountsUpload()" >
  <p-fileUpload id="upload-bookings-input" name="file"
     customUpload="true" (uploadHandler)="fileUploadHandler($event, '/upload/glaccounts')"
     accept=".csv"  invalidFileTypeMessageSummary="{0}:Invalid file Type">
  </p-fileUpload>
  <progress-bar></progress-bar>
</p-dialog>
<p-dialog header="Search" width="800" [(visible)]="displaySearchDialog" [draggable]="true"  showEffect="fade" [modal]="true" [blockScroll]="true">
     <form>
          <div class="ui-grid ui-grid-responsive ui-fluid">
               <div class="ui-g-12">
                    <div class="ui-g-6">
                         <span class="md-inputfield">
                              <span class="selectSpan">Source Account ID</span>
                              <input pInputText class="textbox" placeholder="Source Account ID" name="accountId"  id="accountId"  [ngModelOptions]="{standalone: true}" [(ngModel)]="rmanGlAccountsSearch.sourceAccountId" />
                         </span> 
                    </div>
                    <div class="ui-g-6 pull-right">
                         <span class="md-inputfield">
                              <span class="selectSpan">Accounting String</span>
                              <input pInputText  name="accountingString" class="textbox" placeholder="Accounting String"  id="accountingString"   [ngModelOptions]="{standalone: true}" [(ngModel)]="rmanGlAccountsSearch.accountingString"/>
                        </span>
                    </div>
               </div>
          </div>
     </form>
     <p-footer>
          <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
               <button type="submit" pButton class="primary-btn" label="Search" (click)="search()"></button>
               <button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false;" label="Cancel"></button>
          </div>
     </p-footer>
</p-dialog>
<p-dialog header="RmanGlAccounts" width="500" [(visible)]="displayDialog" [draggable]="true"  showEffect="fade" [modal]="true" (onHide)="cancelAddEdit()">
     <form (ngSubmit)="save()">
          <div class="ui-grid ui-grid-responsive ui-fluid" *ngIf="rmanGlAccounts">
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="accountId">{{columns['accountId']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="accountId" name="accountId" required [(ngModel)]="rmanGlAccounts.accountId" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="sourceAccountId">{{columns['sourceAccountId']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="sourceAccountId" name="sourceAccountId" required [(ngModel)]="rmanGlAccounts.sourceAccountId" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="sourceLedgerId">{{columns['sourceLedgerId']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="sourceLedgerId" name="sourceLedgerId" required [(ngModel)]="rmanGlAccounts.sourceLedgerId" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="ledgerId">{{columns['ledgerId']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="ledgerId" name="ledgerId" required [(ngModel)]="rmanGlAccounts.ledgerId" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="accountingString">{{columns['accountingString']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="accountingString" name="accountingString" required [(ngModel)]="rmanGlAccounts.accountingString" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="segment1">{{columns['segment1']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="segment1" name="segment1" required [(ngModel)]="rmanGlAccounts.segment1" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="segment2">{{columns['segment2']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="segment2" name="segment2" required [(ngModel)]="rmanGlAccounts.segment2" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="segment3">{{columns['segment3']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="segment3" name="segment3" required [(ngModel)]="rmanGlAccounts.segment3" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="segment4">{{columns['segment4']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="segment4" name="segment4" required [(ngModel)]="rmanGlAccounts.segment4" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="segment5">{{columns['segment5']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="segment5" name="segment5" required [(ngModel)]="rmanGlAccounts.segment5" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="segment6">{{columns['segment6']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="segment6" name="segment6" required [(ngModel)]="rmanGlAccounts.segment6" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="segment7">{{columns['segment7']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="segment7" name="segment7" required [(ngModel)]="rmanGlAccounts.segment7" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="segment8">{{columns['segment8']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="segment8" name="segment8" required [(ngModel)]="rmanGlAccounts.segment8" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="segment9">{{columns['segment9']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="segment9" name="segment9" required [(ngModel)]="rmanGlAccounts.segment9" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="segment10">{{columns['segment10']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="segment10" name="segment10" required [(ngModel)]="rmanGlAccounts.segment10" /></div>
               </div>
               <div class="ui-grid-row">
                    <span class="selectSpan"> Account Type </span>
                    <p-dropdown [options]="rmanLookupsV"  [(ngModel)]="rmanGlAccounts.accountType" appendTo=body name="accountType" [filter]="true"></p-dropdown>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="enabledFlag">{{columns['enabledFlag']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="enabledFlag" name="enabledFlag" required [(ngModel)]="rmanGlAccounts.enabledFlag" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="creationDate">{{columns['creationDate']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="creationDate" name="creationDate" required [(ngModel)]="rmanGlAccounts.creationDate" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="createdBy">{{columns['createdBy']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="createdBy" name="createdBy" required [(ngModel)]="rmanGlAccounts.createdBy" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="lastUpdateDate">{{columns['lastUpdateDate']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="lastUpdateDate" name="lastUpdateDate" required [(ngModel)]="rmanGlAccounts.lastUpdateDate" /></div>
               </div>
               <div class="ui-grid-row">
                    <div class="ui-grid-col-4"><label for="lastUpdatedBy">{{columns['lastUpdatedBy']}}</label></div>
                    <div class="ui-grid-col-8"><input pInputText id="lastUpdatedBy" name="lastUpdatedBy" required [(ngModel)]="rmanGlAccounts.lastUpdatedBy" /></div>
               </div>
          </div>
     </form>
     <footer>
          <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
               <button type="button" pButton icon="fa-close" (click)="cancelAddEdit()" label="Cancel"></button>
               <button type="submit" pButton icon="fa-check" label="Save" (click)="save()"></button>
          </div>
     </footer>
</p-dialog>

