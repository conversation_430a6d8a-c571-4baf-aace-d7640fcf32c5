import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require: any;
const appSettings = require('../appsettings');

@Injectable()
export class ConcurrentProgramsService {

  constructor(private http: HttpClient) { }

  executeConcurrentprogram(programName: any): Promise<any[]> {
    let releaseUrl: any;
    if (programName == 'PROCESS_DEALS') {
      releaseUrl = appSettings.apiUrl + '/revmantra/processcontracts';
    } else if (programName == 'PROCESS_BILLINGS') {
      releaseUrl = appSettings.apiUrl + '/revmantra/processbillings';
    } else if (programName == 'IMPORT_BOOKINGS') {
      releaseUrl = appSettings.apiUrl + '/revmantra/importbookings';
    } else if (programName == 'IMPORT_DISTRIBUTIONS') {
      releaseUrl = appSettings.apiUrl + '/revmantra/importdistributions';
    } else if (programName == 'PROCESS_CONT_RELEASE') {
      releaseUrl = appSettings.apiUrl + '/revmantra/contrelease';
    } else if (programName == 'PROCESS_FMV_DEALS') {
      releaseUrl = appSettings.apiUrl + '/revmantra/dealFMVSubmitProcess';
    } else if (programName == 'PROCESS_VC_UPDATE') {
      releaseUrl = appSettings.apiUrl + '/revmantra/dealVCUpdateProcess?arrangementId=0';
    } else if (programName == 'PROCESS_DEAL_ALLOCATIONS') {
      releaseUrl = appSettings.apiUrl + '/revmantra/dealAllocationProcess?arrangementId=0';
    } else if (programName == 'PROCESS_UPDATE_POB') {
      releaseUrl = appSettings.apiUrl + '/revmantra/dealPOBUpdateProcess';
    } else if (programName == 'IMPORT_ORDER_EVENTS') {
      releaseUrl = appSettings.apiUrl + '/revmantra/processEvents';
    } else if (programName == 'LT_ST_REVERSE_ENTRY') {
      releaseUrl = appSettings.apiUrl + '/revmantra/reverseLTandSTAccountEntry';
    } else if (programName == 'CL_ACCOUNT_ADJ_PROG') {
      releaseUrl = appSettings.apiUrl + '/revmantra/clAccountAdjustmentsJob';
    } else if (programName == 'CL_REVERSAL_ACCOUNT_ADJ_PROG') {
      releaseUrl = appSettings.apiUrl + '/revmantra/clRevrsalAccountAdjustmentsJob';
    } else if(programName == 'GL_JE_IMPORT_PROG'){
	  releaseUrl = appSettings.apiUrl + '/journalImport';
	} else if(programName == 'USAGES_CONGA_BILLING_SCHEDULES_JOB'){
	  releaseUrl = appSettings.apiUrl + '/usages/processBillingSchedules';
	} else if (programName == 'AYARA_ANALITICAL_DATA_UPDATE') {
      releaseUrl = appSettings.apiUrl + '/revmantra/analyticalDataUpdateProcess';
    }
    return this.http.post(releaseUrl,null).toPromise().then((data: any) => {
      return data;
    });
  }
  	
  runJournalImport(account: any,rcNumber: any){
	  let serviceUrl = appSettings.apiUrl + '/journalImport?';
	  serviceUrl = serviceUrl + 'account='+account+'&rcNumber='+rcNumber;
	  return this.http.post(serviceUrl,null).toPromise().then((data: any) => {
        return data;
      });
  }	
  
  releaseContingency(headerId:any,fromDate:any,toDate:any,customerNumber:any,productGroup:any,
    productFamily:any, productLine:any,orderNumber:any,customerPo:any, releaseDate: any){

      let serviceUrl = appSettings.apiUrl + '/revmantra/releaseContingency?';

      serviceUrl = serviceUrl + 'headerId='+headerId+'&fromDate='+fromDate+'&toDate='+ toDate+
              '&customerNumber='+customerNumber+'&productGroup='+productGroup+'&productFamily='+productFamily+
              '&productLine='+productLine+'&orderNumber='+orderNumber+'&customerPo='+customerPo+'&releaseDate='+releaseDate;

        
      return this.http.post(serviceUrl,null).toPromise().then((data: any) => {
        return data;
      });

  }
  
  customersMerge(fromCustomer:any,toCustomer:any){

      let serviceUrl = appSettings.apiUrl + '/customerMergeProcess?';
      serviceUrl = serviceUrl + 'fromCustomer='+fromCustomer+'&toCustomer='+toCustomer;
      return this.http.post(serviceUrl,null);
  }

  exportArchiveFiles(fileType: any, groupId: any, fileName: any) {
    return appSettings.apiUrl + '/rmanArchiveFileExport?fileType=' + fileType + '&groupId=' + groupId + '&fileName=' + fileName;
  }

  downloadUploadedFile(jobId) {
    return appSettings.apiUrl + '/downloadFile/' + jobId;
  }

  fetchUploadLogs(paginationOptions: any) {
    let uploadJobRunsUrl: any;

    uploadJobRunsUrl = appSettings.apiUrl + '/rmanFileUploadJobsSearch?';

    if (paginationOptions.pageNumber != undefined && paginationOptions.pageNumber != null && !isNaN(paginationOptions.pageNumber)) {
      uploadJobRunsUrl = uploadJobRunsUrl + 'search=&page=' + paginationOptions.pageNumber + '&size=' + paginationOptions.pageSize;

    } else {
      uploadJobRunsUrl = uploadJobRunsUrl + 'search=%25';
    }

    return this.http.get(uploadJobRunsUrl).toPromise().then((data: any) => {
      return data;
    });
  }
  
  fetchRCNumbers(rc: any) {
        let unpostedRCUrl = appSettings.apiUrl + '/fetchUnPostedRC?rc='+rc;

        return this.http.get(unpostedRCUrl).toPromise().then((data: any) => {
      return data;
    });
  }


}
