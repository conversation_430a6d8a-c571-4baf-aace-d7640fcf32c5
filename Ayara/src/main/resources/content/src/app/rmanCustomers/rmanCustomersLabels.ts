interface ILabels {
    [index: string]: string;
}

export class RmanCustomersLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["groupType"] = "Groupe Type";
        this.fieldLabels["globalAttribute10"] = "Globale Attribute10";
        this.fieldLabels["lastUpdateDate"] = "Last Upadate Date";
        this.fieldLabels["globalAttribute5"] = "Global Attribute5";
        this.fieldLabels["globalAttribute4"] = "Global Attribute4";
        this.fieldLabels["personTitle"] = "Person Title";
        this.fieldLabels["status"] = "Status";
        this.fieldLabels["globalAttribute7"] = "Global Attribute7";
        this.fieldLabels["globalAttribute6"] = "Global Attribute6";
        this.fieldLabels["globalAttribute1"] = "Global Attribute1";
        this.fieldLabels["country"] = "Country";
        this.fieldLabels["emailAddress"] = "Email Address";
        this.fieldLabels["knownAs"] = "Global As";
        this.fieldLabels["globalAttribute3"] = "Global Attribute3";
        this.fieldLabels["globalAttribute2"] = "Global Attribute2";
        this.fieldLabels["personPreviousLastName"] = "Person Previous Last Name";
        this.fieldLabels["personNameSuffix"] = "Person Name Suffix";
        this.fieldLabels["globalAttribute9"] = "Global Attribute9";
        this.fieldLabels["personFirstName"] = "Person First Name";
        this.fieldLabels["globalAttribute8"] = "Global Attribute8";
        this.fieldLabels["county"] = "Country";
        this.fieldLabels["createdBy"] = "Created By";
        this.fieldLabels["attribute3"] = "Attribute3";
        this.fieldLabels["lastUpdatedBy"] = "Last Updated By";
        this.fieldLabels["attribute2"] = "Attribute2";
        this.fieldLabels["origSystemReference"] = "Orig System Reference";
        this.fieldLabels["attribute1"] = "Attribute1";
        this.fieldLabels["customerNumber"] = "Customer Number";
        this.fieldLabels["customerClassCode"] = "Customer Class Code";
        this.fieldLabels["personIdentifier"] = "Person Identifier";
        this.fieldLabels["revAccountId"] = "Rev Account Id";
        this.fieldLabels["customerId"] = "Customer Id";
        this.fieldLabels["creationDate"] = "Creation Date";
        this.fieldLabels["attribute9"] = "Attribute9";
        this.fieldLabels["attribute8"] = "Attribute8";
        this.fieldLabels["attribute7"] = "Attribute7";
        this.fieldLabels["attribute6"] = "Attribute6";
        this.fieldLabels["attribute5"] = "Attribute5";
        this.fieldLabels["attribute4"] = "Attribute4";
        this.fieldLabels["attributeCategory"] = "Attribute CATEGORY";
        this.fieldLabels["personMiddleName"] = "Person Middle Name";
        this.fieldLabels["attribute10"] = "Attribute10";
        this.fieldLabels["attribute14"] = "Attribute14";
        this.fieldLabels["attribute13"] = "Attribute13";
        this.fieldLabels["attribute12"] = "Attribute12";
        this.fieldLabels["customerType"] = "Customer Type";
        this.fieldLabels["attribute11"] = "Attribute11";
        this.fieldLabels["state"] = "State";
        this.fieldLabels["dunsNumber"] = "Duns Number";
        this.fieldLabels["requestId"] = "Request Id";
        this.fieldLabels["primarySalesrepName"] = "Primary Salesrep Name";
        this.fieldLabels["financeContact"] = "Finance Contact";
        this.fieldLabels["postalCode"] = "Postal Code";
        this.fieldLabels["paymentTerms"] = "Payment Terms";
        this.fieldLabels["salesChannelCode"] = "Sales Channel Code";
        this.fieldLabels["sicCodeType"] = "Sic Code Type";
        this.fieldLabels["address1"] = "Address1";
        this.fieldLabels["primaryPhoneNumber"] = "Primary Phone Number";
        this.fieldLabels["address3"] = "Address3";
        this.fieldLabels["address2"] = "Address2";
        this.fieldLabels["attribute21"] = "Attribute21";
        this.fieldLabels["cogsAccountId"] = "Cogs Account Id";
        this.fieldLabels["attribute20"] = "Attribute20";
        this.fieldLabels["address4"] = "Address4";
        this.fieldLabels["attribute24"] = "Attribute24";
        this.fieldLabels["attribute23"] = "Attribute23";
        this.fieldLabels["attribute22"] = "Attribute22";
        this.fieldLabels["globalAttribute20"] = "Global Attribute0";
        this.fieldLabels["personAcademicTitle"] = "Person Academic Title";
        this.fieldLabels["globalAttributeCategory"] = "Global Attribute Category";
        this.fieldLabels["globalAttribute17"] = "Global Attribute17";
        this.fieldLabels["globalAttribute18"] = "Global Attribute18";
        this.fieldLabels["globalAttribute15"] = "Global Attribute15";
        this.fieldLabels["personLastName"] = "Person Last Name";
        this.fieldLabels["globalAttribute16"] = "Global Attribute16";
        this.fieldLabels["faxNumber"] = "Fax Number";
        this.fieldLabels["customerName"] = "Customer Name";
        this.fieldLabels["globalAttribute13"] = "Global Attribute13";
        this.fieldLabels["city"] = "City";
        this.fieldLabels["doNotMailFlag"] = "Do Not Mail Flag";
        this.fieldLabels["globalAttribute14"] = "Global Attribute14";
        this.fieldLabels["globalAttribute11"] = "Global Attribute11";
        this.fieldLabels["personIdenType"] = "Person Iden Type";
        this.fieldLabels["globalAttribute12"] = "Global Attribute12";
        this.fieldLabels["attribute18"] = "Attribute18";
        this.fieldLabels["attribute17"] = "Attribute17";
        this.fieldLabels["attribute16"] = "Attribute16";
        this.fieldLabels["province"] = "Province";
        this.fieldLabels["attribute15"] = "Attribute15";
        this.fieldLabels["globalAttribute19"] = "Global Attribute19";
        this.fieldLabels["attribute19"] = "Attribute19";
    }

}
