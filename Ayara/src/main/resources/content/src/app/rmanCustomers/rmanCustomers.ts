export interface RmanCustomers {
    groupType?: any;
    globalAttribute10?: any;
    lastUpdateDate?: any;
    globalAttribute5?: any;
    globalAttribute4?: any;
    personTitle?: any;
    status?: any;
    globalAttribute7?: any;
    globalAttribute6?: any;
    globalAttribute1?: any;
    country?: any;
    emailAddress?: any;
    knownAs?: any;
    globalAttribute3?: any;
    globalAttribute2?: any;
    personPreviousLastName?: any;
    personNameSuffix?: any;
    globalAttribute9?: any;
    personFirstName?: any;
    globalAttribute8?: any;
    county?: any;
    createdBy?: any;
    attribute3?: any;
    lastUpdatedBy?: any;
    attribute2?: any;
    origSystemReference?: any;
    attribute1?: any;
    customerNumber?: any;
    customerClassCode?: any;
    personIdentifier?: any;
    revAccountId?: any;
    customerId?: any;
    creationDate?: any;
    attribute9?: any;
    attribute8?: any;
    attribute7?: any;
    attribute6?: any;
    attribute5?: any;
    attribute4?: any;
    attributeCategory?: any;
    personMiddleName?: any;
    attribute10?: any;
    attribute14?: any;
    attribute13?: any;
    attribute12?: any;
    customerType?: any;
    attribute11?: any;
    state?: any;
    dunsNumber?: any;
    requestId?: any;
    primarySalesrepName?: any;
    financeContact?: any;
    postalCode?: any;
    paymentTerms?: any;
    salesChannelCode?: any;
    sicCodeType?: any;
    address1?: any;
    primaryPhoneNumber?: any;
    address3?: any;
    address2?: any;
    attribute21?: any;
    cogsAccountId?: any;
    attribute20?: any;
    address4?: any;
    attribute24?: any;
    attribute23?: any;
    attribute22?: any;
    globalAttribute20?: any;
    personAcademicTitle?: any;
    globalAttributeCategory?: any;
    globalAttribute17?: any;
    globalAttribute18?: any;
    globalAttribute15?: any;
    personLastName?: any;
    globalAttribute16?: any;
    faxNumber?: any;
    customerName?: any;
    globalAttribute13?: any;
    city?: any;
    doNotMailFlag?: any;
    globalAttribute14?: any;
    globalAttribute11?: any;
    personIdenType?: any;
    globalAttribute12?: any;
    attribute18?: any;
    attribute17?: any;
    attribute16?: any;
    province?: any;
    attribute15?: any;
    globalAttribute19?: any;
    attribute19?: any;
    region?: any;
}
