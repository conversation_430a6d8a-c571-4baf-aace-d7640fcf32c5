interface ILabels {
    [index: string]: string;
}

export class DealArrangementLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["dealCosedDate"] = "DEAL COSED DATE";
        this.fieldLabels["attribute30"] = "ATTRIBUTE30";
        this.fieldLabels["lastUpdateDate"] = "LAST UPDATE DATE";
        this.fieldLabels["dealArrangementBasis"] = "DEAL ARRANGEMENT BASIS";
        this.fieldLabels["dealArrangementKey"] = "DEAL ARRANGEMENT KEY";
        this.fieldLabels["dealArrangementTotal"] = "DEAL ARRANGEMENT TOTAL";
        this.fieldLabels["changeReason"] = "CHANGE REASON";
        this.fieldLabels["attribute29"] = "ATTRIBUTE29";
        this.fieldLabels["attribute28"] = "ATTRIBUTE28";
        this.fieldLabels["dealArrangementType"] = "DEAL ARRANGEMENT TYPE";
        this.fieldLabels["attribute27"] = "ATTRIBUTE27";
        this.fieldLabels["dealApprovalDate"] = "DEAL APPROVAL DATE";
        this.fieldLabels["attribute26"] = "ATTRIBUTE26";
        this.fieldLabels["masterArrgId"] = "MASTER ARRG ID";
        this.fieldLabels["dealName"] = "DEAL NAME";
        this.fieldLabels["attribute3"] = "ATTRIBUTE3";
        this.fieldLabels["createdBy"] = "CREATED BY";
        this.fieldLabels["attribute2"] = "ATTRIBUTE2";
        this.fieldLabels["lastUpdatedBy"] = "LAST UPDATED BY";
        this.fieldLabels["attribute1"] = "ATTRIBUTE1";
        this.fieldLabels["dealArrangementSource"] = "DEAL ARRANGEMENT SOURCE";
        this.fieldLabels["salesContact"] = "Sales Contact";
        this.fieldLabels["legalEntityId"] = "LEGAL ENTITY ID";
        this.fieldLabels["creationDate"] = "CREATION DATE";
        this.fieldLabels["attribute9"] = "ATTRIBUTE9";
        this.fieldLabels["attribute8"] = "ATTRIBUTE8";
        this.fieldLabels["attribute7"] = "ATTRIBUTE7";
        this.fieldLabels["attribute6"] = "ATTRIBUTE6";
        this.fieldLabels["attribute5"] = "ATTRIBUTE5";
        this.fieldLabels["attribute4"] = "ATTRIBUTE4";
        this.fieldLabels["allocationEligible"] = "ALLOCATION ELIGIBLE";
        this.fieldLabels["dealAgreementId"] = "DEAL AGREEMENT ID";
        this.fieldLabels["dealArrangementStatus"] = "DEAL ARRANGEMENT STATUS";
        this.fieldLabels["attribute10"] = "ATTRIBUTE10";
        this.fieldLabels["endCustomerName"] = "Customer Name";
        this.fieldLabels["dealArrangementNumber"] = "Deal Arrangement Number";
        this.fieldLabels["masterArrgName"] = "Master Arrangement Name";
        this.fieldLabels["revManagerId"] = "REV MANAGER ID";
        this.fieldLabels["attribute14"] = "Additional Info2";
        this.fieldLabels["attribute13"] = "ATTRIBUTE13";
        this.fieldLabels["arrangementCurrency"] = "ARRANGEMENT CURRENCY";
        this.fieldLabels["attribute12"] = "ATTRIBUTE12";
        this.fieldLabels["msaName"] = "MSA Name";
        this.fieldLabels["attribute11"] = "ATTRIBUTE11";
        this.fieldLabels["dealApprovedBy"] = "DEAL APPROVED BY";
        this.fieldLabels["dealArrangementName"] = "Deal Arrangement Name";
        this.fieldLabels["endCustomerNumber"] = "Customer Number";
        this.fieldLabels["customerContact"] = "CUSTOMER CONTACT";
        this.fieldLabels["dealId"] = "DEAL ID";
        this.fieldLabels["dealArrangementSaMe"] = "DEAL ARRANGEMENT SA ME";
        this.fieldLabels["dealNumber"] = "DEAL NUMBER";
        this.fieldLabels["attribute21"] = "ATTRIBUTE21";
        this.fieldLabels["salesNodeLevel4"] = "SALES NODE LEVEL4";
        this.fieldLabels["attribute20"] = "ATTRIBUTE20";
        this.fieldLabels["msaNumber"] = "MSA Number";
        this.fieldLabels["salesNodeLevel2"] = "SALES NODE LEVEL2";
        this.fieldLabels["salesNodeLevel3"] = "SALES NODE LEVEL3";
        this.fieldLabels["dealArrangementQtr"] = "DEAL ARRANGEMENT QTR";
        this.fieldLabels["attribute25"] = "ATTRIBUTE25";
        this.fieldLabels["attribute24"] = "ATTRIBUTE24";
        this.fieldLabels["attribute23"] = "ATTRIBUTE23";
        this.fieldLabels["revAccountantId"] = "REV ACCOUNTANT ID";
        this.fieldLabels["attribute22"] = "ATTRIBUTE22";
        this.fieldLabels["reasonCode"] = "REASON CODE";
        this.fieldLabels["masterArrgFlag"] = "MASTER ARRG FLAG";
        this.fieldLabels["salesNodeLevel1"] = "SALES NODE LEVEL1";
        this.fieldLabels["dealArrangementId"] = "DEAL ARRANGEMENT ID";
        this.fieldLabels["legalEntityName"] = "Legal Entity Name";
        this.fieldLabels["attribute18"] = "ATTRIBUTE18";
        this.fieldLabels["attribute17"] = "ATTRIBUTE17";
        this.fieldLabels["attribute16"] = "ATTRIBUTE16";
        this.fieldLabels["attribute15"] = "Additional Info1";
        this.fieldLabels["dealAgreementName"] = "DEAL AGREEMENT NAME";
        this.fieldLabels["attribute19"] = "ATTRIBUTE19";
    }

}
