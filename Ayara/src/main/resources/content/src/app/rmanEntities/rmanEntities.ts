export interface RmanEntities {
    attribute10: any;
    dealFlag: any;
    tableName: any;
    attribute30: any;
    entityId: any;
    attribute14: any;
    attribute13: any;
    attribute12: any;
    attribute11: any;
    postBooking: any;
    lastUpdateDate: any;
    objectType: any;
    description: any;
    attribute29: any;
    attribute28: any;
    attribute27: any;
    attribute26: any;
    endDateActive: any;
    entityName: any;
    attribute3: any;
    attribute21: any;
    createdBy: any;
    attribute2: any;
    attribute20: any;
    lastUpdatedBy: any;
    startDateActive: any;
    attribute1: any;
    entityCategory: any;
    attribute25: any;
    attribute24: any;
    attribute23: any;
    attribute22: any;
    creationDate: any;
    attribute9: any;
    attribute8: any;
    attribute7: any;
    attribute6: any;
    attribute5: any;
    attribute4: any;
    attribute18: any;
    auditFlag: any;
    attribute17: any;
    attribute16: any;
    attribute15: any;
    attribute19: any;
}
