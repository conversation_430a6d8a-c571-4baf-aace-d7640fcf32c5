import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { Table } from 'primeng/table';
import { NotificationService } from '../shared/notifications.service';
import { UploadService } from '../shared/upload.service';
import { RmanUsageSummaryInterface } from './rmanUsageSummaryInterface';
import { RmanUsageSummaryInterfaceService } from './rmanUsageSummaryInterfaceservice';
import { CommonSharedService } from '../shared/common.service';

declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Component({
    templateUrl: './rmanUsageSummaryInterface.component.html',
    selector: 'rmanUsageSummaryInterface-data',
    providers: [RmanUsageSummaryInterfaceService, ConfirmationService]
})

export class RmanUsageSummaryInterfaceComponent {

    displayDialog: boolean;

    displaySearchDialog: boolean;

    uploadLoading: boolean = false;

    rmanUsageSummaryInterface: RmanUsageSummaryInterface = new RmanUsageSummaryInterfaceImpl();

    rmanUsageSummaryInterfaceSearch: RmanUsageSummaryInterface = new RmanUsageSummaryInterfaceImpl();

    isSerached: number = 0;

    selectedRmanUsageSummaryInterface: RmanUsageSummaryInterface;

    newRmanUsageSummaryInterface: boolean;

	displayUsageSummaryInterfaceDialog: boolean = false;

    rmanUsageSummaryInterfaceList: RmanUsageSummaryInterface[];

    cols: any[];
    //columns: ILabels;

    columnOptions: any[];

    paginationOptions = {};

    pages: {};


    datasource: any[];
    pageSize: number;
    totalElements: number;
    collapsed: boolean = true;

    customerForm: FormGroup;
    noData = appSettings.noData;
    loading: boolean;
    rmanUsageSummaryInterfacetatus: any[];
    regions:any[];
    customerTypes:any[];
    isSelectAllChecked = true;
    globalCols: any[];
    clonedCols: any[];
    userId: number;
    showPaginator: boolean = true;
    startIndex: number;
    showAddColumns = true;
    columns: any[];

    exportCols: string[] = [];
  	disableExport: boolean = true;
    selectedLines: any[] = [];
	  exceptionsList: any[] = [];
    deletedExceptions: any[]=[];




    constructor(
        private rmanUsageSummaryInterfaceService: RmanUsageSummaryInterfaceService,
        private confirmationService: ConfirmationService,
        private formBuilder: FormBuilder,
        private notificationService:NotificationService,
        public _uploadService:UploadService,  private commonSharedService: CommonSharedService
    ) {

        // generate list code
        this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };
		/*this.rmanUsageSummaryInterfaceList =[{
			"usageId":83233,
			"attribute8":"USG238231",
			"summaryStartDate":"2023-10-01",
			"summaryEndDate":"2023-10-31",
			"totalQty":3,
			"unitSellingPrice":3939.39,
			"unbilledSubTotal":39023.32,
			"orderNumber":"O-00882",
			"orderLineNumber":"**********",
			"orderLineId":"29202212",
			"attribute4":"Jan-23",
			"attribute5":"Oct-23",
			"attribute6":"Test",
			"sfUsageSummaryId":"Test2829",
			"sfUsageSummaryNuber":"TESTSF8392",
			"attribute9":"329832.233",
			"attribute10":"232.324",
			"attribute11":"2393.393",
			"interfaceProcessId":12893183,
			"errorMessage":"This is failed due to no Order Line Reference exists"
		}];*/

    }

    ngOnInit() {
      this.selectedLines = [];
        this.globalCols = [
            { field: 'usageId', header: 'USAGE_ID', showField: true, drag: false, display: "table-cell",type:'text'},
            { field: 'attribute8', header: 'USAGE_NUMBER', showField: true, drag: true, display: "table-cell",type:'text' },
            { field: 'summaryStartDate', header: 'USAGE_START_DATE', showField: true, drag: true, display: "table-cell",type:'date'},
            { field: 'summaryEndDate', header: 'USAGE_END_DATE', showField: true, drag: true, display: "table-cell",type:'date'},
            { field: 'totalQty', header: 'TOTAL_QTY', showField: true, drag: true, display: "table-cell",type:'number'},
            { field: 'unitSellingPrice', header: 'UNIT_SELLING_PRICE', showField: true, drag: true, display: "table-cell",type:'round'},
			{ field: 'unbilledSubTotal', header: 'UNBILLED_SUB_TOTAL', showField: true, drag: true, display: "table-cell",type:'round'},
			{ field: 'orderNumber', header: 'ORDER_NUMBER', showField: true, drag: false, display: "table-cell",type:'text'},
            { field: 'orderLineNumber', header: 'ORDER_LINE_NUMBER', showField: true, drag: true, display: "table-cell",type:'text' },
            { field: 'orderLineId', header: 'ORDER_LINE_ID', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'attribute4', header: 'ACCOUNTING_PERIOD', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'attribute5', header: 'USAGE_PERIOD', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'attribute6', header: 'USAGE_TYPE', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'sfUsageSummaryId', header: 'USAGE_SUMMARY_ID', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'sfUsageSummaryNumber', header: 'USAGE_SUMMARY_NUMBER', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'attribute9', header: 'USAGE_LIST_PRICE', showField: true, drag: true, display: "table-cell",type:'round'},
            { field: 'attribute10', header: 'USAGE_TOTAL', showField: true, drag: true, display: "table-cell",type:'round'},
            { field: 'attribute11', header: 'USAGE_CUMMULATIVE_TOTAL', showField: true, drag: true, display: "table-cell",type:'round'},
			{ field: 'attribute12', header: 'ATTRIBUTE12', showField: true, drag: true, display: "table-cell",type:'round'},
            { field: 'attribute13', header: 'ATTRIBUTE13', showField: true, drag: true, display: "table-cell",type:'round'},
            { field: 'attribute14', header: 'ATTRIBUTE14', showField: true, drag: true, display: "table-cell",type:'round'},
			{ field: 'interfaceProcessId', header: 'INTERFACE_PROCESS_ID', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'errorMessage', header: 'ERROR_MESSAGE', showField: true, drag: true, display: "table-cell",type:'text'}
        ];

        this.columns = [];
        this.getTableColumns("rmanUsageSummaryInterface", "UsageSummaryInterface");
    }

    prepareRmanStatus() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectStat, value: null }];
        this.rmanUsageSummaryInterfacetatus.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode });
        });
        this.rmanUsageSummaryInterfacetatus = rmanLookupsVTempObj;
    }

    prepareRegions() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectRegion, value: null }];
        this.regions.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupDescription });
        });
        this.regions = rmanLookupsVTempObj;
    }

    prepareCustomerTypes() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectCustomerType, value: null }];
        this.customerTypes.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupDescription });
        });
        this.customerTypes = rmanLookupsVTempObj;
    }

    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
          if (response && response != null && response.userId) {
            this.columns = [];
            let colsList = response.tableColumns.split(",");
            if (colsList.length > 0) {
              colsList.forEach((item, index) => {
                if (item) {
                  this.startIndex = this.globalCols.findIndex(col => col.field == item);
                  this.onDrop(index);
                }
              });
            }
            this.globalCols.forEach(col => {
              if (response.tableColumns.indexOf(col.field) !== -1) {
                this.columns.push(col);
              } else {
                col.showField = false;
              }
            });
            if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
            this.showPaginator = this.columns.length !== 0;
            this.userId = response.userId;
          } else {
            this.columns = this.globalCols;
          }
        }).catch(() => {
          this.notificationService.showError('Error occured while getting table columns data');
          this.loading = false;
        });
      }

      saveColumns() {
        let selectedCols = "";
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
          if (col.showField) {
            selectedCols += col.field;
            if (index < colLength) {
              selectedCols += ",";
            }
          }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns("rmanUsageSummaryInterface", "UsageSummaryInterface", selectedCols, this.userId).then((response) => {
          this.columns = this.globalCols.filter(item => item.showField);
          this.userId = response["userId"];
          this.showPaginator = this.columns.length !== 0;
          this.loading = false;
        }).catch(() => {
          this.notificationService.showError('Error occured while getting data');
          this.loading = false;
        });
      }

      onDragStart(index: number) {
        this.startIndex = index;
      }

      onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
        //console.log(this.globalCols);
      }

      selectColumns(col: any) {
        let cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
          this.isSelectAllChecked = false;
        } else {
          this.isSelectAllChecked = true;
        }
      }

      onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
          if (this.isSelectAllChecked) {
            col.showField = true;
          } else {
            if (col.drag) {
              col.showField = false;
            }
          }
        });
      }

      onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
      }

      closeConfigureColumns(event: any) {
      this.showAddColumns = true;
      this.globalCols = this.clonedCols;
      let configCol = this.globalCols.filter(item => !item.showField);
      this.isSelectAllChecked = !(configCol.length > 0);
      }



    getAllRmanUsageSummaryInterface() {
        this.loading = true;
        this.rmanUsageSummaryInterfaceService.getAllRmanUsageSummaryInterface(this.paginationOptions, this.rmanUsageSummaryInterface, this.exportCols).then((rmanUsageSummaryInterfaceList: any) => {
            this.loading = false;
            this.datasource = rmanUsageSummaryInterfaceList.content;
            this.rmanUsageSummaryInterfaceList = rmanUsageSummaryInterfaceList.content;
            this.totalElements = rmanUsageSummaryInterfaceList.totalElements;
            this.pageSize = rmanUsageSummaryInterfaceList.size;
            this.displaySearchDialog = false;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    getRmanUsageSummaryInterface(event: any) {
        this.loading = true;
        let first: number = event.first;
        let rows: number = event.rows;
        let pageNumber: number = first / rows;
        this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
        this.rmanUsageSummaryInterfaceService.getAllRmanUsageSummaryInterface(this.paginationOptions, this.rmanUsageSummaryInterface, this.exportCols).then((rmanUsageSummaryInterfaceList: any) => {
            this.loading = false;
            this.datasource = rmanUsageSummaryInterfaceList.content;
            this.rmanUsageSummaryInterfaceList = rmanUsageSummaryInterfaceList.content;
            this.totalElements = rmanUsageSummaryInterfaceList.totalElements;
            this.pageSize = rmanUsageSummaryInterfaceList.size;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });

    }

	exportExcel() {
	    this.exportCols = [];

	    for (let index = 0; index < this.columns.length; index++) {
	      if (this.columns[index].showField) {
	        this.exportCols.push(this.columns[index].field);
	      }
	    }
	    let serviceUrl = this.rmanUsageSummaryInterfaceService.getServiceUrl(this.paginationOptions, this.rmanUsageSummaryInterface, 1, this.exportCols);
	    window.location.href = serviceUrl;
     }


   onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }


    reset(dt: Table) {
        this.paginationOptions = {};
        this.rmanUsageSummaryInterface = new RmanUsageSummaryInterfaceImpl();
        this.rmanUsageSummaryInterfaceSearch = new RmanUsageSummaryInterfaceImpl();
        this.selectedLines = [];
        dt.reset();
    }
    deleteSelected(dt: Table) {

        this.exceptionsList = [];
        this.deletedExceptions=[];

        for (let line of this.selectedLines) {
            this.exceptionsList.push(line.usageId);
            this.deletedExceptions.push(line);
        }
        if (this.exceptionsList.length > 0) {

        this.confirmationService.confirm({
            message: 'Are you sure you want to delete?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
              this.loading = true;
              this.rmanUsageSummaryInterfaceService.deleteSelectedLines(this.exceptionsList).then((response: any) => {
                for(let usage of this.deletedExceptions){
                  this.rmanUsageSummaryInterfaceService.updateExceptionLogs(usage,'deleted').subscribe();
                }
                this.notificationService.showSuccess('Selected records are deleted Successfully  ');
                this.deletedExceptions=[];
            }).then(() => {
                this.reset(dt);
            }).catch((err: any) => {
                this.loading = false;
                this.notificationService.showError('Getting error While delete records');
            });
            },
            reject: () => {
              this.notificationService.showWarning('You have rejected');
            }
          });

        } else {
            this.notificationService.showInfo('Select at least one record to delete');
        }
    }




    findSelectedRmanUsageSummaryInterfaceIndex(): number {
        return this.rmanUsageSummaryInterfaceList.indexOf(this.selectedRmanUsageSummaryInterface);
    }

    onRowSelect(event: any) {

    }


    hideColumnMenu: boolean = true;

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    showFilter: boolean = false;

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {
        if (this.isSerached == 0) {
            this.rmanUsageSummaryInterfaceSearch = new RmanUsageSummaryInterfaceImpl();
        }
        this.rmanUsageSummaryInterfaceSearch = new RmanUsageSummaryInterfaceImpl();
        this.displaySearchDialog = true;

    }

	search() {

        this.isSerached = 1;
        this.rmanUsageSummaryInterface = this.rmanUsageSummaryInterfaceSearch;
        this.paginationOptions={};
        this.getAllRmanUsageSummaryInterface();
    }


    showUploadLoader(){
    	this.uploadLoading = true;
    }

}


export class RmanUsageSummaryInterfaceImpl implements RmanUsageSummaryInterface {
    constructor(
	public usageId?:any,
	public orderLineId?:any,
	public attribute9?:any,
	public unbilledSubTotal?:any,
	public attribute8?:any,
	public attribute7?:any,
	public attribute6?:any,
	public attribute5?:any,
	public attribute4?:any,
	public sfUsageSummaryNumber?:any,
	public unitSellingPrice?:any,
	public orderLineNumber?:any,
	public totalQty?:any,
	public orderNumber?:any,
	public summaryStartDate?:any,
	public summaryEndDate?:any,
	public attribute11?:any,
	public attribute12?:any,
	public attribute13?:any,
	public attribute14?:any,
	public attribute15?:any,
	public attribute2?:any,
	public attribute3?:any,
	public attribute1?:any,
	public attribute10?:any
	) { }
}

interface ILabels {
    [index: string]: string;
}
