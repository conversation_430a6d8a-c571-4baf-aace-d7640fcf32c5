import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require:any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type':  'application/json',
  })
};

@Injectable()
export class RmanUsageSummaryInterfaceService {

    constructor(private http: HttpClient) {}
    
    getServiceUrl(paginationOptions:any,rmanUsageSummaryInterfaceSearchObject:any,  exportFlag: any, exportCols: any) {
        
         let serviceUrl = appSettings.apiUrl + '/usagesInterfaceExport?';

    	 if (exportFlag == 0) {
      	 	serviceUrl = appSettings.apiUrl + '/rmanUsageSummaryInterfaceSearch?';
    	 }
    	 
    	 let searchString='';
		 
		 if (rmanUsageSummaryInterfaceSearchObject.sfUsageSummaryId!=undefined && rmanUsageSummaryInterfaceSearchObject.sfUsageSummaryId!="") {
            searchString=searchString+'sfUsageSummaryId:'+rmanUsageSummaryInterfaceSearchObject.sfUsageSummaryId+',';
        }

        if (rmanUsageSummaryInterfaceSearchObject.attribute8!=undefined && rmanUsageSummaryInterfaceSearchObject.attribute8!="") {
            searchString=searchString+'attribute8:'+rmanUsageSummaryInterfaceSearchObject.attribute8+',';
        }
		
		if (rmanUsageSummaryInterfaceSearchObject.sfUsageSummaryNumber!=undefined && rmanUsageSummaryInterfaceSearchObject.sfUsageSummaryNumber!="") {
            searchString=searchString+'sfUsageSummaryNumber:'+rmanUsageSummaryInterfaceSearchObject.sfUsageSummaryNumber+',';
        }


        if (rmanUsageSummaryInterfaceSearchObject.orderNumber!=undefined && rmanUsageSummaryInterfaceSearchObject.orderNumber!="") {
            searchString=searchString+'orderNumber:'+rmanUsageSummaryInterfaceSearchObject.orderNumber+',';
        }

        if (rmanUsageSummaryInterfaceSearchObject.interfaceProcessId!=undefined && rmanUsageSummaryInterfaceSearchObject.interfaceProcessId!="") {
            searchString=searchString+'interfaceProcessId:'+rmanUsageSummaryInterfaceSearchObject.interfaceProcessId+',';
        }

        searchString=searchString+'interfaceStatus:E';


        if (searchString == '') {
            serviceUrl=serviceUrl+'search=%25';
        }
        else {
            serviceUrl=serviceUrl+'search='+searchString;
        }
        
        if (paginationOptions.pageNumber!=undefined && paginationOptions.pageNumber!="" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber!=0) {
           serviceUrl=serviceUrl+'&page='+paginationOptions.pageNumber+'&size='+paginationOptions.pageSize;
        }
        
        if (exportCols != undefined && exportCols != "") {
      		serviceUrl = serviceUrl + '&exportCols=' + exportCols;
    	}
   		
   		return serviceUrl;
        
     }   

    getAllRmanUsageSummaryInterface(paginationOptions:any,rmanUsageSummaryInterfaceSearchObject:any, exportCols:any): Promise<any[]> {
        let serviceUrl = this.getServiceUrl(paginationOptions, rmanUsageSummaryInterfaceSearchObject, 0, exportCols);
        return this.http.get(serviceUrl).toPromise().then((data:any) => {
            return data;
        });
    }
    deleteSelectedLines(exceptionLines: any[]){

        let delUrl = appSettings.apiUrl + '/bulkDelUsagesExceptions?usageIds='+exceptionLines;
        return this.http.delete(delUrl,{responseType : "text"}).toPromise().then((data:any) => {
          return data;
        });

    }
    updateExceptionLogs(exception: any, event: string) {
          return this.http.post(
            `${appSettings.apiUrl}/updateUsageExceptionLogs/${exception.usageId}`,
            exception,
            {
              headers: {
                'Content-Type': 'application/json'
              },
              params: {
                event: event
              }
            }
          );
        }


	
}
