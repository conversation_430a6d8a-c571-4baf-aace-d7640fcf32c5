export interface RmanUsageSummaryInterface {
    usageId?:any;
	orderLineId?:any;
	attribute9?:any;
	unbilledSubTotal?:any;
	attribute8?:any;
	attribute7?:any;
	attribute6?:any;
	attribute5?:any;
	attribute4?:any;
	sfUsageSummaryNumber?:any;
	unitSellingPrice?:any;
	orderLineNumber?:any;
	totalQty?:any;
	orderNumber?:any;
	summaryStartDate?:any;
	summaryEndDate?:any;
	attribute11?:any;
	attribute12?:any;
	attribute13?:any;
	attribute14?:any;
	attribute15?:any;
	attribute2?:any;
	attribute3?:any;
	attribute1?:any;
	attribute10?:any;
	interfaceProcessId? :any;
	interfaceStatus?: any;
	errorMessage?: any;
}
