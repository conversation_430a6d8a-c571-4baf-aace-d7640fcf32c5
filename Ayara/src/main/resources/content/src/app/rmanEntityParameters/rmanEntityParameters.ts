export interface RmanEntityParameters {
    attribute10: any;
    attribute30: any;
    entityId: any;
    attribute14: any;
    attribute13: any;
    columnName: any;
    attribute12: any;
    attribute11: any;
    lastUpdateDate: any;
    parameterName: any;
    description: any;
    entityParameterId: any;
    columnType: any;
    attribute29: any;
    attribute28: any;
    attribute27: any;
    attribute26: any;
    columnSize: any;
    attribute3: any;
    attribute21: any;
    createdBy: any;
    attribute2: any;
    attribute20: any;
    lastUpdatedBy: any;
    attribute1: any;
    attribute25: any;
    attribute24: any;
    attribute23: any;
    attribute22: any;
    creationDate: any;
    attribute9: any;
    attribute8: any;
    attribute7: any;
    enabledFlag: any;
    attribute6: any;
    attribute5: any;
    columnSequence: any;
    attribute4: any;
    attribute18: any;
    attribute17: any;
    attribute16: any;
    attribute15: any;
    attribute19: any;
}
