<div class="content-section implementation">
</div>


<p-panel [header]="!isDealPage() ? 'Deal Forecasting View' : ''" [style]="{'margin-bottom':'20px'}"
		 (onBeforeToggle)=onBeforeToggle($event)>
	<p-header>
		<div class="pull-right icons-list" >
			<p-dropdown [options]="sourceTypesList" [(ngModel)]="srcType" placeholder="Select Source Type" appendTo="body" [filter]="true" [ngStyle]="{'margin-left': '-100px', 'position': 'absolute', 'height': '31px', 'padding': '0px', 'width': '100px', 'margin-top': '-3px'}" (onChange)="onSourceTypeChange()"></p-dropdown>
			<p-toggleButton [ngStyle]="{'margin-left': '10px', 'position': 'relative', 'top': '-2px'}" class="ui-inputswitch" onLabel="TC" offLabel="FC" onIcon="fa fa-toggle-on fa-sm" offIcon="fa fa-toggle-off fa-sm" [(ngModel)]="isTCSelected" (ngModelChange)="onToggleTCFC()"></p-toggleButton>
			<a (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
			<a (click)="reset(dt)" title="Reset">
				<em class="fa fa-refresh"></em>
			</a>
			<a (click)="dt.exportCSV()" title="Export">
				<em class="fa fa-external-link"></em>
			</a>
			<div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
				<div class="user-popup">
					<div class="content overflow">
						<input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall"
							(click)="onSelectAll()" />
						<label for="selectall">Select All</label>
						<a class="close" title="Close" (click)="closeConfigureColumns($event)">&times;</a>
						<p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
							<ng-template let-col let-index="index" pTemplate="item">
								<div *ngIf="col.drag">
									<div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens"
										(onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
										<div class="drag">
											<input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField"
												(change)="selectColumns(col)" />
											<label>{{col.header}}</label>
										</div>
									</div>
								</div>
								<div *ngIf="!col.drag">
									<div class="ui-helper-clearfix">
										<div>
											<input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField"
												(change)="selectColumns(col)" [disabled]="!col.drag" />
											<label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
										</div>
									</div>
								</div>

							</ng-template>
						</p-listbox>
					</div>
					<div class="pull-right">
						<a class="configColBtn" (click)="saveColumns()">Save</a>
						<a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
					</div>
				</div>
			</div>
		</div>
	</p-header>

	<div class="x-scroll">
		<p-table class="ui-datatable arrangementMgrTbl" #dt [columns]="columns" exportFilename="RmanDealRfcstV"
			[loading]="loading"  [value]="rDealFCSTViewList" 
			[paginator]="true" [rows]="10" [resizableColumns]="true" columnResizeMode="expand"
			scrollable="true">
			<ng-template pTemplate="colgroup" let-columns>
				<colgroup>
					
					<col *ngFor="let col of columns">
				</colgroup>
			</ng-template>
			
			<ng-template pTemplate="header" class="arrangementMgrTblHead">
				<tr>
					<th *ngFor="let col of columns" pResizableColumn>
						<a>
							{{col.header}}
						</a>
					</th>

				</tr>
			</ng-template>
			<ng-template pTemplate="body" let-rowData let-forecast>
				<!-- <tr [pSelectableRow]="rowData">
					<td *ngFor="let col of columns">
						<span title="{{rowData[col.field]}}">{{ fieldType(rowData[col.field]) == 'number' ?
							(rowData[col.field]|round) : rowData[col.field]}}
						</span>
					</td> -->
				<!-- <td *ngIf="col.type == 'roundCurrency'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
                    {{rowData[col.field] | roundCurrency : rowData['arrangementCurrency']}}
                </td> -->


				<!-- </tr> -->
				<tr [pSelectableRow]="rowData">
					<td *ngFor="let col of columns">
				<span title="{{ rowData[col.field] }}">
				<ng-container *ngIf="isCurrencyField(col.field) && rowData[col.field] != null; else normalText">
					{{ rowData[col.field] | roundCurrency : rowData['Currency'] }}
				</ng-container>
				<ng-template #normalText>
					{{ fieldType(rowData[col.field]) === 'number' ? (rowData[col.field] | round) : rowData[col.field] }}
				</ng-template>
				</span>
					</td>
				</tr>
			</ng-template>
			<ng-template pTemplate="emptymessage">
				<div class="no-results-data">
					<p>{{noData}}</p>
				</div>
			</ng-template>



		</p-table>
	</div>
</p-panel>