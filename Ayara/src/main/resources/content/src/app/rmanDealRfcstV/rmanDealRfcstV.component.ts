import { Component } from '@angular/core';
import { NavigationEnd, Router } from "@angular/router";
import { Table } from 'primeng/table';
import { NotificationService } from '../shared/notifications.service';
import { RmanDealRfcstV } from './rmanDealRfcstV';
import { CommonSharedService } from '../shared/common.service';
import { RmanDealRfcstVService } from './rmanDealRfcstVservice';
import { SharedModule } from 'primeng/api';
import { NewSharedModule } from '../shared/shared.module';
import { RoundCurrencyPipe } from '../shared/round-currency.pipe';
import { RmanLookupsVService } from '../rmanLookupsV/rmanLookupsVservice';
declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Component({
    templateUrl: './rmanDealRfcstV2.component.html',
    selector: 'rmanDealRfcstV-data',
    providers: [RmanDealRfcstVService,NewSharedModule]
})

export class RmanDealRfcstVComponent {

    displayDialog: boolean;

    displaySearchDialog: boolean;

    rmanDealRfcstV: any = new RmanDealRfcstVImpl();

    rmanDealRfcstVSearch: any = new RmanDealRfcstVImpl();

    isSerached: number = 0;

    selectedRmanDealRfcstV: RmanDealRfcstV;

    newRmanDealRfcstV: boolean;

    rDealFCSTViewList: any[];

    rmanDealRfcstVList: RmanDealRfcstV[];

//    columns: ILabels;
    noData = appSettings.noData;
    totalRecords: any;

    columnOptions: any[];

    paginationOptions = {};

    pages: {};

    // @Input() arrId: any;

    arrIdK: any;
    
    showAddColumns = true;
    isSelectAllChecked = true;
    globalCols: any[];
    clonedCols: any[];
    userId: number;
    showPaginator: boolean = true;
    startIndex: number;

    datasource: any[];
    pageSize: number;
    totalElements: number;
    collapsed: boolean = true;

	columns: any[] = [];


    loading: boolean;
    isTCSelected: boolean = true;
    sourceTypesList=[];
    srcType:any='Q';




    constructor(private rmanDealRfcstVService: RmanDealRfcstVService, private router: Router,
        private commonSharedService: CommonSharedService, private notificationService: NotificationService,private rmanLookupsVService:RmanLookupsVService) {

        // generate list code
        this.paginationOptions = { 'pageNumber': 0, 'pageSize': 10 };

        this.router.events.subscribe((event) => {
            if (event instanceof NavigationEnd) {
                this.arrIdK = event.url.split('/')[3];
            }
        });
        this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, { 'lookupTypeName': 'FORECAST_DOCUMENT_TYPE' }).then((rmanLookupsVList: any) => {
            this.sourceTypesList = rmanLookupsVList.content
                .map((item: any) => ({
                label: item.lookupCode,
                value: item.lookupDescription
            }));
        });


    }

    ngOnInit() {
        this.getDealForecastingView();
    }
    onToggleTCFC(){
        this.getDealForecastingView();
    }

    checkIfStringStartsWith(str, substrs) {
        return substrs.some(substr => str.startsWith(substr));
    }
      
    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        const substrings = ["Jan-", "Feb-", "Mar-","Apr-","May-","Jun-","Jul-","Aug-","Sep-","Oct-","Nov-","Dec-","Future"];
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
          if (response && response != null && response.userId) {
            this.columns = [];
            let colsList = response.tableColumns.split(",");
            if (colsList.length > 0) {
              colsList.forEach((item, index) => {
                if (item) {
                  this.startIndex = this.globalCols.findIndex(col => col.field == item);
                  this.onDrop(index);
                }
              });
            }
            this.globalCols.forEach(col => {
              if (response.tableColumns.indexOf(col.field) !== -1) {
                this.columns.push(col);
              } else {
				if(this.checkIfStringStartsWith(col.field,substrings)==true){ 
					this.columns.push(col);
				}else{
					col.showField = false;	
				}	 
                
              }
            });
            if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
            this.showPaginator = this.columns.length !== 0;
            this.userId = response.userId;
          } else {
            this.columns = this.globalCols;
          }

        }).catch(() => {
          this.notificationService.showError('Error occured while getting table columns data');
          this.loading = false;
        });
      }
      saveColumns() {
        let selectedCols = "";
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
            const substrings = ["Jan-", "Feb-", "Mar-","Apr-","May-","Jun-","Jul-","Aug-","Sep-","Oct-","Nov-","Dec-","Future"];
          if (col.showField) {
            if(this.checkIfStringStartsWith(col.field,substrings)==false){
                selectedCols += col.field;
                if (index < colLength) {
                    selectedCols += ",";
                }
            }
          }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns("rmanDealForecasting", "Forecasting Details", selectedCols, this.userId).then((response) => {
          this.columns = this.globalCols.filter(item => item.showField);
          this.userId = response["userId"];
          this.showPaginator = this.columns.length !== 0;
          this.loading = false;
        }).catch(() => {
          this.notificationService.showError('Error occured while saving');
          this.loading = false;
        });
      }
    
      onDragStart(index: number) {
        this.startIndex = index;
      }
    
      onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
      }
    
      selectColumns(col: any) {
        let cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
          this.isSelectAllChecked = false;
        } else {
          this.isSelectAllChecked = true;
        }
      }
    
      onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
          if (this.isSelectAllChecked) {
            col.showField = true;
          } else {
            if (col.drag) {
              col.showField = false;
            }
          }
        });
      }
    
      onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
      }
    
      closeConfigureColumns(event: any) {
        this.showAddColumns = true;
        this.globalCols = this.clonedCols;
        let configCol = this.globalCols.filter(item => !item.showField);
        this.isSelectAllChecked = !(configCol.length > 0);
      }
    


    isDealPage() {
        return this.router.url.includes('deal-manager');
    }

    reset(dt: Table) {
        dt.reset();
        this.paginationOptions = {};
        this.rmanDealRfcstV = new RmanDealRfcstVImpl();
        this.getDealForecastingView();
    }

    exportExcel() {
        let serviceUrl = this.rmanDealRfcstVService.getServiceUrl(this.paginationOptions, { 'arrangementId': this.arrIdK }, 1);
        window.location.href = serviceUrl;

    }

    getAllRmanDealRfcstV() {
        this.loading = true;
        this.rmanDealRfcstVService.getAllRmanDealRfcstV(this.paginationOptions, { 'arrangementId': this.arrIdK }).then((rmanDealRfcstVList: any) => {
            this.datasource = rmanDealRfcstVList.content;
            this.rmanDealRfcstVList = rmanDealRfcstVList.content;
            this.loading = false;
            this.totalElements = rmanDealRfcstVList.totalElements;
            this.pageSize = rmanDealRfcstVList.size;
            this.displaySearchDialog = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }
    onSourceTypeChange(){
        this.getDealForecastingView();
    }


    getDealForecastingView() {
        this.loading = true;
        this.rmanDealRfcstVService.getRmanForecastView2(this.arrIdK,this.isTCSelected,this.srcType).then((data: any) => {
            this.rDealFCSTViewList = data.Content;
            this.globalCols = [];
            this.loading = false;
            let dTemp = this.rDealFCSTViewList[0];
            for (let prop in dTemp) {
                if (['Element Type'].includes(prop)) {
                    this.globalCols.push({
                        field: prop,
                        header: prop,
                        hide: false,
                        showField: true,
                        drag: true,
                        display: "table-cell"
                    });

                } else if (['Arrangement Number', 'SKU'].includes(prop)) {
                    this.globalCols.push({
                        field: prop,
                        header: prop,
                        hide: false,
                        showField: true,
                        drag: true,
                        display: "table-cell"
                    });
                } else if (['Expected Booking Amount'].includes(prop)) {
                    this.globalCols.push({
                        field: prop,
                        header: prop,
                        hide: false,
                        showField: true,
                        drag: true,
                        display: "table-cell"
                    });
                } else if (['Expected Start Date'].includes(prop)) {
                    this.globalCols.push({
                        field: prop,
                        header: 'Forecast Date',
                        hide: false,
                        showField: true,
                        drag: true,
                        display: "table-cell"
                    });
                } else if (['Ayara Line Id'].includes(prop)) {
                    
                } else if (['Quote Line Id'].includes(prop)) {
                    
                } else if(['Line No'].includes(prop)){
					this.globalCols.push({ field: prop,
                        header: 'Line Number',
                        hide: false,
                        showField: true,
                        drag: true,
                        display: "table-cell"
                        });
				} else if(['Parent Line No'].includes(prop)){
					this.globalCols.push({ field: prop,
                        header: 'Parent Line Number',
                        hide: false,
                        showField: true,
                        drag: true,
                        display: "table-cell"
                        });
				} else {
                    this.globalCols.push({
                        field: prop,
                        header: prop,
                        hide: false,
                        showField: true,
                        drag: true,
                        display: "table-cell"
                    });
                }


            }
            this.columns = [];
            this.getTableColumns("rmanDealForecasting", "Forecasting Details");
      		
            this.rDealFCSTViewList = data.Content;
            this.totalRecords = this.rDealFCSTViewList.length;
            $.unblockUI();
        }).catch((err: any) => {
            this.loading = false;
            this.notificationService.showError('Error occured while getting data');
            $.unblockUI();
        });

    }

    fieldType(fieldValue: any) {
        return typeof fieldValue;
    }

    showDialogToAdd() {

        this.newRmanDealRfcstV = true;
        this.rmanDealRfcstV = new RmanDealRfcstVImpl();
        this.displayDialog = true;

    }

    save() {

        if (this.newRmanDealRfcstV) {
            this.loading = true;
            this.rmanDealRfcstVService.saveRmanDealRfcstV(this.rmanDealRfcstV).then((response: any) => {
                this.getAllRmanDealRfcstV();

            }).catch((err: any) => {
                this.notificationService.showError('Error occured while saving the data');
                this.loading = false;
            });
        }
        else {
            this.loading = true;
            this.rmanDealRfcstVService.updateRmanDealRfcstV(this.rmanDealRfcstV).then((response: any) => {
                this.getAllRmanDealRfcstV();

            }).catch((err: any) => {
                this.notificationService.showError('Error occured while updating the data');
                this.loading = false;
            });
        }

        this.rmanDealRfcstV = new RmanDealRfcstVImpl();

        this.displayDialog = false;

    }


    delete(rmanDealRfcstV: any) {
        this.rmanDealRfcstV = rmanDealRfcstV;
        this.displayDialog = false;

        if (window.confirm('Are you sure you want to delete this record?')) {
            this.rmanDealRfcstVList.splice(this.findSelectedRmanDealRfcstVIndex(), 1);
            this.rmanDealRfcstVService.deleteRmanDealRfcstV(this.rmanDealRfcstV).then(response => {
                this.rmanDealRfcstV = new RmanDealRfcstVImpl();
                this.getAllRmanDealRfcstV();
            });
        }

    }

    editRow(rmanDealRfcstV: any) {
        this.newRmanDealRfcstV = false;
        this.rmanDealRfcstV = this.cloneRmanDealRfcstV(rmanDealRfcstV);
        this.displayDialog = true;

    }


    findSelectedRmanDealRfcstVIndex(): number {
        return this.rmanDealRfcstVList.indexOf(this.selectedRmanDealRfcstV);
    }

    onRowSelect(event: any) {

    }

    cloneRmanDealRfcstV(c: RmanDealRfcstV): RmanDealRfcstV {
        let rmanDealRfcstV = new RmanDealRfcstVImpl();
        for (let prop in c) {
            rmanDealRfcstV[prop] = c[prop];
        }
        return rmanDealRfcstV;
    }

    hideColumnMenu: boolean = true;

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    showFilter: boolean = false;

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {

        this.rmanDealRfcstVSearch = new RmanDealRfcstVImpl();

        if (this.isSerached == 0) {
            this.rmanDealRfcstVSearch = new RmanDealRfcstVImpl();
        }
        this.displaySearchDialog = true;

    }

    search() {

        this.isSerached = 1;
        this.rmanDealRfcstV = this.rmanDealRfcstVSearch;
        this.getAllRmanDealRfcstV();
    }

    onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }
    isCurrencyField(field: string): boolean {
        const fixedFields = [
        'Unit Net Price', 'Net Price', 'Allocation Amount','Extended List Price'
        ];

        const monthPattern = /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{2}$/;

        return fixedFields.includes(field) || monthPattern.test(field);
    }
}


class RmanDealRfcstVImpl implements RmanDealRfcstV {
    constructor(public glPeriod?: any, public expectedBookingAmount?: any, public amount?: any, public qty?: any, public netPrice?: any, public elementType?: any, public startDate?: any, public arrangementName?: any, public sku?: any, public arrangementId?: any) { }
}

interface ILabels {
    [index: string]: string;
}
