interface ILabels {
    [index: string]: string;
}

export class RmanDealRfcstVLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["glPeriod"] = "Period";
        this.fieldLabels["expectedBookingAmount"] = "Expected Booking Amount";
        this.fieldLabels["amount"] = "Amount";
        this.fieldLabels["qty"] = "QTY";
        this.fieldLabels["netPrice"] = "Net Price";
        this.fieldLabels["elementType"] = "Element Type";
        this.fieldLabels["startDate"] = "Start Date";
        this.fieldLabels["arrangementName"] = "Arrangement Name";
        this.fieldLabels["sku"] = "SKU";
        this.fieldLabels["arrangementId"] = "Arrangement ID";
    }

}
