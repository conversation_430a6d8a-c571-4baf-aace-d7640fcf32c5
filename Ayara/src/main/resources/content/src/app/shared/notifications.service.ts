import { Injectable } from "@angular/core";
import { MessageService } from "primeng/api";

@Injectable()
export class NotificationService {

    stickyToast:boolean = false;
    constructor(private messageService:MessageService){

    }

    showError(details, lifeTime = 10000){
        this.messageService.add({severity:'error', summary:'Error Message', detail:details, sticky: this.stickyToast, life: lifeTime});
    }
    showInfo(details, lifeTime = 10000){
        this.messageService.add({severity:'info', summary:'Info Message', detail:details, sticky: this.stickyToast, life: lifeTime});
    }
    showWarning(details, lifeTime = 10000){
        this.messageService.add({severity:'warn', summary:'Warn Message', detail:details, sticky: this.stickyToast, life: lifeTime});
    }
    showSuccess(details, lifeTime = 10000){
        this.messageService.add({severity:'success', summary:'Success Message', detail:details, sticky: this.stickyToast, life: lifeTime});
    }

    clear(){
        this.messageService.clear();
    }

}
