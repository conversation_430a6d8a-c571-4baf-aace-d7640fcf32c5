import {Component, EventEmitter, Input, OnChanges, Output, SimpleChanges} from '@angular/core';
import {Column} from '../../models/column.interface';

@Component({
    selector: 'app-columns-config',
    templateUrl: './columns-config.component.html',
    styleUrls: ['./columns-config.component.css']
})
export class ColumnsConfigComponent implements OnChanges {

    @Input() frozenCols: Column[];
    @Input() scrollableCols: Column[];
    @Input() isSelectAllChecked: boolean;
    @Input() showConfigPopup = false;

    @Output() openConfig = new EventEmitter();
    @Output() closeConfig = new EventEmitter();
    @Output() saveConfig = new EventEmitter();
    @Output() toggleFreeze = new EventEmitter<Column>();
    @Output() selectColumn = new EventEmitter();
    @Output() selectAll = new EventEmitter();

    startIndex: number;
    draggableCol: Column;

    columnsList = [
        {
            label: 'Frozen Columns',
            columns: [],
            iconClassNames: ['pi', 'pi-lock']
        },
        {
            label: 'Other Columns',
            columns: [],
            iconClassNames: ['pi', 'pi-lock']
        }
    ];

    constructor() {
    }

    ngOnChanges(changes: SimpleChanges) {
        const frozen = changes['frozenCols'];
        const scrollable = changes['scrollableCols'];
        if ((frozen && frozen.currentValue) && (scrollable && scrollable.currentValue)) {

            this.columnsList = [
                {
                    label: 'Frozen Columns',
                    columns: frozen.currentValue,
                    iconClassNames: ['pi', 'pi-lock']
                },
                {
                    label: 'Other Columns',
                    columns: scrollable.currentValue,
                    iconClassNames: ['pi', 'pi-lock-open']
                }
            ];
        }
    }

    onOpenConfigPopup() {
        this.openConfig.emit();
    }

    onCloseConfigPopup() {
        this.closeConfig.emit();
    }

    onSaveConfig() {
        this.saveConfig.emit();
    }

    onDragStart(col: Column, index: number) {
        this.startIndex = index;
        this.draggableCol = col;
    }

    onDrop(index: number) {
        if (this.draggableCol.isFrozen) {
            this.frozenCols.splice(this.startIndex, 1);
            this.frozenCols.splice(index, 0, this.draggableCol);
        } else {
            this.scrollableCols.splice(this.startIndex, 1);
            this.scrollableCols.splice(index, 0, this.draggableCol);
        }
    }

    onToggleFreeze(col: Column) {
        this.toggleFreeze.emit(col);
    }

    onSelectColumn() {
        this.selectColumn.emit();
    }

    onSelectAll() {
        this.selectAll.emit();
    }
}
