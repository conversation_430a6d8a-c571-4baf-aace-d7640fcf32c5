export interface TableData<T> {
    content: T;
    pageable: PageData;
    totalElements: number;
    totalPages: number;
    last: boolean;
    size: number;
    number: number;
    sort: SortData;
    first: boolean;
    numberOfElements: number;
    empty: boolean;
}

interface PageData {
    sort: SortData;
    offset: number;
    pageNumber: number;
    pageSize: number;
    unpaged: boolean;
    paged: boolean;
}

interface SortData {
    sorted: boolean;
    unsorted: boolean;
    empty: boolean;
}
