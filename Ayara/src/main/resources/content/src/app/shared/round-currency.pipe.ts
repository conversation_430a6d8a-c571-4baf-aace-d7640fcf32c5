import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'roundCurrency'
})
export class RoundCurrencyPipe implements PipeTransform {
  transform(value: number, currencyCode: string): string {
    if (!value || !currencyCode) {
      value = 0;
    }
    const locale = this.getLocaleForCurrency(currencyCode);

    const roundedValue = Math.round(value * 100) / 100;
    var val = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: (currencyCode || 'USD').toUpperCase(),
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(roundedValue);
    return val.charAt(0) === '-' ?'(' + val.substring(1, val.length) + ')' : val;
  }
  private getLocaleForCurrency(currency: string): string {
      switch (currency) {
        case 'INR': return 'en-IN';
        case 'USD': return 'en-US';
        case 'EUR': return 'de-DE';
        case 'GBP': return 'en-GB';
        case 'JPY': return 'ja-JP';
        case 'CNY': return 'zh-CN';
        default: return 'en-US'; // fallback
      }
    }
}