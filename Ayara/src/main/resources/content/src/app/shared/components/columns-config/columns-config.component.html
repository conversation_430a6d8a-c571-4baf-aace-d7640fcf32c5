<a (click)="onOpenConfigPopup()" class="add-column"><em class="fa fa-cog"></em>Columns</a>

<div id="add-column-popup" class="contracts_add_column" *ngIf="showConfigPopup">
    <div class="user-popup">
        <div class="content overflow">
            <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall"
                   (click)="onSelectAll()"/>
            <label for="selectall">Select All</label>
            <a class="close" title="Close" (click)="onCloseConfigPopup()">&times;</a>
            <ng-container *ngFor="let list of columnsList">
                <p-listbox [options]="list.columns">
                    <p-header>
                        {{list.label}}
                    </p-header>
                    <ng-template let-col let-index="index" pTemplate="item">
                        <div *ngIf="col.drag">
                            <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens"
                                 (onDragStart)="onDragStart(col, index)" (onDrop)="onDrop(index)">
                                <div class="d-flex align-items-center drag">
                                    <input type="checkbox" [checked]="col.showField"
                                           [(ngModel)]="col.showField"
                                           (change)="onSelectColumn()"/>
                                    <label class="col-label">{{col.header}}</label>
                                    <div class="ml-2 freeze-btn" (click)="onToggleFreeze(col)">
                                        <i [class]="list.iconClassNames"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="!col.drag">
                            <div class="ui-helper-clearfix">
                                <div class="d-flex align-items-center">
                                    <input type="checkbox" [checked]="col.showField"
                                           [(ngModel)]="col.showField"
                                           (change)="onSelectColumn()" [disabled]="!col.drag"/>
                                    <label class="col-label" [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                                    <div class="ml-2 freeze-btn" (click)="onToggleFreeze(col)">
                                        <i [class]="list.iconClassNames"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                </p-listbox>
            </ng-container>
        </div>
        <div class="pull-right">
            <a class="configColBtn" (click)="onSaveConfig()">Save</a>
            <a class="configColBtn conf-cancel" (click)="onCloseConfigPopup()">Cancel</a>
        </div>
    </div>
</div>
