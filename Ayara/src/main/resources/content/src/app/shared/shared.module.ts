import { CommonModule, DecimalPipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ChartsModule } from 'ng2-charts';
import { SharedModule } from 'primeng/api';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DragDropModule } from 'primeng/dragdrop';
import { DropdownModule } from 'primeng/dropdown';
import { FileUploadModule } from 'primeng/fileupload';
import { InputMaskModule } from 'primeng/inputmask';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ListboxModule } from 'primeng/listbox';
import { MenuModule } from 'primeng/menu';
import { MenubarModule } from 'primeng/menubar';
import { MessagesModule } from 'primeng/messages';
import { MultiSelectModule } from 'primeng/multiselect';
import { PaginatorModule } from 'primeng/paginator';
import { PanelModule } from 'primeng/panel';
import { PasswordModule } from 'primeng/password';
import { PickListModule } from 'primeng/picklist';
import { ProgressBarModule } from 'primeng/progressbar';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SliderModule } from 'primeng/slider';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { ToastModule } from 'primeng/toast';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { TooltipModule } from 'primeng/tooltip';
import { DateTimePipe } from '../date-time.pipe';
import { IsAuthorizedDirective } from '../roles-and-permissions/is-authorized.directive';
import { RoundPipe } from '../round.pipe';
import { NegativeParenthesisPipe } from '../negativeParenthesis.pipe';
import { UploadProgressComponent } from '../uploadProgressBar/uploadProgress.component';
import {ColumnsConfigComponent} from './components/columns-config/columns-config.component';
import { RoundCurrencyPipe } from './round-currency.pipe';



@NgModule({
  imports: [CommonModule, TableModule, ProgressBarModule, DropdownModule, AutoCompleteModule, InputMaskModule, PanelModule, TabViewModule, DialogModule, MenubarModule, TooltipModule, InputTextModule, PasswordModule, CalendarModule, RadioButtonModule, CheckboxModule, ListboxModule, InputTextareaModule, SliderModule, InputSwitchModule, ButtonModule, MenuModule, MultiSelectModule,
    SharedModule, ToggleButtonModule, BreadcrumbModule, FileUploadModule, ChartsModule, ToastModule, MessagesModule, ConfirmDialogModule,
    PickListModule, DragDropModule, FormsModule, ReactiveFormsModule, PaginatorModule, ProgressSpinnerModule],
  declarations: [NegativeParenthesisPipe, RoundPipe, DateTimePipe, IsAuthorizedDirective,UploadProgressComponent, ColumnsConfigComponent,RoundCurrencyPipe],
  exports: [CommonModule, TableModule, ProgressBarModule, DropdownModule, AutoCompleteModule, InputMaskModule, PanelModule, TabViewModule, DialogModule, MenubarModule, TooltipModule, InputTextModule, PasswordModule, CalendarModule, RadioButtonModule, CheckboxModule, ListboxModule, InputTextareaModule, SliderModule, InputSwitchModule, ButtonModule, MenuModule, MultiSelectModule,
    SharedModule, ToggleButtonModule, BreadcrumbModule, PaginatorModule, FileUploadModule, ChartsModule, ToastModule, MessagesModule, ConfirmDialogModule,
    PickListModule, DragDropModule, FormsModule, ReactiveFormsModule, RoundPipe, DateTimePipe, CardModule, IsAuthorizedDirective, UploadProgressComponent, ProgressSpinnerModule, ColumnsConfigComponent,NegativeParenthesisPipe,RoundCurrencyPipe],
  providers: [DecimalPipe, DateTimePipe, NegativeParenthesisPipe]
})

export class NewSharedModule { }
