import { Injectable } from '@angular/core';
import { CanDeactivate } from '@angular/router';
import { Observable } from 'rxjs';
import { ConfirmationService } from 'primeng/api';
import { LoadingService } from '../loading.service';

export interface ComponentCanDeactivate {
  hasUnsavedChanges(): boolean;
}

@Injectable()
export class PendingChangesGuard implements CanDeactivate<ComponentCanDeactivate> {

  constructor(private confirmationService: ConfirmationService, private loadingService: LoadingService) {}

  canDeactivate(component: ComponentCanDeactivate): Observable<boolean> | boolean {
    // Check if the component implements the hasUnsavedChanges method

    if (!component || typeof component.hasUnsavedChanges !== 'function') {
      
      return true;
    }

    const hasChanges = component.hasUnsavedChanges();
    console.log('Component has unsaved changes:', hasChanges);

    if (!hasChanges) {
      return true; 
    }
    this.loadingService.setLoading(false);

   
    return new Observable<boolean>(observer => {
      
      this.confirmationService.close();

    
      setTimeout(() => {
        this.confirmationService.confirm({
          message: 'You have unsaved changes. Do you want to save changes?',
          header: 'Confirmation',
          icon: 'pi pi-exclamation-triangle',
          key: 'navigationConfirm',
          accept: () => {
            if (typeof component['save'] === 'function') {
              component['save']();
            }

        
            observer.next(false);
            observer.complete();
          },
          reject: () => {
           
            if (typeof component['getAllRmanFunctions'] === 'function' && component['roleId']) {
              component['getAllRmanFunctions'](component['roleId']);
            }

            
            observer.next(true);
            observer.complete();
          }
        });
      }, 100); 
    });
  }
}

