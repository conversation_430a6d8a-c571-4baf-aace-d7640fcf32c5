interface ILabels {
    [index: string]: string;
}

export class RmanFmvRulesDefParaValueLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["attribute10"] = "Attribute10";
        this.fieldLabels["dealFlag"] = "Deal Flag";
        this.fieldLabels["attribute14"] = "Attribute14";
        this.fieldLabels["attribute13"] = "Attribute13";
        this.fieldLabels["ruleHeaderId"] = "Rule Header Id";
        this.fieldLabels["attribute12"] = "Attribute12";
        this.fieldLabels["attribute11"] = "Attribute11";
        this.fieldLabels["lastUpdateDate"] = "Last Update Date";
        this.fieldLabels["parameterValueId"] = "Parameter Value Id";
        this.fieldLabels["parameterId"] = "Parameter Name";
        this.fieldLabels["attribute3"] = "Attribute3";
        this.fieldLabels["createdBy"] = "Created By";
        this.fieldLabels["attribute2"] = "Attribute2";
        this.fieldLabels["lastUpdatedBy"] = "Last Updated By";
        this.fieldLabels["attribute1"] = "Attribute1";
        this.fieldLabels["creationDate"] = "Creation Date";
        this.fieldLabels["attribute9"] = "Attribute9";
        this.fieldLabels["attribute8"] = "Attribute8";
        this.fieldLabels["attribute7"] = "Attribute7";
        this.fieldLabels["attribute6"] = "Attribute6";
        this.fieldLabels["attribute5"] = "Attribute5";
        this.fieldLabels["attribute4"] = "Attribute4";
        this.fieldLabels["parameterGroup"] = "Parameter Group";
        this.fieldLabels["fmvRuleDefId"] = "Fmv Rule Def Id";
        this.fieldLabels["attribute15"] = "Attribute15";
        this.fieldLabels["qualifier"] = "Qualifier";
        this.fieldLabels["andOr"] = "And/Or";
        this.fieldLabels["parameterValue"] = "Parameter Value";
    }

}
