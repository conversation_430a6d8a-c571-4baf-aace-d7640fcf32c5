interface ILabels {
    [index: string]: string;
}

export class RmanLookupsVLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["lookupCodeEndDate"] = "Lookup Code End Date";
        this.fieldLabels["lookupTypeName"] = "Lookup Type Name";
        this.fieldLabels["lookupTypeEnabledFlag"] = "Lookup Type Enabled Flag";
        this.fieldLabels["lookupCodeEnabledFlag"] = "Lookup Code Enabled Flag";
        this.fieldLabels["lookupTypeDescription"] = "Lookup Type Description";
        this.fieldLabels["lookupDescription"] = "Lookup Description";
        this.fieldLabels["lookupCode"] = "Lookup Code";
        this.fieldLabels["lookupTypeEndDate"] = "Lookup Type End Date";
        this.fieldLabels["lookupTypeStartDate"] = "Lookup Type Start Date";
        this.fieldLabels["lookupCodeStartDate"] = "Lookup Code Start Date";
        this.fieldLabels["lookupTypeMeaning"] = "Lookup Type Meaning";
    }

}
