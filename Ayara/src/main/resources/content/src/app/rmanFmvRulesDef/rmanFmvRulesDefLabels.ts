interface ILabels {
    [index: string]: string;
}

export class RmanFmvRulesDefLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["quarterStartDate"] = "Start Date";
        this.fieldLabels["dealFlag"] = "Deal Flag";
        this.fieldLabels["attribute30"] = "Attribute30";
        this.fieldLabels["lastUpdateDate"] = "Last Update Date";
        this.fieldLabels["approverId"] = "Approver Id";
        this.fieldLabels["attribute29"] = "Attribute29";
        this.fieldLabels["attribute28"] = "Attribute28";
        this.fieldLabels["attribute27"] = "Attribute27";
        this.fieldLabels["attribute26"] = "Attribute26";
        this.fieldLabels["attribute3"] = "Attribute3";
        this.fieldLabels["createdBy"] = "Created By";
        this.fieldLabels["attribute2"] = "Currency";
        this.fieldLabels["lastUpdatedBy"] = "Last Updated By";
        this.fieldLabels["attribute1"] = "Attribute1";
        this.fieldLabels["creationDate"] = "Creation Date";
        this.fieldLabels["attribute9"] = "Attribute9";
        this.fieldLabels["fmvPrice"] = "SSP Price";
        this.fieldLabels["attribute8"] = "Attribute8";
        this.fieldLabels["attribute7"] = "Attribute7";
        this.fieldLabels["attribute6"] = "Attribute6";
        this.fieldLabels["quarterName"] = "Quarter Name";
        this.fieldLabels["attribute5"] = "Attribute5";
        this.fieldLabels["attribute4"] = "Attribute4";
        this.fieldLabels["fmvPct"] = "SSP pct";
        this.fieldLabels["min"] = "Min";
        this.fieldLabels["fmvRuleDefId"] = "SSP rule id";
        this.fieldLabels["attribute10"] = "Attribute10";
        this.fieldLabels["attribute14"] = "Attribute14";
        this.fieldLabels["max"] = "Max";
        this.fieldLabels["attribute13"] = "Attribute13";
        this.fieldLabels["attribute12"] = "Attribute12";
        this.fieldLabels["attribute11"] = "Attribute11";
        this.fieldLabels["fmvCategory"] = "SSP Category";
        this.fieldLabels["quarterEndDate"] = "End Date";
        this.fieldLabels["median"] = "Median";
        this.fieldLabels["fmvRuleStatus"] = "SSP Rule Status";
        this.fieldLabels["fmvRuleName"] = "SSP Rule Name";
        this.fieldLabels["attribute21"] = "Attribute21";
        this.fieldLabels["attribute20"] = "Attribute20";
        this.fieldLabels["attribute25"] = "Attribute25";
        this.fieldLabels["attribute24"] = "Attribute24";
        this.fieldLabels["attribute23"] = "Attribute23";
        this.fieldLabels["attribute22"] = "Attribute22";
        this.fieldLabels["enabledFlag"] = "Enabled Flag";
        this.fieldLabels["attribute18"] = "Attribute18";
        this.fieldLabels["attribute17"] = "Attribute17";
        this.fieldLabels["attribute16"] = "Attribute16";
        this.fieldLabels["attribute15"] = "Attribute15";
        this.fieldLabels["vsoe"] = "Vsoe";
        this.fieldLabels["fmvType"] = "SSP Type";
        this.fieldLabels["attribute19"] = "Attribute19";
        this.fieldLabels["legalEntityId"] = "Legal Entity Id";
        // this.fieldLabels["legalEntityName"] = "Entity Name";
        this.fieldLabels["legalEntityName"] = "Legal Entity";
    }

}
