export interface RmanFmvRulesDef {
    quarterStartDate: any;
    dealFlag: any;
    attribute30: any;
    lastUpdateDate: any;
    approverId: any;
    attribute29: any;
    attribute28: any;
    attribute27: any;
    attribute26: any;
    attribute3: any;
    createdBy: any;
    attribute2: any;
    lastUpdatedBy: any;
    attribute1: any;
    creationDate: any;
    attribute9: any;
    fmvPrice: any;
    attribute8: any;
    attribute7: any;
    attribute6: any;
    quarterName: any;
    attribute5: any;
    attribute4: any;
    fmvPct: any;
    min: any;
    fmvRuleDefId: any;
    attribute10: any;
    attribute14: any;
    max: any;
    attribute13: any;
    attribute12: any;
    attribute11: any;
    fmvCategory: any;
    quarterEndDate: any;
    median: any;
    fmvRuleStatus: any;
    fmvRuleName: any;
    attribute21: any;
    attribute20: any;
    attribute25: any;
    attribute24: any;
    attribute23: any;
    attribute22: any;
    enabledFlag: any;
    attribute18: any;
    attribute17: any;
    attribute16: any;
    attribute15: any;
    vsoe: any;
    fmvType: any;
    attribute19: any;
    legalEntityId: any;
    legalEntityName: any;
    bookId: any;
}
