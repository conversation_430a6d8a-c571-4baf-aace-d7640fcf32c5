export const DEAL_DETAILS_COLUMNS = [
    {
        field: 'dealQuoteNum',
        header: 'Deal Number',
        showField: true,
        drag: true,
        alignment: 'left',
        display: 'table-cell',
        type: 'text',
        footer: 'totals'
    },
    {
        field: 'productCatgeory',
        header: 'Product Category',
        showField: true,
        drag: true,
        alignment: 'left',
        display: 'table-cell',
        type: 'text',
        footer: 'empty'
    },
    {
        field: 'extendedListAmount',
        header: 'Ext List Amount',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'totalExtendedListAmt'
    },
    {
        field: 'lineAmount',
        header: 'Bookings Value',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'totalLineAmt'
    },
    {
        field: 'discount',
        header: 'Discount',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'percent',
        footer: 'totalDiscount'
    },
    {
        field: 'revenueAmount',
        header: 'Revenue Amount',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'totalAllocationAmt'
    },
    {
        field: 'carveInOut',
        header: 'Carve In/ Out',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'totalCarveInOut'
    },
    {
        field: 'grossMarginPercentage',
        header: 'Gross Margin%',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'percent',
        footer: 'totalGrossMarginPercent'
    },
    {
        field: 'allocationMarginPercentage',
        header: 'Allocated Margin%',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'percent',
        footer: 'totalAllocatedMarginPercent'
    },
    {
        field: 'revenueAdjustment',
        header: 'Adjusted Revenue',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'totalRevenueAdjustment'
    },
    {
        field: 'lineCost',
        header: 'Standard Cost',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'parentheses',
        footer: 'totalStandardCost'
    },
    {
        field: 'adjLineCost',
        header: 'Adjusted Cost',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'parentheses',
        footer: 'totalAdjustedCost'
    },
    {
        field: 'grossMargin',
        header: 'Gross Margin',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'totalGrossMargin'
    },
    {
        field: 'allocatedMarginAmount',
        header: 'Allocated Margin',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'totalAllocatedMarginAmount'
    }
];

export const DEALS_SUMMARY_COLUMNS = [
    {field: 'dealNumber', header: 'Deal Number', showField: true, drag: true, display: 'table-cell', type: 'link'},
    //{field: 'quoteNumber', header: 'Quote Number', showField: true, drag: false, display: 'table-cell', type: 'text'},
    {field: 'customerName', header: 'Account', showField: true, drag: true, display: 'table-cell', type: 'text'},
    {field: 'trxCurrency', header: 'Currency', showField: true, drag: true, display: 'table-cell', type: 'text'},
    {field: 'amount', header: 'Amount', showField: true, drag: true, display: 'table-cell', type: 'round'},
    {field: 'legalEntityName', header: 'Legal Entity', showField: true, drag: true, display: 'table-cell', type: 'text'},
    {field: 'dealDate', header: 'Deal Date', showField: true, drag: true, display: 'table-cell', type: 'date'},
    {field: 'revenueGuidance', header: 'Guidance', showField: true, drag: true, display: 'table-cell', type: 'symbol'},
    {field: 'dealStatus', header: 'Status', drag: true, showField: true, display: 'table-cell', type: 'text'},
];


export const DEAL_ALLOCATION_COLUMNS = [
    {
        field: 'pobGrouping',
        header: 'POB ID',
        showField: true,
        drag: true,
        alignment: 'left',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'arrgLineNumber',
        header: 'Revenue Contract Line#',
        showField: true,
        drag: true,
        alignment: 'left',
        display: 'table-cell',
        type: 'number',
        footer: 'empty',
        isFrozen: false
    },
    {field: 'customerPoNum', header: 'PO', showField: true, drag: true, display: 'table-cell', type: 'number', footer: 'empty'},
    {
        field: 'orderNumber',
        header: 'SO#',
        showField: true,
        drag: true,
        alignment: 'left',
        display: 'table-cell',
        type: 'number',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'sourceLineNumber',
        header: 'SO Line#',
        showField: true,
        drag: true,
        alignment: 'left',
        display: 'table-cell',
        type: 'number',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'dealNumber',
        header: 'Contract/Deal#',
        showField: true,
        drag: true,
        alignment: 'left',
        display: 'table-cell',
        type: 'number',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'dealLineNumber',
        header: 'Line#',
        showField: true,
        drag: true,
        alignment: 'left',
        display: 'table-cell',
        type: 'number',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'productName',
        header: 'Product Name',
        drag: true,
        showField: true,
        alignment: 'left',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'productDescription',
        header: 'Product Description',
        drag: true,
        showField: true,
        alignment: 'left',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'uomCode',
        header: 'UOM',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'quantity',
        header: 'Quantity',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'number',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'dealCurrencyCode',
        header: 'Currency',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'unitListPrice',
        header: 'Unit List Price',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'unitSellingPrice',
        header: 'Unit Selling Price',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'netPriceDiscount',
        header: 'Net Price Discount%',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'percent',
        footer: 'totals',
        isFrozen: false
    },
    {
        field: 'extendedListAmount',
        header: 'Extended List Amount',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'totalListAmount',
        isFrozen: false
    },
    {
        field: 'lineAmount',
        header: 'Bookings Value',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'lineAmountTotal',
        isFrozen: false
    },
    {
        field: 'vc',
        header: 'Variable Consideration',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'vcTotal',
        isFrozen: false
    },
    {
        field: 'allocableAmount',
        header: 'Allocable Net Price',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'allocableNetTotal',
        isFrozen: false
    },
    {
        field: 'elementType',
        header: 'Element Type',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'bundleFlag',
        header: 'Bundle Flag',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'parentLineId',
        header: 'Parent Product',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'number',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'productGroup',
        header: 'Product Family',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'productType',
        header: 'Product Line',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'fmvRuleName',
        header: 'SSP Rule Name',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'fmvCategory',
        header: 'SSP Category',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'fmvType',
        header: 'SSP Type',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'fmvMin',
        header: 'SSP Min',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'fmvMean',
        header: 'SSP Trx',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'fmvMax',
        header: 'SSP Max',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'bundleAttributionList',
        header: 'Bundle Attribution List',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'bround',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'bundleAttributionNet',
        header: 'Bundle Attribution Net',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'bround',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'sspLow',
        header: 'SSP Low',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'sspHigh',
        header: 'SSP High',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'empty',
        isFrozen: false
    },
    {field: 'ssp', header: 'SSP', showField: true, drag: true, alignment: 'right', display: 'table-cell', type: 'round', footer: 'empty', isFrozen: false},
    {
        field: 'fmvAmount',
        header: 'Extended SSP',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'fvTotal',
        isFrozen: false
    },
    {
        field: 'allocationAmount',
        header: 'Allocation Amount',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'allocationTotal',
        isFrozen: false
    },
    {
        field: 'cvInOutAmount',
        header: 'Carve In/Carve Out ',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'cvInOutAmountTotal',
        isFrozen: false
    },
    {
        field: 'allocationFlag',
        header: 'Allocation Flag',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'allocationInclusive',
        header: 'Allocation Inclusive',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'totalCost',
        header: 'Total Cost',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'parentheses',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'fxRate',
        header: 'Fx Rate',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'fxCurrency',
        header: 'Fx Currency',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'number',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'fxDate',
        header: 'Fx Date',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'date',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'allocationAmountFc',
        header: 'Allocation Amount(FC)',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'allocationFcTotal',
        isFrozen: false
    },
    {
        field: 'allocationMarginPercentage',
        header: 'Line to Revenue%',
        showField: true,
        drag: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'percent',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'grossMarginPercentage',
        header: 'Allocated Margin%',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'percent',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'allocationGuidance',
        header: 'Revenue Guidance',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'symbol',
        footer: 'allocStatusTotal',
        isFrozen: false
    },
    {
        field: 'allocationMarginGuidance',
        header: 'Margin Guidance',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'symbol',
        footer: 'allocMarginStatusTotal',
        isFrozen: false
    },
    {
        field: 'discountGuidance',
        header: 'Discount Guidance',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'symbol',
        footer: 'discountStatusTotal',
        isFrozen: false
    },
    {
        field: 'revenueGuidance',
        header: 'Deal Guidance',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'symbol',
        footer: 'statusTotal',
        isFrozen: false
    },
    {
        field: 'comments',
        header: 'Guidance Comments',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'text',
        footer: 'commentsTotal',
        isFrozen: false
    },
    {
        field: 'adjDealLineCost',
        header: 'Adjusted Cost',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'parentheses',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'adjRevenue',
        header: 'Adjusted Revenue',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'empty',
        isFrozen: false
    },
    {
        field: 'grossMargin',
        header: 'Gross Margin',
        drag: true,
        showField: true,
        alignment: 'right',
        display: 'table-cell',
        type: 'round',
        footer: 'totalGrossMargin',
        isFrozen: false
    }
];
