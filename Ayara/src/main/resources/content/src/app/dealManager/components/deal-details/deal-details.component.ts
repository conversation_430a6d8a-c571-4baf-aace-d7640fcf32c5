import {AfterContentChecked, ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {NotificationService} from '../../../shared/notifications.service';

@Component({
    selector: 'app-deal-details',
    templateUrl: './deal-details.component.html',
    styleUrls: ['./deal-details.component.scss']
})
export class DealDetailsComponent implements OnInit, AfterContentChecked {

    dealNumber: string;
    constructor(
        private route: ActivatedRoute,
        private changeDetector: ChangeDetectorRef,
        private notification: NotificationService
    ) {
    }

    ngOnInit(): void {
        this.dealNumber = this.route.snapshot.paramMap.get('dealNumber');
    }

    ngAfterContentChecked() {
        this.changeDetector.detectChanges();
    }

    onCopyNumber() {
        navigator.clipboard.writeText(this.dealNumber)
            .finally(() => {
                this.notification.showInfo('Deal number is copied', 2000);
            });
    }
}
