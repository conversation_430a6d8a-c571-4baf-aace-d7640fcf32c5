export interface DealSummary {
    sno: number;
    dealQuoteNum: number;
    productCatgeory: string;
    extendedListAmount: number;
    lineAmount: number;
    discount: number;
    revenueAmount: number;
    carveInOut: number;
    grossMarginPercentage: number;
    allocationMarginPercentage: number;
    revenueAdjustment: number;
    lineCost: number;
    grossMargin: number;
    allocatedMarginAmount: number;
}

export interface DealSummaryTotals {
    totalExtendedListAmt: number;
    totalLineAmt: number;
    totalDiscount: number;
    totalAllocationAmt: number;
    totalCarveInOut: number;
    totalGrossMarginPercent: number;
    totalAllocatedMarginPercent: number;
    totalRevenueAdjustment: number;
    totalAdjustedAllocatedMarginPercent: number;
    totalStandardCost: number;
    totalAdjustedCost: number;
    totalGrossMargin: number;
    totalAllocatedMarginAmount: number;
}

