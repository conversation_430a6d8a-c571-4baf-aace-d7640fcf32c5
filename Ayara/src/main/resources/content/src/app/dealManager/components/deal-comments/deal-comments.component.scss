.arrangementMgrTbl.comments-table tr td {
  width: 100% !important;
  white-space: pre-wrap !important;
}

.arrangementMgrTbl.comments-table {
  .small-col {
    width: 200px !important;
  }
}


.fa-paper-plane {
  transform: scale(2);
}

.fa-share {
  transform: rotateY(180deg);
}

.comments {
  &__list {
    margin-top: 40px;

    &-item {
      display: flex;
      list-style: none;
    }
  }

  &__form {
    form {
      display: flex;
      align-items: center;
      max-width: 1100px;
      width: 100%;
      margin: 40px auto 20px auto;

      textarea {
        border: 2px solid #262d74;
        border-radius: 6px;
        flex: 1;
      }

      .add-btn {
        cursor: pointer;
        margin: 0 0 0 20px;
        height: auto !important;
        background: none;

        em {
          color: #262b74;
        }

        &:hover {
          opacity: .7;
        }

        &[disabled] {
          cursor: not-allowed;
          opacity: .7;
          em {
            color: #1fac90
          }
        }
      }
    }
  }
}

.comment {

  &__user {
    margin-right: 5px;
    color: #262b74;
    font-weight: 700;
  }

  &__content {
    margin-right: 5px;
    color: #000;
  }
}
