import {Component, OnInit, ViewEncapsulation} from '@angular/core';
import {DealsService} from '../../services/deals.service';
import {ActivatedRoute} from '@angular/router';
import {Observable} from 'rxjs';
import {DealComment} from '../../models/dealComment';
import {finalize, map, tap} from 'rxjs/operators';
import {NotificationService} from '../../../shared/notifications.service';

@Component({
    selector: 'app-deal-comments',
    templateUrl: './deal-comments.component.html',
    styleUrls: ['./deal-comments.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class DealCommentsComponent implements OnInit {

    dealId: string;
    commentsData$: Observable<DealComment[]>;

    isLoading = false;
    totalElements: number;
    inputComment = '';

    constructor(
        private dealsService: DealsService,
        private route: ActivatedRoute,
        private notificationService: NotificationService,
    ) {
    }

    ngOnInit(): void {
        this.dealId = this.route.snapshot.parent.paramMap.get('dealId');
        this.getDealComments();
    }

    getDealComments(event?: any) {
        const page = event ? event.first / event.rows : 0;
        this.isLoading = true;
        this.commentsData$ = this.dealsService.getDealComments(this.dealId, page)
            .pipe(
                tap(data => this.totalElements = data.totalElements),
                map(data => data.content),
                finalize(() => {
                    this.isLoading = false;
                }),
            );
    }

    onAdd() {
        const comment = {
            dealArrangementId: this.dealId,
            userComment: this.inputComment
        };
        this.dealsService.addDealComment(comment)
            .subscribe(
                () => {
                    this.notificationService.showSuccess('Saved successfully');
                    this.getDealComments();
                    this.inputComment = '';
                },
                (error) => this.notificationService.showError(error.message)
            );
    }
}
