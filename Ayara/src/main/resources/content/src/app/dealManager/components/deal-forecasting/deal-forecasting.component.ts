import { Component, OnInit } from '@angular/core';
import {RmanDealRfcstV} from '../../../rmanDealRfcstV/rmanDealRfcstV';
import * as appSettings from '../../../appsettings';
import {RmanDealRfcstVService} from '../../../rmanDealRfcstV/rmanDealRfcstVservice';
import {NavigationEnd, Router} from '@angular/router';
import {NotificationService} from '../../../shared/notifications.service';
import {Table} from 'primeng/table';
import {CommonSharedService} from '../../../shared/common.service';
import {Column} from '../../../shared/models/column.interface';

@Component({
  selector: 'app-deal-forecasting',
  templateUrl: './deal-forecasting.component.html'
})
export class DealForecastingComponent implements OnInit {

  displayDialog: boolean;

  displaySearchDialog: boolean;

  rmanDealRfcstV: any = new RmanDealRfcstVImpl();

  isSerached: number = 0;

  rDealFCSTViewList: any[];

  globalCols = [];
  frozenCols: Column[] = [];
  scrollableCols: Column[] = [];
  frozenWidth: string;
  tableHeaderTmpl = ['header', 'frozenheader'];
  tableBodyTmpl = ['body', 'frozenbody'];
  isTableFrozen = false;
  noData = appSettings.noData;
  totalRecords: any;

  columnOptions: any[];

  paginationOptions = {};

  pages: {};
  arrIdK: any;

  datasource: any[];
  pageSize: number;
  totalElements: number;
  isSelectAllChecked = true;
  loading: boolean;
  isConfigOpened = false;
  startIndex: number;
  showPaginator = true;
  userId: any;



  constructor(private rmanDealRfcstVService: RmanDealRfcstVService, private router: Router,
              private notificationService: NotificationService,
              private commonSharedService: CommonSharedService) {

    // generate list code
    this.paginationOptions = { 'pageNumber': 0, 'pageSize': 10 };

    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.arrIdK = event.url.split('/')[3];
      }
    });


  }

  ngOnInit() {
    this.getDealForecastingView();
  }

  reset(dt: Table) {
    dt.reset();
    this.paginationOptions = {};
    this.rmanDealRfcstV = new RmanDealRfcstVImpl();
    this.getDealForecastingView();
  }

  exportExcel() {
    let serviceUrl = this.rmanDealRfcstVService.getServiceUrl(this.paginationOptions, { 'arrangementId': this.arrIdK }, 1);
    window.location.href = serviceUrl;

  }

  getDealForecastingView() {
    this.loading = true;
    this.rmanDealRfcstVService.getRmanForecastView(this.arrIdK).then((data: any) => {
      this.rDealFCSTViewList = data.Content;
      this.loading = false;
      let dTemp = this.rDealFCSTViewList[0];
      for (let prop in dTemp) {
        if (['Element Type'].includes(prop)) {
          this.globalCols.push({
            field: prop,
            header: prop,
            drag: true,
            showField: true,
            style: { 'width': '100px', 'text-align': this.setColAlignment(prop)},
            hide: false
          });

        } else if (['Arrangement Number', 'SKU'].includes(prop)) {
          this.globalCols.push({
            field: prop,
            header: prop,
            drag: true,
            showField: true,
            style: { 'width': '180px', 'text-align': this.setColAlignment(prop)},
            hide: false
          });
        } else if (['Expected Booking Amount'].includes(prop)) {
          this.globalCols.push({
            field: prop,
            header: prop,
            drag: true,
            showField: true,
            style: { 'width': '180px', 'text-align': this.setColAlignment(prop)},
            hide: false
          });
        } else {
          this.globalCols.push({
            field: prop,
            header: prop,
            drag: true,
            showField: true,
            style: { 'width': '100px', 'text-align': this.setColAlignment(prop)},
            hide: false
          });
        }
      }
      this.getTableColumns('Deal Details', 'Deal Forecasting');
      this.rDealFCSTViewList = data.Content;
      this.totalRecords = this.rDealFCSTViewList.length;
    }).catch((err: any) => {
      this.loading = false;
      this.notificationService.showError('Error occured while getting data');
    });
  }

  setColAlignment(colName: string) {
   return  ['Line No', 'Ayara Line Id', 'Quote Line Id', 'Product Name'].some(name => name === colName) ? 'left' : 'right';
  }

  getTableColumns(pageName: string, tableName: string) {
    this.isSelectAllChecked = true;
    this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
      if (response && response != null && response.userId) {
        const cols = response.tableColumns.split('/');
        const colsList: string[] = cols[0].split(',');
        const frozenColsList: string[] = cols[1] ? cols[1].split(',') : [];

        this.globalCols.forEach(col => {
          col.showField = [...colsList, ...frozenColsList].indexOf(col.field) !== -1;
        });

        const hiddenCols = this.globalCols.filter(col => !col.showField);

        colsList.forEach(colName => {
          const column = this.globalCols.find(col => col.field === colName);
          if (column) {
            this.scrollableCols.push(column);
          }
        });
        frozenColsList.forEach(colName => {
          const column = this.globalCols.find(col => col.field === colName);
          if (column) {
            column.isFrozen = true;
            this.frozenCols.push(column);
          }
        });
        hiddenCols.forEach(col => {
          this.scrollableCols.push(col);
        });

        this.globalCols = [...this.frozenCols, ...this.scrollableCols];
        this.getFrozenWidth();
        if ((colsList.length + frozenColsList.length) !== this.globalCols.length) {
          this.isSelectAllChecked = false;
        }
        this.showPaginator = (colsList.length + frozenColsList.length) !== 0;
        this.userId = response.userId;
      } else {
        this.recollectColumns();
      }
    }).catch(() => {
      this.notificationService.showError('Error occured while getting table columns data');
      this.loading = false;
    });
  }


  onConfiguringColumns() {
    this.isConfigOpened = true;
  }

  onToggleFreeze(column: Column) {
    const globalCol = this.globalCols.find(col => col.field === column.field);
    globalCol.isFrozen = !globalCol.isFrozen;
    this.recollectColumns();
  }

  recollectColumns() {
    this.frozenCols = this.globalCols.filter(col => col.isFrozen);
    this.scrollableCols = this.globalCols.filter(col => !col.isFrozen);
    this.getFrozenWidth();
  }

  getFrozenWidth() {
    const visibleFrozenCols = this.frozenCols.filter(col => col.showField);
    this.frozenWidth = (visibleFrozenCols.length ? visibleFrozenCols.length * 150 : 0) + 'px';
    this.isTableFrozen = !!this.frozenCols && !!this.frozenCols.length && !!this.frozenCols.some(col => !!col.showField);
  }

  selectColumns() {
    this.isSelectAllChecked = this.globalCols.filter(item => !item.showField).length <= 0;
    this.getFrozenWidth();
  }

  onSelectAll() {
    this.isSelectAllChecked = !this.isSelectAllChecked;
    this.globalCols.forEach(col => {
      if (this.isSelectAllChecked) {
        col.showField = true;
      } else {
        if (col.drag) {
          col.showField = false;
        }
      }
    });
  }

  closeConfigureColumns() {
    this.isConfigOpened = false;
  }

  saveColumns() {
    let selectedFrozenCols = '';
    let selectedScrollableCols = '';
    this.isConfigOpened = false;

    this.frozenCols.forEach((col, index) => {
      if (col.showField) {
        selectedFrozenCols += col.field;
        if (index < (this.frozenCols.length - 1)) {
          selectedFrozenCols += ',';
        }
      }
    });
    this.scrollableCols.forEach((col, index) => {
      if (col.showField) {
        selectedScrollableCols += col.field;
        if (index < (this.scrollableCols.length - 1)) {
          selectedScrollableCols += ',';
        }
      }
    });

    const configuredCols = selectedScrollableCols + '/' + selectedFrozenCols ;
    this.loading = true;
    this.commonSharedService.saveOrUpdateTableColumns('Deal Details', 'Deal Forecasting', configuredCols, this.userId).then((response) => {
      this.userId = response['userId'];
      this.loading = false;
    }).catch(() => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });
  }
}


class RmanDealRfcstVImpl implements RmanDealRfcstV {
  constructor(public glPeriod?: any, public expectedBookingAmount?: any, public amount?: any, public qty?: any, public netPrice?: any, public elementType?: any, public startDate?: any, public arrangementName?: any, public sku?: any, public arrangementId?: any) { }
}

interface ILabels {
  [index: string]: string;
}

