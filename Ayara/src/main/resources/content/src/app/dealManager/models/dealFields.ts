export interface DealFields {
    dealArrangementId: number;
    dealSummaryId: number;
    dealHeaderId: number;
    dealNumber: string;
    quoteNumber: string;
    customerName: string;
    amount: string;
    legalEntityName: string;
    dealDate: string;
    revenueGuidance: string;
    revenueGuidanceCode: number;
    dealStatus: string;
    trxCurrency: string;
}

export interface SearchData {
    revenueGuidance: string;
    dealStatus: string;
    globalSearch: string;
}
