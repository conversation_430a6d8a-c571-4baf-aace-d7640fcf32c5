@import "~src/assets/sass/_variables.scss";

:host {
  color: $textSecondaryColor;
}

.card-block {
  margin-top: 0;
}

.title {
  cursor: pointer;
  font-size: 20px;
  color: $blueColor;
  padding: 15px;
  font-weight: 700;
  margin-left: 15px;

  &:hover {
    text-decoration: underline;
  }
}

.copy-icon {
  cursor: pointer;
  font-size: 20px;
  color: $blueColor;

  &:hover {
    opacity: .7;
  }
}

.top-menu {
  display: flex;
  justify-content: center;
}

.tabs-container {
  display: flex;
  align-items: center;
}

.tab-item {
  border: 1px solid $greenColor;
  margin-left: -1px;
  padding: 1em;
  font-size: 13px;
  line-height: 100%;
  font-weight: 600;
  cursor: pointer;

  &.active, &:hover {
    color: #fff;
    background: $greenColor;
  }

  &:first-child{
    border-right: 1px solid $greenColor;
    border-left: 1px solid $greenColor;
    border-radius: 6px 0 0 6px;
  }
   &:last-child {
    border-right: 1px solid $greenColor;
    border-left: 1px solid $greenColor;
    border-radius: 0 6px 6px 0;
  }
}

.icons-list {
  a {
    font-size: 14px;
  }

  span {
    padding-left: 5px;
    font-size: $fontSize;
  }
}

.info {
  &__block {
    display: flex;
    margin: 30px 0;
  }

  &__item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 200px;
    border-radius: 6px;
    height: 100px;
    padding: 20px;
    margin-right: 30px;
    font-size: 18px;
    box-shadow: $boxShadow;

    &-value {
      color: $greenColor;
    }

    &.comparison {
      width: auto;
      align-items: start;
      justify-content: start;
    }
  }
}

.comparison-item {
  display: flex;
  align-items: center;

  .info__item-title {
    width: 90px;
  }

  .line {
    height: 2px;
    margin: 0 5px;
  }

  &:nth-child(even) {

    .info__item-value {
      color: $blueColor;
    }

    .line {
      background-color: $blueColor;
    }
  }

  &:nth-child(odd) {
    .line {
      background-color: $greenColor;
    }
  }
}

.icons-list {
  margin-left: 20px;
  margin-bottom: 20px;
}
