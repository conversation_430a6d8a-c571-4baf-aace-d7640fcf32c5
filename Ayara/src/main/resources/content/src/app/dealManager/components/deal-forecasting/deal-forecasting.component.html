<div class="content-section implementation">
</div>


<p-panel [style]="{'margin-bottom':'20px'}">
    <p-header>
        <div class="pull-right icons-list">

            <app-columns-config
                    [frozenCols]="frozenCols"
                    [scrollableCols]="scrollableCols"
                    [isSelectAllChecked]="isSelectAllChecked"
                    [showConfigPopup]="isConfigOpened"
                    (openConfig)="onConfiguringColumns()"
                    (closeConfig)="closeConfigureColumns()"
                    (saveConfig)="saveColumns()"
                    (toggleFreeze)="onToggleFreeze($event)"
                    (selectColumn)="selectColumns()"
                    (selectAll)="onSelectAll()"
            ></app-columns-config>

            <a (click)="reset(dt)" title="Reset">
                <em class="fa fa-refresh"></em>
            </a>
            <a (click)="dt.exportCSV()" title="Export">
                <em class="fa fa-external-link"></em>
            </a>
        </div>
    </p-header>

    <div class="x-scroll">
        <p-table class="ui-datatable arrangementMgrTbl wide-columns" [class]="{unfrozen: !isTableFrozen}"
                 #dt
                 [columns]="scrollableCols"
                 [frozenColumns]="frozenCols"
                 [frozenWidth]="frozenWidth"
                 [loading]="loading"
                 [value]="rDealFCSTViewList" selectionMode="single" [paginator]="true"
                 [rows]="10"
                 [totalRecords]="totalRecords"
                 scrollable="true"
                 scrollHeight="500px"
                 exportFilename="RmanDealRfcstV"
        >

            <ng-template pTemplate="colgroup" let-columns>
                <colgroup>
                    <ng-container *ngFor="let col of columns">
                        <col *ngIf="col.showField">
                    </ng-container>
                </colgroup>
            </ng-template>


            <ng-container *ngFor="let template of tableHeaderTmpl">
                <ng-template [pTemplate]="template" let-columns>
                    <tr>
                        <ng-container *ngFor="let col of columns">
                            <th *ngIf="col.showField" [style]="col.style">
                                {{col.header === 'Net Price' ? 'Bookings Value' : col.header}}
                            </th>
                        </ng-container>
                    </tr>
                </ng-template>
            </ng-container>

            <ng-container *ngFor="let template of tableBodyTmpl">
                <ng-template [pTemplate]="template" let-rowData let-columns="columns">
                    <tr [pSelectableRow]="rowData">
                        <ng-container *ngFor="let col of columns">
                            <td *ngIf="col.showField" [style]="col.style">
						<span title="{{rowData[col.field]}}">{{ col.type == 'number' ? (rowData[col.field]|number:'1.2-2') : rowData[col.field]}}
						</span>
                            </td>
                        </ng-container>
                    </tr>
                </ng-template>
            </ng-container>


            <ng-template pTemplate="emptymessage">
                <div class="no-results-data">
                    <p>{{noData}}</p>
                </div>
            </ng-template>


        </p-table>
    </div>
</p-panel>
