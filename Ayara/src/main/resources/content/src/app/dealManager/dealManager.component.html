<div class="card-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card-block">
                    <h2>Deal Manager</h2>

                    <div class="search__container">
                        <form>
                            <input pInputText class="" name="searchKey" id="searchKey"
                                   [(ngModel)]="searchTerm"
                                   placeholder="Search here..."/>
                            <a class="globalSearchBtn" (click)="commonSearch()">
                                <em class="fa fa-search" aria-hidden="true"></em>
                            </a>
                            <a *ngIf="isSearched" class="globalSearchBtnx" (click)="onResetGlobalSearch()">
                                <em class="fa fa-times" aria-hidden="true"></em>
                            </a>
                        </form>
                    </div>

                    <div class="pull-right icons-list">
                        <!--                        <a (click)="onClickAdvanceSearch($event)" class="add-column">-->
                        <!--                            <em class="fa fa-search" *ngIf="!displayAdvancedSearch"></em>-->
                        <!--                            <em class="fa fa-times" *ngIf="displayAdvancedSearch"></em>Advanced Search</a>-->

                        <a (click)="onEnablingFilters()" class="clear-filters">
							<span *ngIf="!isFilterEnabled">
								<em class="fa fa-check-square-o"></em>Enable Filters</span>
                            <span *ngIf="isFilterEnabled">
								<em class="fa fa-times"></em>Clear Filters</span>
                        </a>
                        <a (click)="onAddingColumns()" class="add-column">
                            <em class="fa fa-cog"></em>Columns</a>
                        <a *isAuthorized="['upload','FILEUPLOAD']" (click)="importFile()" title="Import">
                            <em class="fa fa-upload"></em>
                        </a>
                        <a (click)="reset()" title="Reset">
                            <em class="fa fa-refresh"></em>
                        </a>
                        <a title="Export" (click)="exportExcel()">
                            <em class="fa fa-external-link"></em>
                        </a>
                        <a title="toggleView" (click)="onToggleView()">
                            <em class="fa fa-bars"></em>
                            <span>{{isGridView ? 'Tile View' : 'Grid View'}}</span>
                        </a>


                        <div id="add-column-popup" *ngIf="showAddColumns">
                            <div class="user-popup">
                                <div class="content overflow">
                                    <input type="checkbox" [checked]="isSelectAllChecked" id="selectall"
                                           name="selectall" (click)="onSelectAll()"/>
                                    <label for="selectall">Select All</label>
                                    <a class="close" title="Close" (click)="closeAddColumns()">&times;</a>
                                    <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                                        <ng-template let-col let-index="index" pTemplate="item">
                                            <div *ngIf="col.drag">
                                                <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens"
                                                     (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                                                    <div class="drag">
                                                        <input type="checkbox" [checked]="col.showField"
                                                               [(ngModel)]="col.showField"
                                                               (change)="selectColumns(col)"/>
                                                        <label>{{col.header}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div *ngIf="!col.drag">
                                                <div class="ui-helper-clearfix">
                                                    <div>
                                                        <input type="checkbox" [checked]="col.showField"
                                                               [(ngModel)]="col.showField" (change)="selectColumns(col)"
                                                               [disabled]="!col.drag"
                                                        />
                                                        <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </ng-template>
                                    </p-listbox>

                                </div>
                                <div class="pull-right pt-2">
                                    <a class="configColBtn" (click)="saveColumns()">Save</a>
                                    <a class="configColBtn conf-cancel" (click)="closeAddColumns()">Cancel</a>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="x-scroll" *ngIf="isGridView;else tileView">
                        <p-table #dt class="ui-datatable arrangementMgrTbl revenueContractTbl wide-columns"
                                     id="revenueResults-dt" [value]="deals"
                                     lazy="true"
                                     [loading]="loading"
                                     (onLazyLoad)="getDealsSummaryData($event)"
                                     [paginator]="showPaginator" [rows]="10"
                                     [totalRecords]="totalElements" [columns]="columns" [resizableColumns]="true"
                                     columnResizeMode="expand"
                                     exportFilename="Deals_Summary.csv">

                                <ng-template pTemplate="colgroup" let-columns>
                                    <colgroup>
                                        <col *ngFor="let col of columns">
                                    </colgroup>
                                </ng-template>

                                <ng-template pTemplate="header" class="arrangementMgrTblHead">

                                    <tr>
                                        <th *ngFor="let col of columns;let i = index" [ngStyle]="{'display': col.display}"
                                            pResizableColumn>
                                            <div [class]="{number: col.type === 'number', 'text-align-right': i > 2}"
                                               title="{{col.header}}">{{col.header}}
                                            </div>
                                        </th>

                                    </tr>

                                    <tr *ngIf="isFilterEnabled" class="filter-row">
                                        <ng-container *ngFor="let col of columns;let i = index">
                                            <ng-container>
                                                <th [ngStyle]="{'display': col.display}" title="{{col.header}}"
                                                    [class]="{hidden: col.field !== 'dealStatus' && col.field !== 'revenueGuidance', 'text-align-right': i > 2}"
                                                    pResizableColumn>
                                                    <input pInputText type="text" class="table-textbox"
                                                           name="{{col.field}}" [(ngModel)]="searchData[col.field]"/>
                                                    <button type="submit" (click)="onFilter()"
                                                            class="pi pi-search"></button>
                                                </th>
                                            </ng-container>
                                        </ng-container>

                                    </tr>
                                </ng-template>

                                <ng-template pTemplate="body" let-rowData>
                                    <tr [pSelectableRow]="rowData">

                                        <ng-container *ngFor="let col of columns;let i = index">
                                            <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}"  [class]="{'text-align-right': i > 2}"
                                                [ngStyle]="{'display': col.display}">
                                                {{rowData[col.field]}}
                                            </td>

                                            <td *ngIf="col.type == 'link'" title="{{rowData[col.field]}}" [class]="{'text-align-right': i > 2}"
                                                [ngStyle]="{'display': col.display}">
												<span>
													<a (click)="navigateToDetails(rowData['dealArrangementId'],rowData['dealNumber'])"
                                                       title="{{rowData[col.field]}}">{{rowData[col.field]}}</a>
												</span>
                                            </td>

                                            <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" [class]="{'text-align-right': i > 2}"
                                                class="number" [ngStyle]="{'display': col.display}">
                                                {{rowData[col.field]}}
                                            </td>

                                            <td *ngIf="col.type == 'date'"
                                                title="{{rowData[col.field] |date:'yyyy-MM-dd'}}" [class]="{'text-align-right': i > 2}"
                                                [ngStyle]="{'display': col.display}">
                                                {{rowData[col.field] | date:'yyyy-MM-dd'}}
                                            </td>

                                            <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" [class]="{'text-align-right': i > 2}"
                                                [ngStyle]="{'display': col.display}">
                                                {{rowData[col.field] | round}}
                                            </td>

                                            <td *ngIf="col.type == 'symbol'" class="text-align-right" title="{{rowData[col.field]}}"
                                                [ngStyle]="{'display': col.display}">
                                                <span *ngIf="rowData[col.field]=='RED'" [ngStyle]="{'margin-right': '25px'}"><i
                                                        class="fa fa-circle red-color" aria-hidden="true"></i></span>
                                                <span *ngIf="rowData[col.field]=='GREEN'" [ngStyle]="{'margin-right': '25px'}"><i
                                                        class="fa fa-circle green-color" aria-hidden="true"></i></span>
                                                <span *ngIf="rowData[col.field]=='YELLOW'" [ngStyle]="{'margin-right': '25px'}"><i
                                                        class="fa fa-circle yellow-color" aria-hidden="true"></i></span>
                                            </td>
                                        </ng-container>

                                    </tr>
                                </ng-template>

                                <ng-template pTemplate="emptymessage" let-columns>
                                    <div class="no-results-data">
                                        <p>No records found</p>
                                    </div>
                                </ng-template>
                            </p-table>
                    </div>

                    <ng-template #tileView>
                        <div class="tile-container">
                            <div class="card-wrapper tile-item" *ngFor="let deal of deals" (click)="navigateToDetails(deal.dealArrangementId, deal.dealNumber)">
                                <div class="item-row">Deal - {{deal.dealNumber}}</div>
                                <div class="item-row">{{deal.revenueGuidance}} | {{deal.dealStatus}}</div>
                                <div class="item-row">{{deal.amount | currency:'USD'}} | {{deal.dealDate}}</div>
                                <div class="item-row">{{deal.rcNumber}} | {{deal.customerName}}</div>
                            </div>
                        </div>
                    </ng-template>

                </div>
            </div>
        </div>
    </div>
</div>

<div class="lds-roller" style="top: 0px;" *ngIf="uploadLoading">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div>

<p-dialog header="Select Upload File Type" width="450" [(visible)]="displaySelectMode" [draggable]="true"
          showEffect="fade"
          [modal]="true">

    <div class="ui-md-12">
        <div class="row" class="ml-0">
            <div class="ui-md-4">
                <p-radioButton name="group1" value="QUOTES" label="Quotes" inputId="opt4"
                               (onClick)="onQuotes($event)"></p-radioButton>
            </div>
        </div>
    </div>
</p-dialog>

<p-dialog header="Upload Quotes" width="500" [(visible)]="uploadService.displayQuotesDialog" [draggable]="true"
          showEffect="fade"
          [modal]="true" (onAfterHide)="cancel()">
    <p-fileUpload id="upload-quotes-input" name="file" customUpload="true"
                  (uploadHandler)="fileUploadHandler($event, '/upload/documents/QUOTE', 'quote')"
                  accept=".csv" invalidFileTypeMessageSummary="{0}:Invalid file Type"></p-fileUpload>
    <progress-bar></progress-bar>
</p-dialog>
