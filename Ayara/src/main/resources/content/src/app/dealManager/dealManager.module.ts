import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {DealManagerComponent} from './dealManager.component';
import {RouterModule, Routes} from '@angular/router';
import {NewSharedModule} from '../shared/shared.module';
import {DealDetailsComponent} from './components/deal-details/deal-details.component';
import {DealSummaryComponent} from './components/deal-summary/deal-summary.component';
import {DealAllocationsComponent} from './components/deal-allocations/deal-allocations.component';
import {DealForecastingComponent} from './components/deal-forecasting/deal-forecasting.component';
import {DealsService} from './services/deals.service';
import {ArrgContractAllocationsVService} from '../arrgContractAllocationsV/arrgContractAllocationsVservice';
import {ConfirmationService} from 'primeng/api';
import {RmanDealArrangementsService} from '../rmanDealArrangements/rmanDealArrangementsservice';
import {RmanDealRfcstVService} from '../rmanDealRfcstV/rmanDealRfcstVservice';
import {DealCommentsComponent} from './components/deal-comments/deal-comments.component';


const routes: Routes = [
    {path: '', component: DealManagerComponent},
    {
        path: 'details/:dealId/:dealNumber', component: DealDetailsComponent, children: [
            {
                path: 'summary',
                component: DealSummaryComponent
            },
            {
                path: 'summary/comments',
                component: DealCommentsComponent
            },
            {
                path: 'allocations',
                component: DealAllocationsComponent
            },
            {
                path: 'forecasting',
                component: DealForecastingComponent
            }
        ]
    }
];

@NgModule({
    declarations: [
        DealManagerComponent,
        DealDetailsComponent,
        DealSummaryComponent,
        DealAllocationsComponent,
        DealForecastingComponent,
        DealCommentsComponent
    ],
    imports: [
        RouterModule.forChild(routes),
        CommonModule,
        NewSharedModule
    ],
    providers: [DealsService, ArrgContractAllocationsVService, ConfirmationService, RmanDealArrangementsService, RmanDealRfcstVService]
})
export class DealManagerModule {
}
