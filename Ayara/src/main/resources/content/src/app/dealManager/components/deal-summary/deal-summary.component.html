<div class="pull-right icons-list">

    <p-toggleButton class="ui-inputswitch" onLabel="TC View" offLabel="USD View" onIcon="fa fa-toggle-on fa-sm"
                    offIcon="fa fa-toggle-off fa-sm"
                    [(ngModel)]="isUsdView"
                    (ngModelChange)="getSummaryData()"
                    [disabled]="loading">
    </p-toggleButton>

    <app-columns-config
            [frozenCols]="frozenCols"
            [scrollableCols]="scrollableCols"
            [isSelectAllChecked]="isSelectAllChecked"
            [showConfigPopup]="isConfigOpened"
            (openConfig)="onConfiguringColumns()"
            (closeConfig)="closeConfigureColumns()"
            (saveConfig)="saveColumns()"
            (toggleFreeze)="onToggleFreeze($event)"
            (selectColumn)="selectColumns()"
            (selectAll)="onSelectAll()"
    ></app-columns-config>

    <a title="Export" (click)="exportExcel()">
        <em class="fa fa-external-link"></em>
    </a>

    <a (click)="toggleView()">
        <em class="fa fa-list"></em>
        {{isQuoteView ? 'Deal View' : 'Quote View'}}
    </a>

    <a class="comments-icon" routerLink="comments">
        <em class="fa fa-comments">
        </em>
        <ng-container *ngIf="(totalComments$ | async) as totalComments">
            <span class="count">{{totalComments}}</span>
        </ng-container>
    </a>

</div>

<div class="x-scroll">
    <p-table #dt class="ui-datatable arrangementMgrTbl wide-columns" [class]="{unfrozen: !isTableFrozen}" id="deal-summary"
             [value]="dealRollUpSummaryData"
             [loading]="loading"
             lazy="true"
             (onLazyLoad)="getSummaryData($event)"
             [paginator]="showPaginator"
             [rows]="10"
             [totalRecords]="totalElements"
             [columns]="scrollableCols"
             [frozenColumns]="frozenCols"
             [frozenWidth]="frozenWidth"
             [scrollable]="true"
             [resizableColumns]="true"
             columnResizeMode="expand"
             exportFilename="Deal_Data.csv">

        <ng-template pTemplate="colgroup" let-columns>
            <colgroup>
                <ng-container *ngFor="let col of columns">
                    <col *ngIf="col.showField">
                </ng-container>
            </colgroup>
        </ng-template>

        <ng-container *ngFor="let template of tableHeaderTmpl">
            <ng-template [pTemplate]="template" let-columns>
                <tr>
                    <ng-container *ngFor="let col of columns">
                        <th *ngIf="col.showField"
                            [ngStyle]="{'display': col.display, 'width':'300px', 'text-align': col.alignment}"
                            pResizableColumn
                            [class]="{number: col.type === 'number'}"
                            title="{{col.header}}">{{col.header}}
                        </th>
                    </ng-container>
                </tr>
            </ng-template>
        </ng-container>

        <ng-container *ngFor="let template of tableBodyTmpl">
            <ng-template [pTemplate]="template" let-rowData let-columns="columns">
                <tr [pSelectableRow]="rowData">

                    <ng-container *ngFor="let col of columns">
                        <ng-container *ngIf="col.showField">
                            <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}"
                                [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                {{rowData[col.field]}}
                            </td>

                            <td *ngIf="col.type == 'link'" title="{{rowData[col.field]}}"
                                [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                            <span>
                                <a title="{{rowData[col.field]}}">{{rowData[col.field]}}</a>
                            </span>
                            </td>

                            <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}"
                                [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                {{rowData[col.field] | number}}
                            </td>

                            <td *ngIf="col.type == 'percent'" title="{{rowData[col.field]}}"
                                [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                {{(rowData[col.field] != null ? ((rowData[col.field] | number:'1.2-2') + '%') : '-')}}
                            </td>

                            <td *ngIf="col.type == 'date'" title="{{rowData[col.field] |date:'yyyy-MM-dd'}}"
                                [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                {{rowData[col.field] | date:'yyyy-MM-dd'}}
                            </td>

                            <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}"
                                [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                {{rowData[col.field] | round}}
                            </td>

                            <td *ngIf="col.type == 'parentheses'" title="{{rowData[col.field] | round}}"
                                [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                ( {{rowData[col.field] | round}} )
                            </td>
                        </ng-container>

                    </ng-container>
                </tr>

            </ng-template>
        </ng-container>

        <ng-container *ngFor="let template of tableFooterTmpl">
            <ng-template [pTemplate]="template" let-columns>
                <tr *ngIf="dealSummaryTotals$ | async; let totals" class="table-footer right-align">
                    <ng-container *ngFor="let col of columns">
                        <ng-container *ngIf="col.showField">
                            <td *ngIf="col.footer=='empty'"></td>
                            <td *ngIf="col.footer=='totals'">Totals:</td>
                            <td *ngIf="col.footer=='totalExtendedListAmt'"
                            >{{totals.totalExtendedListAmt | round}}</td>
                            <td *ngIf="col.footer=='totalLineAmt'">{{totals.totalLineAmt | round}}</td>
                            <td *ngIf="col.footer=='totalDiscount'">
                                {{(totals.totalDiscount != null ? ((totals.totalDiscount | number:'1.2-2') + '%') : '-')}}
                            </td>
                            <td *ngIf="col.footer=='totalAllocationAmt'"
                            >{{totals.totalAllocationAmt | round}}</td>
                            <td *ngIf="col.footer=='totalCarveInOut'">{{totals.totalCarveInOut | round}}</td>
                            <td *ngIf="col.footer=='totalGrossMarginPercent'">
                                {{(totals.totalGrossMarginPercent != null ? ((totals.totalGrossMarginPercent | number:'1.2-2') + '%') : '-')}}
                            </td>
                            <td *ngIf="col.footer=='totalAllocatedMarginPercent'">
                                {{(totals.totalAllocatedMarginPercent != null ? ((totals.totalAllocatedMarginPercent | number:'1.2-2') + '%') : '-')}}
                            </td>
                            <td *ngIf="col.footer=='totalRevenueAdjustment'"
                            >{{totals.totalRevenueAdjustment | number}}</td>
                            <td *ngIf="col.footer=='totalAdjustedAllocatedMarginPercent'">
                                {{(totals.totalAdjustedAllocatedMarginPercent != null ? ((totals.totalAdjustedAllocatedMarginPercent | number:'1.2-2') + '%') : '-')}}
                            </td>
                            <td *ngIf="col.footer=='totalStandardCost'"
                            >( {{totals.totalStandardCost | round}} )
                            </td>
                            <td *ngIf="col.footer=='totalCostAdjusted'"
                            >( {{totals.totalCostAdjusted | round}} )
                            </td>
                            <td *ngIf="col.footer=='totalAdjustedCost'"
                            >( {{totals.totalAdjustedCost | round}} )
                            </td>
                            <td *ngIf="col.footer=='totalGrossMargin'"
                            >{{totals.totalGrossMargin | round}}</td>
                            <td *ngIf="col.footer=='totalAllocatedMarginAmount'"
                            >{{totals.totalAllocatedMarginAmount | round}}</td>
                        </ng-container>
                    </ng-container>
                </tr>
            </ng-template>
        </ng-container>
        <ng-template pTemplate="emptymessage" let-columns>
            <div class="no-results-data">
                <p>No records found</p>
            </div>
        </ng-template>
    </p-table>
</div>
