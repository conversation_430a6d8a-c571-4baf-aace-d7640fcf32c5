import {Component, OnDestroy, OnInit} from '@angular/core';
import {Column} from '../../../shared/models/column.interface';
import {DealSummary, DealSummaryTotals} from '../../models/dealSummary';
import {ActivatedRoute} from '@angular/router';
import {DealsService} from '../../services/deals.service';
import {NotificationService} from '../../../shared/notifications.service';
import {CommonSharedService} from '../../../shared/common.service';
import {Observable, Subscription} from 'rxjs';
import {DEAL_DETAILS_COLUMNS} from '../../mocks/columns';
import {map} from 'rxjs/operators';

@Component({
    selector: 'app-deal-summary',
    templateUrl: './deal-summary.component.html',
    styleUrls: ['./deal-summary.component.css']
})
export class DealSummaryComponent implements OnInit, OnDestroy {

    columns: Column[] = [];
    clonedCols: Column[] = [];
    globalCols: Column[] = [];
    frozenCols: Column[] = [];
    scrollableCols: Column[] = [];
    tableHeaderTmpl = ['frozenheader', 'header'];
    tableBodyTmpl = ['frozenbody', 'body'];
    tableFooterTmpl = ['frozenfooter', 'footer'];
    frozenWidth: string;
    isTableFrozen = false;
    dealRollUpSummaryData: DealSummary[];
    dealSummaryTotals$: Observable<DealSummaryTotals>;
    dealId: string;
    dealNumber: string;
    loading = false;
    showPaginator = true;
    pageSize = 10;
    isQuoteView = false;
    isConfigOpened = false;
    isSelectAllChecked = true;
    startIndex: number;
    userId: number;
    totalElements: number;
    totalComments$: Observable<number>;
    subscription: Subscription;
    isUsdView = false;

    constructor(private route: ActivatedRoute,
                private dealsService: DealsService,
                private notificationService: NotificationService,
                private commonSharedService: CommonSharedService) {
    }

    ngOnInit(): void {
        this.dealId = this.route.parent.snapshot.paramMap.get('dealId');
        this.totalComments$ = this.dealsService.getDealComments(this.dealId)
            .pipe(map(data => data.totalElements));
        this.setDetailsTable();
    }

    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
            if (response && response != null && response.userId) {
                const cols = response.tableColumns.split('/');
                const colsList: string[] = cols[0].split(',');
                const frozenColsList: string[] = cols[1] ? cols[1].split(',') : [];

                this.globalCols.forEach(col => {
                    col.showField = [...colsList, ...frozenColsList].indexOf(col.field) !== -1;
                });

                const hiddenCols = this.globalCols.filter(col => !col.showField);

                colsList.forEach(colName => {
                    const column = this.globalCols.find(col => col.field === colName);
                    if (column) {
                        this.scrollableCols.push(column);
                    }
                });
                frozenColsList.forEach(colName => {
                    const column = this.globalCols.find(col => col.field === colName);
                    if (column) {
                        column.isFrozen = true;
                        this.frozenCols.push(column);
                    }
                });
                hiddenCols.forEach(col => {
                    this.scrollableCols.push(col);
                });

                this.globalCols = [...this.frozenCols, ...this.scrollableCols];
                this.getFrozenWidth();
                if ((colsList.length + frozenColsList.length) !== this.globalCols.length) {
                    this.isSelectAllChecked = false;
                }
                this.showPaginator = (colsList.length + frozenColsList.length) !== 0;
                this.userId = response.userId;
            } else {
                this.recollectColumns();
            }
        }).catch(() => {
            this.notificationService.showError('Error occured while getting table columns data');
            this.loading = false;
        });
    }

    getSummaryData(event?: any) {
        const page = event ? event.first / event.rows : 0;
        const viewFlag = this.isQuoteView ? 'Q' : 'D';
        this.loading = true;
        this.getSummaryTotals();
        this.subscription = this.dealsService.getDealRollUpSummaryData(this.dealId, viewFlag, this.isUsdView, page)
            .subscribe(
                data => {
                    this.loading = false;
                    this.dealRollUpSummaryData = data.content;
                    this.totalElements = data.totalElements;
                },
                () => {
                    this.notificationService.showError('Error occured while getting table columns data');
                    this.loading = false;
                });
    }

    getSummaryTotals() {
        const viewFlag = this.isQuoteView ? 'Q' : 'D';
        this.dealSummaryTotals$ = this.dealsService.getSummaryDataTotals(this.dealId, viewFlag, this.isUsdView);
    }

    toggleView() {
        this.isQuoteView = !this.isQuoteView;
        const numberCol = this.globalCols.find(col => col.field === 'dealQuoteNum');
        if (this.isQuoteView) {
            numberCol.header = 'Quote Number';
            this.getSummaryData();
        } else {
            numberCol.header = 'Deal Number';
            this.getSummaryData();
        }
    }

    setDetailsTable() {
        this.globalCols = DEAL_DETAILS_COLUMNS;
        this.getTableColumns('Deal Details', 'Deal Summary');
    }

    onConfiguringColumns() {
        this.clonedCols = [...this.globalCols];
        this.isConfigOpened = true;
    }

    onToggleFreeze(column: Column) {
        const globalCol = this.globalCols.find(col => col.field === column.field);
        globalCol.isFrozen = !globalCol.isFrozen;
        this.recollectColumns();
    }

    recollectColumns() {
        this.frozenCols = this.globalCols.filter(col => col.isFrozen);
        this.scrollableCols = this.globalCols.filter(col => !col.isFrozen);
        this.getFrozenWidth();
    }

    getFrozenWidth() {
        const visibleFrozenCols = this.frozenCols.filter(col => col.showField);
        this.frozenWidth = (visibleFrozenCols.length ? visibleFrozenCols.length * 150 : 0) + 'px';
        this.isTableFrozen = !!this.frozenCols && !!this.frozenCols.length && !!this.frozenCols.some(col => !!col.showField);
    }

    selectColumns() {
        this.isSelectAllChecked = this.globalCols.filter(item => !item.showField).length <= 0;
        this.getFrozenWidth();
    }

    onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
            if (this.isSelectAllChecked) {
                col.showField = true;
            } else {
                if (col.drag) {
                    col.showField = false;
                }
            }
        });
    }

    closeConfigureColumns() {
        this.isConfigOpened = false;
    }

    saveColumns() {
        let selectedFrozenCols = '';
        let selectedScrollableCols = '';
        this.isConfigOpened = false;

        this.frozenCols.forEach((col, index) => {
            if (col.showField) {
                selectedFrozenCols += col.field;
                if (index < (this.frozenCols.length - 1)) {
                    selectedFrozenCols += ',';
                }
            }
        });
        this.scrollableCols.forEach((col, index) => {
            if (col.showField) {
                selectedScrollableCols += col.field;
                if (index < (this.scrollableCols.length - 1)) {
                    selectedScrollableCols += ',';
                }
            }
        });
        const configuredCols = selectedScrollableCols + '/' + selectedFrozenCols ;

        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns('Deal Details', 'Deal Summary', configuredCols, this.userId).then((response) => {
            this.userId = response['userId'];
            this.loading = false;
        }).catch(() => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    exportExcel() {
        const exportCols = [];
        for (let index = 0; index < this.globalCols.length; index++) {
            if (this.globalCols[index].showField) {
                exportCols.push(this.globalCols[index].field);
            }
        }
        const viewFlag = this.isQuoteView ? 'Q' : 'D';
        window.location.href = this.dealsService.getRollUpExportUrl(this.dealId, viewFlag, this.isUsdView, exportCols);
    }

    ngOnDestroy() {
        this.subscription.unsubscribe();
    }
}
