<div class="content-section implementation">
</div>

<p-confirmDialog width="425"></p-confirmDialog>

<p-panel (onBeforeToggle)="onBeforeToggle($event)">
    <p-header>
        <div class="pull-right icons-list" *ngIf="collapsed">

            <app-columns-config
                    [frozenCols]="frozenCols"
                    [scrollableCols]="scrollableCols"
                    [isSelectAllChecked]="isSelectAllChecked"
                    [showConfigPopup]="isConfigOpened"
                    (openConfig)="onConfiguringColumns()"
                    (closeConfig)="closeConfigureColumns()"
                    (saveConfig)="saveColumns()"
                    (toggleFreeze)="onToggleFreeze($event)"
                    (selectColumn)="selectColumns()"
                    (selectAll)="onSelectAll()"
            ></app-columns-config>

            <a (click)="reset(dt) || reset(table)" title="Reset"><em class="fa fa-refresh"></em></a>
            <ng-container *ngIf="!disableExport"><a (click)="exportExcel()" title="Export"><em
                    class="fa fa-external-link"></em></a>
            </ng-container>


        </div>
    </p-header>
    <div>
        <p-table class="ui-datatable arrangementMgrTbl wide-columns" [class]="{unfrozen: !isTableFrozen}" #dt id="alloc-dt"
                 [loading]="loading"
                 [columns]="scrollableCols"
                 [frozenColumns]="frozenCols"
                 [frozenWidth]="frozenWidth"
                 [scrollable]="true"
                 [value]="arrgContractAllocationsVList"
                 (onLazyLoad)="getArrgContractAllocationsV($event)"
                 [lazy]="true"
                 [paginator]="showPaginator"
                 [rows]="pageSize"
                 [totalRecords]="totalElements"
                 [resizableColumns]="true"
                 columnResizeMode="expand">

            <ng-template pTemplate="colgroup" let-columns>
                <colgroup>
                    <ng-container *ngFor="let col of columns">
                        <col *ngIf="col.showField">
                    </ng-container>
                </colgroup>
            </ng-template>


            <ng-container *ngFor="let template of tableHeaderTmpl">
                <ng-template [pTemplate]="template" let-columns>
                    <tr>
                        <ng-container *ngFor="let col of columns">
                            <ng-container *ngIf="col.showField">
                                <th [ngStyle]="{'display': col.display, 'text-align': col.alignment}"
                                    title="{{col.header}}"
                                    pResizableColumn>{{col.header}}</th>
                            </ng-container>
                        </ng-container>
                    </tr>
                </ng-template>
            </ng-container>

            <ng-container *ngFor="let template of tableBodyTmpl">
                <ng-template [pTemplate]="template" let-rowData let-columns="columns">
                    <tr>

                        <ng-container *ngFor="let col of columns">

                            <ng-container *ngIf="col.showField">
                                <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}"
                                    [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                    {{rowData[col.field]}}
                                </td>

                                <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}"
                                    [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                    {{rowData[col.field]}}
                                </td>

                                <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}"
                                    [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                    {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                                </td>

                                <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}"
                                    [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                    {{rowData[col.field] | round}}
                                </td>

                                <td *ngIf="col.type == 'bround'" title="{{rowData[col.field] | round}}"
                                    [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                    {{(rowData[col.field] != null ? (rowData[col.field] | round) : '-')}}
                                </td>

                                <td *ngIf="col.type == 'percent'" title="{{rowData[col.field] | round}}"
                                    [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                    {{(rowData[col.field] != null ? ((rowData[col.field] | number:'1.2-2') + '%') : '-')}}
                                </td>

                                <td *ngIf="col.type == 'parentheses'" title="{{rowData[col.field] | round}}"
                                    [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                                    ( {{rowData[col.field] | round}} )
                                </td>

                                <td *ngIf="col.type == 'symbol'" title="{{rowData[col.field]}}"
                                    [ngStyle]="{'display': col.display, 'text-align': col.alignment}">
                            <span *ngIf="rowData[col.field]=='RED'"><i class="fa fa-circle red-color"
                                                                       aria-hidden="true"></i></span>
                                    <span *ngIf="rowData[col.field]=='GREEN'"><i class="fa fa-circle green-color"
                                                                                 aria-hidden="true"></i></span>
                                    <span *ngIf="rowData[col.field]=='YELLOW'"><i class="fa fa-circle yellow-color"
                                                                                  aria-hidden="true"></i></span>
                                </td>
                            </ng-container>

                        </ng-container>

                    </tr>
                </ng-template>
            </ng-container>

            <ng-container *ngFor="let template of tableFooterTmpl">
                <ng-template [pTemplate]="template" let-columns>
                    <tr class="table-footer right-align">
                        <ng-container *ngFor="let col of columns">
                            <ng-container *ngIf="col.showField">
                                <td *ngIf="col.footer=='empty'"></td>
                                <td *ngIf="col.footer=='totals'">Totals:</td>
                                <td *ngIf="col.footer=='totalListAmount'">{{totalListAmount | round}}</td>
                                <td *ngIf="col.footer=='lineAmountTotal'">{{lineAmountTotal | round}}</td>
                                <td *ngIf="col.footer=='vcTotal'">{{vcTotal | round}}</td>
                                <td *ngIf="col.footer=='allocableNetTotal'">{{allocableNetTotal | round}}</td>
                                <td *ngIf="col.footer=='fvTotal'">{{fvTotal | round}}</td>
                                <td *ngIf="col.footer=='allocationTotal'">{{allocationTotal | round}}</td>
                                <td *ngIf="col.footer=='cvInOutAmountTotal'">{{cvInOutAmountTotal | round}}</td>
                                <td *ngIf="col.footer=='allocationFcTotal'">{{allocationFcTotal | round}}</td>
                                <td *ngIf="col.footer=='espTotal'">{{espTotal | round}}</td>
                                <td *ngIf="col.footer=='espInstTotal'">{{espInstTotal | round}}</td>
                                <td *ngIf="col.footer=='allocStatusTotal'" class="text">
                            <span *ngIf="allocStatusTotal=='RED'"><i class="fa fa-circle red-color"
                                                                     aria-hidden="true"></i></span>
                                    <span *ngIf="allocStatusTotal=='GREEN'"><i class="fa fa-circle green-color"
                                                                               aria-hidden="true"></i></span>
                                    <span *ngIf="allocStatusTotal=='YELLOW'"><i class="fa fa-circle yellow-color"
                                                                                aria-hidden="true"></i></span>
                                </td>
                                <td *ngIf="col.footer=='allocMarginStatusTotal'" class="text">
                            <span *ngIf="allocMarginStatusTotal=='RED'"><i class="fa fa-circle red-color"
                                                                           aria-hidden="true"></i></span>
                                    <span *ngIf="allocMarginStatusTotal=='GREEN'"><i class="fa fa-circle green-color"
                                                                                     aria-hidden="true"></i></span>
                                    <span *ngIf="allocMarginStatusTotal=='YELLOW'"><i class="fa fa-circle yellow-color"
                                                                                      aria-hidden="true"></i></span>
                                </td>
                                <td *ngIf="col.footer=='discountStatusTotal'" class="text">
                            <span *ngIf="discountStatusTotal=='RED'"><i class="fa fa-circle red-color"
                                                                        aria-hidden="true"></i></span>
                                    <span *ngIf="discountStatusTotal=='GREEN'"><i class="fa fa-circle green-color"
                                                                                  aria-hidden="true"></i></span>
                                    <span *ngIf="discountStatusTotal=='YELLOW'"><i class="fa fa-circle yellow-color"
                                                                                   aria-hidden="true"></i></span>
                                </td>
                                <td *ngIf="col.footer=='statusTotal'" class="text">
                            <span *ngIf="statusTotal=='RED'"><i class="fa fa-circle red-color"
                                                                aria-hidden="true"></i></span>
                                    <span *ngIf="statusTotal=='GREEN'"><i class="fa fa-circle green-color"
                                                                          aria-hidden="true"></i></span>
                                    <span *ngIf="statusTotal=='YELLOW'"><i class="fa fa-circle yellow-color"
                                                                           aria-hidden="true"></i></span>
                                </td>
                                <td *ngIf="col.footer=='commentsTotal'" class="text"><span *ngIf="commentsTotal !=null">Lines Failing: {{commentsTotal}}</span>
                                </td>
                                <td *ngIf="col.footer=='totalRebate'">{{totalRebate | round}}</td>
                                <td *ngIf="col.footer=='totalGrossMargin'">{{totalGrossMargin | round}}</td>
                                <td *ngIf="col.footer=='totalAllocatedMarginAmount'">{{totalAllocatedMarginAmount | round}}</td>
                            </ng-container>
                        </ng-container>
                    </tr>
                </ng-template>
            </ng-container>

            <ng-template pTemplate="emptymessage" let-columns>
                <div class="no-results-data">
                    <p>{{noData}}</p>
                </div>
            </ng-template>
        </p-table>

    </div>
</p-panel>
