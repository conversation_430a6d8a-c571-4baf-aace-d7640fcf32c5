import {Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {DealFields, SearchData} from './models/dealFields';
import {Table} from 'primeng/table';
import {UploadService} from '../shared/upload.service';
import {NotificationService} from '../shared/notifications.service';
import {CommonSharedService} from '../shared/common.service';
import {DealsService} from './services/deals.service';
import {tap} from 'rxjs/operators';
import {Subscription} from 'rxjs';
import {Column} from '../shared/models/column.interface';
import {Router} from '@angular/router';
import {DEALS_SUMMARY_COLUMNS} from './mocks/columns';

@Component({
    selector: 'app-deal-manager',
    templateUrl: './dealManager.component.html',
    styleUrls: ['./dealManager.component.scss']
})
export class DealManagerComponent implements OnInit, OnDestroy {

    columns: Column[] = [];
    globalCols: Column[] = [];
    clonedCols = [];
    searchData: SearchData;
    deals: DealFields[];
    paginationOptions = {
        page: 0,
        size: 10
    };
    searchTerm = '';
    pageSize = 10;
    isSearched = false;
    showAddColumns = false;
    loading = false;
    isGridView = true;
    showPaginator = true;
    displayAdvancedSearch = false;
    isFilterEnabled = false;
    displaySelectMode = false;
    uploadLoading = false;
    isSelectAllChecked = true;
    startIndex: number;
    userId: number;
    totalElements: number;
    subscription: Subscription;
    guidance: string;


    @ViewChild('dt') table: Table;

    constructor(
        private router: Router,
        public uploadService: UploadService,
        private notificationService: NotificationService,
        private commonSharedService: CommonSharedService,
        private dealsService: DealsService) {
        this.guidance = this.commonSharedService.guidance;
    }

    ngOnInit(): void {
        this.resetSearchData();
        if (this.guidance) {
            this.commonSharedService.guidance = null;
            this.getDealsSummaryData(null, this.guidance);
        } else {
            this.getDealsSummaryData();
        }
        this.globalCols = DEALS_SUMMARY_COLUMNS;

        this.columns = [];
        this.getTableColumns("Deal Manager", "Deals Summary");
        if (this.columns.length !== this.globalCols.length) {
            this.isSelectAllChecked = false;
        }
    }

    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
            if (response && response != null && response.userId) {
                this.columns = [];
                const colsList = response.tableColumns.split(',');
                if (colsList.length > 0) {
                    colsList.forEach((item, index) => {
                        if (item) {
                            this.startIndex = this.globalCols.findIndex(col => col.field == item);
                            this.onDrop(index);
                        }
                    });
                }
                this.globalCols.forEach(col => {
                    if (response.tableColumns.indexOf(col.field) !== -1) {
                        this.columns.push(col);
                    } else {
                        col.showField = false;
                    }
                });
                if (this.columns.length !== this.globalCols.length) {
                    this.isSelectAllChecked = false;
                }
                this.showPaginator = this.columns.length !== 0;
                this.userId = response.userId;
            } else {
                this.columns = this.globalCols;
            }
        }).catch(() => {
            this.notificationService.showError('Error occured while getting table columns data');
            this.loading = false;
        });
    }

    getDealsSummaryData(event?: any, guidance?: string) {
        if (guidance) {
            this.searchData = {
                dealStatus: '',
                globalSearch: '',
                revenueGuidance: guidance
            };
        }
        if (event) {
            this.paginationOptions = {
                page: event.first / event.rows,
                size: 10
            };
        }

        this.loading = true;
        this.subscription = this.dealsService.getDealsSummary(this.searchData, this.paginationOptions)
            .pipe(tap(() => this.loading = false))
            .subscribe(
                data => {
                    this.loading = false;
                    this.totalElements = data.totalElements;
                    this.deals = data.content;
                },
                () => {
                    this.notificationService.showError('Error occured while getting table columns data');
                    this.loading = false;
                });
    }

    onToggleView() {
        this.isGridView = !this.isGridView;
    }

    resetSearchData() {
        this.searchTerm = '';
        this.searchData = {
            revenueGuidance: '',
            dealStatus: '',
            globalSearch: ''
        };
    }

    onFilter() {
        this.getDealsSummaryData();
    }

    onEnablingFilters() {
        if (this.isFilterEnabled) {
            this.resetSearchData();
            this.getDealsSummaryData();
        }
        this.isFilterEnabled = !this.isFilterEnabled;
    }

    reset() {
        this.resetSearchData();
        this.isSearched = false;
        this.isFilterEnabled = false;
        this.paginationOptions = {
            page: 0,
            size: 10
        };
        this.getDealsSummaryData();
        this.table.reset();
    }

    onAddingColumns() {
        this.clonedCols = [...this.globalCols];
        this.showAddColumns = true;
    }

    selectColumns(col: any) {
        const cols = this.globalCols.filter(item => !item.showField);
        this.isSelectAllChecked = cols.length <= 0;
    }

    onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
            if (this.isSelectAllChecked) {
                col.showField = true;
            } else {
                if (col.drag) {
                    col.showField = false;
                }
            }
        });
    }

    closeAddColumns() {
        this.showAddColumns = false;
        this.globalCols = this.clonedCols;
        const configCol = this.columns.filter(item => !item.showField);
        this.isSelectAllChecked = !(configCol.length > 0);
    }

    onDragStart(index: number) {
        this.startIndex = index;
    }

    onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
    }

    saveColumns() {
        let selectedCols = '';
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
            if (col.showField) {
                selectedCols += col.field;
                if (index < colLength) {
                    selectedCols += ',';
                }
            }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns('Deal Manager', 'Deals Summary', selectedCols, this.userId).then((response) => {
            this.columns = this.globalCols.filter(item => item.showField);
            this.userId = response['userId'];
            this.showPaginator = this.columns.length !== 0;
            this.loading = false;
        }).catch(() => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }


    importFile() {
        this.displaySelectMode = true;
    }

    onQuotes(event: any) {
        this.displaySelectMode = false;
        this.uploadService.displayQuotesDialog = true;
    }

    cancel() {
        this.displaySelectMode = true;
    }

    exportExcel() {
        const exportCols = [];
        for (let index = 0; index < this.columns.length; index++) {
            if (this.columns[index].showField) {
                exportCols.push(this.columns[index].field);
            }
        }
        window.location.href = this.dealsService.getSummaryExportUrl(this.searchData, exportCols);
    }

    fileUploadHandler(event: any, url: any, type: any) {
        let file: any;
        let processFlag: boolean;
        const tempFiles: any = [];
        for (const {name} of event.files) {
            tempFiles.push(name);
        }

        if (tempFiles.length > 0) {
            console.log(tempFiles[0]);
            const fileName = tempFiles[0].toString().toLowerCase();
            file = fileName.replace(/[^a-z\s]+/gi, '');
        }

        processFlag = type === 'quote' && file.startsWith(type);

        if (processFlag) {
            this.uploadService.processUpload(event, url);
        } else {
            this.notificationService.showError('Upload Failed, File Name should start with ' + type.toString().toUpperCase() + ' Key');
        }

    }

    commonSearch() {
        this.isSearched = true;
        this.searchData.globalSearch = this.searchTerm;
        this.table.reset();
        this.getDealsSummaryData();
    }

    onResetGlobalSearch() {
        this.searchTerm = '';
        this.searchData = {...this.searchData, globalSearch: ''};
        this.getDealsSummaryData();
        this.isSearched = false;
    }

    navigateToDetails(id: number, dealNumber: string) {
        this.router.navigate(['/', 'deal-manager', 'details', id, dealNumber, 'summary']);
    }

    ngOnDestroy() {
        this.subscription.unsubscribe();
    }
}
