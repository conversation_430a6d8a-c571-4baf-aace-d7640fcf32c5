<div class="comments__form">
    <form (ngSubmit)="onAdd()" #commentForm='ngForm'>
        <textarea pInputTextarea [(ngModel)]="inputComment" id="comment" name="comment"
                  required placeholder="Type Comment"></textarea>
        <button class="add-btn"
                type="submit"
                [disabled]="!commentForm.form.valid">
            <em class="fa fa-paper-plane"></em>
        </button>
    </form>
</div>

<p-panel header="Comments History" [style]="{'margin-bottom':'20px'}">
    <p-header>
        <div class="pull-right icons-list">
            <a routerLink="../">
                <em class="fa fa-share"></em>
            </a>
        </div>
    </p-header>

    <div class="x-scroll" *ngIf="{comments: commentsData$ | async} as data">
        <p-table class="arrangementMgrTbl comments-table"
                 [resizableColumns]="true"
                 (onLazyLoad)="getDealComments($event)"
                 [value]="data.comments"
                 [loading]="isLoading"
                 [paginator]="true" [rows]="5"
                 [totalRecords]="totalElements"
                 lazy="true"
                 scrollable="true">

            <ng-template pTemplate="header">
                <tr>
                    <th pResizableColumn class="small-col">
                        User
                    </th>
                    <th pResizableColumn class="small-col">
                        Date & Time
                    </th>
                    <th pResizableColumn>
                        Comment
                    </th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-comment>
                <tr>
                    <td class="small-col">
                        {{comment.userName}}
                    </td>
                    <td class="small-col">
                        {{comment.creationDate | date:'MM/dd/yyyy hh:mm'}}
                    </td>
                    <td>
                        {{comment.userComment}}
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td class="no-data">No Data</td>
                </tr>
            </ng-template>
        </p-table>
    </div>

</p-panel>
