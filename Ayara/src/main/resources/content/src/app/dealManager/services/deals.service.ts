import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import * as appSettings from '../../appsettings';
import {DealFields, SearchData} from '../models/dealFields';
import {Observable} from 'rxjs';
import {TableData} from '../../shared/models/tableData';
import {DealSummary, DealSummaryTotals} from '../models/dealSummary';
import {DealComment} from '../models/dealComment';

@Injectable()
export class DealsService {

    constructor(private http: HttpClient) {
    }

    getDealsSummary(searchData?: SearchData, paginationOptions = {page: 0, size: 10}): Observable<TableData<DealFields[]>> {
        const {revenueGuidance, dealStatus, globalSearch} = searchData;
        const {page, size} = paginationOptions;

        if (revenueGuidance || dealStatus) {
            const revenueGuidanceFilter = revenueGuidance ? `revenueGuidance:${revenueGuidance},` : '';
            const dealStatusFilter = dealStatus ? `dealStatus:${dealStatus}` : '';
            return this.http.get<TableData<DealFields[]>>(`${appSettings.apiUrl}/fetchDealSummaryDataGlobalSearch?search=${revenueGuidanceFilter}${dealStatusFilter}&globalSearch=${globalSearch}&page=${page}&size=${size}`);
        }
        return this.http.get<TableData<DealFields[]>>(`${appSettings.apiUrl}/fetchDealSummaryDataGlobalSearch?search=%25&globalSearch=${globalSearch}&page=${page}&size=${size}`);
    }

    getDealRollUpSummaryData(id: string, viewFlag = 'D', isUsdView = false, page = 0): Observable<TableData<DealSummary[]>> {
        if (isUsdView) {
            return this.http.get<TableData<DealSummary[]>>(`${appSettings.apiUrl}/fetchDealRollupSummaryData?dealArrgId=${id}&dealQuoteFlag=${viewFlag}&currencyType=USD&page=${page}&size=10`);
        }
        return this.http.get<TableData<DealSummary[]>>(`${appSettings.apiUrl}/fetchDealRollupSummaryData?dealArrgId=${id}&dealQuoteFlag=${viewFlag}&page=${page}&size=10`);
    }

    getSummaryDataTotals(id: string, viewFlag = 'D', isUsdView = false): Observable<DealSummaryTotals> {
        if (isUsdView) {
			return this.http.get<DealSummaryTotals>(`${appSettings.apiUrl}/fetchDealRollupSummaryDataTotals?dealArrgId=${id}&dealQuoteFlag=${viewFlag}&currencyType=USD`);
		}	
        return this.http.get<DealSummaryTotals>(`${appSettings.apiUrl}/fetchDealRollupSummaryDataTotals?dealArrgId=${id}&dealQuoteFlag=${viewFlag}`);
    }

    getSummaryExportUrl(searchData: SearchData, exportCols: string[]) {
        const {revenueGuidance, dealStatus, globalSearch} = searchData;
        const revenueGuidanceFilter = revenueGuidance ? `revenueGuidance:${revenueGuidance},` : '';
        const dealStatusFilter = dealStatus ? `dealStatus:${dealStatus}` : '';
        return appSettings.apiUrl + `/dealSummaryDataExport?search=${revenueGuidanceFilter}${dealStatusFilter}&globalSearch=${globalSearch}&exportCols=${exportCols.join(',')}`;
    }

    getRollUpExportUrl(id: string, viewFlag, isUsdView = false, exportCols: string[]) {
        if (isUsdView) {
			return appSettings.apiUrl + `/dealRollupViewExport?dealArrgId=${id}&dealQuoteFlag=${viewFlag}&currencyType=USD&exportCols=${exportCols.join(',')}`;
		}
        return appSettings.apiUrl + `/dealRollupViewExport?dealArrgId=${id}&dealQuoteFlag=${viewFlag}&exportCols=${exportCols.join(',')}`;
    }

    getDealComments(dealId: string, page = 0): Observable<TableData<DealComment[]>> {
        return this.http.get<TableData<DealComment[]>>(`${appSettings.apiUrl}/fetchDealUserComments?search=dealArrangementId:${dealId}&page=${page}&size=5`);
    }

    addDealComment(comment: Partial<DealComment>) {
        return this.http.post<DealComment[]>(`${appSettings.apiUrl}/AYARA_DEAL_USER_COMMENTS`, comment);
    }
}