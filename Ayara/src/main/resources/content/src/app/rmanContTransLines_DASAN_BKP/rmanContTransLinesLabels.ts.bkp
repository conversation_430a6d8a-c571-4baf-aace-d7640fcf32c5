interface ILabels {
         [index: string]: string;
}

export class RmanContTransLinesLabels {

        fieldLabels: ILabels;

    constructor() {

    this.fieldLabels = {};

        /* this.fieldLabels["attribute30"] = "ATTRIBUTE30";
        this.fieldLabels["invoiceHold"] = "INVOICE HOLD";
        this.fieldLabels["sourceLineId"] = "SOURCE LINE ID";
        this.fieldLabels["ruleHeaderId"] = "RULE HEADER ID";
        this.fieldLabels["releaseCogs"] = "RELEASE COGS";
        this.fieldLabels["applyType"] = "APPLY TYPE";
        this.fieldLabels["sourceHeaderId"] = "SOURCE HEADER ID";
        this.fieldLabels["lastUpdateDate"] = "LAST UPDATE DATE";
        this.fieldLabels["event"] = "EVENT";
        this.fieldLabels["ranking"] = "RANKING";
        this.fieldLabels["status"] = "STATUS";
        this.fieldLabels["attribute29"] = "ATTRIBUTE29";
        this.fieldLabels["attribute28"] = "ATTRIBUTE28";
        this.fieldLabels["attribute27"] = "ATTRIBUTE27";
        this.fieldLabels["avgContAcceptDur"] = "AVG CONT ACCEPT DUR";
        this.fieldLabels["attribute26"] = "ATTRIBUTE26";
        this.fieldLabels["attribute3"] = "ATTRIBUTE3";
        this.fieldLabels["createdBy"] = "CREATED BY";
        this.fieldLabels["transHeaderId"] = "TRANS HEADER ID";
        this.fieldLabels["maxDuration"] = "MAX DURATION";
        this.fieldLabels["attribute2"] = "ATTRIBUTE2";
        this.fieldLabels["lastUpdatedBy"] = "LAST UPDATED BY";
        this.fieldLabels["attribute1"] = "ATTRIBUTE1";
        this.fieldLabels["applicationLevel"] = "APPLICATION LEVEL";
        this.fieldLabels["soHeaderId"] = "SO HEADER ID";
        this.fieldLabels["revenue"] = "REVENUE";
        this.fieldLabels["creationDate"] = "CREATION DATE";
        this.fieldLabels["attribute9"] = "ATTRIBUTE9";
        this.fieldLabels["attribute8"] = "ATTRIBUTE8";
        this.fieldLabels["attribute7"] = "ATTRIBUTE7";
        this.fieldLabels["productName"] = "PRODUCT NAME";
        this.fieldLabels["attribute6"] = "Source Line";
        this.fieldLabels["attribute5"] = "Source Header";
        this.fieldLabels["releaseType"] = "RELEASE TYPE";
        this.fieldLabels["attribute4"] = "ATTRIBUTE4";
        this.fieldLabels["dealNum"] = "DEAL NUM";
        this.fieldLabels["soLineId"] = "SO LINE ID";
        this.fieldLabels["releaseRevenue"] = "RELEASE REVENUE";
        this.fieldLabels["attribute10"] = "ATTRIBUTE10";
        this.fieldLabels["attribute14"] = "ATTRIBUTE14";
        this.fieldLabels["attribute13"] = "ATTRIBUTE13";
        this.fieldLabels["attribute12"] = "ATTRIBUTE12";
        this.fieldLabels["contEventType"] = "CONT EVENT TYPE";
        this.fieldLabels["application"] = "APPLICATION";
        this.fieldLabels["attribute11"] = "ATTRIBUTE11";
        this.fieldLabels["cogs"] = "COGS";
        this.fieldLabels["comments"] = "COMMENTS";
        this.fieldLabels["templateId"] = "TEMPLATE ID";
        this.fieldLabels["ruleCategory"] = "RULE CATEGORY";
        this.fieldLabels["description"] = "DESCRIPTION";
        this.fieldLabels["deferredMethod"] = "DEFERRED METHOD";
        this.fieldLabels["attribute21"] = "ATTRIBUTE21";
        this.fieldLabels["attribute20"] = "ATTRIBUTE20";
        this.fieldLabels["transLineId"] = "TRANS LINE ID";
        this.fieldLabels["attribute25"] = "ATTRIBUTE25";
        this.fieldLabels["attribute24"] = "ATTRIBUTE24";
        this.fieldLabels["attribute23"] = "ATTRIBUTE23";
        this.fieldLabels["attribute22"] = "ATTRIBUTE22";
        this.fieldLabels["autoReleaseDays"] = "AUTO RELEASE DAYS";
        this.fieldLabels["percentage"] = "PERCENTAGE";
        this.fieldLabels["dealArrangementId"] = "DEAL ARRANGEMENT ID";
        this.fieldLabels["element"] = "ELEMENT";
        this.fieldLabels["attribute18"] = "ATTRIBUTE18";
        this.fieldLabels["attribute17"] = "ATTRIBUTE17";
        this.fieldLabels["attribute16"] = "ATTRIBUTE16";
        this.fieldLabels["attribute15"] = "ATTRIBUTE15";
        this.fieldLabels["attribute19"] = "ATTRIBUTE19"; */

		this.fieldLabels["attribute30"] = "attribute30";
		this.fieldLabels["productGroup"] = "Product Group";
		this.fieldLabels["releaseEvent"] = "Release Event";
		this.fieldLabels["productCategory"] = "Product Category";
		this.fieldLabels["postAllocationContingency"] = "Post Allocation Contingency";
		this.fieldLabels["name"] = "Name";
		this.fieldLabels["includeInFVAllocation"] = "Include in FV Allocation";
        this.fieldLabels["invoiceHold"] = "Invoice Hold";
        this.fieldLabels["sourceLineId"] = "Source Line #";
        this.fieldLabels["ruleHeaderId"] = "Name";
        this.fieldLabels["releaseCogs"] = "RELEASE COGS";
        this.fieldLabels["applyType"] = "Apply Type";
        this.fieldLabels["sourceHeaderId"] = "Source Header #";
        this.fieldLabels["lastUpdateDate"] = "LAST UPDATE DATE";
        this.fieldLabels["event"] = "Release Event";
        this.fieldLabels["ranking"] = "Ranking";
        this.fieldLabels["status"] = "STATUS";
        this.fieldLabels["attribute29"] = "Post Allocation Contingency";
        this.fieldLabels["attribute28"] = "Include in FV Allocation";
        this.fieldLabels["attribute27"] = "ATTRIBUTE27";
        this.fieldLabels["avgContAcceptDur"] = "AVG CONT ACCEPT DUR";
        this.fieldLabels["attribute26"] = "Standard";
        this.fieldLabels["attribute3"] = "ATTRIBUTE3";
        this.fieldLabels["createdBy"] = "CREATED BY";
        this.fieldLabels["transHeaderId"] = "Transaction #";
        this.fieldLabels["maxDuration"] = "ForeCasting Delay";
        this.fieldLabels["attribute2"] = "ATTRIBUTE2";
        this.fieldLabels["lastUpdatedBy"] = "LAST UPDATED BY";
        this.fieldLabels["attribute1"] = "ATTRIBUTE1";
        this.fieldLabels["applicationLevel"] = "Application Level";
        this.fieldLabels["soHeaderId"] = "SO HEADER ID";
        this.fieldLabels["revenue"] = "Revenue";
        this.fieldLabels["creationDate"] = "CREATION DATE";
        this.fieldLabels["attribute9"] = "ATTRIBUTE9";
        this.fieldLabels["attribute8"] = "ATTRIBUTE8";
        this.fieldLabels["attribute7"] = "ATTRIBUTE7";
        this.fieldLabels["productName"] = "Product Name";
		this.fieldLabels["forecastingDelay"] = "Forecasting Delay";
        this.fieldLabels["attribute6"] = "ATTRIBUTE6";
        this.fieldLabels["attribute5"] = "ATTRIBUTE5";
        this.fieldLabels["releaseType"] = "Release Status";
        this.fieldLabels["attribute4"] = "ATTRIBUTE4";
        this.fieldLabels["dealNum"] = "DEAL NUM";
        this.fieldLabels["soLineId"] = "SO LINE ID";
        this.fieldLabels["releaseRevenue"] = "RELEASE REVENUE";
		this.fieldLabels["additionalInfo"] = "Additional Info";
        this.fieldLabels["attribute10"] = "ATTRIBUTE10";
        this.fieldLabels["attribute14"] = "Additional Info";
        this.fieldLabels["attribute13"] = "ATTRIBUTE13";
        this.fieldLabels["attribute12"] = "ATTRIBUTE12";
        this.fieldLabels["contEventType"] = "Current/Future/ Both";
        this.fieldLabels["application"] = "APPLICATION";
        this.fieldLabels["attribute11"] = "ATTRIBUTE11";
        this.fieldLabels["cogs"] = "COGS";
        this.fieldLabels["comments"] = "Comments";
        this.fieldLabels["templateId"] = "Template";
        this.fieldLabels["ruleCategory"] = "Contingency Type";
        this.fieldLabels["description"] = "DESCRIPTION";
        this.fieldLabels["deferredMethod"] = "Method";
        this.fieldLabels["attribute21"] = "ATTRIBUTE21";
        this.fieldLabels["attribute20"] = "ATTRIBUTE20";
        this.fieldLabels["transLineId"] = "Transaction Line #";
        this.fieldLabels["attribute25"] = "ATTRIBUTE25";
        this.fieldLabels["attribute24"] = "ATTRIBUTE24";
        this.fieldLabels["attribute23"] = "ATTRIBUTE23";
        this.fieldLabels["attribute22"] = "ATTRIBUTE22";
        this.fieldLabels["autoReleaseDays"] = "Release Days";
        this.fieldLabels["percentage"] = "PERCENTAGE";
        this.fieldLabels["dealArrangementId"] = "DEAL ARRANGEMENT ID";
        this.fieldLabels["element"] = "Element";
        this.fieldLabels["attribute18"] = "ATTRIBUTE18";
        this.fieldLabels["attribute17"] = "ATTRIBUTE17";
        this.fieldLabels["attribute16"] = "ATTRIBUTE16";
        this.fieldLabels["attribute15"] = "ATTRIBUTE15";
        this.fieldLabels["attribute19"] = "ATTRIBUTE19";
    }

}
