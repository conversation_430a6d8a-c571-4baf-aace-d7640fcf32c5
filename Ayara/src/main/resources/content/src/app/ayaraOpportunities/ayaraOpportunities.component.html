<div class="content-section implementation">
</div>

<div class="card-wrapper">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card-block">
          <p-panel header="Manage Opportunities" [toggleable]="false" (onBeforeToggle)="onBeforeToggle($event)">
            <p-header>

              <div class="pull-right icons-list" *ngIf="collapsed">
                  <a (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
                <a *isAuthorized="['write','MOPPORTUNITY']" (click)="showDialogToAdd()" title="Add">
                  <em class="fa fa-plus-circle"></em>
                </a>
                <a (click)="showDialogToSearch()" title="Search">
                  <em class="fa fa-search"></em>
                </a>
                <a (click)="reset(dt)" title="Reset">
                  <em class="fa fa-refresh"></em>
                </a>

                <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
                    <div class="user-popup">
                      <div class="content overflow">
                        <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()"/>
                        <label for="selectall">Select All</label>
                        <a class="close" title="Close" (click)="closeConfigureColumns($event)" >&times;</a>
                        <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                          <ng-template let-col let-index="index" pTemplate="item">
                            <div *ngIf="col.drag">
                            <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" 
                             (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                              <div class="drag">
                                <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)"/>
                                <label>{{col.header}}</label>
                              </div>
                            </div>
                            </div>
                            <div *ngIf="!col.drag">
                            <div class="ui-helper-clearfix">
                              <div>
                                <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"/>
                                <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                              </div>
                            </div>
                            </div>
                          </ng-template>
                          </p-listbox>
                
                      </div>
                
                      <div class="pull-right">
                        <a class="configColBtn" (click)="saveColumns()">Save</a>
                        <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
                      </div>
                
                    </div>
                  </div>

              </div>
            </p-header>

            <div class="x-scroll">
              <p-table class="ui-datatable arrangementMgrTbl" #dt id="ayaraOpportunities-dt" [loading]="loading" [value]="ayaraOpportunitiesList"
                selectionMode="single" (onRowSelect)="onRowSelect($event)" (onLazyLoad)="getAyaraOpportunities($event)" [lazy]="true"
                [paginator]="true" [rows]="pageSize" [totalRecords]="totalElements" scrollable="true" [columns]="columns" [resizableColumns]="true" columnResizeMode="expand" >
                
                <ng-template pTemplate="colgroup" let-columns>
                    <colgroup>
                      <col *ngFor="let col of columns">
                    </colgroup>
                  </ng-template>
    
                   
                  <ng-template pTemplate="header" class="arrangementMgrTblHead">
                    <tr>
                      <ng-container *ngFor="let col of columns">
                        <th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                        <th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                      </ng-container>
                    </tr>
                  </ng-template>
    
                  <ng-template pTemplate="body" let-rowData let-ayaraOpportunities let-columns="columns">
                    <tr [pSelectableRow]="rowData" [pSelectableRowIndex]="rowIndex">
                     
                      <ng-container *ngFor="let col of columns" >
                        <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                          {{rowData[col.field]}}
                        </td>
                      
                        <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
                          {{rowData[col.field]}}
                        </td>
                      
                        <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
                          {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                        </td>
                      
                        <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
                          {{rowData[col.field] | round}}
                        </td>
                      </ng-container>
                            
                    </tr>                 
                  </ng-template>
                <ng-template pTemplate="emptymessage" let-columns>
                  <tr *ngIf="!columns">
                    <td class="no-data">{{noData}}</td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </p-panel>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog header="Search" width="auto" [(visible)]="displaySearchDialog" showEffect="fade" [draggable]="true" [modal]="true" [blockScroll]="true">
  <form>
    <div class="ui-grid ui-grid-responsive ui-fluid">
      <div class="ui-g-12">
        <div class="ui-g-6">
          <span class="md-inputfield">
            <span class="selectSpan">Opportunity Name</span>
            <input pInputText name="opportunityName" id="opportunityName" class="textbox" placeholder="Opportunity Name" [ngModelOptions]="{standalone: true}"
              [(ngModel)]="ayaraOpportunitiesSearch.opportunityName" />
          </span>
        </div>
        <div class="ui-g-6 pull-right">
          <span class="md-inputfield">
            <span class="selectSpan">Opportunity Number</span>
            <input pInputText name="opportunityNumber" id="opportunityNumber" class="textbox" placeholder="Opportunity Number" [ngModelOptions]="{standalone: true}"
              [(ngModel)]="ayaraOpportunitiesSearch.opportunityNumber" />
          </span>
        </div>
      </div>
	  <div class="ui-g-12">
        <div class="ui-g-6">
          <span class="md-inputfield">
            <span class="selectSpan">Customer Name</span>
            <input pInputText name="customerName" id="customerName" class="textbox" placeholder="Customer Name" [ngModelOptions]="{standalone: true}"
              [(ngModel)]="ayaraOpportunitiesSearch.customerName" />
          </span>
        </div>
        <div class="ui-g-6 pull-right">
          <span class="md-inputfield">
            <span class="selectSpan">Opportunity Status</span>
            <input pInputText name="status" id="status" class="textbox" placeholder="Opportunity Status" [ngModelOptions]="{standalone: true}"
              [(ngModel)]="ayaraOpportunitiesSearch.status" />
          </span>
        </div>
      </div>
	  <div class="ui-g-12">
        <div class="ui-g-6">
          <span class="md-inputfield">
            <span class="selectSpan">Deal Number</span>
            <input pInputText name="dealNumber" id="dealNumber" class="textbox" placeholder="Deal Number" [ngModelOptions]="{standalone: true}"
              [(ngModel)]="ayaraOpportunitiesSearch.dealNumber" />
          </span>
        </div>
		<div class="ui-g-6 pull-right">
          <span class="selectSpan">Opportunity Date</span>
            <p-calendar inputStyleClass="textbox" showAnim="slideDown" name="opportunityDate" id="opportunityDate" [ngModelOptions]="{standalone: true}" 
			[(ngModel)]="ayaraOpportunitiesSearch.opportunityDate" [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" appendTo="body" dateFormat="yy-mm-dd"
                                    placeholder="Opportunity Date">
			</p-calendar>
        </div>

      </div>
    </div>
  </form>
  <p-footer>
    <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
      <button type="submit" pButton class="primary-btn" label="Search" (click)="search()"></button>
      <button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>
    </div>
  </p-footer>

</p-dialog>
<p-dialog header="{{(newAyaraOpportunities) ? 'Create Opportunity' : 'Edit Opportunity'}}" width="500" [(visible)]="displayDialog" [draggable]="true"
  showEffect="fade" [modal]="true" [blockScroll]="true" (onHide)="cancelAddEdit()">
  <form (ngSubmit)="save()" [formGroup]="opportunityForm">
    <div class="ui-g ui-fluid" *ngIf="ayaraOpportunities">
      <div class="ui-g-12 form-group">
        <div class="ui-grid-row">
          <div class="ui-g-12">
            <div class="ui-g-6">
              <span class="md-inputfield">
                <span class="selectSpan">Opportunity Name
                  <span class="red-color">*</span>
                </span>
                <input pInputText id="opportunityName" name="opportunityName" class="textbox" placeholder="Opportunity Name" required [(ngModel)]="ayaraOpportunities.opportunityName"
                  formControlName="opportunityName" />
                <div *ngIf="formErrors.opportunityName" class="ui-message ui-messages-error ui-corner-all">
                  {{ formErrors.opportunityName }}
                </div>
              </span>
            </div>
            <div class="ui-g-6 pull-right">
              <span class="md-inputfield">
                <span class="selectSpan">Opportunity Number</span>
                <input pInputText id="opportunityNumber" class="textbox" placeholder="Opportunity Number" name="opportunityNumber" [ngModelOptions]="{standalone: true}"  [(ngModel)]="ayaraOpportunities.opportunityNumber"/>
              </span>
            </div>
          </div>
          <div class="ui-g-12">
            <div class="ui-g-6">
              <span class="selectSpan">Opportunity Date</span>
			  <p-calendar inputStyleClass="textbox" showAnim="slideDown" name="opportunityDate" id="opportunityDate" [ngModelOptions]="{standalone: true}" 
				[(ngModel)]="ayaraOpportunities.opportunityDate" [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" appendTo="body" dateFormat="yy-mm-dd"
                                    placeholder="Opportunity Date">
			</p-calendar>
            </div>
            <div class="ui-g-6 pull-right">
              <span class="md-inputfield">
                <span class="selectSpan">Opportunity Status
                  <span class="red-color">*</span>
                </span>
                <input pInputText id="status" class="textbox" placeholder="Opportunity Status" name="status" required [(ngModel)]="ayaraOpportunities.status"
                  formControlName="status" />
				<div *ngIf="formErrors.status" class="ui-message ui-messages-error ui-corner-all">
                  {{ formErrors.status }}
                </div>  
              </span>
            </div>
          </div>
		 <div class="ui-g-12"> 
			<div class="ui-g-6">
              <span class="md-inputfield">
                <span class="selectSpan">Customer Name
                  <span class="red-color">*</span>
                </span>
                <input pInputText id="customerName" name="customerName" class="textbox" placeholder="Customer Name" required [(ngModel)]="ayaraOpportunities.customerName"
                  formControlName="customerName" />
                <div *ngIf="formErrors.customerName" class="ui-message ui-messages-error ui-corner-all">
                  {{ formErrors.customerName }}
                </div>
              </span>
            </div>
            <div class="ui-g-6 pull-right">
              <span class="md-inputfield">
                <span class="selectSpan">Win(%)
                  <span class="red-color">*</span>
                </span>
                <input pInputText id="winPercent" class="textbox" placeholder="Win(%)" name="winPercent" required [(ngModel)]="ayaraOpportunities.winPercent"
                  formControlName="winPercent" />
				<div *ngIf="formErrors.winPercent" class="ui-message ui-messages-error ui-corner-all">
                  {{ formErrors.winPercent }}
                </div>
			  </span>
			</div>
		  </div>
		  <div class="ui-g-12"> 
			<div class="ui-g-6">
              <span class="md-inputfield">
                <span class="selectSpan">Opportunity Amount
                  <span class="red-color">*</span>
                </span>
                <input pInputText id="opportunityAmount" name="opportunityAmount" class="textbox" placeholder="Opportunity Amount" required [(ngModel)]="ayaraOpportunities.opportunityAmount"
                  formControlName="opportunityAmount" />
                <div *ngIf="formErrors.opportunityAmount" class="ui-message ui-messages-error ui-corner-all">
                  {{ formErrors.opportunityAmount }}
                </div>
              </span>
            </div>
            <div class="ui-g-6 pull-right">
              <span class="md-inputfield">
                <span class="selectSpan">Deal Number</span>
                <input pInputText id="dealNumber" class="textbox" placeholder="Deal Number" name="dealNumber" [ngModelOptions]="{standalone: true}"  [(ngModel)]="ayaraOpportunities.dealNumber"/>
              </span>
            </div>
          </div>	
        </div>
      </div>
    </div>
  </form>
  <p-footer>
    <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
      <button type="submit" pButton class="primary-btn" label="Save" (click)="save()" [disabled]="!opportunityForm.valid"></button>
      <button type="button" pButton class="secondary-btn" (click)="cancelAddEdit()" label="Cancel"></button>
    </div>
  </p-footer>

</p-dialog>

<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>