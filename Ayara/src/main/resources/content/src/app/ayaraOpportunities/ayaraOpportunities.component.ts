import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { Table } from 'primeng/table';
import { NotificationService } from '../shared/notifications.service';
import { AyaraOpportunities } from './ayaraOpportunities';
import { AyaraOpportunitiesService } from './ayaraOpportunitiesservice';
import { CommonSharedService } from '../shared/common.service';

declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Component({
    templateUrl: './ayaraOpportunities.component.html',
    selector: 'ayaraOpportunities-data',
    providers: [AyaraOpportunitiesService, ConfirmationService]
})

export class AyaraOpportunitiesComponent {

    displayDialog: boolean;

    displaySearchDialog: boolean;
    
    uploadLoading: boolean = false;

    ayaraOpportunities: AyaraOpportunities = new AyaraOpportunitiesImpl();

    ayaraOpportunitiesSearch: AyaraOpportunities = new AyaraOpportunitiesImpl();

    isSerached: number = 0;

    selectedAyaraOpportunities: AyaraOpportunities;

    newAyaraOpportunities: boolean;
	
	displayOpportunitiesDialog: boolean = false;
	
    ayaraOpportunitiesList: AyaraOpportunities[];
	
	numRegex = /^-?\d*[.,]?\d{0,5}$/;

    cols: any[];
    //columns: ILabels;

    columnOptions: any[];

    paginationOptions = {};

    pages: {};


    datasource: any[];
    pageSize: number;
    totalElements: number;
    collapsed: boolean = true;
    
    opportunityForm: FormGroup;
    noData = appSettings.noData;
    loading: boolean;
    isSelectAllChecked = true;
    globalCols: any[];
    clonedCols: any[];
    userId: number;
    showPaginator: boolean = true;
    startIndex: number;
    showAddColumns = true;
    columns: any[];


    constructor(
        private ayaraOpportunitiesService: AyaraOpportunitiesService,
        private confirmationService: ConfirmationService,
        private formBuilder: FormBuilder,
        private notificationService:NotificationService,
        private commonSharedService: CommonSharedService
    ) {
		this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };

        this.buildForm();

    }

    ngOnInit() {
        this.globalCols = [
			{ field: 'opportunityId', header: 'Opportunity Id', showField: true, drag: false, display: "table-cell",type:'text'},
            { field: 'dealNumber', header: 'Deal Number', showField: true, drag: false, display: "table-cell",type:'text'},
			{ field: 'opportunityNumber', header: 'Opportunity Number', showField: true, drag: true, display: "table-cell",type:'text' },
			{ field: 'opportunityName', header: 'Opportunity Name', showField: true, drag: true, display: "table-cell",type:'text' },
			{ field: 'opportunityDate', header: 'Opportunity Date', showField: true, drag: true, display: "table-cell",type:'date' },
            { field: 'customerName', header: 'Customer Name', showField: true, drag: true, display: "table-cell",type:'text' },
            { field: 'status', header: 'Status', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'winPercent', header: 'Win (%)', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'opportunityAmount', header: 'Opportunity Amount', showField: true, drag: true, display: "table-cell",type:'round'}
        ];

        this.columns = [];
        this.getTableColumns("ayaraOpportunities", "Opportunities");
    }

    
    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
          if (response && response != null && response.userId) {
            this.columns = [];
            let colsList = response.tableColumns.split(",");
            if (colsList.length > 0) {
              colsList.forEach((item, index) => {
                if (item) {
                  this.startIndex = this.globalCols.findIndex(col => col.field == item);
                  this.onDrop(index);
                }
              });
            }
            this.globalCols.forEach(col => {
              if (response.tableColumns.indexOf(col.field) !== -1) {
                this.columns.push(col);
              } else {
                col.showField = false;
              }
            });
            if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
            this.showPaginator = this.columns.length !== 0;
            this.userId = response.userId;
          } else {
            this.columns = this.globalCols;
          }
        }).catch(() => {
          this.notificationService.showError('Error occured while getting table columns data');
          this.loading = false;
        });
      }
    
      saveColumns() {
        let selectedCols = "";
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
          if (col.showField) {
            selectedCols += col.field;
            if (index < colLength) {
              selectedCols += ",";
            }
          }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns("ayaraOpportunities", "Opportunities", selectedCols, this.userId).then((response) => {
          this.columns = this.globalCols.filter(item => item.showField);
          this.userId = response["userId"];
          this.showPaginator = this.columns.length !== 0;
          this.loading = false;
        }).catch(() => {
          this.notificationService.showError('Error occured while getting data');
          this.loading = false;
        });
      }
    
      onDragStart(index: number) {
        this.startIndex = index;
      }
    
      onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
        //console.log(this.globalCols);
      }
    
      selectColumns(col: any) {
        let cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
          this.isSelectAllChecked = false;
        } else {
          this.isSelectAllChecked = true;
        }
      }
    
      onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
          if (this.isSelectAllChecked) {
            col.showField = true;
          } else {
            if (col.drag) {
              col.showField = false;
            }
          }
        });
      }
    
      onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
      }
    
      closeConfigureColumns(event: any) {
      this.showAddColumns = true;
      this.globalCols = this.clonedCols;
      let configCol = this.globalCols.filter(item => !item.showField);
      this.isSelectAllChecked = !(configCol.length > 0);
      }



    getAllAyaraOpportunities() {
        this.loading = true;
        this.ayaraOpportunitiesService.getAllAyaraOpportunities(this.paginationOptions, this.ayaraOpportunities, false).then((ayaraOpportunitiesList: any) => {
            this.loading = false;
            this.datasource = ayaraOpportunitiesList.content;
            this.ayaraOpportunitiesList = ayaraOpportunitiesList.content;
            this.totalElements = ayaraOpportunitiesList.totalElements;
            this.pageSize = ayaraOpportunitiesList.size;
            this.displaySearchDialog = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    getAyaraOpportunities(event: any) {
        this.loading = true;
        let first: number = event.first;
        let rows: number = event.rows;
        let pageNumber: number = first / rows;
        this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
        this.ayaraOpportunitiesService.getAllAyaraOpportunities(this.paginationOptions, this.ayaraOpportunities, false).then((ayaraOpportunitiesList: any) => {
            this.loading = false;
            this.datasource = ayaraOpportunitiesList.content;
            this.ayaraOpportunitiesList = ayaraOpportunitiesList.content;
            this.totalElements = ayaraOpportunitiesList.totalElements;
            this.pageSize = ayaraOpportunitiesList.size;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });

    }

    buildForm() {
        this.opportunityForm = this.formBuilder.group({
            'opportunityName': ['', [Validators.required, Validators.minLength(4),Validators.maxLength(100)]],
            'customerName': ['', [Validators.required, Validators.minLength(4),Validators.maxLength(60)]],
            'status': ['', [Validators.required]],
			'winPercent': ['', [Validators.required]],
			'opportunityAmount': ['', [Validators.required,Validators.pattern(this.numRegex)]]
        });
        this.opportunityForm.valueChanges
            .subscribe(data => this.onValueChanged(data));

        this.onValueChanged();
    }

    onValueChanged(data?: any) {
        if (!this.opportunityForm) { return; }
        const form = this.opportunityForm;

        for (const field in this.formErrors) {

            this.formErrors[field] = '';
            const control = form.get(field);

            if (control && control.dirty && !control.valid) {
                const messages = this.validationMessages[field];
                for (const key in control.errors) {
                    this.formErrors[field] += messages[key] + ' ';
                }
            }
        }
    }

    formErrors = {
        'opportunityName': '','customerName': '', 'status': '', 'winPercent': '', 'opportunityAmount': ''
    };

    validationMessages = {
        'opportunityName': {
            'required': "Opportunity Name is required (minimum length is 4 and maximum length is 100 characters)",
            'minlength': 'Opportunity Name must be at least 4 characters long.',
            'maxlength': 'Opportunity Name cannot be more than 100 characters long.',
        },
		'customerName': {
            'required': "Customer Name is required (minimum length is 4 and maximum length is 60 characters)",
            'minlength': 'Customer Name must be at least 4 characters long.',
            'maxlength': 'Customer Name cannot be more than 60 characters long.',
        },
        'winPercent': {
            'required': "Win Percent is Required",
        },
        'status': {
            'required': "Status is Required"
        },
		'opportunityAmount': {
            'required': "Opportunity Amount is Required",
			'pattern': 'Only number values are allowed [0-9]',
        }
    }


    showDialogToAdd() {
        this.newAyaraOpportunities = true;
        this.ayaraOpportunities = new AyaraOpportunitiesImpl();
        this.displayDialog = true;
        this.buildForm()
    }


    save() {

        if (this.newAyaraOpportunities) {
            this.loading = true;
            this.ayaraOpportunitiesService.saveAyaraOpportunities(this.ayaraOpportunities).then((response: any) => {
                this.notificationService.showSuccess('Saved successfully');
                this.getAllAyaraOpportunities();
            }).catch((err: any) => {
                this.notificationService.showError('Error occured while saving data');
                this.loading = false;

            });
        }
        else {
            this.loading = true;
            this.ayaraOpportunitiesService.updateAyaraOpportunities(this.ayaraOpportunities).then((response: any) => {
                this.notificationService.showSuccess('Updated successfully');
                this.getAllAyaraOpportunities();
            }).catch((err: any) => {
                this.notificationService.showError('Error occured while updating data');
                this.loading = false;

            });
        }

        this.ayaraOpportunities = new AyaraOpportunitiesImpl();

        this.displayDialog = false;

    }

    onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }

    reset(dt: Table) {
        this.paginationOptions = {};
        this.ayaraOpportunities = new AyaraOpportunitiesImpl();
        this.ayaraOpportunitiesSearch = new AyaraOpportunitiesImpl();
        dt.reset();
    }


    delete(ayaraOpportunities: any) {
        this.ayaraOpportunities = ayaraOpportunities;
        this.displayDialog = false;

        this.confirmationService.confirm({
            message: 'Are you sure you want to delete this record?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
                this.ayaraOpportunitiesList.splice(this.findSelectedAyaraOpportunitiesIndex(), 1);
                this.ayaraOpportunitiesService.deleteAyaraOpportunities(this.ayaraOpportunities).then(response => {
                    this.notificationService.showSuccess('Deleted successfully');
                    this.ayaraOpportunities = new AyaraOpportunitiesImpl();
                    this.getAllAyaraOpportunities();
                });
            },
            reject: () => {
                this.notificationService.showWarning('You have rejected');
            }
        })

    }

    editRow(ayaraOpportunities: any) {
        this.newAyaraOpportunities = false;
        this.ayaraOpportunities = this.cloneAyaraOpportunities(ayaraOpportunities);
        this.displayDialog = true;

    }


    findSelectedAyaraOpportunitiesIndex(): number {
        return this.ayaraOpportunitiesList.indexOf(this.selectedAyaraOpportunities);
    }

    onRowSelect(event: any) {

    }

    cloneAyaraOpportunities(c: AyaraOpportunities): AyaraOpportunities {
        let ayaraOpportunities = new AyaraOpportunitiesImpl();
        for (let prop in c) {
            ayaraOpportunities[prop] = c[prop];
        }
        return ayaraOpportunities;
    }

    hideColumnMenu: boolean = true;

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    showFilter: boolean = false;

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {
        if (this.isSerached == 0) {
            this.ayaraOpportunitiesSearch = new AyaraOpportunitiesImpl();
        }
        this.ayaraOpportunitiesSearch = new AyaraOpportunitiesImpl();
        this.displaySearchDialog = true;

    }
    cancelAddEdit(){
        this.displayDialog=false;
        this.ayaraOpportunities = new AyaraOpportunitiesImpl();
    }
    search() {

        this.isSerached = 1;
        this.ayaraOpportunities = this.ayaraOpportunitiesSearch;
        this.paginationOptions={};
        this.getAllAyaraOpportunities();
    }
    
    
}


export class AyaraOpportunitiesImpl implements AyaraOpportunities {
    constructor(public opportunityId?: any ,public dealNumber?: any ,public opportunityNumber?: any ,public opportunityName?: any ,public opportunityDate?: any ,public customerNumber?: any ,public customerName?: any ,public status?: any ,public winPercent?: any ,public opportunityAmount?: any ,public source?: any ,public sourceId?: any ,public attribute1?: any ,public attribute2?: any ,public attribute3?: any ,public attribute4?: any ,public attribute5?: any ,public attribute6?: any ,public attribute7?: any ,public attribute8?: any ,public attribute9?: any ,public attribute10?: any ,public createdBy?: any ,public creationDate?: any ,public lastUpdatedBy?: any ,public lastUpdatedDate?: any) { }
}

interface ILabels {
    [index: string]: string;
}
