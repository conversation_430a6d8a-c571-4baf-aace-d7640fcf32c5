import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require:any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type':  'application/json',
  })
};

@Injectable()
export class AyaraOpportunitiesService {

    constructor(private http: HttpClient) {}
    
    getAllAyaraOpportunities(paginationOptions:any,ayaraOpportunitiesSearchObject:any, fetchAll:any): Promise<any[]> {
        let serviceUrl=appSettings.apiUrl+'/ayaraOpportunitiesSearch?';
        
        let searchString='';

        if(fetchAll){
            serviceUrl = serviceUrl + 'fetchAll=true&'
        }

        if (ayaraOpportunitiesSearchObject.opportunityId!=undefined && ayaraOpportunitiesSearchObject.opportunityId!="") {
            searchString=searchString+'opportunityId:'+ayaraOpportunitiesSearchObject.opportunityId+',';
        }

        if (ayaraOpportunitiesSearchObject.opportunityName!=undefined && ayaraOpportunitiesSearchObject.opportunityName!="") {
            searchString=searchString+'opportunityName:'+encodeURIComponent(ayaraOpportunitiesSearchObject.opportunityName)+',';
        }

        if (ayaraOpportunitiesSearchObject.opportunityNumber!=undefined && ayaraOpportunitiesSearchObject.opportunityNumber!="") {
            searchString=searchString+'opportunityNumber:'+ayaraOpportunitiesSearchObject.opportunityNumber+',';
        }

        if (ayaraOpportunitiesSearchObject.status!=undefined && ayaraOpportunitiesSearchObject.status!="") {
            searchString=searchString+'status:'+ayaraOpportunitiesSearchObject.status+',';
        }

        if (ayaraOpportunitiesSearchObject.opportunityDate!=undefined && ayaraOpportunitiesSearchObject.opportunityDate!="") {
            searchString=searchString+'opportunityDate:'+ayaraOpportunitiesSearchObject.opportunityDate+',';
        }

        if (ayaraOpportunitiesSearchObject.customerName!=undefined && ayaraOpportunitiesSearchObject.customerName!="") {
            searchString=searchString+'customerName:'+ayaraOpportunitiesSearchObject.customerName+',';
        }

        if (ayaraOpportunitiesSearchObject.customerNumber!=undefined && ayaraOpportunitiesSearchObject.customerNumber!="") {
            searchString=searchString+'customerNumber:'+ayaraOpportunitiesSearchObject.customerNumber+',';
        }

        
        if (searchString == '') {
            serviceUrl=serviceUrl+'search=%25';
        }
        else {
            serviceUrl=serviceUrl+'search='+searchString;
        }
        
        if (paginationOptions.pageNumber!=undefined && paginationOptions.pageNumber!="" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber!=0) {
           serviceUrl=serviceUrl+'&page='+paginationOptions.pageNumber+'&size='+paginationOptions.pageSize;
        }
                
        return this.http.get(serviceUrl).toPromise().then((data:any) => {
            return data;
        });
    }
    
	
    saveAyaraOpportunities(ayaraOpportunities:any): Promise<any[]> {
        let body = JSON.stringify(ayaraOpportunities);
        return this.http.post(appSettings.apiUrl+'/AYARA_OPPORTUNITIES',body,httpOptions).toPromise().then((data: any) => {
            return data;
        });
    }
    
    updateAyaraOpportunities(ayaraOpportunities:any): Promise<any[]> {
        
        delete ayaraOpportunities._links;
        delete ayaraOpportunities.interests;
        let body = JSON.stringify(ayaraOpportunities);
        return this.http.put(appSettings.apiUrl+'/AYARA_OPPORTUNITIES/'+ayaraOpportunities.opportunityId,body,httpOptions).toPromise().then((data: any) => {
            return data;
        });
        
    }
    
    deleteAyaraOpportunities(ayaraOpportunities:any): Promise<any[]> {
        let deleteUrl=appSettings.apiUrl+'/AYARA_OPPORTUNITIES/'+ayaraOpportunities.opportunityId;
        return this.http.delete(deleteUrl,httpOptions).toPromise().then((data: any) => {
            return data;
        });
    }

}
