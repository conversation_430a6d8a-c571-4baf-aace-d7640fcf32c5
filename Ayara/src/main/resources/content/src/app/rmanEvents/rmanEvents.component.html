<div class="content-section implementation">
</div>

<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>

<div class="card-wrapper">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card-block">

					<p-panel header="Events" [toggleable]="false" (onBeforeToggle)=onBeforeToggle($event)>

						<p-header>
							<div class="pull-right icons-list" *ngIf="collapsed">
								<a (click)="onConfiguringColumns($event)" class="add-column">
									<em class="fa fa-cog"></em>Columns</a>
								<a (click)="showDialogToSearch()" title="Search">
									<em class="fa fa-search"></em>
								</a>
								<a (click)="reset(dt)" title="Reset">
									<em class="fa fa-refresh"></em>
								</a>
								<div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
									<div class="user-popup">
										<div class="content overflow">
											<input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()" />
											<label for="selectall">Select All</label>
											<a class="close" title="Close" (click)="closeConfigureColumns($event)">&times;</a>
											<p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
												<ng-template let-col let-index="index" pTemplate="item">
													<div *ngIf="col.drag">
														<div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
															<div class="drag">
																<input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" />
																<label>{{col.header}}</label>
															</div>
														</div>
													</div>
													<div *ngIf="!col.drag">
														<div class="ui-helper-clearfix">
															<div>
																<input type="checkbox" [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"
																/>
																<label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
															</div>
														</div>
													</div>
												</ng-template>
											</p-listbox>

										</div>

										<div class="pull-right">
											<a class="configColBtn" (click)="saveColumns()">Save</a>
											<a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
										</div>

									</div>
								</div>
							</div>
						</p-header>

						<div class="x-scroll">
							<p-table [loading]="loading" class="ui-datatable arrangementMgrTbl" #dt id="rmanOrderEvents-dt" [value]="rmanEventsList"
							 selectionMode="single" (onRowSelect)="onRowSelect($event)" (onLazyLoad)="getRmanEvents($event)" [lazy]="true" [paginator]="true"
							 [rows]="pageSize" [totalRecords]="totalElements" scrollable="true" [columns]="columns" [resizableColumns]="true" columnResizeMode="expand">

								<ng-template pTemplate="colgroup" let-columns>
									<colgroup>
										<col>
										<col *ngFor="let col of columns">
									</colgroup>
								</ng-template>


								<ng-template pTemplate="header" class="arrangementMgrTblHead">
									<tr>
										<th></th>
										<ng-container *ngFor="let col of columns">
											<th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
											<th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}"
											 title="{{col.header}}" pResizableColumn>{{col.header}}</th>
										</ng-container>
									</tr>
								</ng-template>

								<ng-template pTemplate="body" let-rowData let-rmanEvents let-columns="columns">
									<tr [pSelectableRow]="rowData" [pSelectableRowIndex]="rowIndex">
										<td>
											<a *isAuthorized="['write','ME']" (click)="editRow(rmanEvents)" class="icon-edit"> </a>
										</td>
										<ng-container *ngFor="let col of columns">
											<td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
												{{rowData[col.field]}}
											</td>

											<td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
												{{rowData[col.field]}}
											</td>

											<td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
												{{rowData[col.field] | date: 'MM/dd/yyyy'}}
											</td>

											<td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
												{{rowData[col.field] | round}}
											</td>
										</ng-container>

									</tr>
								</ng-template>


								<ng-template pTemplate="emptymessage" let-columns>
									<tr *ngIf="!columns">
										<td class="no-data">{{noData}}</td>
									</tr>
								</ng-template>
							</p-table>
						</div>
					</p-panel>
				</div>
			</div>
		</div>
	</div>
</div>

<p-dialog header="Search" width="800" [(visible)]="displaySearchDialog" [draggable]="true" showEffect="fade" [modal]="true">
	<form>
		<div class="ui-grid ui-grid-responsive ui-fluid">
			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">SO#</span>
						<input pInputText name="orderNumber" class="textbox" placeholder="SO#" id="orderNumber" [(ngModel)]="rmanEventsSearch.orderNumber"
						/>
					</span>
				</div>
				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">SO Line#</span>
						<input pInputText name="orderLineNo" class="textbox" placeholder="SO Line#" id="orderLineNo" [(ngModel)]="rmanEventsSearch.orderLineNo"
						/>
					</span>
				</div>
			</div>
		</div>
	</form>
	<p-footer>
		<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
			<button type="submit" pButton class="primary-btn" (click)="search()" label="Search"></button>
			<button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>

		</div>
	</p-footer>

</p-dialog>
<p-dialog header="{{(newRmanEvents) ? 'Create New Event' : 'Edit Event'}}" width="800" [(visible)]="displayDialog" [draggable]="true"
 showEffect="fade" [modal]="true" (onHide)="cancelAddEdit()">
	<form>
		<div class="ui-grid ui-grid-responsive ui-fluid" *ngIf="rmanEvents">
			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">SO#</span>
						<input pInputText name="orderNumber" class="textbox" placeholder="SO#" id="orderNumber" [disabled]="true" [(ngModel)]="rmanEvents.orderNumber"
						/>
					</span>
				</div>
				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">SO Line#</span>
						<input pInputText name="orderLineNo" class="textbox" placeholder="SO Line#" id="orderLineNo" [disabled]="true" [(ngModel)]="rmanEvents.orderLineNo"
						/>
					</span>
				</div>
			</div>

			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Event Name</span>
						<input pInputText name="eventName" class="textbox" placeholder="Event Name" id="eventName" [(ngModel)]="rmanEvents.eventName"
						/>
					</span>
				</div>

				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">Event Name</span>
						<p-calendar showAnim="slideDown" inputStyleClass="textbox" placeholder="Event Date" name="eventDate" id="eventDate" [monthNavigator]="true"
						 [yearNavigator]="true" yearRange="1950:2030" appendTo="body" [ngModelOptions]="{standalone: true}" [(ngModel)]="rmanEvents.eventDate"
						 dateFormat="yy-mm-dd">
						</p-calendar>
					</span>

				</div>

			</div>

			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Service Start Date</span>
						<p-calendar showAnim="slideDown" inputStyleClass="textbox" placeholder="Service Start Date" name="serviceStartDate" id="serviceStartDate"
						 [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" [ngModelOptions]="{standalone: true}" [(ngModel)]="rmanEvents.serviceStartDate"
						 (ngModelChange)="dateCheck($event)" dateFormat="yy-mm-dd" appendTo="body"></p-calendar>
					</span>
				</div>

				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">Service End Date</span>
						<p-calendar showAnim="slideDown" inputStyleClass="textbox" placeholder="Service End Date" name="serviceEndDate" id="serviceEndDate"
						 [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" appendTo="body" [ngModelOptions]="{standalone: true}"
						 [(ngModel)]="rmanEvents.serviceEndDate" (ngModelChange)="dateCheck($event)" dateFormat="yy-mm-dd" appendTo="body"></p-calendar>
						<div *ngIf="dateFlag">
							<span class="red-color">End date should be greater then start date</span>
						</div>
					</span>
				</div>

			</div>


			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">COGS Amount</span>
						<input pInputText name="cogsAmount" class="textbox" placeholder="COGS Amount" id="cogsAmount" [(ngModel)]="rmanEvents.cogsAmount"
						/>
					</span>
				</div>
				<div class="ui-g-6 pull-right">
					<span class="md-inputfield">
						<span class="selectSpan">Additional Discount</span>
						<input pInputText name="attribute1" class="textbox" placeholder="Additional Discount" id="attribute1" [(ngModel)]="rmanEvents.attribute1"
						/>
					</span>
				</div>
			</div>
		</div>
	</form>
	<p-footer>
		<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
			<button type="submit" pButton class="primary-btn" label="Save" (click)="save()"></button>
			<button type="button" pButton class="secondary-btn" (click)="cancelAddEdit()" label="Cancel"></button>
		</div>
	</p-footer>
</p-dialog>