export interface RmanEvents {
    createdBy?: any;
    lastUpdatedBy?: any;
    eventDate?: any;
    transactionSource?: any;
    errorMessage?: any;
    orderLineId?: any;
    eventName?: any;
    orderLineNo?: any;
    creationDate?: any;
    lastUpdateLogin?: any;
    lastUpdateDate?: any;
    transactionType?: any;
    status?: any;
    orderNumber?: any;
    cogsAmount?: any;
    serviceStartDate?: any;
    serviceEndDate?: any;
    productName?: any;
}
