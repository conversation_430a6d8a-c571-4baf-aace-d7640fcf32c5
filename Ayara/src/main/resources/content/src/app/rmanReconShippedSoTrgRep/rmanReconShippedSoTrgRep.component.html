<div class="content-section implementation">
</div>
<div class="card-wrapper">
    <div class="container-fluid">
      <div class="row">
        <div class="col-md-12">
          <div class="card-block">

<p-panel header="Reconciliation Shipments/Fulfillments to Revenue Triggers Report" [toggleable]="false" (onBeforeToggle)=onBeforeToggle($event)>

			<p-header>
				<div class="pull-right icons-list">
					<a  (click)="goToReconciliationReports()" class="add-column"><em class="fa fa-reply"></em>Back</a>
					<a  (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
					 <a  (click)="showDialogToSearch()" title="Search"><em class="fa fa-search"></em></a>
					 <a  (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
					 <a  (click)="exportExcel()" title="Export" *ngIf="!disableExport"><em class="fa fa-external-link"></em></a>
					
					 <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
						<div class="user-popup">
						  <div class="content overflow">
							<input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()"/>
							<label for="selectall">Select All</label>
							<a class="close" title="Close" (click)="closeConfigureColumns($event)" >&times;</a>
							<p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
								<ng-template let-col let-index="index" pTemplate="item">
								  <div *ngIf="col.drag">
								  <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" 
								   (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
									<div class="drag">
									  <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)"/>
									  <label>{{col.header}}</label>
									</div>
								  </div>
								  </div>
								  <div *ngIf="!col.drag">
								  <div class="ui-helper-clearfix">
									<div>
									  <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"/>
									  <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
									</div>
								  </div>
								  </div>
								</ng-template>
								</p-listbox>
						   </div>
						   <div class="pull-right">
							<a class="configColBtn" (click)="saveColumns()">Save</a>
							<a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
						  </div>
						</div>
					 </div>
				</div>
			</p-header>


		<div class="x-scroll">
		<p-table class="ui-datatable arrangementMgrTbl" [loading]="loading" #dt id="reconciliation4-dt" [value]="rmanReconShippedSoTrgRepList" selectionMode="single"   (onRowSelect)="onRowSelect($event)"
        (onLazyLoad)="getRmanReconShippedSoTrgRep($event)" [lazy]="true" [paginator]="showPaginator" [rows]="pageSize" [totalRecords]="totalElements"
         scrollable="true"  [columns]="columns" [resizableColumns]="true" columnResizeMode="expand">
						 
		
						<ng-template pTemplate="colgroup" let-columns>
							<colgroup>
								<col *ngFor="let col of columns">
							</colgroup>
						</ng-template>

		
						<ng-template pTemplate="header" class="arrangementMgrTblHead">
                   		 <tr>
							<ng-container *ngFor="let col of columns">
								<th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
								<th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
						   </ng-container>
                          </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-rowData let-rmanReconShippedSoTrgRep>
                                <tr [pSelectableRow]="rowData">           
									<ng-container *ngFor="let col of columns" >
										<td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
											{{rowData[col.field]}}
										</td>
									
										<td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
											{{rowData[col.field]}}
										</td>
									
										<td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
											{{rowData[col.field] | date: 'MM/dd/yyyy'}}
										</td>
									
										<td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
											{{rowData[col.field] | round}}
										</td>
									</ng-container>
								 </tr>
						</ng-template>
					
                      <ng-template pTemplate="emptymessage" let-columns>
							<tr *ngIf="!columns">
								<td [attr.colspan]="columns.length" class="no-data">{{noData}}</td>
							</tr>
	 				  </ng-template>
					</p-table>
					</div>
</p-panel>
</div>
</div>
</div>
</div>
</div>

<p-dialog header="Search" width="500" [(visible)]="displaySearchDialog" [draggable]="true"  showEffect="fade" [modal]="true">
  <form  novalidate>
	<div class="ui-grid ui-grid-responsive ui-fluid">

	<div class="ui-g-12">
		<div class="ui-g-6">
			<span class="selectSpan">From Period</span>
			<p-calendar showAnim="slideDown" inputStyleClass="textbox" name="fromPeriod"  id="fromPeriod" [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" appendTo="body" [ngModelOptions]="{standalone: true}"  
			[(ngModel)]="fromDate" dateFormat="yy-mm-dd" placeholder="From Period"></p-calendar>
		</div>

		<div class="ui-g-6 pull-right">
			<span class="selectSpan">To Period</span>
			<p-calendar showAnim="slideDown" inputStyleClass="textbox" name="toPeriod"  id="toPeriod" [monthNavigator]="true" [yearNavigator]="true" yearRange="1950:2030" appendTo="body" [ngModelOptions]="{standalone: true}" 
			 [(ngModel)]="toDate" dateFormat="yy-mm-dd" placeholder="To Period"></p-calendar>
		</div>
	
	  <div class="ui-g-12">
	  	<span class="selectSpan">Legal Entity</span>
		<p-dropdown [options]="entities"  appendTo="body" [(ngModel)]="legalEntity" [ngModelOptions]="{standalone: true}"  name="legalEntity" [filter]="true" >
		</p-dropdown>
	  </div>
	</div>
	
	</div>
			
                                              
  

	</form>
	<p-footer>
		<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
			<button type="submit" pButton class="primary-btn" label="Search" (click)="searchFields(dt)"></button>
           <button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>
		</div>
   </p-footer>
	</p-dialog>


