interface ILabels {
    [index: string]: string;
}

export class RmanReconShippedSoTrgRepLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["srcDeliveredQty"] = "Delivered Quantity (Source)";
        this.fieldLabels["arrangementNumber"] = "Arrangement #";
        this.fieldLabels["exception"] = "Exceptions";
        this.fieldLabels["eventReceived"] = "Event Received";
        this.fieldLabels["revTriggerForSku"] = "Revenue Trigger as per SKU";
        this.fieldLabels["entity"] = "Entity";
        this.fieldLabels["deliveredDate"] = "Delivered Date";
        this.fieldLabels["arrgLineNum"] = "Linked Arrangement Line #";
        this.fieldLabels["soLineNum"] = "SO Line #";
        this.fieldLabels["sourceLineId"] = "Source Line Id";
        this.fieldLabels["arrangementName"] = "Arrangement Name";
        this.fieldLabels["so"] = "SO #";
        this.fieldLabels["sku"] = "Product Name";
        this.fieldLabels["trgDeliveredQty"] = "Delivered Quantity (Destination)";
    }

}
