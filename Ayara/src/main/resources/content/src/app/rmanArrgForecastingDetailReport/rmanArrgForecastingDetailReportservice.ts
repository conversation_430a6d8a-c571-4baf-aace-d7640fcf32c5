import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require: any;
const appSettings = require('../appsettings');


@Injectable()
export class RmanArrgForecastingDetailReportService {

    constructor(private http: HttpClient) { }


    getRmanForecastDetailsReport(arrgLevel: any, arrgId: any, fromPeriod: any, toPeriod: any, currency?: any, product?: any, customerNumber?: any, region?: any, quote?: any, order?: any, quoteLine?: any, orderLine?: any, assetId?: any, productFamily?: any, productLine?: any, legalEntity?: any) {

        // Initialize all parameters to empty strings if undefined
        if (fromPeriod == undefined) {
            fromPeriod = '';
        }
        if (toPeriod == undefined) {
            toPeriod = '';
        }
        if (arrgLevel == undefined || arrgLevel === '') {
            arrgLevel = 'L';
        }
        if (arrgId == undefined) {
            arrgId = '';
        }
        if (currency == undefined) {
            currency = '';
        }
        if (product == undefined) {
            product = '';
        }
        if (customerNumber == undefined) {
            customerNumber = '';
        }
        if (region == undefined) {
            region = '';
        }
        if (quote == undefined) {
            quote = '';
        }
        if (order == undefined) {
            order = '';
        }
        if (quoteLine == undefined) {
            quoteLine = '';
        }
        if (orderLine == undefined) {
            orderLine = '';
        }
        if (assetId == undefined) {
            assetId = '';
        }
        if (productFamily == undefined) {
            productFamily = '';
        }
        if (productLine == undefined) {
            productLine = '';
        }
        if (legalEntity == undefined) {
            legalEntity = '';
        }

       
        let serviceUrl = appSettings.apiUrl + '/reports/forecastingReport?arrgLevel=' + arrgLevel + '&arrgId=' + arrgId + '&fromPeriod=' + fromPeriod + '&toPeriod=' + toPeriod;

        
        if (currency) {
            serviceUrl += '&currency=' + encodeURIComponent(currency);
        }
        if (product) {
            serviceUrl += '&product=' + encodeURIComponent(product);
        }
        if (customerNumber) {
            serviceUrl += '&customerNumber=' + encodeURIComponent(customerNumber);
        }
        if (region) {
            serviceUrl += '&region=' + encodeURIComponent(region);
        }
        if (quote) {
            serviceUrl += '&quote=' + encodeURIComponent(quote);
        }
        if (order) {
            serviceUrl += '&order=' + encodeURIComponent(order);
        }
        if (quoteLine) {
            serviceUrl += '&quoteLine=' + encodeURIComponent(quoteLine);
        }
        if (orderLine) {
            serviceUrl += '&orderLine=' + encodeURIComponent(orderLine);
        }
        if (assetId) {
            serviceUrl += '&assetId=' + encodeURIComponent(assetId);
        }
        if (productFamily) {
            serviceUrl += '&productFamily=' + encodeURIComponent(productFamily);
        }
        if (productLine) {
            serviceUrl += '&productLine=' + encodeURIComponent(productLine);
        }
        if (legalEntity) {
            serviceUrl += '&legalEntity=' + encodeURIComponent(legalEntity);
        }

       
        return this.http.get(serviceUrl).toPromise().then((data: any) => {
            return data;
        });
    }

    getRmanForecastingDetailsReportFile(arrgLevel: any, arrgId: any, fromPeriod: any, toPeriod: any, exportCols: any, currency?: any, product?: any, customerNumber?: any, region?: any, quote?: any, order?: any, quoteLine?: any, orderLine?: any, assetId?: any, productFamily?: any, productLine?: any, legalEntity?: any) {

        if (fromPeriod == undefined) {
            fromPeriod = '';
        }
        if (toPeriod == undefined) {
            toPeriod = '';
        }
        if (arrgLevel == undefined || arrgLevel === '') {
            arrgLevel = 'L';
        }
        if (arrgId == undefined) {
            arrgId = '';
        }
        if (currency == undefined) {
            currency = '';
        }
        if (product == undefined) {
            product = '';
        }
        if (customerNumber == undefined) {
            customerNumber = '';
        }
        if (region == undefined) {
            region = '';
        }
        if (quote == undefined) {
            quote = '';
        }
        if (order == undefined) {
            order = '';
        }
        if (quoteLine == undefined) {
            quoteLine = '';
        }
        if (orderLine == undefined) {
            orderLine = '';
        }
        if (assetId == undefined) {
            assetId = '';
        }
        if (productFamily == undefined) {
            productFamily = '';
        }
        if (productLine == undefined) {
            productLine = '';
        }
        if (legalEntity == undefined) {
            legalEntity = '';
        }

        let serviceUrl = appSettings.apiUrl + '/reports/exportForecastingReport?arrgLevel=' + arrgLevel + '&arrgId=' + arrgId + '&fromPeriod=' + fromPeriod + '&toPeriod=' + toPeriod;

        if (exportCols != undefined && exportCols != "") {
            serviceUrl = serviceUrl + '&exportCols=' + exportCols;
        }

        if (currency !== '') {
            serviceUrl += '&currency=' + currency;
        }
        if (product !== '') {
            serviceUrl += '&product=' + product;
        }
        if (customerNumber !== '') {
            serviceUrl += '&customerNumber=' + customerNumber;
        }
        if (region !== '') {
            serviceUrl += '&region=' + region;
        }
        if (quote !== '') {
            serviceUrl += '&quote=' + quote;
        }
        if (order !== '') {
            serviceUrl += '&order=' + order;
        }
        if (quoteLine !== '') {
            serviceUrl += '&quoteLine=' + quoteLine;
        }
        if (orderLine !== '') {
            serviceUrl += '&orderLine=' + orderLine;
        }
        if (assetId !== '') {
            serviceUrl += '&assetId=' + assetId;
        }
        if (productFamily !== '') {
            serviceUrl += '&productFamily=' + productFamily;
        }
        if (productLine !== '') {
            serviceUrl += '&productLine=' + productLine;
        }
        if (legalEntity !== '') {
            serviceUrl += '&legalEntity=' + legalEntity;
        }

        return serviceUrl;
    }

    fetchRCNumbers(rc: any) {
        let unpostedRCUrl = appSettings.apiUrl + '/fetchAllRC?rc=' + rc;

        return this.http.get(unpostedRCUrl).toPromise().then((data: any) => {
            return data;
        });
    }

    getParameterValues(paramName: string, paramValue: string) {
        return this.http.get<any>(appSettings.apiUrl + `/fetchFilterValues?parameterName=${paramName}&parameterValue=${paramValue}`);
    }
}
