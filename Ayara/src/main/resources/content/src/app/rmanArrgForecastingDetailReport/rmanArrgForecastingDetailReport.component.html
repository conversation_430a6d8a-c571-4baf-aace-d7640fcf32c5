<div class="content-section implementation">
</div>

<div class="card-wrapper waterfall-detail-report">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card-block">

					<p-panel header="Forecasting Details Report" [toggleable]="false" (onBeforeToggle)=onBeforeToggle($event)>
						<p-header>
							<div class="pull-right icons-list">

								<a (click)="goToOperationReports()" class="add-column">
									<em class="fa fa-reply"></em>Back</a>
									
								<!-- Rollup view selector -->
								<p-dropdown [options]="rollupOptions" [(ngModel)]="rollupLevel" (onChange)="onRollupLevelChange($event)" 
									styleClass="mr-2" placeholder="Select Roll-up View"></p-dropdown>								
								
								<a (click)="showDialogToSearch()" title="Search">
									<em class="fa fa-search"></em>
								</a>
								<a (click)="reset(dt)" title="Reset">
									<em class="fa fa-refresh"></em>
								</a>
								<!-- <a (click)="exportCSVfile()" title="Export" *ngIf="!disableExport">
									<em class="fa fa-external-link"></em>
								</a> -->
								<a (click)="loadAllDataThenExport(dt)" title="Export">
									<em class="fa fa-external-link"></em>
								</a>

							</div>
						</p-header>

						<div class="x-scroll mb-2">
							
							<p-table class="ui-datatable arrangementMgrTbl" [loading]="loading" #dt id="waterfall-dt" [value]="arrgForecastRepList"
							 selectionMode="single" (onRowSelect)="onRowSelect($event)" [lazy]="true" [paginator]="false" [rows]="10" [totalRecords]="totalRecords"
							 scrollable="true" [columns]="columns" [resizableColumns]="true" columnResizeMode="expand" (onPage)="paginate($event)" exportFilename="Forecasting_Report">

								<ng-template pTemplate="colgroup" let-columns>
									<colgroup>
										<col *ngFor="let col of columns">
									</colgroup>
								</ng-template>

								<ng-template pTemplate="header" class="arrangementMgrTblHead">
									<tr>
										<th style="width:100px" *ngFor="let col of columns" pResizableColumn [ngStyle]="{'display': col.display, 'text-align':col.text}">
											{{col.header}}
										</th>

									</tr>
								</ng-template>
								<ng-template pTemplate="body" let-rowData let-arrgForecastRep>
									<tr [pSelectableRow]="rowData">
										<td style="width:100px" *ngFor="let col of columns" [ngStyle]="{'display': col.display, 'text-align': col.text}">
											<!--<span title="{{arrgForecastRep[col.field]}}">{{isDateField(col.field) ? (arrgForecastRep[col.field]|date:'MM/dd/yyyy') :(isCurrencyField(col.field) ? (arrgForecastRep[col.field]|number:'1.2-2') :(isIntegerField(col.field) && col.type== 'number' ? (arrgForecastRep[col.field]|number:'1.0-0') : (arrgForecastRep[col.field])))}}</span>-->
											<span title="{{arrgForecastRep[col.field]}}">
											  {{isDateField(col.field) 
											    ? (arrgForecastRep[col.field]|date:'MM/dd/yyyy') 
											    : (col.header === 'Discount or Premium%'
											        ? (arrgForecastRep[col.field] | number:'1.2-2')
											        : (isRawNumberField(col.header)
											            ? arrgForecastRep[col.field] 
											            : (col.type === 'integer' 
											                ? (arrgForecastRep[col.field]|number:'1.0-0') 
											                : (col.type === 'currency' || isCurrencyField(col.field) 
											                    ? (arrgForecastRep[col.field]|number:'1.2-2') 
											                    : arrgForecastRep[col.field]))))}}
											</span>
										</td>


									</tr>
								</ng-template>
								<ng-template pTemplate="emptymessage">
									<tr>
										<td class="no-data">{{noData}}</td>
									</tr>
								</ng-template>





							</p-table>
						</div>

						<p-paginator [rows]="9" id="paginator-c" [totalRecords]="totalRecords" (onPageChange)="paginate($event)">
						</p-paginator>

					</p-panel>
				</div>
			</div>
		</div>
	</div>
</div>

<p-dialog header="Search" width="800" [(visible)]="displayDialog" [closable]="true" [draggable]="true" showEffect="fade"
 [modal]="true" >
	<form>
		<div class="ui-grid ui-grid-responsive ui-fluid">
			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Revenue Contract Number</span>
						<p-autoComplete inputStyleClass=" textbox" [(ngModel)]="arrgId" [suggestions]="dealArrangementNumbers" (completeMethod)="searchRCList($event)" 
                       [ngModelOptions]="{standalone: true}"  [minLength]="3"  styleClass="wid100"
                        placeholder="RC Number atleast 3 Characters" ></p-autoComplete>
					</span>
				</div>
				<div class="ui-g-6">
						<span class="selectSpan">Legal Entity</span>
		<p-dropdown [options]="entities"  appendTo="body" [(ngModel)]="legalEntity" [ngModelOptions]="{standalone: true}"  name="legalEntity" [filter]="true"    >
		</p-dropdown>

				</div>
		
			</div>
			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">From Period</span>
						<p-autoComplete inputId="fromPeriodName" [(ngModel)]="fromPeriod" inputStyleClass="textbox" [ngModelOptions]="{standalone: true}"
						 [suggestions]="periodArray" appendTo="body" (completeMethod)="searchPeriod($event)" [minLength]="3"></p-autoComplete>
					</span>
				</div>
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">To Period</span>
						<p-autoComplete inputId="toPeriodName" [(ngModel)]="toPeriod" inputStyleClass="textbox" [ngModelOptions]="{standalone: true}"
						 [suggestions]="periodArray" appendTo="body" (completeMethod)="searchPeriod($event)" [minLength]="3 "></p-autoComplete>
					</span>
				</div>
			</div>
			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Currency</span>
						<p-dropdown [options]="currencies" [(ngModel)]="currency" [ngModelOptions]="{standalone: true}"
						 placeholder="Select Currency" [style]="{'width':'100%'}"></p-dropdown>
					</span>
				</div>
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Region</span>
						<p-autoComplete [(ngModel)]="region" [suggestions]="regions" (completeMethod)="searchRegions($event)" 
                        [ngModelOptions]="{standalone: true}" [minLength]="3" 
                        placeholder="Search Region"></p-autoComplete>
					</span>
				</div>
			</div>
			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Quote Number</span>
						<input pInputText name="quote" class="textbox" placeholder="Quote Number" [ngModelOptions]="{standalone: true}"
						 [(ngModel)]="quote" />
					</span>
				</div>
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Quote Line</span>
						<input pInputText name="quoteLine" class="textbox" placeholder="Quote Line" [ngModelOptions]="{standalone: true}"
						 [(ngModel)]="quoteLine" />
					</span>
				</div>
			</div>
			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Order Number</span>
						<input pInputText name="order" class="textbox" placeholder="Order Number" [ngModelOptions]="{standalone: true}"
						 [(ngModel)]="order" />
					</span>
				</div>
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Order Line</span>
						<input pInputText name="orderLine" class="textbox" placeholder="Order Line" [ngModelOptions]="{standalone: true}"
						 [(ngModel)]="orderLine" />
					</span>
				</div>
			</div>
			<div class="ui-g-12">
				<div class="ui-g-6">
					 <span class="md-inputfield">
													<span class="selectSpan">Asset ID</span>
                                                        <input pInputText name="assetId" id="assetId" class="textbox" placeholder="Asset ID" [ngModelOptions]="{standalone: true}" [(ngModel)]="assetId"
                                                        />
                                                </span>
					
				</div>
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Product</span>
						<p-autoComplete [(ngModel)]="product" [suggestions]="products" (completeMethod)="searchProducts($event)" 
                        field="displayLabel" [ngModelOptions]="{standalone: true}"  [minLength]="3" 
                        placeholder="Product atleast 3 Characters"></p-autoComplete>
					</span>
				</div>
			</div>
			<div class="ui-g-12">
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Product Family</span>
						<p-autoComplete [(ngModel)]="productFamily" [suggestions]="productFamilies" (completeMethod)="searchProductFamilies($event)" 
                        [ngModelOptions]="{standalone: true}"  [minLength]="3" 
                        placeholder="Product Line atleast 3 Characters"></p-autoComplete>
					</span>
				</div>
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Product Line</span>
						<p-autoComplete [(ngModel)]="productLine" [suggestions]="productLines" (completeMethod)="searchProductLines($event)" 
                        [ngModelOptions]="{standalone: true}" [minLength]="3" 
                        placeholder="Product Line atleast 3 Characters"></p-autoComplete>
					</span>
				</div>
			</div>
			<div class="ui-g-12">
				<!-- <div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Customer</span>
						<p-autoComplete [(ngModel)]="customer" [suggestions]="customers" (completeMethod)="searchCustomerNames($event)" 
						 [ngModelOptions]="{standalone: true}"  [minLength]="3" 
							placeholder="Customer at least 3 Characters"></p-autoComplete>
					</span>
				</div> -->
				<div class="ui-g-6">
					<span class="md-inputfield">
						<span class="selectSpan">Customer Number</span>
						<p-autoComplete [(ngModel)]="customerNumber" [suggestions]="customerNumbers" (completeMethod)="searchCustomerNumbers($event)" 
							[ngModelOptions]="{standalone: true}"  [minLength]="3" 
							placeholder="Customer Number at least 3 Characters"></p-autoComplete>
					</span>
				</div>
			</div>
		</div>
	</form>
	<p-footer>
		<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
			<button type="submit" pButton class="primary-btn" label="Search" (click)="search()"></button>
			<button type="reset" pButton class="secondary-btn" (click)="reset(dt)" label="Reset"></button>
		</div>
	</p-footer>
</p-dialog>