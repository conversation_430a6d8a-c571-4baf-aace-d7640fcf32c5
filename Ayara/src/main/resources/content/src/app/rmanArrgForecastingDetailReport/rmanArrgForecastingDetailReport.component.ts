import { Location } from '@angular/common';
import { Component, Injectable, OnInit,ViewChild } from '@angular/core'; // Import OnInit
import { Table } from 'primeng/table';
import { RmanFiscalPeriodsService } from '../rmanFiscalPeriods/rmanFiscalPeriodsservice';
import { RmanLegalEntitiesService } from '../rmanLegalEntities/rmanLegalEntitiesservice';
import { CommonSharedService } from '../shared/common.service';
import { NotificationService } from '../shared/notifications.service';
import { RmanArrgForecastingDetailReportService } from './rmanArrgForecastingDetailReportservice';
import { RmanCustomersService } from '../rmanCustomers/rmanCustomersservice';
import { RmanCurrencyService } from '../rmanCurrency/rmanCurrencyservice';
import { RmanLookupCodesService } from '../rmanLookupCodes/rmanLookupCodesservice';
import { RmanProductsService } from '../rmanProducts/rmanProductsservice';
import { RmanLookupsVService } from '../rmanLookupsV/rmanLookupsVservice';

declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Injectable()
@Component({
  templateUrl: './rmanArrgForecastingDetailReport.component.html',
  selector: 'rmanArrgForecastingDetailReport-data',
  styleUrls: ['./rmanArrgForecastingDetailReport.component.scss'],
  providers: [RmanArrgForecastingDetailReportService, RmanFiscalPeriodsService, RmanLegalEntitiesService, RmanCustomersService, RmanCurrencyService, RmanLookupCodesService, RmanProductsService, RmanLookupsVService]
})


export class RmanArrgForecastingDetailReportComponent implements OnInit { // Implement OnInit
   
  @ViewChild('dt') _dataTable: Table; 
  paginationOptions: any;
  data: any[];
  columns: any[] = [];
  pageSize: number;
  totalElements: number;
  fromPeriod: any;
  toPeriod: any;
  arrgId: any;
  toArrgId: any;
  po: any;
  so: any;
  legalEntity: any;
  rowCount: any;
  arrgForecastRepList: any[];
  arrgForecastList: any[];
  displayDialog: boolean;
legalEntitiesList: any[] = [];
entites: any[] = [];
fiscalPeriodsList: any[] = [];
fromPeriodArray: any[] = [];
entities: any[] = [];
totalRecords: any;
legalEntities: any[] = [];
  assetIds: string[] = [];
  searchFields: any = {};

  noData = appSettings.noData;
  loading: boolean = false;
  savingColumns: boolean = false;
  collapsed: boolean = false;
  //_dataTable: any;

  valueResults: any[] = [];
isSearched: number = 0;
  showAddColumns = true;
  isSelectAllChecked = true;
  globalCols: any[];
  clonedCols: any[];
  userId: number;
  showPaginator: boolean = true;
  startIndex: number;
  rmanCurrencyList: any[] = [];
  currencies: any[] = [];
  rmanCurrency: any[] = [];
  exportCols: string[] = [];
  disableExport: boolean = true;
  arrgLevel: string = 'L';
  arrgLevelSwitch: boolean = false;
  rollupLevel: string = 'L';
  rollupOptions = [
    { label: 'Product View', value: 'PR' },
    { label: 'Revenue Contract View', value: 'S' },
    { label: 'Account View', value: 'CR' },
    { label: 'Region View', value: 'RR' }
  ];

  transactionPeriod: any;
  product: any;
  customerAccount: string;
  region: string;
  customerNumber: string;

  currency: any;
  quote: any;
  order: any;
  rc: any;
  quoteLine: any;
  orderLine: any;
  assetId: any;
  productFamily: string;
  productLine: string;


  products: any[] = [];
  customers: string[] = [];
  customerNames: string[] = [];
  customerNumbers: string[] = [];
  regions: string[] = [];
  productFamilies: string[] = [];
  productLines: string[] = [];
  dealArrangementNumbers: any[] = [];
  periodArray: any[] = [];
  customer: any;
  regionsDropdown: any[] = [];
  rmanLookupsV2List: any[] = [];

  currentDate: Date = new Date();

  constructor(private rmanArrgForecastingDetailReportService: RmanArrgForecastingDetailReportService,
    private rmanFiscalPeriodsService: RmanFiscalPeriodsService,
    private location: Location,
    private commonSharedService: CommonSharedService,
    private notificationService: NotificationService,
    private rmanCustomersService: RmanCustomersService,
    private rmanCurrencyService: RmanCurrencyService,
    private rmanLegalEntitiesService: RmanLegalEntitiesService,
    private rmanLookupCodesService: RmanLookupCodesService,
    private rmanProductsService: RmanProductsService,
    private rmanLookupsVService: RmanLookupsVService) {
    this.globalCols = [];
    this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };

    this.arrgForecastRepList = [];
    this.arrgForecastList = [];
  }

  ngOnInit() {
    this.rowCount = 20;
    this.loadCurrencyDropdown();
    this.loadRegionsDropdown();
    this.rollupLevel = 'L'; // Default rollup level
   this.rmanLegalEntitiesService.getAllRmanLegalEntities(this.paginationOptions, {}, 'Y').then((data: any) => {
      this.legalEntitiesList = data.content;
      this.getEntities();
    })

    this.getAllRmanForecastingDetailsReport();
  }

  loadRegionsDropdown() {

    this.rmanLookupsVService.getAllRmanLookupsV(this.paginationOptions, { 'lookupTypeName': 'SALES_LEVEL2' }).then((rmanLookupsV2List: any) => {
      this.rmanLookupsV2List = rmanLookupsV2List.content;
      this.prepareRmanLookupsV2Object();
    }).catch((err: any) => {
      this.notificationService.showError('Error occurred while getting regions data');
    });
  }

  prepareRmanLookupsV2Object() {
    let rmanLookupsV2TempObj: any = [];
    this.rmanLookupsV2List.forEach((rmanLookupsV2) => {
      rmanLookupsV2TempObj.push(rmanLookupsV2.lookupDescription);
    });
    this.regions = rmanLookupsV2TempObj;


    let regionsDropdownOptions: any = [];
    regionsDropdownOptions.push({ label: 'Select Region', value: null });
    this.rmanLookupsV2List.forEach((rmanLookupsV2) => {
      regionsDropdownOptions.push({ label: rmanLookupsV2.lookupDescription, value: rmanLookupsV2.lookupCode });
    });
    this.regionsDropdown = regionsDropdownOptions;
  }

  loadCurrencyDropdown() {
    this.rmanCurrencyService.getAllRmanCurrency(this.paginationOptions, {}).then((rmanCurrencyList: any) => {
      this.rmanCurrencyList = rmanCurrencyList.content;
      this.prepareCurrencyDropdown();
    }).catch((err: any) => {
      this.notificationService.showError('Error occurred while getting currency data');
    });
  }
  prepareCurrencyDropdown() {
    let currencyOptions: any[] = [];
    currencyOptions.push({ label: 'Select Currency', value: null });
    this.rmanCurrencyList.forEach((currency) => {
      currencyOptions.push({ label: currency.name + ' (' + currency.currencyCode + ')', value: currency.currencyCode });
    });
    this.currencies = currencyOptions;
    this.rmanCurrency = currencyOptions;
  }
 getEntities() {
    let entitiesTemp: any[] = [{ label: '--Select Entity Name--', value: null }];
    this.legalEntitiesList.forEach((obj: any) => {
      entitiesTemp.push({ label: obj.name, value: obj.legalEntityId })
    })
    this.entities = entitiesTemp;
  }
  getTableColumns(pageName: string, tableName: string) {
    this.isSelectAllChecked = true;
    this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
      if (response && response != null && response.userId) {
        this.columns = [];
        let colsList = response.tableColumns.split(",");
        if (colsList.length > 0) {
          colsList.forEach((item, index) => {
            if (item) {
              this.startIndex = this.globalCols.findIndex(col => col.field == item);
              this.onDrop(index);
            }
          });
        }
        this.globalCols.forEach(col => {
          if (response.tableColumns.indexOf(col.field) !== -1) {
            this.columns.push(col);
          } else {
            col.showField = false;
          }
        });
        if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
        this.showPaginator = this.columns.length !== 0;
        this.userId = response.userId;
      } else {
        this.columns = this.globalCols;
      }


    }).catch(() => {
      this.notificationService.showError('Error occured while getting table columns data');
      
    });
  }

  saveColumns() {
    let selectedCols = "";
    this.showAddColumns = !this.showAddColumns;
    const colLength = this.globalCols.length - 1;
    this.globalCols.forEach((col, index) => {
      if (col.showField) {
        selectedCols += col.field;
        if (index < colLength) {
          selectedCols += ",";
        }
      }
    });
    this.savingColumns = true;
    this.commonSharedService.saveOrUpdateTableColumns("rmanArrgForecastingDetailReport", "Forecasting Details Report", selectedCols, this.userId).then((response) => {
      this.columns = this.globalCols.filter(item => item.showField);
      this.userId = response["userId"];
      this.showPaginator = this.columns.length !== 0;
      this.savingColumns = false;
    }).catch(() => {
      this.notificationService.showError('Error occured while saving');
      this.savingColumns = false;
    });
  }

  onDragStart(index: number) {
    this.startIndex = index;
  }

  onDrop(dropIndex: number) {
    const general = this.globalCols[this.startIndex]; // get element
    this.globalCols.splice(this.startIndex, 1);      // delete from old position
    this.globalCols.splice(dropIndex, 0, general);    // add to new position
  }
  onRollupLevelChange(event: any) {
    this.rollupLevel = event.value;
    
    localStorage.setItem('forecastRollupLevel', this.rollupLevel); // Save to localStorage when changed
    this.getAllRmanForecastingDetailsReport();
  }
  onRowSelect(data) {
  }



  selectColumns(col: any) {
    let cols = this.globalCols.filter(item => !item.showField);
    if (cols.length > 0) {
      this.isSelectAllChecked = false;
    } else {
      this.isSelectAllChecked = true;
    }
  }
  onSelectAll() {
    this.isSelectAllChecked = !this.isSelectAllChecked;
    this.globalCols.forEach(col => {
      if (this.isSelectAllChecked) {
        col.showField = true;
      } else {
        if (col.drag) {
          col.showField = false;
        }
      }
    });
  }

  /* Commenting out column configuration functionality
  onConfiguringColumns(event: any) {
    this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
    this.showAddColumns = false;
  }

  closeConfigureColumns(event: any) {
    this.showAddColumns = true;
    this.globalCols = this.clonedCols;
    let configCol = this.globalCols.filter(item => !item.showField);
    this.isSelectAllChecked = !(configCol.length > 0);
  }
  */

  goToOperationReports() {
    this.location.back();
  }


  onBeforeToggle(evt: any) {
    this.collapsed = evt.collapsed;
  }
  paginate(data) {
    this.loading = true;
    this.arrgForecastRepList = this.arrgForecastList.slice(data.first, data.first + 10);
    this.loading = false;
  }




 getAllRmanForecastingDetailsReport() {
  this.globalCols = [];
  this.loading = true;
  const levelToSend = this.rollupLevel === 'L' ? '' : this.rollupLevel;
  

  // If no search was performed and we're just loading the initial view,
  // we might want to limit the data or apply default filters
  if (this.isSearched === 0 && !this.legalEntity) {
    console.log('Initial load - could apply default limits');
  }
  
  // Make sure to pass all parameters properly to the service
  this.rmanArrgForecastingDetailReportService.getRmanForecastDetailsReport(
    levelToSend,
    this.arrgId,
    this.fromPeriod,
    this.toPeriod,
    this.currency,
    this.product,
    this.customerNumber, // Make sure we're passing this directly
    this.region,
    this.quote,
    this.order,
    this.quoteLine,
    this.orderLine,
    this.assetId, // Make sure we're passing the directly bound assetId
    this.productFamily,
    this.productLine,
    this.legalEntity
    ).then((data: any) => {
      // Handle the response
      if (data && data.Content && data.Content.length > 0) {
        this.arrgForecastRepList = data.Content;
        this.arrgForecastList = data.Content;
        let dTemp: any = this.arrgForecastRepList[0];
        for (let prop in dTemp) {
          // Determine if this field should be treated as text based on field name and value
          const isTextField = this.shouldBeTextField(prop, dTemp[prop]);
          const isCurrencyField = this.isCurrencyField(prop);
            const isIntegerField = this.isIntegerField(prop);
              // Set the correct type based on field characteristics
				  let fieldType;
				  if (isTextField) {
				    fieldType = "text";
				  } else if (isIntegerField) {
				    fieldType = "integer";  // Special type for integer fields
				  } else if (isCurrencyField) {
				    fieldType = "currency";
				  } else {
				    fieldType = "number";
				  }
          this.globalCols.push({

            field: prop,
            header: prop,
            style: { 'width': '100px', 'text-align': isTextField ? 'left' : 'right' },
            display: 'table-cell',
            showField: true,
            text: isTextField ? "left" : "right",
            drag: true,
           // type: isTextField ? "text" : "number"
           type: fieldType
          });

          console.log(`Field ${prop} with value ${dTemp[prop]} is treated as ${isTextField ? 'text' : 'number'}`);
        }

        console.log(this.globalCols);


        for (let index = 0; index < this.globalCols.length; index++) {
          const fieldName = this.globalCols[index].header;
          const fieldValue = this.arrgForecastRepList[0][this.globalCols[index].field];
          const isTextField = this.shouldBeTextField(fieldName, fieldValue);

          this.globalCols[index].text = isTextField ? "left" : "right";
          this.globalCols[index].type = isTextField ? "text" : "number";

           // Preserve existing type if it's "integer" or "currency"
  if (this.globalCols[index].type !== "integer" && this.globalCols[index].type !== "currency") {
    this.globalCols[index].type = isTextField ? "text" : "number";
  }

          console.log(`Final column ${fieldName} is treated as ${isTextField ? 'text' : 'number'}`);
        }

        this.arrgForecastRepList = data.Content.slice(0, 10);
        this.totalRecords = data.Content.length;
        this.columns = [];

        const regionCol = this.globalCols.find(col => col.field === 'REGION');


        this.getTableColumns("rmanArrgForecastingDetailReport", "Forecasting Details Report");
        this.disableExport = false;
      } else {
        this.arrgForecastRepList = [];
        this.arrgForecastList = [];
        this.totalRecords = 0;
        this.columns = [];
        this.disableExport = true;
        this.notificationService.showInfo('No data found for the selected criteria');
      }
      this.loading = false;

    }).catch((error) => {
      console.error('Error in API call:', error);
      this.notificationService.showError('Error occurred while getting data');
      this.loading = false;
    });
  }
 isCurrencyField(fieldName: string): boolean {
  const currencyFieldPatterns = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec',
    'Amount', 'Revenue', 'Price', 'Total', 'Cost','Past','Future'
  ];
  
  return currencyFieldPatterns.some(pattern => 
    fieldName.includes(pattern) || 
    fieldName.match(/^(Q[1-4]|FY|CY)/)
  );
}
isRawNumberField(headerName: string): boolean {
  return headerName === 'Revenue Contract Number' || 
         headerName === 'Revenue Contract Line Number' || headerName === 'Discount or Premium%';
}

//   if((fieldName === 'Revenue Contract Number' || 
//       fieldName === 'Revenue Contract Line Number' || fieldName === 'Quantity')) {
//     return true; 
//       } 
//   // Fields that should be shown as integers (no decimal places)
//   const integerFieldPatterns = [
//     'Quantity', 'Count', 'Number', 'ID', 'Discount', 'Percent', 'Revenue Contract Number', 'Revenue Contract Line Number'  // Add these specific columns
//   ];
  
//   return integerFieldPatterns.some(pattern => 
//     fieldName.includes(pattern)
//   );
// }
isIntegerField(fieldName: string): boolean {
  // Check for exact matches first
  if (fieldName === 'Revenue Contract Number' || 
      fieldName === 'Revenue Contract Line Number' ||
      fieldName === 'Line No' ||
      fieldName === 'Quote Line Number') {
    console.log(`Found integer field: ${fieldName}`);
    return true;
  }
  
  // Then check patterns
  const integerFieldPatterns = [
    'Quantity', 'Count', 'Number', 'ID', 'Discount', 'Percent', 'Revenue Contract Number', 'Revenue Contract Line Number',
  ];
  
  return integerFieldPatterns.some(pattern => 
    fieldName.includes(pattern)
  );
}
  showDialogToSearch() {
    this.arrgId = null;
    this.fromPeriod = '';
    this.toPeriod = '';
    this.currency = '';
    this.product = '';
    this.customer = '';
    this.region = '';
    this.quote = '';
    this.order = '';
    this.quoteLine = '';
    this.orderLine = '';
    this.assetId = '';
    this.productFamily = '';
    this.productLine = '';
    this.legalEntity= '';
    this.customerNumber = ''; // Reset customerNumber field
    this.displayDialog = true;
  }
//   searchLegalEntities(event: any) {
//   this.rmanLegalEntitiesService.getAllRmanLegalEntities(this.paginationOptions, { 'name': event.query }, 'Y').then((response: any) => {
//     this.legalEntities = response.content.map(item => item.name);
//   }).catch(err => {
//     this.notificationService.showError('Error fetching legal entities');
//     this.legalEntities = [];
//   });
// }
// Instead of storing the legal entity name
searchLegalEntities(event: any) {
  this.rmanLegalEntitiesService.getAllRmanLegalEntities(this.paginationOptions, { 'name': event.query }, 'Y').then((response: any) => {
    // Store objects with both id and name
    this.legalEntities = response.content.map(item => ({
      id: item.legalEntityId,  // Assuming this is the ID field
      name: item.name
    }));
  }).catch(err => {
    this.notificationService.showError('Error fetching legal entities');
    this.legalEntities = [];
  });
}



  searchPeriod(event: any) {
    let emp = {
      'periodName': event.query
    }
    let temp: any = [];
    this.rmanFiscalPeriodsService.getAllRmanFiscalPeriods(this.paginationOptions, emp).then((data: any) => {
      this.fiscalPeriodsList = data.content;
      this.fiscalPeriodsList.forEach((obj: any) => {
        temp.push(obj.periodName);

      })
      this.fromPeriodArray = temp;
      this.periodArray = temp; // Also populate periodArray for HTML template consistency

    })
  }

  // exportCSVfile() {
  //   this.exportCols = [];

  //   for (let index = 0; index < this.columns.length; index++) {
  //     if (this.columns[index].showField) {
  //       var header = this.columns[index].header;
  //       this.exportCols.push(header.replace(/[#$/()]/g, ''));
  //     }
  //   }


  //   const levelToSend = this.rollupLevel === 'L' ? '' : this.rollupLevel;
  //   let exportServiceUrl = this.rmanArrgForecastingDetailReportService.getRmanForecastingDetailsReportFile(levelToSend, this.arrgId, this.fromPeriod, this.toPeriod, this.exportCols, this.currency, this.product, this.customer, this.region, this.quote, this.order, this.quoteLine, this.orderLine, this.assetId, this.productFamily, this.productLine, this.legalEntity);
  //   window.location.href = exportServiceUrl;
  // }
  exportCSVfile() {
    this.exportCols = [];

    for (let index = 0; index < this.columns.length; index++) {
      if (this.columns[index].showField) {
        var header = this.columns[index].header;
        this.exportCols.push(header.replace(/[#$/()]/g, ''));
      }
    }
     const levelToSend = this.rollupLevel === 'L' ? '' : this.rollupLevel;
   let exportServiceUrl = this.rmanArrgForecastingDetailReportService.getRmanForecastingDetailsReportFile(levelToSend, this.arrgId, this.fromPeriod, this.toPeriod, this.exportCols, this.currency, this.product, this.customer, this.region, this.quote, this.order, this.quoteLine, this.orderLine, this.assetId, this.productFamily, this.productLine, this.legalEntity);
  //   window.location.href = exportServiceUrl;
    window.location.href = exportServiceUrl;
  }



//   search() {
// if (typeof this.legalEntity === 'object' && this.legalEntity !== null) {
//     console.log('Converting legal entity object to ID:', this.legalEntity);
//     this.legalEntity = this.legalEntity.id;
//   }
//     this.getAllRmanForecastingDetailsReport();
//     this.displayDialog = false;
//   }
search() {
  console.log('Search with parameters:', {
    legalEntity: this.legalEntity,
    customerNumber: this.customerNumber,
    assetId: this.assetId,
    product: this.product,
    productFamily: this.productFamily,
    productLine: this.productLine
  });
  
  // Ensure legalEntity is a number if it exists
  if (this.legalEntity !== null && this.legalEntity !== undefined && this.legalEntity !== '') {
    // Convert to number if it's a string
    if (typeof this.legalEntity === 'string') {
      this.legalEntity = parseInt(this.legalEntity, 10);
      console.log('Converted legal entity to number:', this.legalEntity);
    }
  }
  
  // Handle product object if needed
  if (this.product && typeof this.product === 'object') {
    this.product = this.product.productName;
    console.log('Using product name from object:', this.product);
  }
  
  // Set flag to indicate search was performed
  this.isSearched = 1;
  
  this.getAllRmanForecastingDetailsReport();
  this.displayDialog = false;
}


  reset(_dt: Table) {
  // Clear all filter values
  this.paginationOptions = {};
  this.arrgId = null;
  this.fromPeriod = '';
  this.toPeriod = '';
  this.arrgLevel = 'L';
  this.rollupLevel = 'L';
  this.arrgLevelSwitch = false;
  this.currency = null;
  this.product = '';
  this.customer = '';
  this.region = '';
  this.quote = '';
  this.order = '';
  this.quoteLine = '';
  this.orderLine = '';
  this.assetId = '';
  this.productFamily = '';
  this.productLine = '';
  this.legalEntity = '';
  this.customerNumber = '';
  
  if(this._dataTable) {
    this._dataTable.reset();
  }
  
  // Set a flag to indicate we want all data
  this.isSearched = 0;
  
  // Get all data
  this.getAllRmanForecastingDetailsReport();
  this.displayDialog = false;
}

  fieldType(fieldValue: any) {
    return typeof fieldValue;
  }

  isDateField(fieldName: any) {
    if (fieldName.search('Date') == -1) {
      return false;
    } else {
      return true;
    }
  }
  shouldBeTextField(fieldName: string, fieldValue: any): boolean {
    // Define fields that should always be treated as text regardless of their value
    const textFieldNames = [
      "REGION", "SOURCE_PRODUCT_ID", "PRODUCT_NAME", "PRODUCT_NUMBER", "PRODUCT_GROUP", 
      "PRODUCT_FAMILY", "PRODUCT_LINE", "Revenue Contract Number", "Entity Name", "Quote Number", 
      "Line No", "Ayara Line Id", "Parent Line No", "Quote Line Id", "Product Name", 
      "Bundle Flag", "Currency", "CUSTOMER_NUMBER", "CUSTOMER_NAME", "CUSTOMER_TYPE"
    ];

    // Check for exact field name match in our predefined list
    if (textFieldNames.some(name => fieldName === name)) {
      return true;
    }
    
    // Check if the field name contains key words suggesting it should be text
    if (fieldName.includes('Name') || fieldName.includes('Number') || fieldName.includes('ID') || 
        fieldName.includes('Flag') || fieldName.includes('Region') || fieldName.includes('Type')) {
      return true;
    }
    
    // Check the data type - if it's a string that's not a number, it's text
    if (typeof fieldValue === 'string') {
      if (isNaN(Number(fieldValue))) {
        return true;
      }
    }
    
    // Null values should be treated as text for display purposes
    if (fieldValue === null) {
      return true;
    }

    return false;
  }

  searchProducts(event: any) {
    this.rmanProductsService.getAllRmanProducts(this.paginationOptions, { 'productName': event.query }, false).then((response: any) => {
      if (response && response.content) {
        this.products = response.content;
        // Format product data with name and code in a more concise way
        this.products.forEach(item => {
          // You could use a different format here if needed
          item.displayLabel = item.productName; // Show only name in dropdown
          
          // Store both values for use in search
          item.searchValue = item.productName; // Use only name for search
        });
      } else {
        this.products = [];
      }
    }).catch(err => {
      this.notificationService.showError('Error fetching product data');
      this.products = [];
    });
  }
  searchProductFamilies(event:any){
	  this.rmanLookupCodesService.getAllRmanLookupCodes(this.paginationOptions, { 'lookupTypeCode': 'PRODUCT_FAMILIES','enabledFlag':'Y','description': event.query }).then((response: any) => {
	  	this.productFamilies= response.content.map(item => item.description);
	  }).catch(err => {
      this.productFamilies = [];
    });  
  }

  searchProductLines(event:any){
	  this.rmanLookupCodesService.getAllRmanLookupCodes(this.paginationOptions, { 'lookupTypeCode': 'PRODUCT_LINES','enabledFlag':'Y','description': event.query }).then((response: any) => {
	  	this.productLines= response.content.map(item => item.description);
	  }).catch(err => {
      this.productLines = [];
    });  
  }


  searchCustomerNames(event:any){
	  this.rmanCustomersService.getAllRmanCustomers(this.paginationOptions, { 'customerName': event.query },false).then((response: any) => {
      this.customerNames = response.content.map(item => item.customerName);
      this.customers = response.content.map(item => item.customerName);
    }).catch(err => {
      this.customerNames = [];
      this.customers = [];
    });  
  }
  
  searchCustomerNumbers(event:any){
    console.log('Searching customer numbers with query:', event.query);
    this.rmanCustomersService.getAllRmanCustomers(this.paginationOptions, { 'customerNumber': event.query }, false).then((response: any) => {
      console.log('Customer number search response:', response);
      this.customerNumbers = response.content.map(item => item.customerNumber);
    }).catch(err => {
      console.error('Customer number search error:', err);
      this.customerNumbers = [];
    });  
  }
  

  searchRegions(event:any){
	  this.rmanLookupCodesService.getAllRmanLookupCodes(this.paginationOptions, { 'lookupTypeCode': 'DB_REGION','enabledFlag':'Y','lookupCode': event.query }).then((response: any) => {
	  	this.regions= response.content.map(item => item.lookupCode);
	  }).catch(err => {
      this.regions = [];
    });  
  }

  searchRCList(event: any) {
    console.log('Searching RC with query:', event.query);
    this.rmanArrgForecastingDetailReportService.fetchRCNumbers(event.query).then((response: any) => {
      console.log('RC search response:', response);
      this.dealArrangementNumbers = response || [];
    }).catch(err => {
      console.error('RC search error:', err);
      this.dealArrangementNumbers = [];
    });
  }
  searchValues(parameterName:any,event: any) {
    this.rmanArrgForecastingDetailReportService.getParameterValues(parameterName, event.query)
          .subscribe(results => this.valueResults = results);
  } 
  
  // Add or update this method to load all data before export
  loadAllDataThenExport(dt: Table) {
    this.loading = true;
    this.notificationService.showInfo('Preparing data for export...');
    
    // If we already have all the data loaded in arrgForecastList
    if (this.arrgForecastList && this.arrgForecastList.length > 0) {
      // Set all data to be visible in the table temporarily
      this.arrgForecastRepList = this.arrgForecastList;
      
      // Give UI time to update the table
      setTimeout(() => {
        // Export the full data
        dt.exportCSV();
        
        // Restore the pagination view
        this.paginate({ first: 0, rows: 10 });
        this.loading = false;
      }, 300);
    } else {
      // If we don't have all the data loaded, we need to get it first
      const levelToSend = this.rollupLevel === 'L' ? '' : this.rollupLevel;
      
      this.rmanArrgForecastingDetailReportService.getRmanForecastDetailsReport(
        levelToSend,
        this.arrgId,
        this.fromPeriod,
        this.toPeriod,
        this.currency,
        this.product,
        this.customerNumber,
        this.region,
        this.quote,
        this.order,
        this.quoteLine,
        this.orderLine,
        this.assetId,
        this.productFamily,
        this.productLine,
        this.legalEntity
      ).then((data: any) => {
        if (data && data.Content && data.Content.length > 0) {
          // Store all data
          this.arrgForecastList = data.Content;
          // Set all data to be visible for export
          this.arrgForecastRepList = data.Content;
          
          // Give UI time to update the table
          setTimeout(() => {
            // Export the data
            dt.exportCSV();
            
            // Restore the pagination view
            this.paginate({ first: 0, rows: 10 });
            this.loading = false;
          }, 300);
        } else {
          this.notificationService.showInfo('No data available to export');
          this.loading = false;
        }
      }).catch((error) => {
        this.notificationService.showError('Error preparing data for export');
        this.loading = false;
      });
    }
  }
}