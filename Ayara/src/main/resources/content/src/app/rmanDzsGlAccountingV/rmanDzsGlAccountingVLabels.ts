interface ILabels {
    [index: string]: string;
}

export class RmanDzsGlAccountingVLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};
        this.fieldLabels["dealArrangementNumber"] = "Deal Arrangement Number";
        this.fieldLabels["sourceLineId"] = "Source Line Id";
        this.fieldLabels["po"] = "PO#";
        this.fieldLabels["so"] = "SO#";
        this.fieldLabels["elementType"] = "Element Type";
        this.fieldLabels["dealArrangementName"] = "Deal Arrangement Name";
        this.fieldLabels["postedFlag"] = "Posted Flag";
        this.fieldLabels["periodName"] = "Period Name";
        this.fieldLabels["account"] = "Account";
        this.fieldLabels["tcur"] = "T-CUR";
        this.fieldLabels["amountTc"] = "Amount TC";
        this.fieldLabels["note"] = "NOTE";
        this.fieldLabels["customer"] = "Customer";
        this.fieldLabels["rmanInterfaceId"] = "RMAN INTERFACE ID";
        this.fieldLabels["accountDescription"] = "Account Description";
        this.fieldLabels["amountFc"] = "Amount FC";
        this.fieldLabels["dealLineNumber"] = "Deal Line#";
        this.fieldLabels["drCr"] = "DR/CR";
        this.fieldLabels["soLine"] = "SO Line#";
        this.fieldLabels["cust"] = "Cust#";
        this.fieldLabels["lineStatus"] = "Line Status";
        this.fieldLabels["glDate"] = "GL Date";
        this.fieldLabels["invoice"] = "Invoice#";
        this.fieldLabels["accountClass"] = "Account Class";
        this.fieldLabels["fcur"] = "F-CUR";
        this.fieldLabels["deliveredDate"] = "Delivered Date";
        this.fieldLabels["contName"] = "Contingency Name";
        this.fieldLabels["productName"] = "Product Name";
        this.fieldLabels["productLine"] = "Product Line";
        this.fieldLabels["division"] = "Division";
        this.fieldLabels["entryType"] = "Entry Type";
    }

}
