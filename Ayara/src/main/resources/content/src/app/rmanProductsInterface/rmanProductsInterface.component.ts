import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { Table } from 'primeng/table';
import { NotificationService } from '../shared/notifications.service';
import { UploadService } from '../shared/upload.service';
import { RmanProductsInterface } from './rmanProductsInterface';
import { RmanProductsInterfaceService } from './rmanProductsInterfaceservice';
import { CommonSharedService } from '../shared/common.service';
import { AllExceptionsService } from '../exceptions/allExceptions/allExceptions.service';

declare var $: any;
declare var require: any;
const appSettings = require('../appsettings');

@Component({
    templateUrl: './rmanProductsInterface.component.html',
    selector: 'rmanProductsInterface-data',
    providers: [RmanProductsInterfaceService, ConfirmationService]
})

export class RmanProductsInterfaceComponent {

    displayDialog: boolean;

    displaySearchDialog: boolean;

    uploadLoading: boolean = false;

    rmanProductsInterface: RmanProductsInterface = new RmanProductsInterfaceImpl();

    rmanProductsInterfaceSearch: RmanProductsInterface = new RmanProductsInterfaceImpl();

    isSerached: number = 0;

    selectedRmanProductsInterface: RmanProductsInterface;

    newRmanProductsInterface: boolean;

	displayProductsInterfaceDialog: boolean = false;

    rmanProductsInterfaceList: RmanProductsInterface[];

    cols: any[];
    //columns: ILabels;

    columnOptions: any[];

    paginationOptions = {};

    pages: {};


    datasource: any[];
    pageSize: number;
    totalElements: number;
    collapsed: boolean = true;

    customerForm: FormGroup;
    noData = appSettings.noData;
    loading: boolean;
    rmanProductsInterfacetatus: any[];
    regions:any[];
    customerTypes:any[];
    isSelectAllChecked = true;
    globalCols: any[];
    clonedCols: any[];
    userId: number;
    showPaginator: boolean = true;
    startIndex: number;
    showAddColumns = true;
    columns: any[];

    exportCols: string[] = [];
  	disableExport: boolean = true;
    selectedLines: any[] = [];
	  exceptionsList: any[] = [];
    deletedProducts: any[] = [];



    constructor(
        private rmanProductsInterfaceService: RmanProductsInterfaceService,
        private confirmationService: ConfirmationService,
        private formBuilder: FormBuilder,
        private notificationService:NotificationService,
        public _uploadService:UploadService,  private commonSharedService: CommonSharedService
    ) {

        // generate list code
        this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };


    }

    ngOnInit() {
      this.selectedLines=[];
        this.globalCols = [
            { field: 'productName', header: 'PRODUCT_NAME', showField: true, drag: false, display: "table-cell",type:'text'},
            { field: 'productDescription', header: 'PRODUCT_DESCRIPTION', showField: true, drag: true, display: "table-cell",type:'text' },
            { field: 'productStatus', header: 'PRODUCT_STATUS', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'productType', header: 'PRODUCT_TYPE', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'productCost', header: 'PRODUCT_COST', showField: true, drag: true, display: "table-cell",type:'number'},
            { field: 'productRevAccount', header: 'PRODUCT_REV_ACCOUNT', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'shippableFlag', header: 'SHIPPABLE_FLAG', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'sourceProductId', header: 'SOURCE_PRODUCT_ID', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'source', header: 'SOURCE', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'uom', header: 'UOM', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'allocationFlag', header: 'ALLOCATION_FLAG', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'productGroup', header: 'PRODUCT_GROUP', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'productFamily', header: 'PRODUCT_FAMILY', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'productLine', header: 'PRODUCT_LINE', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'bundleFlag', header: 'BUNDLE_FLAG', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'revenueTemplate', header: 'REVENUE_TEMPLATE', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'productNumber', header: 'PRODUCT_NUMBER', showField: true, drag: true, display: "table-cell",type:'text'},
            { field: 'productModel', header: 'PRODUCT_MODEL', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'interfaceProcessId', header: 'INTERFACE_PROCESS_ID', showField: true, drag: true, display: "table-cell",type:'text'},
			{ field: 'errorMessage', header: 'ERROR_MESSAGE', showField: true, drag: true, display: "table-cell",type:'text'}

        ];

        this.columns = [];
        this.getTableColumns("rmanProductsInterface", "ProductsInterface");
    }

    prepareRmanStatus() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectStat, value: null }];
        this.rmanProductsInterfacetatus.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupCode });
        });
        this.rmanProductsInterfacetatus = rmanLookupsVTempObj;
    }

    prepareRegions() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectRegion, value: null }];
        this.regions.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupDescription });
        });
        this.regions = rmanLookupsVTempObj;
    }

    prepareCustomerTypes() {
        let rmanLookupsVTempObj: any = [{ label: appSettings.dropDownOptions.selectCustomerType, value: null }];
        this.customerTypes.forEach((rmanLookupsV) => {
            rmanLookupsVTempObj.push({ label: rmanLookupsV.lookupDescription, value: rmanLookupsV.lookupDescription });
        });
        this.customerTypes = rmanLookupsVTempObj;
    }

    getTableColumns(pageName: string, tableName: string) {
        this.isSelectAllChecked = true;
        this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
          if (response && response != null && response.userId) {
            this.columns = [];
            let colsList = response.tableColumns.split(",");
            if (colsList.length > 0) {
              colsList.forEach((item, index) => {
                if (item) {
                  this.startIndex = this.globalCols.findIndex(col => col.field == item);
                  this.onDrop(index);
                }
              });
            }
            this.globalCols.forEach(col => {
              if (response.tableColumns.indexOf(col.field) !== -1) {
                this.columns.push(col);
              } else {
                col.showField = false;
              }
            });
            if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
            this.showPaginator = this.columns.length !== 0;
            this.userId = response.userId;
          } else {
            this.columns = this.globalCols;
          }
        }).catch(() => {
          this.notificationService.showError('Error occured while getting table columns data');
          this.loading = false;
        });
      }

      saveColumns() {
        let selectedCols = "";
        this.showAddColumns = !this.showAddColumns;
        const colLength = this.globalCols.length - 1;
        this.globalCols.forEach((col, index) => {
          if (col.showField) {
            selectedCols += col.field;
            if (index < colLength) {
              selectedCols += ",";
            }
          }
        });
        this.loading = true;
        this.commonSharedService.saveOrUpdateTableColumns("rmanProductsInterface", "ProductsInterface", selectedCols, this.userId).then((response) => {
          this.columns = this.globalCols.filter(item => item.showField);
          this.userId = response["userId"];
          this.showPaginator = this.columns.length !== 0;
          this.loading = false;
        }).catch(() => {
          this.notificationService.showError('Error occured while getting data');
          this.loading = false;
        });
      }

      onDragStart(index: number) {
        this.startIndex = index;
      }

      onDrop(dropIndex: number) {
        const general = this.globalCols[this.startIndex]; // get element
        this.globalCols.splice(this.startIndex, 1);       // delete from old position
        this.globalCols.splice(dropIndex, 0, general);    // add to new position
        //console.log(this.globalCols);
      }

      selectColumns(col: any) {
        let cols = this.globalCols.filter(item => !item.showField);
        if (cols.length > 0) {
          this.isSelectAllChecked = false;
        } else {
          this.isSelectAllChecked = true;
        }
      }

      onSelectAll() {
        this.isSelectAllChecked = !this.isSelectAllChecked;
        this.globalCols.forEach(col => {
          if (this.isSelectAllChecked) {
            col.showField = true;
          } else {
            if (col.drag) {
              col.showField = false;
            }
          }
        });
      }

      onConfiguringColumns(event: any) {
        this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
        this.showAddColumns = false;
      }

      closeConfigureColumns(event: any) {
      this.showAddColumns = true;
      this.globalCols = this.clonedCols;
      let configCol = this.globalCols.filter(item => !item.showField);
      this.isSelectAllChecked = !(configCol.length > 0);
      }



    getAllRmanProductsInterface() {
        this.loading = true;
        this.rmanProductsInterfaceService.getAllRmanProductsInterface(this.paginationOptions, this.rmanProductsInterface, this.exportCols).then((rmanProductsInterfaceList: any) => {
            this.loading = false;
            this.datasource = rmanProductsInterfaceList.content;
            this.rmanProductsInterfaceList = rmanProductsInterfaceList.content;
            this.totalElements = rmanProductsInterfaceList.totalElements;
            this.pageSize = rmanProductsInterfaceList.size;
            this.displaySearchDialog = false;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });
    }

    getRmanProductsInterface(event: any) {
        this.loading = true;
        let first: number = event.first;
        let rows: number = event.rows;
        let pageNumber: number = first / rows;
        this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
        this.rmanProductsInterfaceService.getAllRmanProductsInterface(this.paginationOptions, this.rmanProductsInterface, this.exportCols).then((rmanProductsInterfaceList: any) => {
            this.loading = false;
            this.datasource = rmanProductsInterfaceList.content;
            this.rmanProductsInterfaceList = rmanProductsInterfaceList.content;
            this.totalElements = rmanProductsInterfaceList.totalElements;
            this.pageSize = rmanProductsInterfaceList.size;
            this.disableExport = false;
        }).catch((err: any) => {
            this.notificationService.showError('Error occured while getting data');
            this.loading = false;
        });

    }


   onBeforeToggle(evt: any) {
        this.collapsed = evt.collapsed;
    }


    reset(dt: Table) {
        this.paginationOptions = {};
        this.rmanProductsInterface = new RmanProductsInterfaceImpl();
        this.rmanProductsInterfaceSearch = new RmanProductsInterfaceImpl();
        this.selectedLines=[];
        dt.reset();
    }
    deleteSelected(dt: Table) {

        this.exceptionsList = [];
        this.deletedProducts=[];
        console.log(this.selectedLines);

        for (let line of this.selectedLines) {
            this.exceptionsList.push(line.rpTrxId);
            this.deletedProducts.push(line);
        }
        if (this.exceptionsList.length > 0) {

        this.confirmationService.confirm({
            message: 'Are you sure you want to delete?',
            header: 'Confirmation',
            icon: '',
            accept: () => {
              this.loading = true;
              this.rmanProductsInterfaceService.deleteSelectedLines(this.exceptionsList).then((response: any) => {
                for(let product of this.deletedProducts){
                  this.rmanProductsInterfaceService.updateExceptionLogs(product,'deleted').subscribe();
                }
                this.deletedProducts=[];
                this.notificationService.showSuccess('Selected records are deleted Successfully  ');
            }).then(() => {
                this.reset(dt);
            }).catch((err: any) => {
                this.loading = false;
                this.notificationService.showError('Getting error While delete records');
            });
            },
            reject: () => {
              this.notificationService.showWarning('You have rejected');
            }
          });

        } else {
            this.notificationService.showInfo('Select at least one record to delete');
        }
    }


	exportExcel() {
	    this.exportCols = [];
	    for (let index = 0; index < this.columns.length; index++) {
	      if (this.columns[index].showField) {
	        this.exportCols.push(this.columns[index].field);
	      }
	    }
		
	    let serviceUrl = this.rmanProductsInterfaceService.getServiceUrl(this.paginationOptions, this.rmanProductsInterface, 1, this.exportCols);
	    window.location.href = serviceUrl;
    }
    
    findSelectedRmanProductsInterfaceIndex(): number {
        return this.rmanProductsInterfaceList.indexOf(this.selectedRmanProductsInterface);
    }

    onRowSelect(event: any) {

    }

    
    hideColumnMenu: boolean = true;

    toggleColumnMenu() {
        if (this.hideColumnMenu) {
            this.hideColumnMenu = false;
        } else {
            this.hideColumnMenu = true;
        }
    }

    showFilter: boolean = false;

    toggleFilterBox() {
        if (this.showFilter) {
            this.showFilter = false;
        } else {
            this.showFilter = true;
        }
    }

    showDialogToSearch() {
        if (this.isSerached == 0) {
            this.rmanProductsInterfaceSearch = new RmanProductsInterfaceImpl();
        }
        this.rmanProductsInterfaceSearch = new RmanProductsInterfaceImpl();
        this.displaySearchDialog = true;

    }
    
	search() {

        this.isSerached = 1;
        this.rmanProductsInterface = this.rmanProductsInterfaceSearch;
        this.paginationOptions={};
        this.getAllRmanProductsInterface();
    }
    
    
    showUploadLoader(){
    	this.uploadLoading = true;
    }
	
}


export class RmanProductsInterfaceImpl implements RmanProductsInterface {
    constructor(
	public	productName?: any,
	public	productDescription?: any,
	public	productStatus?: any,
	public	productType?: any,
	public	productCost?: any,
	public	productRevAccount?: any,
	public	shippableFlag?: any,
	public	sourceProductId?: any,
	public	source?: any,
	public	uom?: any,
	public	allocationFlag?: any,
	public	productGroup?: any,
	public	productFamily?: any,
	public	productLine?: any,
	public	bundleFlag?: any,
	public	revenueTemplate?: any,
	public	ltStFlag?: any,
	public	productNumber?: any,
	public	productModel?: any
	) { }
}

interface ILabels {
    [index: string]: string;
}
