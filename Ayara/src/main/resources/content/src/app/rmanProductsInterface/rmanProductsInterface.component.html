<div class="content-section implementation">
</div>

<!--<div class="card-wrapper">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card-block"> -->
<p-panel header="Products" [toggleable]="false" (onBeforeToggle)="onBeforeToggle($event)">
  <p-header>

    <div class="pull-right icons-list" *ngIf="collapsed">
      <a (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
      <a  (click)="deleteSelected(dt)" title="Delete Selected"><em class="fa fa-trash"></em></a>
      <a (click)="showDialogToSearch()" title="Search">
        <em class="fa fa-search"></em>
      </a>
      <a (click)="reset(dt)" title="Reset">
        <em class="fa fa-refresh"></em>
      </a>
      <ng-container *ngIf="!disableExport">
        <a (click)="exportExcel()" title="Export">
          <em class="fa fa-external-link"></em>
        </a>
      </ng-container>


      <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
        <div class="user-popup">
          <div class="content overflow">
            <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()"/>
            <label for="selectall">Select All</label>
            <a class="close" title="Close" (click)="closeConfigureColumns($event)" >&times;</a>
            <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
              <ng-template let-col let-index="index" pTemplate="item">
                <div *ngIf="col.drag">
                  <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens"
                       (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                    <div class="drag">
                      <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)"/>
                      <label>{{col.header}}</label>
                    </div>
                  </div>
                </div>
                <div *ngIf="!col.drag">
                  <div class="ui-helper-clearfix">
                    <div>
                      <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"/>
                      <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                    </div>
                  </div>
                </div>
              </ng-template>
            </p-listbox>

          </div>

          <div class="pull-right">
            <a class="configColBtn" (click)="saveColumns()">Save</a>
            <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
          </div>

        </div>
      </div>

    </div>
  </p-header>

  <div class="x-scroll">
    <p-table class="ui-datatable arrangementMgrTbl" #dt id="rmanProductsInterface-dt" [loading]="loading" [value]="rmanProductsInterfaceList"
             [(selection)]="selectedLines" (onRowSelect)="onRowSelect($event)" (onLazyLoad)="getRmanProductsInterface($event)" [lazy]="true"
             [paginator]="true" [rows]="pageSize" [totalRecords]="totalElements" scrollable="true" [columns]="columns" [resizableColumns]="true" columnResizeMode="expand" >

      <ng-template pTemplate="colgroup" let-columns>
        <colgroup>
          <col *ngFor="let col of columns">
        </colgroup>
      </ng-template>


      <ng-template pTemplate="header" class="arrangementMgrTblHead">
        <tr>
          <ng-container>
            <th style="width: 5px"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
          </ng-container>

          <ng-container *ngFor="let col of columns">
            <th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
            <th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
          </ng-container>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-rowData let-rmanProductsInterface let-columns="columns">
        <tr [pSelectableRow]="rowData" [pSelectableRowIndex]="rowIndex">
          <ng-container>
            <td style="width:5px">
              <p-tableCheckbox [value]="rowData"></p-tableCheckbox>
            </td>
          </ng-container>


          <ng-container *ngFor="let col of columns" >
            <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
              {{rowData[col.field]}}
            </td>

            <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
              {{rowData[col.field]}}
            </td>

            <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
              {{rowData[col.field] | date: 'MM/dd/yyyy'}}
            </td>

            <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
              {{rowData[col.field] | round}}
            </td>
          </ng-container>

        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage" let-columns>
        <div class="no-results-data">
          <p>{{noData}}</p>
        </div>
      </ng-template>
    </p-table>
  </div>
</p-panel>
<!--     </div>
   </div>
 </div>
</div>
</div> -->


<p-dialog header="Search" width="auto" [(visible)]="displaySearchDialog" showEffect="fade" [draggable]="true" [modal]="true" [blockScroll]="true">
  <form>
    <div class="ui-grid ui-grid-responsive ui-fluid">
      <div class="ui-g-12">
        <div class="ui-g-6">
          <span class="md-inputfield">
            <span class="selectSpan">Product Name</span>
            <input pInputText name="productName" id="productName" class="textbox" placeholder="Product Name" [ngModelOptions]="{standalone: true}"
                   [(ngModel)]="rmanProductsInterfaceSearch.productName" />
          </span>
        </div>
        <div class="ui-g-6 pull-right">
          <span class="md-inputfield">
            <span class="selectSpan">Product Description</span>
            <input pInputText name="productDescription" id="productDescription" class="textbox" placeholder="Product Description" [ngModelOptions]="{standalone: true}"
                   [(ngModel)]="rmanProductsInterfaceSearch.productDescription" />
          </span>
        </div>
      </div>
      <div class="ui-g-12">
        <div class="ui-g-6">
          <span class="md-inputfield">
            <span class="selectSpan">Process Id</span>
            <input pInputText name="interfaceProcessId" id="interfaceProcessId" class="textbox" placeholder="Process ID" [ngModelOptions]="{standalone: true}"
                   [(ngModel)]="rmanProductsInterfaceSearch.interfaceProcessId" />
          </span>
        </div>
      </div>
    </div>
  </form>
  <p-footer>
    <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix">
      <button type="submit" pButton class="primary-btn" label="Search" (click)="search()"></button>
      <button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>
    </div>
  </p-footer>

</p-dialog>

<p-confirmDialog header="Confirmation" icon="fa fa-question-circle" width="425"></p-confirmDialog>