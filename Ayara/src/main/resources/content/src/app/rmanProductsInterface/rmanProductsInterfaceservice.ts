import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require:any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type':  'application/json',
  })
};




@Injectable()
export class RmanProductsInterfaceService {

    constructor(private http: HttpClient) {}
    
    getServiceUrl(paginationOptions:any,rmanProductsInterfaceSearchObject:any,  exportFlag: any, exportCols: any) {
        
         let serviceUrl = appSettings.apiUrl + '/productsInterfaceExport?';

    	 if (exportFlag == 0) {
      	 	serviceUrl = appSettings.apiUrl + '/rmanProductsInterfaceSearch?';
    	 }

        let searchString='';

        
        if (rmanProductsInterfaceSearchObject.productName!=undefined && rmanProductsInterfaceSearchObject.productName!="") {
            searchString=searchString+'productName:'+rmanProductsInterfaceSearchObject.productName+',';
        }

        if (rmanProductsInterfaceSearchObject.productDescription!=undefined && rmanProductsInterfaceSearchObject.productDescription!="") {
            searchString=searchString+'productDescription:'+rmanProductsInterfaceSearchObject.productDescription+',';
        }

        if (rmanProductsInterfaceSearchObject.interfaceProcessId!=undefined && rmanProductsInterfaceSearchObject.interfaceProcessId!="") {
            searchString=searchString+'interfaceProcessId:'+rmanProductsInterfaceSearchObject.interfaceProcessId+',';
        }

        searchString=searchString+'interfaceStatus:E';


        if (searchString == '') {
            serviceUrl=serviceUrl+'search=%25';
        }
        else {
            serviceUrl=serviceUrl+'search='+searchString;
        }
        
        if (paginationOptions.pageNumber!=undefined && paginationOptions.pageNumber!="" && !isNaN(paginationOptions.pageNumber) && paginationOptions.pageNumber!=0) {
           serviceUrl=serviceUrl+'&page='+paginationOptions.pageNumber+'&size='+paginationOptions.pageSize;
        }
        
        if (exportCols != undefined && exportCols != "") {
      		serviceUrl = serviceUrl + '&exportCols=' + exportCols;
    	}
   		
   		return serviceUrl;
        
    }

    getAllRmanProductsInterface(paginationOptions:any,rmanProductsInterfaceSearchObject:any, exportCols: any): Promise<any[]> {
        let serviceUrl = this.getServiceUrl(paginationOptions, rmanProductsInterfaceSearchObject, 0, exportCols);
        return this.http.get(serviceUrl).toPromise().then((data:any) => {
            return data;
        });
    }
    deleteSelectedLines(exceptionLines: any[]){

        let delUrl = appSettings.apiUrl + '/bulkDelProductExceptions?rpTrxIds='+exceptionLines;
        return this.http.delete(delUrl,{responseType : "text"}).toPromise().then((data:any) => {
          return data;
        });

    }
    updateExceptionLogs(exception: any, event: string) {
          return this.http.post(
            `${appSettings.apiUrl}/updateProductExceptionLogs/${exception.rpTrxId}`,
            exception,
            {
              headers: {
                'Content-Type': 'application/json'
              },
              params: {
                event: event
              }
            }
          );
        }



	
}
