export interface RmanContHeader {
    dealFlag: any;
    attribute30: any;
    ruleHeaderId: any;
    lastUpdateDate: any;
    ranking: any;
    approverId: any;
    attribute29: any;
    attribute28: any;
    attribute27: any;
    attribute26: any;
    attribute3: any;
    createdBy: any;
    attribute2: any;
    lastUpdatedBy: any;
    attribute1: any;
    creationDate: any;
    attribute9: any;
    attribute8: any;
    attribute7: any;
    attribute6: any;
    attribute5: any;
    ruleApplyLevel: any;
    attribute4: any;
    attribute10: any;
    attribute14: any;
    attribute13: any;
    attribute12: any;
    attribute11: any;
    ruleName: any;
    comments: any;
    ruleMasterId: any;
    cogsFlag: any;
    ruleEndDate: any;
    sequenceNumber: any;
    ruleCategory: any;
    description: any;
    ruleStartDate: any;
    attribute21: any;
    attribute20: any;
    revFlag: any;
    attribute25: any;
    attribute24: any;
    attribute23: any;
    attribute22: any;
    attribute18: any;
    attribute17: any;
    attribute16: any;
    attribute15: any;
    attribute19: any;
}
