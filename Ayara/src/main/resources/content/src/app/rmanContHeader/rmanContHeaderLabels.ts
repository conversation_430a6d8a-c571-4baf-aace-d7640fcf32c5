interface ILabels {
    [index: string]: string;
}

export class RmanContHeaderLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["dealFlag"] = "Deal Flag";
        this.fieldLabels["attribute30"] = "Attribute30";
        this.fieldLabels["ruleHeaderId"] = "Rule Header Id";
        this.fieldLabels["lastUpdateDate"] = "Last Update Date";
        this.fieldLabels["ranking"] = "Ranking";
        this.fieldLabels["approverId"] = "Approver Id";
        this.fieldLabels["attribute29"] = "Attribute29";
        this.fieldLabels["attribute28"] = "Attribute28";
        this.fieldLabels["attribute27"] = "Attribute27";
        this.fieldLabels["attribute26"] = "Attribute26";
        this.fieldLabels["attribute3"] = "Attribute3";
        this.fieldLabels["createdBy"] = "Created By";
        this.fieldLabels["attribute2"] = "Attribute2";
        this.fieldLabels["lastUpdatedBy"] = "Last Updated By";
        this.fieldLabels["attribute1"] = "Revrec Hold Apply";
        this.fieldLabels["creationDate"] = "Creation Date";
        this.fieldLabels["attribute9"] = "Attribute9";
        this.fieldLabels["attribute8"] = "Attribute8";
        this.fieldLabels["attribute7"] = "Attribute7";
        this.fieldLabels["attribute6"] = "Attribute6";
        this.fieldLabels["attribute5"] = "Attribute5";
        this.fieldLabels["ruleApplyLevel"] = "Rule Apply Level";
        this.fieldLabels["attribute4"] = "Attribute4";
        this.fieldLabels["attribute10"] = "Attribute10";
        this.fieldLabels["attribute14"] = "Attribute14";
        this.fieldLabels["attribute13"] = "Attribute13";
        this.fieldLabels["attribute12"] = "Attribute12";
        this.fieldLabels["attribute11"] = "Attribute11";
        this.fieldLabels["ruleName"] = "Rule Name";
        this.fieldLabels["comments"] = "Comments";
        this.fieldLabels["ruleMasterId"] = "Rule Master Id";
        this.fieldLabels["cogsFlag"] = "COGS";
        this.fieldLabels["ruleEndDate"] = "Rule End Date";
        this.fieldLabels["sequenceNumber"] = "Sequence Number";
        this.fieldLabels["ruleCategory"] = "Rule Category";
        this.fieldLabels["description"] = "Description";
        this.fieldLabels["ruleStartDate"] = "Rule Start Date";
        this.fieldLabels["attribute21"] = "Attribute21";
        this.fieldLabels["attribute20"] = "Attribute20";
        this.fieldLabels["revFlag"] = "Revenue";
        this.fieldLabels["attribute25"] = "Attribute25";
        this.fieldLabels["attribute24"] = "Attribute24";
        this.fieldLabels["attribute23"] = "Attribute23";
        this.fieldLabels["attribute22"] = "Attribute22";
        this.fieldLabels["attribute18"] = "Attribute18";
        this.fieldLabels["attribute17"] = "Attribute17";
        this.fieldLabels["attribute16"] = "Attribute16";
        this.fieldLabels["attribute15"] = "Attribute15";
        this.fieldLabels["attribute19"] = "Attribute19";
    }

}
