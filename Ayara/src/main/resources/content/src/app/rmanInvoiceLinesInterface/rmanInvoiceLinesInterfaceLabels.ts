interface ILabels {
  [index: string]: string;
}

export class RmanInvoiceLinesInterfaceLabels {
  fieldLabels: ILabels;

  constructor() {
    this.fieldLabels = {};
    this.fieldLabels["sourceLineNumber"] = "Source Line#";
    this.fieldLabels["sourceInvoiceId"] = "Source Invoice Id";
    this.fieldLabels["sourceInvoiceLineId"] = "Source Invoice Line Id";
    this.fieldLabels["invoiceType"] = "Invoice Type";
    this.fieldLabels["invoiceSource"] = "Invoice Source";
    this.fieldLabels["invoiceTrxType"] = "Invoice Trx Type";
    this.fieldLabels["invoiceNumber"] = "Invoice Number";
    this.fieldLabels["invoicedDate"] = "Invoice Date";
    this.fieldLabels["salesOrderNumber"] = "Sales Order";
    this.fieldLabels["salesOrderLine"] = "Sales Order Line";
    this.fieldLabels["salesOrderLineId"] = "Sales Order Line Id";
    this.fieldLabels["invoiceLineNumber"] = "Invoice Line Number";
    this.fieldLabels["lineType"] = "Line Type";
    this.fieldLabels["quantityInvoiced"] = "Quantity Invoiced";
    this.fieldLabels["unitSellingPrice"] = "Unit Selling Price";
    this.fieldLabels["invoiceCurrency"] = "Invoice Currency Code";
    this.fieldLabels["revenueAmount"] = "Invoice Amount";
    this.fieldLabels["acctdCurrency"] = "Accounted Currency";
    this.fieldLabels["acctdRevAmount"] = "Accounted Amount";
    this.fieldLabels["fxRate"] = "Fx Rate";
    this.fieldLabels["fxDate"] = "Fx Date";
    this.fieldLabels["errorMessage"] = "Error Message";
  }
}
