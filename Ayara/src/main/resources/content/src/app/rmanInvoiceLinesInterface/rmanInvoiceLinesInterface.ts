export interface RmanInvoiceLinesInterface {
    fxDate?: any;
    acctdCurrency?: any;
    invoiceSource?: any;
    quantity?: any;
    salesOrderNumber?: any;
    origLastUpdatedBy?: any;
    invoiceCurrency?: any;
    lastUpdateDate?: any;
    actualShippedDate?: any;
    invoiceType?: any;
    unitSellingPrice?: any;
    createdBy?: any;
    lastUpdatedBy?: any;
    cogsAmount?: any;
    origCreatedBy?: any;
    debitAccount?: any;
    sourceLineNumber?: any;
    sourceOrderLineId?: any;
    creationDate?: any;
    glDate?: any;
    origLastUpdateDate?: any;
    interfaceProcessId?: any;
    rmanAcctRuleId?: any;
    rmanLineId?: any;
    rmanInvoiceLineId?: any;
    errorMessage?: any;
    origCreationDate?: any;
    invoicedDate?: any;
    sourceInvoiceLineId?: any;
    servicePeriod?: any;
    usdAmount?: any;
    revenueAmount?: any;
    errorFlag?: any;
    interfaceStatus?: any;
    fxRate?: any;
    sourceInvoiceId?: any;
    serviceStartDate?: any;
    serviceDuration?: any;
    lineType?: any;
    cogsAccount?: any;
    invoiceNumber?: any;
    acctdRevAmount?: any;
    pid?: any;
    accountingRuleName?: any;
    serviceEndDate?: any;
    invoiceTrxType?: any;
    invoiceLineNumber?: any;
    processedDate?: any;
    creditAccount?: any;
}
