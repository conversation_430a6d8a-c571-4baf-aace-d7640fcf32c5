<div class="content-section implementation">	
</div>

<div class="card-wrapper">
     <div class="container-fluid">
       <div class="row">
         <div class="col-md-12">
           <div class="card-block">
     <p-panel header="Billing Schedules Report" [toggleable]="false" (onBeforeToggle)=onBeforeToggle($event)>
          <p-header>
               <div class="pull-right icons-list">
                    <a  (click)="goToOperationReports()" class="add-column"><em class="fa fa-reply"></em>Back</a>
                    <a  (click)="onConfiguringColumns($event)" class="add-column"><em class="fa fa-cog"></em>Columns</a>
                    <a  (click)="showDialogToSearch()" title="Search"><em class="fa fa-search"></em></a>
                    <a  (click)="reset(dt)" title="Reset"><em class="fa fa-refresh"></em></a>
                    <a  (click)="exportExcel()" title="Export" *ngIf="!disableExport"><em class="fa fa-external-link"></em></a>
                    <div id="add-column-popup" class="contracts_add_column" *ngIf="!showAddColumns">
                         <div class="user-popup">
                           <div class="content overflow">
                             <input type="checkbox" [checked]="isSelectAllChecked" id="selectall" name="selectall" (click)="onSelectAll()"/>
                             <label for="selectall">Select All</label>
                             <a class="close" title="Close" (click)="closeConfigureColumns($event)" >&times;</a>
                             <p-listbox [options]="globalCols" [style]="{ width: '100%', height: '100%' }">
                              <ng-template let-col let-index="index" pTemplate="item">
                                <div *ngIf="col.drag">
                                <div class="ui-helper-clearfix" pDraggable="gens" pDroppable="gens" 
                                 (onDragStart)="onDragStart(index)" (onDrop)="onDrop(index)">
                                  <div class="drag">
                                    <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)"/>
                                    <label>{{col.header}}</label>
                                  </div>
                                </div>
                                </div>
                                <div *ngIf="!col.drag">
                                <div class="ui-helper-clearfix">
                                  <div>
                                    <input type="checkbox"  [checked]="col.showField" [(ngModel)]="col.showField" (change)="selectColumns(col)" [disabled]="!col.drag"/>
                                    <label [ngStyle]="{'color': 'grey'}">{{col.header}}</label>
                                  </div>
                                </div>
                                </div>
                              </ng-template>
                              </p-listbox>
                         </div>
                         <div class="pull-right">
                              <a class="configColBtn" (click)="saveColumns()">Save</a>
                              <a class="configColBtn conf-cancel" (click)="closeConfigureColumns($event)">Cancel</a>
                            </div>
                    </div>
                    </div>
               
               </div>
     </p-header>

     <div class="x-scroll">
          <p-table class="ui-datatable arrangementMgrTbl" #dt id="billingSchedule-dt" [loading]="loading"  [value]="ayaraBillingSchedulesReportList" selectionMode="single"
                 (onRowSelect)="onRowSelect($event)" scrollable="true"
                [paginator]="showPaginator" [lazy]="true" [rows]="pageSize" [totalRecords]="totalElements"
               (onLazyLoad)="getAyaraBillingSchedulesReport($event)"  [columns]="columns" [resizableColumns]="true" columnResizeMode="expand">
              
               <ng-template pTemplate="colgroup" let-columns>
                    <colgroup>
                         <col *ngFor="let col of columns">
                    </colgroup>
               </ng-template>

               <ng-template pTemplate="header" class="arrangementMgrTblHead">
                    <tr>
                         <ng-container *ngFor="let col of columns">
                              <th *ngIf="col.type=='text' ||col.type=='date' " [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                              <th *ngIf="col.type=='number' ||col.type=='round' ||col.type=='link'" class="number" [ngStyle]="{'display': col.display}" title="{{col.header}}" pResizableColumn>{{col.header}}</th>
                         </ng-container>
                    </tr>
               </ng-template>
               <ng-template pTemplate="body" let-rowData let-revmantra>
                    <tr [pSelectableRow]="rowData">

                         <ng-container *ngFor="let col of columns" >
                              <td *ngIf="col.type == 'text'" title="{{rowData[col.field]}}" [ngStyle]="{'display': col.display}">
                                   {{rowData[col.field]}}
                              </td>
                         
                              <td *ngIf="col.type == 'number'" title="{{rowData[col.field]}}" class="number" [ngStyle]="{'display': col.display}">
                                   {{rowData[col.field]}}
                              </td>
                         
                              <td *ngIf="col.type == 'date'" title="{{rowData[col.field] | date: 'MM/dd/yyyy'}}" [ngStyle]="{'display': col.display}">
                                   {{rowData[col.field] | date: 'MM/dd/yyyy'}}
                              </td>
                         
                              <td *ngIf="col.type == 'round'" title="{{rowData[col.field] | round}}" class="number" [ngStyle]="{'display': col.display}">
                                   {{rowData[col.field] | round}}
                              </td>
                         </ng-container>

                    </tr>
               </ng-template>

               <ng-template pTemplate="emptymessage" let-columns>
                    <tr>
                         <td [attr.colspan]="columns.length" class="no-data">{{noData}}</td>
                    </tr>
               </ng-template>

          </p-table>
          </div>

     </p-panel>
     </div>
     </div>
     </div>
     </div>
     </div>


     <p-dialog header="Search" width="800" [(visible)]="displaySearchDialog" [draggable]="true" showEffect="fade"
          [modal]="true">
          <form (ngSubmit)="search()">
               <div class="ui-grid ui-grid-responsive ui-fluid">
                    <div class="ui-g-12">
                         <div class="ui-g-6">
                              <span class="md-inputfield">
                                   <span class="selectSpan">Revenue Contract</span>
                                   <p-autoComplete  inputStyleClass="textbox" [suggestions]="dealArrangementNumbers" 
										(completeMethod)="searchRCList($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								 placeholder="RC Number Type atleast 3 Characters" id="from" [(ngModel)]="ayaraBillingSchedulesReportSearch.dealArrangementId"></p-autoComplete>

                              </span>
                         </div>
                         <div class="ui-g-6 pull-right">
                              <span class="md-inputfield">
                                   <span class="selectSpan">Usage Number</span>
                                   <p-autoComplete [(ngModel)]="ayaraBillingSchedulesReportSearch.sfUsageSummaryNumber"
                                        placeholder="Type Usage Number Value atleast 3 Characters" [suggestions]="valueResults" (completeMethod)="searchValues('USAGE_NUMBER',$event)" 
                                        [ngModelOptions]="{standalone: true}" minLength="3">
                                  </p-autoComplete>
                                   <!-- <input pInputText name="to" id="to"  class="textbox" placeholder="BS ID" [ngModelOptions]="{standalone: true}"
                                        [(ngModel)]="ayaraBillingSchedulesReportSearch.sfUsageSummaryNumber" /> -->
                              </span>
                         </div>
                    </div>
                    <div class="ui-g-12">
                         <div class="ui-g-6">
                              <span class="selectSpan">Usage Start Date From</span>
                              <p-calendar showAnim="slideDown" inputStyleClass="textbox" name="fromPeriod" id="fromPeriod" [monthNavigator]="true"
                                   [yearNavigator]="true" yearRange="1950:2030" appendTo="body"
                                   [ngModelOptions]="{standalone: true}"
                                   [(ngModel)]="ayaraBillingSchedulesReportSearch.fromPeriod" dateFormat="yy-mm-dd"
                                   placeholder="Usage Start Date From"></p-calendar>
                         </div>

                         <div class="ui-g-6 pull-right">
                              <span class="selectSpan">Usage End Date To</span>
                              <p-calendar showAnim="slideDown" name="toPeriod" inputStyleClass="textbox" id="toPeriod" [monthNavigator]="true"
                                   [yearNavigator]="true" yearRange="1950:2030" appendTo="body"
                                   [ngModelOptions]="{standalone: true}" [(ngModel)]="ayaraBillingSchedulesReportSearch.toPeriod"
                                   dateFormat="yy-mm-dd" placeholder="Usage End Date To"></p-calendar>
                         </div>
					</div>
					
                    <div class="ui-g-12">
                         <div class="ui-g-6">
                              <span class="md-inputfield">
                                   <span class="selectSpan">Quote Number</span>
                                   <!-- <input pInputText name="quoteNumber" id="quoteNumber" class="textbox" placeholder="Quote Number" [ngModelOptions]="{standalone: true}"
                                        [(ngModel)]="ayaraBillingSchedulesReportSearch.quoteNumber" /> -->
                                        <p-autoComplete [(ngModel)]="ayaraBillingSchedulesReportSearch.quoteNumber"
                                        placeholder="Type Quote Number Value atleast 3 Characters" [suggestions]="valueResults" (completeMethod)="searchValues('QUOTE_NUMBER',$event)" 
                                        [ngModelOptions]="{standalone: true}" minLength="3">
                                  </p-autoComplete>     
                              </span>
                         </div>
                         <div class="ui-g-6 pull-right">
                              <span class="md-inputfield">
                                   <span class="selectSpan">Quote Line Number</span>
                                   <!-- <input pInputText name="quoteLine" id="quoteLine" class="textbox" placeholder="Quote Line Number" [ngModelOptions]="{standalone: true}"
                                        [(ngModel)]="ayaraBillingSchedulesReportSearch.quoteLineNumber" /> -->
                                        <p-autoComplete [(ngModel)]="ayaraBillingSchedulesReportSearch.quoteLineNumber"
                                        placeholder="Type Quote Line Number Value atleast 3 Characters" [suggestions]="valueResults" (completeMethod)="searchValues('QUOTE_LINE_NUMBER',$event)" 
                                        [ngModelOptions]="{standalone: true}" minLength="3">
                                      </p-autoComplete>
                              </span>
                         </div>
                    </div>
                    <div class="ui-g-12">
                         <div class="ui-g-6">
                              <span class="md-inputfield">
                                   <span class="selectSpan">Order Number</span>
                                   <p-autoComplete [(ngModel)]="ayaraBillingSchedulesReportSearch.orderNumber"
                                        placeholder="Type Order Number Value atleast 3 Characters" [suggestions]="valueResults" (completeMethod)="searchValues('ORDER_NUMBER',$event)" 
                                        [ngModelOptions]="{standalone: true}" minLength="3">
                                  </p-autoComplete>
                                   <!-- <input pInputText name="orderNumber" id="orderNumber" class="textbox" placeholder="Order Number" [ngModelOptions]="{standalone: true}"
                                        [(ngModel)]="ayaraBillingSchedulesReportSearch.orderNumber" /> -->
                              </span>
                         </div>
                         <div class="ui-g-6 pull-right">
                              <span class="md-inputfield">
                                   <span class="selectSpan">Order Line Number</span>
                                   <!-- <input pInputText name="orderLine" id="orderLine" class="textbox" placeholder="Order Line Number" [ngModelOptions]="{standalone: true}"
                                        [(ngModel)]="ayaraBillingSchedulesReportSearch.orderLineNumber" /> -->
                                        <p-autoComplete [(ngModel)]="ayaraBillingSchedulesReportSearch.orderLineNumber"
                                        placeholder="Type Order Number Value atleast 3 Characters" [suggestions]="valueResults" (completeMethod)="searchValues('ORDER_LINE_NUMBER',$event)" 
                                        [ngModelOptions]="{standalone: true}" minLength="3">
                                  </p-autoComplete>     
                              </span>
                         </div>
                    </div>
                    
                    <div class="ui-g-12">
                         <div class="ui-g-6">
                              <span class="md-inputfield">
                                   <span class="selectSpan">Customer Name</span>
                                   <p-autoComplete  inputStyleClass="textbox" [suggestions]="customers" 
                                   (completeMethod)="searchCustomerNames($event)" styleClass="wid100"
                                        appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
                                       placeholder="Customer Name Type atleast 3 Characters" id="from" [(ngModel)]="selectedCustomer" field="label"></p-autoComplete>
                              </span>
                         </div>
                         <div class="ui-g-6 pull-right">
                              <span class="md-inputfield">
                                   <span class="selectSpan">Customer Number</span>
                                     <p-autoComplete  inputStyleClass="textbox" [suggestions]="customerNumbers" 
										(completeMethod)="searchCustomerNumbers($event)" styleClass="wid100"
       								appendTo="body" [minLength]="3" [ngModelOptions]="{standalone: true}" 
	   								placeholder="Customer Number Type atleast 3 Characters" id="customerNumber" [(ngModel)]="ayaraBillingSchedulesReportSearch.customerNumber" ></p-autoComplete>
                              </span>
                         </div>
                    </div>
					
					<div class="ui-g-12">
                         <div class="ui-g-6">
                              <span class="md-inputfield">
                                   <span class="selectSpan">Sales Contract Number</span>
                                   <!-- <input pInputText name="salesContract" id="salesContract" class="textbox" placeholder="Sales Contract Number" [ngModelOptions]="{standalone: true}" [(ngModel)]="ayaraBillingSchedulesReportSearch.salesContract" /> -->
                                   <p-autoComplete [(ngModel)]="ayaraBillingSchedulesReportSearch.salesContract"
                                        placeholder="Type Sales Contract Value atleast 3 Characters" [suggestions]="valueResults" (completeMethod)="searchValues('SALES_CONTRACT',$event)" 
                                        [ngModelOptions]="{standalone: true}" minLength="3">
                                      </p-autoComplete>
                              </span>
                         </div>
                         <div class="ui-g-6 pull-right">
                              <span class="md-inputfield">
                                   <span class="selectSpan">Asset ID</span>
                                   <!-- <input pInputText name="assetId" id="assetId" class="textbox" placeholder="Asset ID" [ngModelOptions]="{standalone: true}"
                                        [(ngModel)]="ayaraBillingSchedulesReportSearch.assetId" /> -->
                                        <p-autoComplete [(ngModel)]="ayaraBillingSchedulesReportSearch.assetId"
                                        placeholder="Type Asset ID Value atleast 3 Characters" [suggestions]="valueResults" (completeMethod)="searchValues('ASSET_ID',$event)" 
                                        [ngModelOptions]="{standalone: true}" minLength="3">     
                                      </p-autoComplete>
                              </span>
                         </div>
                    </div>
                    

               </div>
               <p-footer>
                    <div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix pull-right">
                         <button type="button" pButton class="primary-btn" (click)="search()" label="Search"></button>
                         <button type="button" pButton class="secondary-btn" (click)="displaySearchDialog=false" label="Cancel"></button>
                    </div>
               </p-footer>
          </form>
     </p-dialog>
