export interface AyaraBillingSchedulesReport {
    usageId?: any;
    sfUsageSummaryId?: any;
    sfUsageSummaryNumber?: any;
    orderNumber?: any;
    orderLineNumber?: any;
    dealArrangementId?: any;
    salesContract?: any;
    dealLineNumber?: any;
    productName?: any;
    feeAmount?: any;
    subTotal?: any;
    cumulativeSubTotal?: any;
    bsDescription?: any;
    summaryStartDate?: any;
    summaryEndDate?: any;
    assetId?: any;
    assetNumber?: any;
    productFamily?: any;
    productLine?: any;
    customerName?: any;
    customerNumber?: any;
    legacy?: any;
    chargeType?: any;
    revenuePolicy?: any;
    opsRevenueName?: any;
    productCode?: any;
    sourceProductId?: any;
    usageSyncPeriod?: any;
    beginBalance?: any;
    currentBalance?: any;
    endingBalance?: any;
    primaryLineNumber?: any;
    itemSequenceNumber?: any;
    quoteNumber?: any;
    quoteLineNumber?: any;
    opportunityNum?: any;
}
