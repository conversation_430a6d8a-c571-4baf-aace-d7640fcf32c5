import { Location } from '@angular/common';
import { Component, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Table } from 'primeng/table';
import { CommonSharedService } from '../shared/common.service';
import { RmanCustomers } from '../rmanCustomers/rmanCustomers';
import { RmanCustomersImpl } from '../rmanCustomers/rmanCustomers.component';
import { NotificationService } from '../shared/notifications.service';
import { AyaraBillingSchedulesReport } from './ayaraBillingSchedulesReport';
import { AyaraBillingSchedulesReportService } from './ayaraBillingSchedulesReportService';
import { RmanCustomersService } from '../rmanCustomers/rmanCustomersservice';
import { RmanFiscalPeriodsService } from '../rmanFiscalPeriods/rmanFiscalPeriodsservice';

declare var require: any;
const appSettings = require('../appsettings');

declare var $: any;

@Component({
  templateUrl: './ayaraBillingSchedulesReport.component.html',
  selector: 'ayaraBillingSchedulesReport-data',
  providers: [AyaraBillingSchedulesReportService,RmanCustomersService,RmanFiscalPeriodsService]
})

export class AyaraBillingSchedulesReportComponent {

  displayDialog: boolean;
  loading: boolean = true;

  displaySearchDialog: boolean;

  ayaraBillingSchedulesReport: any = {};

  ayaraBillingSchedulesReportSearch: any = {};

  isSerached: number = 0;

  @Input() arrId: any;

  selectedAyaraBillingSchedulesReport: AyaraBillingSchedulesReport;

  newAyaraBillingSchedulesReport: boolean;

  ayaraBillingSchedulesReportList: any[] = [];

  columns: any[];

  paginationOptions = {};

  pages: {};

  datasource: any[];
  pageSize: number;
  totalElements: number;
  collapsed: boolean = true;
  
  showAddColumns = true;
  isSelectAllChecked = true;

  globalCols: any[];
  clonedCols: any[];
  userId: number;
  showPaginator: boolean = true;
  startIndex: number;

  exportCols: string[] = [];
  noData = appSettings.noData;
  disableExport: boolean = true;
  
  selectedCustomer:any = {};
  customers:any[] = [];
  rmanCustomers: RmanCustomers = new RmanCustomersImpl();
	
  customerNames: String[]  = [];
  customerNumbers: String[]= [];
  dealArrangementNumbers: String[]  = [];
  
  currentPeriod : any = {};

  valueResults: string[];  

  filteredCustomerNumberList:any[];


  constructor(private route: ActivatedRoute, private ayaraBillingSchedulesReportService: AyaraBillingSchedulesReportService, private location: Location, private commonSharedService: CommonSharedService,
    private notificationService: NotificationService,private rmanCustomersService: RmanCustomersService, private rmanFiscalPeriodsService: RmanFiscalPeriodsService) {

    // generate list code
    this.paginationOptions = { 'pageNumber': 0, 'pageSize': '10000' };
	this.rmanFiscalPeriodsService.getCurrentfiscalperiod().then(response=>{
		this.currentPeriod = response;
	});
  }

  ngOnInit() {
	
    this.route.params.subscribe(params => {
      this.arrId = params['id'];
    });
	
    this.globalCols = [
    { field: 'sfUsageSummaryNumber', header: 'Usage Number', showField: true, display: "table-cell", type: 'text', drag: false },
		{ field: 'orderNumber', header: 'Order Number', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'orderLineNumber', header: 'Order Line Number', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'dealArrangementId', header: 'Revenue Contract Number', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'salesContract', header: 'Sales Contract Number', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'dealLineNumber', header: 'Line Number', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'productName', header: 'Product Name', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'feeAmount', header: 'Fee Amount', showField: true, display: "table-cell", type: 'round', drag: true },
		{ field: 'subTotal', header: 'Sub Total', showField: true, display: "table-cell", type: 'round', drag: true },
		{ field: 'cumulativeSubTotal', header: 'Cumulative Usage Total', showField: true, display: "table-cell", type: 'round', drag: true },
		{ field: 'bsDescription', header: 'BS Description', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'summaryStartDate', header: 'Usage Start Date', showField: true, display: "table-cell", type: 'date', drag: true },
		{ field: 'summaryEndDate', header: 'Usage End Date', showField: true, display: "table-cell", type: 'date', drag: true },
		{ field: 'assetId', header: 'Asset ID', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'assetNumber', header: 'Asset Number', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'productFamily', header: 'Product Family', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'productLine', header: 'Product Line', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'customerName', header: 'Customer Name', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'customerNumber', header: 'Customer Number', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'legacy', header: 'Legacy', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'chargeType', header: 'Charge Type', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'revenuePolicy', header: 'Revenue Policy', showField: true, display: "table-cell", type: 'text', drag: true },
	  { field: 'opsRevenueName', header: 'OpsRevenue Name', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'productCode', header: 'Product Code', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'sourceProductId', header: 'Product Case Safe Id', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'usageSyncPeriod', header: 'Period (Sync period)', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'beginBalance', header: 'Revenue Beginning Balance', showField: true, display: "table-cell", type: 'round', drag: true },
		{ field: 'currentBalance', header: 'Revenue Current Balance', showField: true, display: "table-cell", type: 'round', drag: true },
		{ field: 'endingBalance', header: 'Revenue Ending Balance', showField: true, display: "table-cell", type: 'round', drag: true },
		{ field: 'primaryLineNumber', header: 'Primary Line Number', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'itemSequenceNumber', header: 'Item Sequence Number', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'quoteNumber', header: 'Quote Number', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'quoteLineNumber', header: 'Quote Line Number', showField: true, display: "table-cell", type: 'text', drag: true },
		{ field: 'opportunityNum', header: 'Opportunity Number', showField: true, display: "table-cell", type: 'text', drag: true }
    ];

    this.columns = [];
    this.getTableColumns("ayaraBillingSchedulesReport", "Ayara Billing Schedules Report");
  }

  goToOperationReports() {
    this.location.back();
  }

  reset(dt: Table) {
    this.paginationOptions = {};
    this.ayaraBillingSchedulesReport = {};
    this.ayaraBillingSchedulesReportSearch = {};
    this.isSerached=0;
    dt.reset();
  }
  getTableColumns(pageName: string, tableName: string) {
    this.isSelectAllChecked = true;
    this.commonSharedService.getConfiguredColDetails(pageName, tableName).then((response) => {
      if (response && response != null && response.userId) {
        this.columns = [];
        let colsList = response.tableColumns.split(",");
        if (colsList.length > 0) {
          colsList.forEach((item, index) => {
            if (item) {
              this.startIndex = this.globalCols.findIndex(col => col.field == item);
              this.onDrop(index);
            }
          });
        }
        this.globalCols.forEach(col => {
          if (response.tableColumns.indexOf(col.field) !== -1) {
            this.columns.push(col);
          } else {
            col.showField = false;
          }
        });
        if (this.columns.length != this.globalCols.length) this.isSelectAllChecked = false;
        this.showPaginator = this.columns.length !== 0;
        this.userId = response.userId;
      } else {
        this.columns = this.globalCols;
      }
    }).catch(() => {
      this.notificationService.showError('Error occured while getting table columns data');
      this.loading = false;
    });
  }

  saveColumns() {
    let selectedCols = "";
    this.showAddColumns = !this.showAddColumns;
    const colLength = this.globalCols.length - 1;
    this.globalCols.forEach((col, index) => {
      if (col.showField) {
        selectedCols += col.field;
        if (index < colLength) {
          selectedCols += ",";
        }
      }
    });
    this.loading = true;
    this.commonSharedService.saveOrUpdateTableColumns("ayaraBillingSchedulesReport", "Ayara Billing Schedules Report", selectedCols, this.userId).then((response) => {
      this.columns = this.globalCols.filter(item => item.showField);
      this.userId = response["userId"];
      this.showPaginator = this.columns.length !== 0;
      this.loading = false;
    }).catch(() => {
      this.notificationService.showError('Error occured while getting data');
      this.loading = false;
    });
  }

  onDragStart(index: number) {
    this.startIndex = index;
  }

  onDrop(dropIndex: number) {
    const general = this.globalCols[this.startIndex]; // get element
    this.globalCols.splice(this.startIndex, 1);       // delete from old position
    this.globalCols.splice(dropIndex, 0, general);    // add to new position
  }

  selectColumns(col: any) {
    let cols = this.globalCols.filter(item => !item.showField);
    if (cols.length > 0) {
      this.isSelectAllChecked = false;
    } else {
      this.isSelectAllChecked = true;
    }
  }

  onSelectAll() {
    this.isSelectAllChecked = !this.isSelectAllChecked;
    this.globalCols.forEach(col => {
      if (this.isSelectAllChecked) {
        col.showField = true;
      } else {
        if (col.drag) {
          col.showField = false;
        }
      }
    });
  }

  onConfiguringColumns(event: any) {
    this.clonedCols = JSON.parse(JSON.stringify(this.globalCols));
    this.showAddColumns = false;
  }

  closeConfigureColumns(event: any) {
    this.showAddColumns = true;
    this.globalCols = this.clonedCols;
    let configCol = this.globalCols.filter(item => !item.showField);
    this.isSelectAllChecked = !(configCol.length > 0);
  }
  
  getAllAyaraBillingSchedulesReport() {
    this.loading = true;
    
    this.ayaraBillingSchedulesReportService.getAllAyaraBillingSchedulesReport(this.paginationOptions, this.ayaraBillingSchedulesReport, this.exportCols).then((ayaraBillingSchedulesReportList: any) => {
      this.loading = false;
      this.datasource = ayaraBillingSchedulesReportList.content;
      this.ayaraBillingSchedulesReportList = ayaraBillingSchedulesReportList.content;
      this.ayaraBillingSchedulesReportList = this.ayaraBillingSchedulesReportList.slice();
      this.totalElements = ayaraBillingSchedulesReportList.totalElements;
      this.pageSize = ayaraBillingSchedulesReportList.size;
      this.displaySearchDialog = false;
      this.disableExport = false;
    });
  }


  getAyaraBillingSchedulesReport(event: any) {
    this.loading = true;
    let first: number = event.first;
    let rows: number = event.rows;
    let pageNumber: number = first / rows;
    
    this.paginationOptions = { 'pageNumber': pageNumber, 'pageSize': this.pageSize, 'sortField': event.sortField, 'sortOrder': event.sortOrder };
    
    this.ayaraBillingSchedulesReportService.getAllAyaraBillingSchedulesReport(this.paginationOptions, this.ayaraBillingSchedulesReport, this.exportCols).then((ayaraBillingSchedulesReportList: any) => {
	      	this.loading = false;
	      	this.datasource = ayaraBillingSchedulesReportList.content;
	      	this.ayaraBillingSchedulesReportList = ayaraBillingSchedulesReportList.content;
	      	this.totalElements = ayaraBillingSchedulesReportList.totalElements;
	      	this.pageSize = ayaraBillingSchedulesReportList.size;
	      	this.disableExport = false;
    });
    
    
  }

  exportExcel() {
    this.exportCols = [];
    for (let index = 0; index < this.columns.length; index++) {
      if (this.columns[index].showField) {
        this.exportCols.push(this.columns[index].field);
      }
    }

    let serviceUrl = this.ayaraBillingSchedulesReportService.getServiceUrl(this.paginationOptions, this.ayaraBillingSchedulesReportSearch, this.exportCols, 1);
    window.location.href = serviceUrl;

  }


  onRowSelect(event: any) {

  }

  hideColumnMenu: boolean = true;

  toggleColumnMenu() {
    if (this.hideColumnMenu) {
      this.hideColumnMenu = false;
    } else {
      this.hideColumnMenu = true;
    }
  }

  showFilter: boolean = false;

  toggleFilterBox() {
    if (this.showFilter) {
      this.showFilter = false;
    } else {
      this.showFilter = true;
    }
  }

  showDialogToSearch() {
    this.ayaraBillingSchedulesReportSearch = {};
  this.displaySearchDialog = true;
  this.selectedCustomer = {};

  }
    
		
		
    
  searchCustomerNames(event:any){
    this.rmanCustomersService.getAllRmanCustomers(this.paginationOptions, { 'customerName': event.query },true).then((response: any) => {
      //this.customerNames= response.content.map(item => item.customerName);
          this.customers = response.content;
          this.customers.forEach(item=> item.label = item.customerName+" "+item.customerNumber);
        }).catch(err => {
          this.customers = [];
        });
  }
  
  searchCustomerNumbers(event:any){
	  this.rmanCustomersService.getAllRmanCustomers(this.paginationOptions, { 'customerNumber': event.query },false).then((response: any) => {
	  	this.customerNumbers= response.content.map(item => item.customerNumber);
	  }).catch(err => {
      this.customerNumbers = [];
    });  
  }
  
  searchRCList(event:any){
	  this.ayaraBillingSchedulesReportService.fetchRCNumbers(event.query).then((response: any) => {
	  	this.dealArrangementNumbers= response.map(item => item);
	  }).catch(err => {
      this.dealArrangementNumbers = [];
    });  
  }
  									 
  search() {

    this.isSerached = 1;
    if(this.selectedCustomer){
      this.ayaraBillingSchedulesReportSearch.customerNumber = this.selectedCustomer.customerNumber;
    }
    this.ayaraBillingSchedulesReport = this.ayaraBillingSchedulesReportSearch;
    this.displaySearchDialog = false;
    this.paginationOptions = {};
    this.getAllAyaraBillingSchedulesReport();
  }

  onBeforeToggle(evt: any) {
    this.collapsed = evt.collapsed;
  }

  searchValues(parameterName:any,event: any) {
    this.ayaraBillingSchedulesReportService.getParameterValues(parameterName, event.query)
          .subscribe(results => this.valueResults = results);
  }


}


export class AyaraBillingSchedulesReportImpl implements AyaraBillingSchedulesReport {
  constructor(
public usageId?: any,
public sfUsageSummaryId?: any,
public sfUsageSummaryNumber?: any,
public orderNumber?: any,
public orderLineNumber?: any,
public dealArrangementId?: any,
public salesContract?: any,
public dealLineNumber?: any,
public productName?: any,
public feeAmount?: any,
public subTotal?: any,
public cumulativeSubTotal?: any,
public bsDescription?: any,
public summaryStartDate?: any,
public summaryEndDate?: any,
public assetId?: any,
public assetNumber?: any,
public productFamily?: any,
public productLine?: any,
public customerName?: any,
public customerNumber?: any,
public legacy?: any,
public chargeType?: any,
public revenuePolicy?: any,
public opsRevenueName?: any,
public productCode?: any,
public sourceProductId?: any,
public usageSyncPeriod?: any,
public beginBalance?: any,
public currentBalance?: any,
public endingBalance?: any,
public primaryLineNumber?: any,
public itemSequenceNumber?: any,
public quoteNumber?: any,
public quoteLineNumber?: any,
public opportunityNum?: any) { }
}

interface ILabels {
  [index: string]: string;
}
