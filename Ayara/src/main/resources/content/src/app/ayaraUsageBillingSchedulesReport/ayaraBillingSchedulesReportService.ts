import { DatePipe } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
declare var require:any;
const appSettings = require('../appsettings');


const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type':  'application/json',
  })
};




@Injectable()
export class AyaraBillingSchedulesReportService {

    constructor(private http: HttpClient,private datePipe:DatePipe) {}


    getServiceUrl(paginationOptions: any, ayaraBillingSchedulesReportSearchObject: any,exportCols: any, exportFlag: any) {

      let serviceUrl = appSettings.apiUrl + '/ayaraBillingSchedulesReportVExport?';

      if (exportFlag == 0) {
        serviceUrl = appSettings.apiUrl + '/ayaraBillingSchedulesReportVSearch?';
      }

        let searchString='';

        if (ayaraBillingSchedulesReportSearchObject.dealArrangementId!=undefined && ayaraBillingSchedulesReportSearchObject.dealArrangementId!="") {
            searchString=searchString+'dealArrangementId:'+ayaraBillingSchedulesReportSearchObject.dealArrangementId+',';
        }
		
		if (ayaraBillingSchedulesReportSearchObject.sfUsageSummaryNumber!=undefined && ayaraBillingSchedulesReportSearchObject.sfUsageSummaryNumber!="") {
            searchString=searchString+'sfUsageSummaryNumber:'+ayaraBillingSchedulesReportSearchObject.sfUsageSummaryNumber+',';
        }

        if (ayaraBillingSchedulesReportSearchObject.quoteNumber!=undefined && ayaraBillingSchedulesReportSearchObject.quoteNumber!="") {
            searchString=searchString+'quoteNumber:'+ayaraBillingSchedulesReportSearchObject.quoteNumber+',';
        }
		
		if (ayaraBillingSchedulesReportSearchObject.quoteLineNumber!=undefined && ayaraBillingSchedulesReportSearchObject.quoteLineNumber!="") {
            searchString=searchString+'quoteLineNumber:'+ayaraBillingSchedulesReportSearchObject.quoteLineNumber+',';
        }

        if (ayaraBillingSchedulesReportSearchObject.orderNumber!=undefined && ayaraBillingSchedulesReportSearchObject.orderNumber!="") {
            searchString=searchString+'orderNumber:'+ayaraBillingSchedulesReportSearchObject.orderNumber+',';
        }

        if (ayaraBillingSchedulesReportSearchObject.orderLineNumber!=undefined && ayaraBillingSchedulesReportSearchObject.orderLineNumber!="") {
            searchString=searchString+'orderLineNumber:'+ayaraBillingSchedulesReportSearchObject.orderLineNumber+',';
        }

        if(ayaraBillingSchedulesReportSearchObject.fromPeriod != undefined && ayaraBillingSchedulesReportSearchObject.fromPeriod != null){
            searchString=searchString+'summaryStartDate>'+ this.datePipe.transform(ayaraBillingSchedulesReportSearchObject.fromPeriod,'yyyyMMdd')+',';
        }
        
        if(ayaraBillingSchedulesReportSearchObject.toPeriod != undefined && ayaraBillingSchedulesReportSearchObject.toPeriod != null){
            searchString=searchString+'summaryEndDate<'+ this.datePipe.transform(ayaraBillingSchedulesReportSearchObject.toPeriod,'yyyyMMdd')+',';
        }
        
        if(ayaraBillingSchedulesReportSearchObject.customerName != undefined && ayaraBillingSchedulesReportSearchObject.customerName != null){
            searchString=searchString+'customerName:'+ ayaraBillingSchedulesReportSearchObject.customerName+',';
        }
        
        if(ayaraBillingSchedulesReportSearchObject.customerNumber != undefined && ayaraBillingSchedulesReportSearchObject.customerNumber != null){
            searchString=searchString+'customerNumber:'+ ayaraBillingSchedulesReportSearchObject.customerNumber+',';
        }
        
        if(ayaraBillingSchedulesReportSearchObject.assetId != undefined && ayaraBillingSchedulesReportSearchObject.assetId != null){
            searchString=searchString+'assetId:'+ ayaraBillingSchedulesReportSearchObject.assetId+',';
        }
        
        if(ayaraBillingSchedulesReportSearchObject.salesContract != undefined && ayaraBillingSchedulesReportSearchObject.salesContract != null){
            searchString=searchString+'salesContract:'+ ayaraBillingSchedulesReportSearchObject.salesContract+',';
        }


        if (searchString == '') {
            serviceUrl=serviceUrl+'search=%25';
        }
        else {
            serviceUrl=serviceUrl+'search='+searchString;
        }

        if (paginationOptions.pageNumber!=undefined && paginationOptions.pageNumber!="" && !isNaN(paginationOptions.pageNumber)) {
           serviceUrl=serviceUrl+'&page='+paginationOptions.pageNumber+'&size='+paginationOptions.pageSize;
        }

        if(exportCols!=undefined && exportCols!=""){
            serviceUrl=serviceUrl+'&exportCols='+exportCols;
        }
          return serviceUrl;
      }

      getAllAyaraBillingSchedulesReport(paginationOptions: any, rmanContReleaseEventsVSearchObject: any, exportCols: any): Promise<any[]> {
        let serviceUrl = this.getServiceUrl(paginationOptions, rmanContReleaseEventsVSearchObject,exportCols, 0);
        return this.http.get(serviceUrl).toPromise().then((data: any) => {
          return data;
        });
      }

	fetchRCNumbers(rc: any) {
        let unpostedRCUrl = appSettings.apiUrl + '/fetchAllRC?rc='+rc;

        return this.http.get(unpostedRCUrl).toPromise().then((data: any) => {
               return data;
         });
    } 

    getParameterValues(paramName: string, paramValue: string) {
        return this.http.get<any>(appSettings.apiUrl + `/fetchFilterValues?parameterName=${paramName}&parameterValue=${paramValue}`);
    }


}
