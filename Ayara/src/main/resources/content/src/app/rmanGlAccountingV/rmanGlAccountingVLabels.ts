interface ILabels {
    [index: string]: string;
}

export class RmanGlAccountingVLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};
        this.fieldLabels["dealArrangementId"] = "Deal Arrangement ID";
        this.fieldLabels["accountDescription"] = "Account Description";
        this.fieldLabels["amountFc"] = "Amount in FC";
        this.fieldLabels["sourceLineNumber"] = "SO Line #";
        this.fieldLabels["sourceLineId"] = "Source Line Id";
        this.fieldLabels["drCr"] = "Debit/Credit";
        this.fieldLabels["postedFlag"] = "Posted Flag";
        this.fieldLabels["lineStatus"] = "Line Status";
        this.fieldLabels["glDate"] = "GL Date";
        this.fieldLabels["currencyCode"] = "Transaction Currency";
        this.fieldLabels["periodName"] = "Period";
        this.fieldLabels["account"] = "Account";
        this.fieldLabels["amountTc"] = "Amount in TC";
        this.fieldLabels["orderNumber"] = "SO #";
        this.fieldLabels["rmanInterfaceId"] = "Rman Interface ID";
        this.fieldLabels["dealArrangementNumber"] = "Arrangement Number";
        this.fieldLabels["dealArrangementName"] = "Arrangement Name";
        this.fieldLabels["accountClass"] = "Account Class";
        this.fieldLabels['functionalCurrency'] = "Functional Currency";
        this.fieldLabels["dealLineNumber"] = "Deal Line #";
    }

}
