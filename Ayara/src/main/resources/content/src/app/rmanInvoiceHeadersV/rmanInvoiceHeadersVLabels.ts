interface ILabels {
    [index: string]: string;
}

export class RmanInvoiceHeadersVLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};
        this.fieldLabels["fxRate"] = "Fx Rate";
        this.fieldLabels["fxDate"] = "Fx Date";
        this.fieldLabels["sourceInvoiceId"] = "Source Invoice Id";
        this.fieldLabels["invoiceType"] = "Invoice Type";
        this.fieldLabels["invoiceTrxType"] = "InvoiceTrx Type";
        this.fieldLabels["invoiceSource"] = "Invoice Source";
        this.fieldLabels["salesOrderNumber"] = "SO #";
        this.fieldLabels["invoiceNumber"] = "Invoice Number";
        this.fieldLabels["invoicedDate"] = "Invoiced Date";
        this.fieldLabels["invoiceAmount"] = "Invoice Amount";
        this.fieldLabels["invoiceCurrency"] = "Invoice Currency";
        this.fieldLabels["arrangementId"] = "Arrangement Id";
        this.fieldLabels["acctdCurrency"] = "Functional Currency";
    }

}
