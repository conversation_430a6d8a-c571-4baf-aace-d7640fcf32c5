interface ILabels {
    [index: string]: string;
}

export class RmanEntityParametersVLabels {

    fieldLabels: ILabels;

    constructor() {

        this.fieldLabels = {};

        this.fieldLabels["tableName"] = "Table Name";
        this.fieldLabels["startDateActive"] = "Start Date Active";
        this.fieldLabels["entityId"] = "Entity Id";
        this.fieldLabels["entityCategory"] = "Entity Category";
        this.fieldLabels["columnName"] = "Column Name";
        this.fieldLabels["entityDescription"] = "Entity Description";
        this.fieldLabels["enabledFlag"] = "Enabled Flag";
        this.fieldLabels["columnSequence"] = "Column Sequence";
        this.fieldLabels["parameterName"] = "Parameter Name";
        this.fieldLabels["entityParameterId"] = "Entity Parameter Id";
        this.fieldLabels["columnType"] = "Column Type";
        this.fieldLabels["parameterDescription"] = "Parameter Description";
        this.fieldLabels["endDateActive"] = "End Date Active";
        this.fieldLabels["columnSize"] = "Column Size";
        this.fieldLabels["entityName"] = "Entity Name";
    }

}
