{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"Revmatra": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "allowedCommonJsDependencies": ["chart.js", "raf", "core-js", "@babel/runtime-corejs3"], "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/assets/css/bootstrap.min.css", "node_modules/primeng/resources/primeng.min.css", "src/assets/theme/theme-dark-blue.css", "src/assets/layout/css/layout-dark-blue.css", "src/assets/css/font-awesome.min.css", "src/assets/js/pivot.min.css", "node_modules/primeicons/primeicons.css", "node_modules/jquery-contextmenu/dist/jquery.contextMenu.min.css", "node_modules/smart-webcomponents-angular/source/styles/smart.default.css", "src/assets/css/style.css", "src/styles.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "src/assets/js/bootstrap.min.js", "node_modules/jquery-contextmenu/dist/jquery.contextMenu.js", "src/assets/js/jquery.blockUI.js", "src/assets/js/pivot.min.js", "src/assets/js/chart.js", "src/assets/js/jszip.min.js", "src/assets/js/pdfmake.min.js", "src/assets/js/vfs_fonts.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": true, "deleteOutputPath": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "6mb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "Revmatra:build"}, "configurations": {"production": {"browserTarget": "Revmatra:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "Revmatra:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.css"], "scripts": [], "assets": ["src/favicon.ico", "src/assets"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "Revmatra-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "Revmatra:serve"}, "configurations": {"production": {"devServerTarget": "Revmatra:serve:production"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "<PERSON><PERSON><PERSON>"}