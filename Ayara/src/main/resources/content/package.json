{"name": "content", "version": "1.0.0", "private": true, "scripts": {"ng": "ng", "start": "ng serve", "build": "npm run del && ng build --configuration production", "test": "ng test", "del": "node -e \"const fs=require('fs');['browserslist','browserslist.cmd'].forEach(f=>{try{fs.unlinkSync(f)}catch(e){}})\""}, "dependencies": {"@angular/animations": "19.2.15", "@angular/common": "19.2.15", "@angular/compiler": "19.2.15", "@angular/core": "19.2.15", "@angular/forms": "19.2.15", "@angular/platform-browser": "19.2.15", "@angular/platform-browser-dynamic": "19.2.15", "@angular/router": "19.2.15", "angular-split": "^3.0.3", "chart.js": "^2.9.4", "compression": "^1.7.4", "exceljs": "^4.2.0", "jquery": "^2.2.4", "jquery-contextmenu": "^2.9.0", "jspdf": "^2.3.0", "ng2-charts": "^2.4.2", "primeicons": "^4.1.0", "primeng": "^10.0.3", "rxjs": "^7.8.1", "smart-webcomponents-angular": "^9.4.1", "tslib": "^2.6.2", "zone.js": "^0.14.7"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.0", "@angular/cli": "19.2.15", "@angular/compiler-cli": "19.2.15", "@angular/language-service": "19.2.15", "@types/jasmine": "^5.1.0", "@types/jasminewd2": "~2.0.3", "@types/jquery": "^2.0.46", "@types/node": "^20.11.30", "gzipper": "^4.4.0", "jasmine-core": "^5.1.1", "jasmine-spec-reporter": "^7.0.0", "karma": "^6.4.2", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.0", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "source-map-explorer": "^2.5.3", "ts-node": "^10.9.1", "typescript": "~5.6.3"}}